<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class NewEmployee extends Model
{
    use HasFactory,LogsActivity;

    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*'])
            ->useLogName('hrms_new_employees')
            ->logOnlyDirty()
            ->dontLogIfAttributesChangedOnly(['updated_at'])
            ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    public function mainBranch()
    {
        return $this->belongsTo(Category::class, 'main_branch_id');
    }

    public function designationName()
    {
        return $this->belongsTo(Designation::class, 'designation_id');
    }

    public function genderName()
    {
        return $this->belongsTo(Category::class, 'gender_id');
    }

    public function civilStatusName()
    {
        return $this->belongsTo(Category::class, 'civil_status_id');
    }

    public function raceName()
    {
        return $this->belongsTo(Category::class, 'race_id')->withDefault();
    }

    public function religionName()
    {
        return $this->belongsTo(Category::class, 'religion_id')->withDefault();
    }

    public function citizenshipeStateName()
    {
        return $this->belongsTo(Category::class, 'state_of_citizenship_id');
    }

    public function higheduName()
    {
        return $this->belongsTo(Category::class, 'emp_highest_edu_level');
    }

    public function workTypeName()
    {
        return $this->belongsTo(Category::class, 'employee_work_type');
    }

    public function permanentCityName()
    {

        return $this->belongsTo(City::class, 'permanent_city_id');
    }

    public function postalCityName()
    {

        return $this->belongsTo(City::class, 'postal_city_id');
    }

    public function getNameTitle()
    {
        return $this->belongsTo(Category::class, 'title_id');
    }

    public function getDepartmentName()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }


    public function getAddedOperator()
    {
        return $this->belongsTo(Employee::class, 'added_ma_id');
    }

    public function getFacultyName()
    {
        return $this->belongsTo(Faculty::class, 'faculty_id');
    }

    public function designation()
    {
        return $this->belongsTo(Designation::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'title_id'); // Assuming 'category_id' is the foreign key in the employees table that links to the categories table
    }

    public function getPaymentMethod()
    {
        return $this->belongsTo(Category::class, 'salary_payment_type');
    }
}
