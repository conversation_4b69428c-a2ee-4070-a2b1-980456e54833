<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Department;
use App\Models\Designation;
use App\Models\Employee;
use App\Models\Faculty;
use Illuminate\Http\Request;

class InsuranceController extends Controller
{


    public function empDetailsGet(Request $request)
    {

        $data = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            //->join('categories as genderTb', 'genderTb.id', '=', 'employees.gender_id')
            ->join('categories as cstb', 'cstb.id', '=', 'employees.civil_status_id')
            ->select(
                'employees.employee_no',
                'employees.initials',
                'employees.last_name',
                'employees.name_denoted_by_initials',
                'categories.category_name as title',
                'employees.nic',
                'employees.date_of_birth',
                'employees.mobile_no',
                'employees.email',
                'employees.gender_id',
                'employees.civil_status_id',
                'cstb.category_name as cStatus',
                'employees.designation_id',
                'employees.department_id',
                'employees.faculty_id'
            )
            ->where('employees.employee_no', $request->empNo)
            // ->where('employees.employee_status_id', 110)
            // ->wherein('employees.employee_work_type', [139, 138])
            ->get();

        return $data;

    }

    public function empDesigGet(Request $request)
    {
        $dataText = Designation::join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->select('designations.designation_name', 'categories.category_name')
            ->where('designations.id', $request->deigID)
            ->get();

        return $dataText;
    }

    public function empDepGet(Request $request)
    {
        $dataText = Department::join('faculties', 'faculties.id', '=', 'departments.faculty_code')
            ->where('departments.id', $request->depID)
            ->select('departments.department_name', 'faculties.faculty_name', 'faculties.id')
            ->get();

        return $dataText;
    }

    public function empFacGet(Request $request)
    {

        $dataText = Faculty::where('id', $request->facID)
            ->select('faculty_name')
            ->get();

        return $dataText;
    }

    public function allEmp(Request $request)
    {
        $empIDs = $request->input('empIDs', []);

        $data = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
        ->join('departments','departments.id','=','employees.department_id')
            ->select(
                'employees.employee_no',
                'employees.initials',
                'employees.last_name',
                'employees.name_denoted_by_initials',
                'categories.category_name as title',
                'employees.nic',
                'employees.date_of_birth',
                'departments.department_name',
                'employees.faculty_id',
                'employees.mobile_no'
            )
            ->whereIn('employees.employee_no', $empIDs)
            ->get();

        return $data;

    }

    public function eligibilityCount()
    {
        $data = Employee::where('employees.employee_status_id', 110)
            ->wherein('employees.employee_work_type', [139, 138])
            ->count();

        return $data;
    }
}
