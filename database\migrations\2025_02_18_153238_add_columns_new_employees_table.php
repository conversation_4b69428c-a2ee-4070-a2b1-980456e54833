<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('new_employees', function (Blueprint $table) {
            $table->integer('carder_faculty_id')->default(0)->after('sub_department_id');
            $table->integer('carder_department_id')->default(0)->after('carder_faculty_id');
            $table->string('carder_sub_department_id')->nullable()->after('carder_department_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('new_employees', function (Blueprint $table) {
            $table->dropColumn('carder_faculty_id');
            $table->dropColumn('carder_department_id');
            $table->dropColumn('carder_sub_department_id');
        });
    }
};
