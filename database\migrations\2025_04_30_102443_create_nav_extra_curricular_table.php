<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('nav_extra_curricular', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id'); // Reference to application form ID
            $table->text('description')->nullable(); // Text description of extra curricular activities
            $table->integer('status')->default(1); // Status flag (1 = active, 0 = inactive)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('nav_extra_curricular');
    }
};
