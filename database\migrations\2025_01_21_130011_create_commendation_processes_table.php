<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('commendation_processes', function (Blueprint $table) {
            $table->id();
            $table->integer('type_id');
            $table->integer('emp_no');
            $table->date('letter_date');
            $table->integer('page_no')->default(0);
            $table->text('officer_name');
            $table->text('officer_position');
            $table->text('description');
            $table->integer('ad_user_id');
            $table->date('add_date');
            $table->integer('accept_user_id')->default(0);
            $table->date('accept_date')->nullable();
            $table->integer('status')->default(0);
            $table->integer('remove_user_id')->default(0);
            $table->date('remove_date')->nullable();            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('commendation_processes');
    }
};
