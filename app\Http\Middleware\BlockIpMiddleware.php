<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class BlockIpMiddleware
{
    // set IP addresses
    public $blockIps = ['ip-addr-1', 'ip-addr-2'];


    public function handle($request, Closure $next)
    {
        if (in_array($request->ip(), $this->blockIps)) {
            abort(403);
            //return response()->json(['message' => "You don't have permission to access this website."]);
        }
        return $next($request);
    }
}
