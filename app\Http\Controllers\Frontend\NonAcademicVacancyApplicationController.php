<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\ApplicationForm;
use App\Models\VancancyNonAcademic;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cookie;

class NonAcademicVacancyApplicationController extends Controller
{
    public function index()
    {
        return view('frontend.nonacademic.index');
    }

    public function vacancyList(){

        $currentDate = date('Y-m-d');

        $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                         ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                         ->select('vancancy_non_academics.*','categories.display_name')
                         ->where('date_closed', '>=', $currentDate)
                         ->where('date_opened', '<=', $currentDate)
                         ->where('vacancy_visibility_status', '=', 200)
                         ->where('vacancy_status_id', '=', 1)
                         ->whereIn('vacancy_status_type_id', array(27, 259))
                         ->orderBy('vancancy_non_academics.id')
                         ->get();
        return view('frontend.nonacademic.vacancy_list',compact('vacancies'));
    }

    public function jobSelect(Request $request)
    {

        Log::info('IndexController-> Applicate job select function started');

        session()->get('vacancy_id');
        session()->forget('vacancy_id');
        $session = Session::put('vacancy_id', $request->vacancy_id);

        return redirect()->route('nonacademic.applicant.login');

        Log::info('IndexController-> Applicate selected vacancy id - ' . $session);
        Log::info('IndexController-> Applicate job select function ended');
    }

    public function applicantLogin()
    {

        Log::info('IndexController-> Applicant Login get started');

        if (!session()->get('vacancy_id')) {

            $notification = array(
                'message' => 'Your application session has been expired',
                'alert-type' => 'error'
            );

            return redirect()->route('home')->with($notification);
        }

        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'categories.display_name')
            ->find(session()->get('vacancy_id'));

        return view('frontend.nonacademic.user_register', compact('vacancy'));

        Log::notice('IndexController-> Applicate login for vacancy id - ' . session()->get('vacancy_id'));

        Log::info('IndexController-> Applicamt Login ended');
    }


    public function userVerification(Request $request)
    {
        Log::info('IndexController-> Email and mobile verification get started');

        $validatedData = $request->validate([
            'nic' => ['required', 'regex:/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/m'],
            'mobile_no' => ['required', 'regex:/^(\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/m'],
            'terms' => ['required'],
            'vacancy_id' => ['required'],
        ], [
            'nic.required' => 'please enter valid NIC number',
            'mobile_no.required' => 'please enter valid mobile number',
            'terms.required' => 'please agree to terms and conditions',
            'mobile_no.regex' => 'you entered phone number in invalid format',
        ]);

        $duplicateCheck = $this->applicationDuplicationCheck($validatedData['nic'], $validatedData['vacancy_id']);
        if ($duplicateCheck) {
            return $duplicateCheck;
        }

        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($validatedData['nic'])]);

        $nicData = json_decode($empDetails->body(), true);

        //dd($nicData['age']);

        // Generate OTP
        $otp_code = random_int(100000, 999999); // 6-digit OTP

        Log::alert('NonAcademicVacancyApplicationController-> Mobile verification OTP code - ' . $otp_code . ' send to the ' . $validatedData['mobile_no'] . ' with NIC ' . $validatedData['nic']);

        // Store OTP in cookie (valid for 10 minutes)
        Cookie::queue('mobile_otp_code', $otp_code, 10);

        // Store validated data in session
        session([
            'vacancy_id' => $validatedData['vacancy_id'],
            'nic' => $validatedData['nic'],
            'mobile_no' => $validatedData['mobile_no'],
            'dob' => $nicData['dob'],
            'age' => $nicData['age'],
        ]);

        // Send SMS OTP (using the existing SMS API)
        $this->sendSmsOtp($validatedData['mobile_no'], $otp_code);

        return redirect()->route('nonacademic.mobile.otp.screen');
    }

    private function sendSmsOtp($mobile, $otp)
    {
        try {
            $message = "{$otp} is your verification OTP. Valid for 10 minutes. Do not share this code.";

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://usjnetsso.sjp.ac.lk/sso/api/sms/gateway',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => array(
                    'message' => $message,
                    'destination' => $mobile,
                    'q' => 'e4b0e6fda62e71c354ac723d9a0e75b6'
                ),
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            $responseData = json_decode($response, true);

            if ($httpCode == 200 && isset($responseData['status']) && $responseData['status'] === true) {
                Log::info("SMS OTP sent to {$mobile}: {$otp}");
                return true;
            } else {
                Log::error("Failed to send SMS OTP to {$mobile}: " . $response);
                return false;
            }
        } catch (\Exception $e) {
            Log::error("SMS OTP sending failed: " . $e->getMessage());
            return false;
        }
    }
    public function mobileOtpScreen()
    {
        Log::info('NonAcademicVacancyApplicationController-> Mobile OTP Screen accessed');

        if (!session()->has('mobile_no') || !session()->has('vacancy_id')) {
            $notification = array(
                'message' => 'Your session has expired. Please start again.',
                'alert-type' => 'error'
            );
            return redirect()->route('nonacademic.vacancy.list')->with($notification);
        }

        $mobile = session('mobile_no');
        $maskedMobile = substr($mobile, 0, 3) . '****' . substr($mobile, -3);

        return view('frontend.nonacademic.mobile_otp_verification', compact('maskedMobile'));
    }

    public function mobileOtpVerification(Request $request)
    {
        Log::info('NonAcademicVacancyApplicationController-> Mobile OTP verification started');

        $otp = $request->otp_1 . $request->otp_2 . $request->otp_3 . $request->otp_4 . $request->otp_5 . $request->otp_6;

$request->merge(['full_otp' => $otp]);

$request->validate([
    'full_otp' => 'required|numeric|digits:6',
], [
    'full_otp.required' => 'Please enter a valid 6-digit OTP.',
    'full_otp.numeric' => 'OTP must contain only numbers.',
    'full_otp.digits' => 'OTP must be exactly 6 digits.',
]);

        $userOtp = $request->otp_1 . $request->otp_2 . $request->otp_3 . $request->otp_4 . $request->otp_5 . $request->otp_6;
        $storedOtp = request()->cookie('mobile_otp_code');

        if ($storedOtp && $userOtp == $storedOtp) {
            // OTP verified successfully
            Log::info('NonAcademicVacancyApplicationController-> Mobile OTP verification successful for NIC: ' . session('nic'));

            // Clear OTP cookie
            Cookie::queue(Cookie::forget('mobile_otp_code'));

            return redirect()->route('application.form.view');
        } elseif (!$storedOtp) {
            $notification = array(
                'message' => 'OTP has expired. Please request a new OTP.',
                'alert-type' => 'error'
            );
            return redirect()->route('nonacademic.mobile.otp.screen')->with($notification);
        } else {
            $notification = array(
                'message' => 'Invalid OTP. Please try again.',
                'alert-type' => 'error'
            );
            return redirect()->route('nonacademic.mobile.otp.screen')->with($notification);
        }
    }

    public function mobileOtpResend(Request $request)
    {
        Log::info('NonAcademicVacancyApplicationController-> Mobile OTP resend started');

        if (!session()->has('mobile_no') || !session()->has('nic')) {
            $notification = array(
                'message' => 'Your session has expired. Please start again.',
                'alert-type' => 'error'
            );
            return redirect()->route('nonacademic.vacancy.list')->with($notification);
        }

        // Generate new OTP
        $otp_code = random_int(100000, 999999);

        Log::alert('NonAcademicVacancyApplicationController-> Mobile OTP resend - ' . $otp_code . ' send to the ' . session('mobile_no') . ' with NIC ' . session('nic'));

        // Clear old OTP cookie and set new one
        Cookie::queue(Cookie::forget('mobile_otp_code'));
        Cookie::queue('mobile_otp_code', $otp_code, 10);

        // Send SMS OTP
        $this->sendSmsOtp(session('mobile_no'), $otp_code);

        $notification = array(
            'message' => 'New OTP has been sent to your mobile number.',
            'alert-type' => 'success'
        );

        return redirect()->route('nonacademic.mobile.otp.screen')->with($notification);
    }

    public function detailInstruction(){

        return view('frontend.nonacademic.details_instruction');
    }

    public function sorDownload3(){
        $path = public_path('/pdf/SOR/sample.pdf');

        return response()->file($path, [
            'Content-Type' => 'application/pdf'
        ]);
    }

    private function applicationDuplicationCheck($nic,$vacancy_id)
    {
        //get nic information
        $empDetails = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($nic)]);

        $nicData = json_decode($empDetails->body(), true);

        // Check for existing employees with the same NIC
        $empSimilarData = ApplicationForm::where(function ($query) use ($nic, $nicData) {
            $query->where('nic', strtoupper($nic))
                  ->orWhere('nic', $nicData['oldnic'])
                  ->orWhere('nic', $nicData['newnic']);
        })
        ->where('vacancy_id', $vacancy_id)
        ->first();

        if ($empSimilarData) {
            $notification = array(
                'message' => 'You have already applied for this vacancy with this NIC number',
                'alert-type' => 'error'
            );
            return redirect()->route('nonacademic.vacancy.list')->with($notification);
        }
        return false;
    }
}
