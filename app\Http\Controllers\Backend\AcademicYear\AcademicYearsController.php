<?php

namespace App\Http\Controllers\Backend\AcademicYear;

use App\Http\Controllers\Controller;
use App\Models\AcademicYear;
use App\Models\Faculty;
use Dotenv\Validator;
use Illuminate\Http\Request;

class AcademicYearsController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function addNewOpen()
    {

        $fac_text = Faculty::whereNotIn('id', [50, 51])->get();

        $ac_year = AcademicYear::join('employees', 'employees.employee_no', '=', 'academic_years.add_user_id')
            ->join('faculties', 'faculties.id', '=', 'academic_years.fac_id')
            ->select(
                'academic_years.*',
                'employees.initials',
                'employees.last_name',
                'faculties.faculty_name'
            )
            ->where('academic_years.statsu', 1)->get();

        return view('admin.academicYear.add_new', compact('fac_text', 'ac_year'));
    }

    public function addNewStore(Request $request)
    {

        // Remove spaces from refno
        $refno = str_replace(' ', '', $request->input('refno'));


        $validatedData = $request->validate(
            [
                'fac' => 'required',
                'acYear' => 'required|regex:/^\d{4}\/\d{4}$/',
                'startDate' => 'required|date',
                'endDate' => 'required|date',
                
            ],
            [
                'fac.required' => 'Please select the faculty',
                'acYear.required' => 'Please enter the academic year',
                'acYear.regex' => 'Please enter the correct format',
                'startDate.required' => 'Please select the date',
                'endDate.required' => 'Please select the date',
                
            ]
        );

        // Remove spaces from refno
        $refno = str_replace(' ', '', $request->input('acYear'));

        $up_text = AcademicYear::where('fac_id', $request->fac)
            ->where('statsu', 1)
            ->update([
                'statsu' => 2
            ]);

            $academic_year = new AcademicYear();
            $academic_year->fac_id = $request->fac;
            $academic_year->academic_year_name = $request->acYear;
            $academic_year->start_date = date("Y-m-d", strtotime($request->startDate));
            $academic_year->end_date = date("Y-m-d", strtotime($request->endDate));            
            $academic_year->add_user_id = auth()->user()->employee_no;
            $academic_year->add_date = today();
            $academic_year->save();

            $notification = array(
                'message' => 'Successfully submitted',
                'alert-type' => 'success'
            );
            //test comment

            return redirect() -> route('academicYears.add.new.open');
    }
}
