<?php

namespace App\Http\Controllers\Backend\Commendation;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Commendation;
use App\Models\commendationProcess;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AddOldCommendationsController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function index(Request $request)
    {

        if (isset($request->employee_no)) {

            Log::info('AddOldCommendationsController -> commendation data getAll started');

            $mainBranch = Auth()->user()->main_branch_id;
            $roleID = Auth()->user()->role_id;

            $search_emp_no = $request->employee_no;

            //admin user data collection
            if ($mainBranch == 51) {

                $emp_commendation = Commendation::join('categories', 'categories.id', '=', 'commendations.type_id')
                    ->select('commendations.*', 'categories.category_name')
                    ->where('commendations.emp_no', $search_emp_no)
                    ->where('commendations.emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('letter_date')
                    ->get();

                $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                    ->where('employees.employee_no', $search_emp_no)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->get();

                $row_count = $empfetchDatas->count();
            }
            //academic devision data collection
            elseif ($mainBranch == 52) {

                $emp_commendation = Commendation::join('categories', 'categories.id', '=', 'commendations.type_id')
                    ->select('commendations.*', 'categories.category_name')
                    ->where('commendations.emp_no', $search_emp_no)
                    ->where('commendations.emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('letter_date')
                    ->get();

                if (Auth()->user()->hasRole(['est-head', 'cc'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                } elseif (Auth()->user()->hasRole(['sc'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();
            }
            //non academic division data collection
            elseif ($mainBranch == 53) {

                $emp_commendation = Commendation::join('categories', 'categories.id', '=', 'commendations.type_id')
                    ->select('commendations.*', 'categories.category_name')
                    ->where('commendations.emp_no', $search_emp_no)
                    ->where('commendations.emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('letter_date')
                    ->get();


                if (Auth()->user()->hasRole(['est-head', 'cc'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                } elseif (Auth()->user()->hasRole(['sc'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();
            }



            /****************************************************** */
            if ($row_count > 0) {
                foreach ($empfetchDatas as $empData) {
                    $emp_no = $empData->employee_no;
                    $emp_name = $empData->titleName . " " . $empData->initials . " " . $empData->last_name;
                    $designation_name = $empData->designation_name . " " . $empData->gradeName;
                    $department_name = $empData->department_name;
                }
            } else {
                $emp_no = '';
                $emp_name = '';
                $designation_name = '';
                $department_name = '';
                $emp_commendation = array();

                $notification = array(
                    'message' => 'employee number not found or employee profile lock',
                    'alert-type' => 'error'
                );

                return redirect()->back()->with($notification);
            }


            $pass_employee_no = $request->employee_no;
        } else {
            $pass_employee_no = '';
            $emp_no = '';
            $emp_name = '';
            $designation_name = '';
            $department_name = '';
            $emp_commendation = array();
        }

        $categories = $this->getCategories([31]);
        $cType = $categories->where('category_type_id', '31');
        Log::info('AddOldCommendationsController -> commendation data getAll ended');
        return view('admin.commendation.old_commendation_store', compact('emp_commendation', 'pass_employee_no', 'emp_no', 'emp_name', 'emp_commendation', 'cType', 'designation_name', 'department_name'));
    }


    public function store(Request $request)
    {
        Log::info('AddOldCommendationsController -> old Commendation data sotre started');

        if (!isset($request->emp_no)) {
            $notification = array(
                'message' => 'Please Search Employee Before Submit',
                'alert-type' => 'error'
            );

            return redirect()->route('commendation.old.history.index')->with($notification);
        }

        $request->validate(
            ['emp_no' => 'required'],
            ['emp_no.required' => 'Please search employee before the enter commendation data']
        );

        if ($request->LDate != null) {

            for ($i = 0; $i < count($request->LDate); $i++) {
                $commendation = new Commendation();
                $commendation->type_id = $request->comType[$i];
                $commendation->emp_no = $request->emp_no;
                $commendation->letter_date = date("Y-m-d", strtotime($request->LDate[$i]));
                $commendation->page_no = $request->pageNo[$i];
                $commendation->officer_name = $request->offiName[$i];
                $commendation->officer_position = $request->offiPosi[$i];
                $commendation->description = $request->descri[$i];
                $commendation->add_user_id = auth()->user()->employee_no;
                $commendation->save();
            }

            Log::notice('AddOldCommendationsController -> Created employee old Commendation data employee number - ' . $request->emp_no . ' created by ' . auth()->user()->employee_no);

            $notification = array(
                'message' => 'Old Commendation/Warning/Punishment data Inserted Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('commendation.old.history.index', ['employee_no' => $request->emp_no])->with($notification);
        } else {

            $notification = array(
                'message' => 'Commendation/Warning/Punishment data Inserted Unsuccessfully',
                'alert-type' => 'error'
            );

            return redirect()->route('commendation.old.history.index', ['employee_no' => $request->emp_no])->with($notification);
        }


        Log::info('AddOldCommendationsController -> Old Commendations data store ended');
    }

    public function oldCommendationEdit($id)
    {

        Log::info('AddOldCommendationsController -> employee old Commendation data edit started');

        $categories = $this->getCategories([31]);
        $cTypes = $categories->where('category_type_id', '31');
        $editData = Commendation::find($id);

        Log::notice('AddOldCommendationsController -> edit employee old Commendation data id - ' . $editData->id . ' edited by ' . auth()->user()->employee_no);
        Log::info('AddOldCommendationsController -> employee old Commendation edit ended');
        //dd($editData);
        return view('admin.commendation.edit', compact('editData', 'cTypes'));
    }

    public function oldCommendationUpdate(Request $request, $id)
    {

        Log::info('AddOldCommendationsController -> old employee old Commendation data update started');

        $validatedData = $request->validate([
            'type_id' => 'required',
            'letter_date' => 'required|date',
            'officer_name' => 'required',
            'officer_position' => 'required',
            'description' => 'required',

        ], [
            'letter_date.required' => 'letter date required',
            'officer_name.required' => 'officer name required',
            'officer_position.required' => 'officer designation required',
        ]);


        $commendation = Commendation::find($id);
        $commendation->emp_no = $request->emp_no;
        $commendation->type_id = $request->type_id;
        $commendation->letter_date = date("Y-m-d", strtotime($request->letter_date));
        $commendation->officer_name = $request->officer_name;
        $commendation->officer_position = $request->officer_position;
        $commendation->description = $request->description;
        $commendation->updated_user_id = auth()->user()->employee_no;
        $commendation->save();

        Log::warning('AddOldCommendationsController -> update employee old Commendation data employee number - ' . $commendation->emp_no . ' updated by ' . auth()->user()->employee_no);
        Log::info('AddOldCommendationsController -> employee old Commendation data update ended');

        $notification = array(
            'message' => 'Commendation data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('commendation.old.history.index', ['employee_no' => $commendation->emp_no])->with($notification);
    }

    public function oldCommendationDelete($id)
    {

        Log::info('AddOldCommendationsController -> employee old Commendation data delete started');

        $commendation = Commendation::find($id);
        $commendation->delete();

        Log::emergency('AddOldCommendationsController -> delete employee old Commendation data employee number - ' . $commendation->emp_no . ' deleted by ' . auth()->user()->employee_no);
        Log::info('AddOldCommendationsController -> employee old Commendation data delete ended');


        $notification = array(
            'message' => 'Commendation data Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('commendation.old.history.index', ['employee_no' => $commendation->emp_no])->with($notification);
    }

    public function addNewOpen()
    {

        $cType = Category::where('category_type_id', '31')->get();

        if (Auth()->user()->hasRole(['administrator'])) {

            $data_text = Employee::select(
                'last_name',
                'initials',
                'employee_no'
            )
                ->where('employee_no', '!=', auth()->user()->employee_no)
                ->where('lock', 1)
                ->where('employee_status_id', 110)
                ->get();

            $pending_text = commendationProcess::join('categories', 'categories.id', '=', 'commendation_processes.type_id')
                ->join('employees as emp', 'emp.employee_no', '=', 'commendation_processes.emp_no')
                ->select(
                    'categories.category_name',
                    'emp.initials as emp_initials',
                    'emp.last_name as emp_last_name',
                    'commendation_processes.*'
                )
                ->where('commendation_processes.status', 0)
                ->get();
        } else {
            $data_text = Employee::select(
                'last_name',
                'initials',
                'employee_no'
            )
                ->where('employee_no', '!=', auth()->user()->employee_no)
                ->where('assign_ma_user_id', auth()->user()->employee_no)
                ->where('lock', 1)
                ->where('employee_status_id', 110)
                ->get();

            $pending_text = commendationProcess::join('categories', 'categories.id', '=', 'commendation_processes.type_id')
                ->join('employees as emp', 'emp.employee_no', '=', 'commendation_processes.emp_no')
                ->select(
                    'categories.category_name',
                    'emp.initials as emp_initials',
                    'emp.last_name as emp_last_name',
                    'commendation_processes.*'
                )
                ->where('commendation_processes.status', 0)
                ->where('emp.assign_ma_user_id', auth()->user()->employee_no)
                ->get();
        }

        return view('admin.commendation.add_new', compact('cType', 'data_text', 'pending_text'));
    }

    public function addNewStore(Request $request)
    {

        $save_text = new commendationProcess();
        $save_text->type_id = $request->cType1;
        $save_text->emp_no = $request->emp;
        $save_text->letter_date = date("Y-m-d", strtotime($request->letterDate1));
        $save_text->page_no = $request->pageNo1;
        $save_text->officer_name = $request->officerName1;
        $save_text->officer_position = $request->officerPosition1;
        $save_text->description = $request->description1;
        $save_text->ad_user_id = auth()->user()->employee_no;
        $save_text->add_date = today();
        $save_text->save();

        $notification = array(
            'message' => 'Old Commendation/Warning/Punishment data submitted successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('commendation.add.new.open')->with($notification);
    }

    public function acceptOpen()
    {

        if (Auth()->user()->hasRole(['administrator'])) {

            $data_text = commendationProcess::join('categories', 'categories.id', '=', 'commendation_processes.type_id')
                ->join('employees as emp', 'emp.employee_no', '=', 'commendation_processes.emp_no')
                ->join('employees as operator', 'operator.employee_no', '=', 'commendation_processes.ad_user_id')
                ->select(
                    'categories.category_name',
                    'emp.initials as emp_initials',
                    'emp.last_name as emp_last_name',
                    'commendation_processes.id',
                    'commendation_processes.emp_no',
                    'commendation_processes.add_date',
                    'operator.initials as operator_initials',
                    'operator.last_name as operator_last_name'
                )
                ->where('commendation_processes.status', 0)
                ->get();
        } else {
            $data_text = commendationProcess::join('categories', 'categories.id', '=', 'commendation_processes.type_id')
                ->join('employees as emp', 'emp.employee_no', '=', 'commendation_processes.emp_no')
                ->join('employees as operator', 'operator.employee_no', '=', 'commendation_processes.ad_user_id')
                ->select(
                    'categories.category_name',
                    'emp.initials as emp_initials',
                    'emp.last_name as emp_last_name',
                    'commendation_processes.id',
                    'commendation_processes.emp_no',
                    'commendation_processes.add_date',
                    'operator.initials as operator_initials',
                    'operator.last_name as operator_last_name'
                )
                ->where('commendation_processes.status', 0)
                ->where('emp.main_branch_id', auth()->user()->main_branch_id)
                ->get();
        }

        return view('admin.commendation.accept', compact('data_text'));
    }

    public function addNewAcceptDetails($id)
    {

        $row_id = decrypt($id);

        $data_text = commendationProcess::join('categories', 'categories.id', '=', 'commendation_processes.type_id')
            ->join('employees as emp', 'emp.employee_no', '=', 'commendation_processes.emp_no')
            ->join('departments', 'departments.id', '=', 'emp.department_id')
            ->join('designations', 'designations.id', '=', 'emp.designation_id')
            ->join('categories as tt', 'tt.id', '=', 'designations.staff_grade')
            ->join('employees as operator', 'operator.employee_no', '=', 'commendation_processes.ad_user_id')
            ->select(
                'categories.category_name',
                'emp.initials as emp_initials',
                'emp.last_name as emp_last_name',
                'commendation_processes.*',
                'operator.initials as operator_initials',
                'operator.last_name as operator_last_name',
                'departments.department_name',
                'designations.designation_name',
                'tt.category_name as grade'
            )
            ->where('commendation_processes.id',  $row_id)
            ->get();

        if (count($data_text) > 0) {
            foreach ($data_text as $data_texts) {
                $empNo = $data_texts->emp_no;
                $name = $data_texts->emp_initials . ' ' . $data_texts->emp_last_name;
                $dep = $data_texts->department_name;
                $desig = $data_texts->designation_name . ' ' . $data_texts->grade;
                $type = $data_texts->category_name;
                $letter_date = $data_texts->letter_date;
                $page_no = $data_texts->page_no;
                $officer_name = $data_texts->officer_name;
                $officer_position = $data_texts->officer_position;
                $description = $data_texts->description;
                $added_by = $data_texts->operator_initials . ' ' . $data_texts->operator_last_name;
                $added_date = $data_texts->add_date;
            }
        } else {
            $empNo = '';
            $name = '';
            $dep = '';
            $desig = '';
            $type = '';
            $letter_date = '';
            $page_no = '';
            $officer_name = '';
            $officer_position = '';
            $description = '';
            $added_by = '';
            $added_date = '';
        }


        return view('admin.commendation.add_new_accept_details', compact(
            'empNo',
            'name',
            'dep',
            'desig',
            'type',
            'letter_date',
            'page_no',
            'officer_name',
            'officer_position',
            'description',
            'added_by',
            'added_date',
            'row_id'
        ));
    }

    public function addnewAcceptStore(Request $request)
    {

        $udpate_text = commendationProcess::where('id', $request->row_id)->first();
        if ($udpate_text) {
            $save_text = new Commendation();
            $save_text->type_id = $udpate_text->type_id;
            $save_text->emp_no = $udpate_text->emp_no;
            $save_text->letter_date = $udpate_text->letter_date;
            $save_text->page_no = $udpate_text->page_no;
            $save_text->officer_name = $udpate_text->officer_name;
            $save_text->officer_position = $udpate_text->officer_position;
            $save_text->description = $udpate_text->description;
            $save_text->add_user_id = $udpate_text->ad_user_id;
            $save_text->process_table_id = $udpate_text->id;
            $save_text->save();

            $udpate_text->accept_user_id = auth()->user()->employee_no;
            $udpate_text->accept_date = today();
            $udpate_text->status = 1;
            $udpate_text->save();

            $notification = array(
                'message' => 'Old Commendation/Warning/Punishment data accepted successfully',
                'alert-type' => 'success'
            );
        } else {
            $notification = array(
                'message' => 'Something wrong. Please try again.',
                'alert-type' => 'error'
            );
        }


        return redirect()->route('commendation.add.new.accept.open')->with($notification);
    }

    public function addNewRemove($id)
    {
        $row_id = decrypt($id);

        $udpate_text = commendationProcess::where('id', $row_id)->first();

        if ($udpate_text) {

            $udpate_text->status = 2;
            $udpate_text->remove_user_id = auth()->user()->employee_no;
            $udpate_text->remove_date = today();
            $udpate_text->save();

            $notification = array(
                'message' => 'Old Commendation/Warning/Punishment data removed successfully',
                'alert-type' => 'success'
            );
        } else {
            $notification = array(
                'message' => 'Something wrong. Please try again.',
                'alert-type' => 'error'
            );
        }

        return redirect()->route('commendation.add.new.open')->with($notification);
    }
}
