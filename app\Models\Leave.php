<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Leave extends Model
{
    use HasFactory,LogsActivity;

    protected $table = 'leaves';

    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('hrms_leaves')
        ->logOnlyDirty()
        ->dontLogIfAttributesChangedOnly(['updated_at'])
        ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    protected static function boot()
    {
        parent::boot();

        static::updating(function ($model) {
            $original = $model->getOriginal();
            $newModel = new LeaveHistory();
            $newModel->fill($original);
            $newModel->updated_user_id = Auth()->user()->employee_no;
            $newModel->updated_at = now();
            $newModel->save();
        });
    }
}
