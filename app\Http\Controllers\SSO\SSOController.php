<?php

namespace App\Http\Controllers\SSO;

use App\Http\Controllers\Controller;
use App\Models\LoginHistory;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;

class SSOController extends Controller
{
    public function getLogin(Request $request)
    {
        $request->session()->put("state", $state =  Str::random(40));
        $query = http_build_query([
            "client_id" => "9901c4ec-4e07-4b13-a9bc-20418f304aa4",
            "redirect_uri" => "https://hrms.sjp.ac.lk/callback",
            "response_type" => "code",
            "scope" => "view-user",
            "state" => $state,
            "prompt" => true
        ]);
        return redirect("https://usjnetsso.sjp.ac.lk/sso" .  "/oauth/authorize?" . $query);
    }
    public function getCallback(Request $request)
    {
        $state = $request->session()->pull("state");
        //$request->session()->put('my_name', '<PERSON><PERSON><PERSON>');
        //  throw_unless(strlen($state) > 0 && $state == $request->state, InvalidArgumentException::class);

        $response = Http::asForm()->post(
            "https://usjnetsso.sjp.ac.lk/sso" .  "/oauth/token",
            [
                "grant_type" => "authorization_code",
                "client_id" => "9901c4ec-4e07-4b13-a9bc-20418f304aa4",
                "client_secret" => "DxoIcKXIgIedJ3jcEJmNrCwCvXK8tLAsLHPpXzvV",
                "redirect_uri" => "https://hrms.sjp.ac.lk/callback",
                "code" => $request->code
            ]
        );
        $request->session()->put($response->json());
        return redirect(route("sso.connect"));
    }
    public function connectUser(Request $request)
    {
        $access_token = $request->session()->get("access_token");
        $response = Http::withHeaders([
            "Accept" => "application/json",
            "Authorization" => "Bearer " . $access_token
        ])->get("https://usjnetsso.sjp.ac.lk/sso" .  "/api/user");
        $userArray = $response->json();

        // print_r($userArray);
        try {
            $email = $userArray['email'];
        } catch (\Throwable $th) {
            // return redirect("sso/login")->withError("Failed to get login information! Try again.");
            return redirect("sso/unknown_User");
        }
        $user = User::where("email", $email)->where("status_id", 1)->first();

        if (!$user) {
            return redirect("sso/unknown_User");
            //     $user = new User;
            //     $user->name = $userArray['name'];
            //     $user->email = $userArray['email'];
            //     $user->email_verified_at = $userArray['email_verified_at'];
            //     $user->save();
        }

        Auth::login($user);

        try {
            LoginHistory::create([
                'user_id' => auth()->user()->id,
                'employee_no' => auth()->user()->employee_no,
                'email' => auth()->user()->email,
                'ip' => trim(shell_exec("dig +short myip.opendns.com @resolver1.opendns.com")),
                //'ip' => '*************',
                'login_time' => now(),
            ]);
        } catch (Exception $ex) {
        }

        //dd(session()->get("special_callback_url"));
        if (session()->get("special_callback_url") === null || session()->get("special_callback_url") === "") {

            return redirect(route("admin.dashboard"));

        } else {

            return redirect(session()->get("special_callback_url"));
        }
    }

    public function userAvailerbility(Request $request)
    {
        $access_token = $request->session()->get('access_token');
        $response = Http::withHeaders([
            "Accept" => "application/json",
            "Authorization" => "Bearer " . $access_token
        ])->get("https://usjnetsso.sjp.ac.lk/sso" .  "/api/availability");

        // if ($response["message"] === "Unauthenticated.") {
        //     echo "ok";
        // }
    }
}
