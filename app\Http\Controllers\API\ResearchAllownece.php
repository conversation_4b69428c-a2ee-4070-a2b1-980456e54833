<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ResearchAllownece extends Controller
{
    public function userDetailsGet(Request $request)
    {

        if ($request->sjpemail == "<EMAIL>" || $request->sjpemail == "<EMAIL>" || $request->sjpemail == "<EMAIL>" || $request->sjpemail == "<EMAIL>") {

            $data = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('categories as title', 'employees.title_id', '=', 'title.id')
                ->join('faculties', 'employees.faculty_id', '=', 'faculties.id')
                ->join('departments', 'employees.department_id', '=', 'departments.id')
                ->select(
                    'employees.employee_no',
                    'employees.email',
                    'employees.last_name',
                    'employees.name_denoted_by_initials',
                    'employees.nic',
                    'designations.designation_name',
                    'faculties.faculty_name',
                    DB::raw('CASE WHEN employees.faculty_id IN (50, 51) THEN "None" ELSE faculties.faculty_name END AS faculty_name'),
                    'departments.department_name',
                    'designations.salary_code',
                    'employees.initials',
                    'employees.mobile_no',
                    'title.category_name as title'
                )
                ->where('employees.email', $request->sjpemail)
                //->where('employee_status_id', 110)
                ->get();
        } else {

            $data = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('categories as title', 'employees.title_id', '=', 'title.id')
                ->join('faculties', 'employees.faculty_id', '=', 'faculties.id')
                ->join('departments', 'employees.department_id', '=', 'departments.id')
                ->select(
                    'employees.employee_no',
                    'employees.email',
                    'employees.last_name',
                    'employees.name_denoted_by_initials',
                    'employees.nic',
                    'designations.designation_name',
                    'faculties.faculty_name',
                    DB::raw('CASE WHEN employees.faculty_id IN (50, 51) THEN "None" ELSE faculties.faculty_name END AS faculty_name'),
                    'departments.department_name',
                    'designations.salary_code',
                    'employees.initials',
                    'employees.mobile_no',
                    'title.category_name as title'
                )
                ->where('employees.email', $request->sjpemail)
                ->where('employee_status_id', 110)
                ->where('designations.research_allowance_status', 1)
                ->get();
        }


        return $data;
    }

    public function userDetailsGetAll(Request $request)
    {

        if ($request->sjpemail == "<EMAIL>" || $request->sjpemail == "<EMAIL>" || $request->sjpemail == "<EMAIL>") {

            $data = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('categories as title', 'employees.title_id', '=', 'title.id')
                ->join('faculties', 'employees.faculty_id', '=', 'faculties.id')
                ->join('departments', 'employees.department_id', '=', 'departments.id')
                ->select(
                    'employees.employee_no',
                    'employees.email',
                    'employees.last_name',
                    'employees.name_denoted_by_initials',
                    'employees.nic',
                    'designations.designation_name',
                    'faculties.faculty_name',
                    DB::raw('CASE WHEN employees.faculty_id IN (50, 51) THEN "None" ELSE faculties.faculty_name END AS faculty_name'),
                    'departments.department_name',
                    'designations.salary_code',
                    'employees.initials',
                    'employees.mobile_no',
                    'title.category_name as title',
                    'employees.employee_status_id'
                )
                ->where('employees.email', $request->sjpemail)
                //->where('employee_status_id', 110)
                ->get();
        } else {

            $data = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('categories as title', 'employees.title_id', '=', 'title.id')
                ->join('faculties', 'employees.faculty_id', '=', 'faculties.id')
                ->join('departments', 'employees.department_id', '=', 'departments.id')
                ->select(
                    'employees.employee_no',
                    'employees.email',
                    'employees.last_name',
                    'employees.name_denoted_by_initials',
                    'employees.nic',
                    'designations.designation_name',
                    'faculties.faculty_name',
                    DB::raw('CASE WHEN employees.faculty_id IN (50, 51) THEN "None" ELSE faculties.faculty_name END AS faculty_name'),
                    'departments.department_name',
                    'designations.salary_code',
                    'employees.initials',
                    'employees.mobile_no',
                    'title.category_name as title',
                    'employees.employee_status_id'
                )
                ->where('employees.email', $request->sjpemail)
                //->where('employee_status_id', 110)
                ->where('designations.research_allowance_status', 1)
                ->get();
        }


        return $data;
    }

    public function employeeDataGet()
    {

        $data = Employee::select('employees.email', 'employees.last_name', 'employees.initials', 'employees.employee_no')->where('employees.employee_status_id', 110)->where('email', '!=', NULL)->get();
        return $data;
    }


    public function employeeDepartmentGet(Request $request)
    {
        $empIDs = $request->input('empIDs', []); // Get an array of employee IDs from the request

        $data = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.main_group', '=', 'categories.id')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->leftJoin('faculties', function ($join) {
                $join->on('employees.faculty_id', '=', 'faculties.id')
                    ->whereNotIn('faculties.id', [50, 51]); // Exclude faculty_id 50 and 51
            })
            ->select(
                'employees.employee_no',
                'departments.department_name',
                'categories.category_name as main_group',
                DB::raw('CASE WHEN faculties.id IN (50, 51) THEN "none" ELSE faculties.faculty_name END AS faculty_name') // Using a CASE statement to handle the condition
            )
            ->whereIn('employees.employee_no', $empIDs) // Use whereIn to match multiple employee IDs
            ->get();


        return $data;
    }

    public function eligibilityCount()
    {
        $data = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->where('designations.research_allowance_status', 1)
            ->where('employee_status_id', 110)
            ->count();

        return $data;
    }
}
