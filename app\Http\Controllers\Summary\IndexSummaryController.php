<?php

namespace App\Http\Controllers\Summary;

use App\Http\Controllers\Controller;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Faculty;
use App\Models\NonIncrement;
use App\Models\PromotionApplicationsNonacademic;
use App\Models\User;
use App\Models\Vacancy;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class IndexSummaryController extends Controller
{
    public function __construct()
    {
       // session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('PageControl:0');
    }

    public function dashboard()
    {

        Log::info('IndexSummaryController -> admin index started');

       // activity()->log('Look mum, I logged something');

        if (Auth::check()) {

            $mainBranch = Auth()->user()->main_branch_id;
            $empNo = Auth()->user()->employee_no;

        }else {

            return redirect("https://usjnet.sjp.ac.lk/sso/login");//need to update

        }

        $currentDate = date('Y-m-d');
        $currentYear = date('Y');
        $previousYear = $currentYear - 1;


        if ($mainBranch == 51) {

            $user = User::where('status_id', 1)->count();

            $active_employee = Employee::where('employee_status_id',110)->count();

            $inactive_employee = Employee::where('employee_status_id',111)->count();

            $vacancy = Vacancy::where('date_closed', '>=', $currentDate)->where('vacancy_status_id',1)->count();

            $non_academic_promotions = PromotionApplicationsNonacademic::where('status', 0)->count();

            $non_academic_increments = NonIncrement::where('status','!=',6)->count();

            $activeUser = DB::table('sessions')
                          ->select('users.employee_no', 'sessions.user_id', 'sessions.id', 'sessions.ip_address', 'sessions.user_agent', 'sessions.last_activity')
                          ->join('users', 'users.id', '=', 'sessions.user_id')
                          ->where('sessions.user_id', '!=', '')
                          ->where('sessions.user_id', '!=', 1)
                          ->count();

        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

            $user = User::where('status_id', 1)->where('main_branch_id', 52)->count();

            $active_employee = Employee::where('employee_status_id',110)->where('main_branch_id', 52)->count();

            $inactive_employee = Employee::where('employee_status_id',111)->where('main_branch_id', 52)->count();

            $vacancy = Vacancy::where('date_closed', '>=', $currentDate)->where('vacancy_status_id',1)->whereIn('main_category_id', array(44, 45, 202, 203, 204))->count();

            $non_academic_promotions = 0;

            $non_academic_increments = 0;

            $activeUser = DB::table('sessions')
                          ->select('users.employee_no', 'sessions.user_id', 'sessions.id', 'sessions.ip_address', 'sessions.user_agent', 'sessions.last_activity')
                          ->join('users', 'users.id', '=', 'sessions.user_id')
                          ->where('sessions.user_id', '!=', '')
                          ->where('sessions.user_id', '!=', 1)
                          ->where('users.main_branch_id', 52)
                          ->count();

            }elseif(Auth()->user()->hasRole(['cc'])){

                $user = 0;

                $active_employee = Employee::where('employee_status_id',110)->where('main_branch_id', 52)->count();

                $inactive_employee = Employee::where('employee_status_id',111)->where('main_branch_id', 52)->count();

                $vacancy = 0;

                $non_academic_promotions = 0;

                $non_academic_increments = 0;

                $activeUser = 0;

            }elseif(Auth()->user()->hasRole(['sc'])){

                $user = 0;

                $active_employee = Employee::where('employee_status_id',110)->where('main_branch_id', 52)->where('assign_ma_user_id', $empNo)->count();

                $inactive_employee = Employee::where('employee_status_id',111)->where('main_branch_id', 52)->where('assign_ma_user_id', $empNo)->count();

                $vacancy = 0;

                $non_academic_promotions = 0;

                $non_academic_increments = 0;

                $activeUser = 0;

            }elseif(Auth()->user()->hasRole(['lc'])){

                $user = 0;

                $active_employee = Employee::where('employee_status_id',110)->where('main_branch_id', 52)->count();

                $inactive_employee = Employee::where('employee_status_id',111)->where('main_branch_id', 52)->count();

                $vacancy = 0;

                $non_academic_promotions = 0;

                $non_academic_increments = 0;

                $activeUser = 0;

            }



        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $user = User::where('status_id', 1)->where('main_branch_id', 53)->count();

                $active_employee = Employee::where('employee_status_id',110)->where('main_branch_id', 53)->count();

                $inactive_employee = Employee::where('employee_status_id',111)->where('main_branch_id', 53)->count();

                $vacancy = 0;

                $non_academic_promotions = PromotionApplicationsNonacademic::where('status', 0)->count();

                $non_academic_increments = NonIncrement::where('status','!=',6)->count();

                $activeUser = DB::table('sessions')
                              ->select('users.employee_no', 'sessions.user_id', 'sessions.id', 'sessions.ip_address', 'sessions.user_agent', 'sessions.last_activity')
                              ->join('users', 'users.id', '=', 'sessions.user_id')
                              ->where('sessions.user_id', '!=', '')
                              ->where('sessions.user_id', '!=', 1)
                              ->where('users.main_branch_id', 53)
                              ->count();

                }elseif(Auth()->user()->hasRole(['cc'])){

                    $user = 0;

                    $active_employee = Employee::where('employee_status_id',110)->where('main_branch_id', 53)->count();

                    $inactive_employee = Employee::where('employee_status_id',111)->where('main_branch_id', 53)->count();

                    $vacancy = 0;

                    $non_academic_promotions = PromotionApplicationsNonacademic::where('status', 0)->count();

                    $non_academic_increments = NonIncrement::where('status','!=',6)->count();

                    $activeUser = 0;

                }elseif(Auth()->user()->hasRole(['sc'])){

                    $user = 0;

                    $active_employee = Employee::where('employee_status_id',110)->where('main_branch_id', 53)->where('assign_ma_user_id', $empNo)->count();

                    $inactive_employee = Employee::where('employee_status_id',111)->where('main_branch_id', 53)->where('assign_ma_user_id', $empNo)->count();

                    $vacancy = 0;

                    $non_academic_promotions = PromotionApplicationsNonacademic::where('status', 0)->where('check_user_id', $empNo)->count();

                    $non_academic_increments = NonIncrement::where('status','!=',6)->where('operator',$empNo)->count();

                    $activeUser = 0;

                }elseif(Auth()->user()->hasRole(['lc'])){

                    $user = 0;

                    $active_employee = Employee::where('employee_status_id',110)->where('main_branch_id', 53)->count();

                    $inactive_employee = Employee::where('employee_status_id',111)->where('main_branch_id', 53)->count();

                    $vacancy = 0;

                    $non_academic_promotions = 0;

                    $non_academic_increments = NonIncrement::where('status','=',1)->where('leave_clark_emp_no',$empNo)->count();

                    $activeUser = 0;

                }

        }

        $faculty = Faculty::count();

        $department = Department::count();

        //dd($vacancy);

        Log::info('IndexSummaryController -> admin index ended');

        return view('admin.index', compact('user', 'active_employee','inactive_employee' ,'faculty', 'department', 'activeUser', 'vacancy','non_academic_promotions','non_academic_increments'));
    }

    public function notification(){

        return view('admin.notification.index');

    }

    public function notificationRead($id){

        if($id){

            Auth()->user()->unreadNotifications->where('id',$id)->markAsRead();
        }

        return back();

    }

    public function notificationUnread($id){

        if($id){

            //Auth()->user()->notifications->where('id',$id)->update(['read_at' => NULL]);
            DB::table('notifications')->where('id',$id)->update(['read_at' => NULL]);
        }

        return back();


    }

    public function notificationDelete($id){

        if($id){

            //Auth()->user()->notifications->where('id',$id)->delete();
            DB::table('notifications')->where('id',$id)->delete();
        }

        return back();
    }
}
