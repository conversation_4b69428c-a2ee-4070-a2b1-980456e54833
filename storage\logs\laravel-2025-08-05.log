[2025-08-05 08:52:32] local.INFO: IndexController -> home page index started  
[2025-08-05 08:52:55] local.INFO: IndexSummaryController -> admin index started  
[2025-08-05 08:52:55] local.INFO: IndexSummaryController -> admin index ended  
[2025-08-05 09:00:34] local.INFO: IndexController-> Applicate job select function started  
[2025-08-05 09:00:34] local.INFO: IndexController-> Applicant Login get started  
[2025-08-05 09:00:40] local.INFO: IndexController-> Applicate job select function started  
[2025-08-05 09:00:40] local.INFO: IndexController-> Applicant Login get started  
[2025-08-05 09:01:14] local.INFO: IndexController-> Applicate job select function started  
[2025-08-05 09:01:14] local.INFO: IndexController-> Applicant Login get started  
[2025-08-05 09:03:12] local.INFO: IndexController-> Applicate job select function started  
[2025-08-05 09:03:12] local.INFO: IndexController-> Applicant Login get started  
[2025-08-05 09:26:15] local.INFO: VacancyController -> vacancy add started  
[2025-08-05 09:26:15] local.INFO: VacancyController -> vacancy add ended  
[2025-08-05 09:26:18] local.INFO: VacancyController -> vacancy add started  
[2025-08-05 09:26:18] local.INFO: VacancyController -> vacancy add ended  
[2025-08-05 09:26:44] local.INFO: VacancyController -> Vacancy store started  
[2025-08-05 09:26:44] local.NOTICE: VacancyController -> Created vacancy id - 271 created by 12393  
[2025-08-05 09:26:44] local.INFO: VacancyController -> Vacancy create ended  
[2025-08-05 09:26:44] local.INFO: VacancyController -> vacancy add started  
[2025-08-05 09:26:44] local.INFO: VacancyController -> vacancy add ended  
[2025-08-05 09:26:47] local.INFO: VacancyController -> vacancy index started  
[2025-08-05 09:26:47] local.INFO: VacancyController -> vancancies Count - 208  
[2025-08-05 09:26:47] local.INFO: VacancyController -> vacancy index ended  
[2025-08-05 09:26:51] local.INFO: VacancyController -> vacancy active started  
[2025-08-05 09:26:51] local.NOTICE: VacancyController -> active vacancy id - 271 activate by 12393  
[2025-08-05 09:26:51] local.INFO: VacancyController -> vacancy active ended  
[2025-08-05 09:26:51] local.INFO: VacancyController -> vacancy index started  
[2025-08-05 09:26:51] local.INFO: VacancyController -> vancancies Count - 208  
[2025-08-05 09:26:51] local.INFO: VacancyController -> vacancy index ended  
[2025-08-05 09:26:55] local.INFO: IndexController -> home page index started  
[2025-08-05 09:26:57] local.INFO: IndexController-> vacancy list index started  
[2025-08-05 09:27:00] local.INFO: IndexController-> Applicate job select function started  
[2025-08-05 09:27:00] local.INFO: IndexController-> Applicant Login get started  
[2025-08-05 09:27:13] local.INFO: IndexController-> Email and mobile verification get started  
[2025-08-05 09:27:14] local.ALERT: IndexController-> Email and mobile verification otp code - 31160 send to the 0770247645 and <EMAIL> with NIC 795970836V  
[2025-08-05 09:27:22] local.NOTICE: IndexController->Email and mobile verification code for  - <EMAIL> sending Successfully  
[2025-08-05 09:27:23] local.INFO: IndexController-> Applicant OTP Screen get started  
[2025-08-05 09:27:35] local.INFO: IndexController-> Applicant OTP confimation get started  
[2025-08-05 09:27:35] local.INFO: IndexController-> Applicant original application creation get started  
[2025-08-05 09:27:35] local.NOTICE: IndexController->User Get application data Referance number  - 271/0001 Successfully  
[2025-08-05 09:27:35] local.INFO: IndexController-> Applicant original application creation get ended  
[2025-08-05 09:27:36] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 09:28:53] local.INFO: IndexController-> vacancy application submit get started  
[2025-08-05 09:28:53] local.INFO: IndexController -> save application started  
[2025-08-05 09:28:53] local.NOTICE: IndexController -> save application data id - 271/0001  
[2025-08-05 09:28:53] local.INFO: IndexController -> save application ended  
[2025-08-05 09:28:53] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 09:29:00] local.INFO: IndexController-> vacancy application submit get started  
[2025-08-05 09:29:00] local.INFO: IndexController -> save application started  
[2025-08-05 09:29:00] local.NOTICE: IndexController -> save application data id - 271/0001  
[2025-08-05 09:29:00] local.INFO: IndexController -> save application ended  
[2025-08-05 09:29:01] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 09:50:00] local.INFO: IndexController-> Applicate job select function started  
[2025-08-05 09:50:00] local.INFO: IndexController-> Applicant Login get started  
[2025-08-05 09:50:11] local.INFO: VacancyController -> vacancy index started  
[2025-08-05 09:50:11] local.INFO: VacancyController -> vancancies Count - 208  
[2025-08-05 09:50:11] local.INFO: VacancyController -> vacancy index ended  
[2025-08-05 09:50:13] local.INFO: VacancyController -> vacancy add started  
[2025-08-05 09:50:13] local.INFO: VacancyController -> vacancy add ended  
[2025-08-05 09:58:06] local.INFO: VacancyController -> Vacancy store started  
[2025-08-05 09:58:06] local.NOTICE: VacancyController -> Created vacancy id - 272 created by 12393  
[2025-08-05 09:58:06] local.INFO: VacancyController -> Vacancy create ended  
[2025-08-05 09:58:06] local.INFO: VacancyController -> vacancy add started  
[2025-08-05 09:58:06] local.INFO: VacancyController -> vacancy add ended  
[2025-08-05 09:58:09] local.INFO: VacancyController -> vacancy index started  
[2025-08-05 09:58:09] local.INFO: VacancyController -> vancancies Count - 209  
[2025-08-05 09:58:09] local.INFO: VacancyController -> vacancy index ended  
[2025-08-05 09:58:13] local.INFO: VacancyController -> vacancy active started  
[2025-08-05 09:58:13] local.NOTICE: VacancyController -> active vacancy id - 272 activate by 12393  
[2025-08-05 09:58:13] local.INFO: VacancyController -> vacancy active ended  
[2025-08-05 09:58:13] local.INFO: VacancyController -> vacancy index started  
[2025-08-05 09:58:13] local.INFO: VacancyController -> vancancies Count - 209  
[2025-08-05 09:58:13] local.INFO: VacancyController -> vacancy index ended  
[2025-08-05 09:58:32] local.INFO: IndexController -> home page index started  
[2025-08-05 09:58:34] local.INFO: IndexController-> vacancy list index started  
[2025-08-05 09:58:52] local.INFO: IndexController-> Applicate job select function started  
[2025-08-05 09:58:53] local.INFO: IndexController-> Applicant Login get started  
[2025-08-05 09:59:26] local.INFO: IndexController-> Email and mobile verification get started  
[2025-08-05 09:59:26] local.ALERT: IndexController-> Email and mobile verification otp code - 28045 send to the 0770247645 and <EMAIL> with NIC 795970836V  
[2025-08-05 09:59:30] local.NOTICE: IndexController->Email and mobile verification code for  - <EMAIL> sending Successfully  
[2025-08-05 09:59:30] local.INFO: IndexController-> Applicant OTP Screen get started  
[2025-08-05 09:59:54] local.INFO: IndexController-> Email and mobile verification get started  
[2025-08-05 09:59:54] local.ALERT: IndexController-> Email and mobile verification otp code - 20627 send to the 0770247645 and <EMAIL> with NIC 795970836V  
[2025-08-05 09:59:57] local.NOTICE: IndexController->Email and mobile verification code for  - <EMAIL> sending Successfully  
[2025-08-05 09:59:58] local.INFO: IndexController-> Applicant OTP Screen get started  
[2025-08-05 10:01:36] local.INFO: IndexController-> Applicant OTP confimation get started  
[2025-08-05 10:01:36] local.INFO: IndexController-> Applicant original application creation get started  
[2025-08-05 10:01:36] local.NOTICE: IndexController->User Get application data Referance number  - 272/0001 Successfully  
[2025-08-05 10:01:36] local.INFO: IndexController-> Applicant original application creation get ended  
[2025-08-05 10:01:36] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 10:03:11] local.INFO: IndexController-> vacancy application submit get started  
[2025-08-05 10:03:11] local.INFO: IndexController -> save application started  
[2025-08-05 10:03:11] local.NOTICE: IndexController -> save application data id - 272/0001  
[2025-08-05 10:03:11] local.INFO: IndexController -> save application ended  
[2025-08-05 10:03:11] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 10:15:15] local.INFO: IndexController-> vacancy application submit get started  
[2025-08-05 10:15:15] local.INFO: IndexController -> save application started  
[2025-08-05 10:15:15] local.NOTICE: IndexController -> save application data id - 272/0001  
[2025-08-05 10:15:15] local.INFO: IndexController -> save application ended  
[2025-08-05 10:15:15] local.INFO: IndexController -> home page index started  
[2025-08-05 10:22:35] local.INFO: IndexController-> vacancy list index started  
[2025-08-05 10:22:46] local.INFO: IndexController-> vacancy list index started  
[2025-08-05 10:22:52] local.INFO: IndexController-> vacancy list index started  
[2025-08-05 10:22:53] local.INFO: IndexController-> vacancy list index started  
[2025-08-05 10:22:54] local.INFO: IndexController -> home page index started  
[2025-08-05 10:29:53] local.INFO: VacancyController -> vacancy index started  
[2025-08-05 10:29:53] local.INFO: VacancyController -> vancancies Count - 209  
[2025-08-05 10:29:53] local.INFO: VacancyController -> vacancy index ended  
[2025-08-05 10:33:01] local.INFO: VacancyController -> vacancy index started  
[2025-08-05 10:33:01] local.INFO: VacancyController -> vancancies Count - 209  
[2025-08-05 10:33:01] local.INFO: VacancyController -> vacancy index ended  
[2025-08-05 11:16:28] local.INFO: IndexSummaryController -> admin index started  
[2025-08-05 11:16:28] local.INFO: IndexSummaryController -> admin index ended  
[2025-08-05 11:41:05] local.INFO: IndexController -> home page index started  
[2025-08-05 11:41:07] local.INFO: IndexController-> vacancy list index started  
[2025-08-05 11:41:14] local.INFO: IndexController -> home page index started  
[2025-08-05 11:41:15] local.INFO: IndexController-> Applicant Edit Login get started  
[2025-08-05 11:41:19] local.INFO: IndexController-> Edit Email and mobile verification get started  
[2025-08-05 11:41:19] local.ALERT: IndexController-> Email and mobile verification otp code - 69837 send <NAME_EMAIL> with NIC 795970836V  
[2025-08-05 11:41:23] local.NOTICE: IndexController->Edit Email and mobile verification code for  - <EMAIL> sending Successfully  
[2025-08-05 11:41:24] local.INFO: IndexController-> Application Edit OTP Screen get started  
[2025-08-05 11:41:44] local.INFO: IndexController-> Edit Applicant OTP confimation get started  
[2025-08-05 11:41:47] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 11:48:22] local.INFO: IndexController-> vacancy application submit get started  
[2025-08-05 11:48:22] local.INFO: IndexController -> save application started  
[2025-08-05 11:48:22] local.NOTICE: IndexController -> save application data id - 271/0001  
[2025-08-05 11:48:22] local.INFO: IndexController -> save application ended  
[2025-08-05 11:48:22] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 11:49:03] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 11:50:05] local.INFO: IndexController-> vacancy application submit get started  
[2025-08-05 11:50:05] local.INFO: IndexController -> save application started  
[2025-08-05 11:50:05] local.NOTICE: IndexController -> save application data id - 271/0001  
[2025-08-05 11:50:05] local.INFO: IndexController -> save application ended  
[2025-08-05 11:50:05] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 11:53:33] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 11:57:30] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 11:58:37] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 11:59:46] local.INFO: IndexController-> vacancy application submit get started  
[2025-08-05 11:59:46] local.INFO: IndexController -> save application started  
[2025-08-05 11:59:46] local.NOTICE: IndexController -> save application data id - 271/0001  
[2025-08-05 11:59:46] local.INFO: IndexController -> save application ended  
[2025-08-05 11:59:46] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 12:01:54] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 12:07:38] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 12:08:15] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 12:09:15] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 12:11:27] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 D:\\Development\\hr_system\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 D:\\Development\\hr_system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(152): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 D:\\Development\\hr_system\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 D:\\Development\\hr_system\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 D:\\Development\\hr_system\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\Development\\hr_system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\Development\\hr_system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\Development\\hr_system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 {main}
"} 
[2025-08-05 12:11:30] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 D:\\Development\\hr_system\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 D:\\Development\\hr_system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(152): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 D:\\Development\\hr_system\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 D:\\Development\\hr_system\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 D:\\Development\\hr_system\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\Development\\hr_system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\Development\\hr_system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\Development\\hr_system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 {main}
"} 
[2025-08-05 12:11:36] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 D:\\Development\\hr_system\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 D:\\Development\\hr_system\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 D:\\Development\\hr_system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(152): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 D:\\Development\\hr_system\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 D:\\Development\\hr_system\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 D:\\Development\\hr_system\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\Development\\hr_system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\Development\\hr_system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\Development\\hr_system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 {main}
"} 
[2025-08-05 13:00:05] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 13:02:23] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 13:03:15] local.INFO: IndexController-> vacancy application index started  
[2025-08-05 13:12:47] local.INFO: IndexController-> vacancy application index started  
