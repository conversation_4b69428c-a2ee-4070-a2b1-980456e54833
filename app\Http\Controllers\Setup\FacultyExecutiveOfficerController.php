<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FacultyExecutiveOfficerController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function FacultyExecutiveOfficerView(){

        $data = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as title', 'employees.title_id', '=', 'title.id')
            ->join('faculties', 'employees.faculty_id', '=', 'faculties.id')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->select(
                'employees.employee_no',
                'employees.email',
                'faculties.faculty_name',
                'faculties.id',
                DB::raw("CONCAT(title.category_name, ' ', employees.initials, ' ', employees.last_name) as full_name"),
                'designations.designation_name'
            )
            ->whereNotIn('employees.faculty_id', [50, 51])
            ->whereIn('employees.designation_id', [18, 126, 422, 729, 8, 53, 119, 415, 730, 403])
            ->where('employee_status_id', 110)
            ->orWhereIn('employees.employee_no', [])
            ->orderBy('employees.faculty_id', 'asc')
            ->get();

        return view('admin.setups.faculty_executive_officer.index', compact('data'));

    }
}
