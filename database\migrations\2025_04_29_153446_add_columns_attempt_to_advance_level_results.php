<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('advance_level_results', function (Blueprint $table) {
            $table->integer('attempt')->nullable()->after('result_slot_id');
            //
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('advance_level_results', function (Blueprint $table) {
            $table->dropColumn('attempt');
            //
        });
    }
};
