<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\Department;
use App\Models\Faculty;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class FacultyController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function facultyIndex()
    {
        Log::info('FacultyController -> faculty index started');
        $faculties = Faculty::all();
        $trashFaculties = Faculty::onlyTrashed()->get();
        //dd($trashFaculties);
        return view('admin.setups.faculty.index', compact('faculties', 'trashFaculties'));

        Log::notice('FacultyController -> faculties Count - ' . $$faculties->count());
        Log::info('FacultyController -> faculty index ended');
    }

    public function facultyAdd()
    {
        Log::info('FacultyController -> department add started');

        $departments = Department::where('is_faculty_office',1)
                      ->get();
        return view('admin.setups.faculty.add',compact('departments'));

        Log::info('FacultyController -> department add ended');
    }

    public function facultyStore(Request $request)
    {
        Log::info('FacultyController -> faculty store started');

        $validatedData = $request->validate([
            'id' => 'required|unique:faculties,id',
            'faculty_prefix' => 'unique:faculties,faculty_prefix',
            'faculty_name' => 'required|unique:faculties,faculty_name',
            'faculty_dean_office' => 'required'
        ],[
           'id.required' => 'faculty id is required',
           'id.unique' => 'faculty id is already used'
        ]);

        $data = new Faculty();
        $data->id = $request->id;
        $data->faculty_prefix = strtoupper($request->faculty_prefix);
        $data->faculty_name = $request->faculty_name;
        $data->faculty_dean_office = $request->faculty_dean_office;
        /******************************************************/
        $maxnumber = Faculty::select(DB::raw('MAX(sorting_order) as value'))
                    ->get();

        $maxValue = json_decode($maxnumber, true);

        $sortnumber = $maxValue[0]["value"] + 1;
        /*******************************************************/
        $data->sorting_order = $sortnumber;
        $data->created_at = Carbon::now();
        $data->save();

        Log::notice('FacultyController -> Created faculty id - ' . $data->id . ' created by ' . auth()->user()->id);
        Log::info('FacultyController -> faculty create ended');

        $notification = array(
            'message' => 'New Faculty Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('faculty.index')->with($notification);
    }

    public function facultyEdit($id)
    {
        Log::info('FacultyController -> faculty edit started');
        $editData = Faculty::find($id);
        $departments = Department::where('is_faculty_office',1)
                      ->get();
        return view('admin.setups.faculty.edit', compact('editData','departments'));

        Log::notice('FacultyController -> edit faculty id - ' . $editData->id . ' edited by ' . auth()->user()->id);
        Log::info('FacultyController -> faculty edit ended');
    }

    public function facultyUpdate(Request $request, $id)
    {

        Log::info('FacultyController -> department update started');
        $validatedData = $request->validate([
            'id' => ['required', Rule::unique('faculties')->ignore($id)],
            'faculty_prefix' => [Rule::unique('faculties')->ignore($id)],
            'faculty_name' => ['required', Rule::unique('faculties')->ignore($id)],
            'faculty_dean_office' => 'required'
        ],[
            'id.required' => 'faculty id is required',
             'id.unique' => 'faculty id is already used'
        ]);


        $data = Faculty::find($id);
        $data->id = $request->id;
        $data->faculty_prefix = strtoupper($request->faculty_prefix);
        $data->faculty_name = $request->faculty_name;
        $data->faculty_dean_office = $request->faculty_dean_office;
        $data->updated_at = Carbon::now();
        $data->save();

        Log::notice('FacultyController -> update faculty id - ' . $data->id . ' updated by ' . auth()->user()->id);
        Log::info('FacultyController -> faculty update ended');

        $notification = array(
            'message' => 'Faculty data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('faculty.index')->with($notification);
    }

    public function facultySoftdelete($id)
    {

        Log::info('FacultyController -> faculty soft delete started');
        $faculty = Faculty::find($id);
        $faculty->delete();

        $notification = array(
            'message' => 'Faculty Deleted Successfully',
            'alert-type' => 'warning'
        );

        return redirect()->route('faculty.index')->with($notification);

        Log::notice('FacultyController -> soft delete faculty id - ' . $faculty->id . ' deleted by ' . auth()->user()->id);
        Log::info('FacultyController -> Faculty soft delete ended');
    }

    public function facultyRestore($id)
    {

        Log::info('FacultyController -> faculty restore started');

        $faculty = Faculty::withTrashed()->find($id);
        $faculty->restore();

        $notification = array(
            'message' => 'Faculty Restore Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('faculty.index')->with($notification);

        Log::notice('FacultyController -> restore Faculty id - ' . $faculty->id . ' deleted by ' . auth()->user()->id);
        Log::info('FacultyController -> Faculty restore ended');
    }

    public function facultyDelete($id)
    {

        Log::info('FacultyController -> Faculty delete started');

        $faculty = Faculty::onlyTrashed()->find($id);
        $faculty->forceDelete();

        $notification = array(
            'message' => 'Faculty Permenetly Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('faculty.index')->with($notification);

        Log::emergency('FacultyController -> delete Faculty id - ' . $faculty->id . ' deleted by ' . auth()->user()->id);
        Log::info('FacultyController -> Faculty delete ended');
    }

    public function departmentList($id)
    {

        $faculty = Faculty::find($id);
        //dd($faculty);
        $departments = Department::where('faculty_code', $id)->get();
        return view('admin.setups.faculty.list', compact('departments', 'faculty'));
    }
}
