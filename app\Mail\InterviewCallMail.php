<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class InterviewCallMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }


    public function envelope()
    {
        return new Envelope(
            subject: 'USJHRMS - Interview Calling Notice',
            tags: ['recrutement'],
        );
    }


    public function content()
    {
        return new Content(
            markdown: 'emails.applicant_interview_calling',
        );
    }

    public function attachments()
    {
        return [];
    }
}
