<?php

namespace App\Http\Controllers\Backend\Increment;

use App\Http\Controllers\Controller;
use App\Mail\IncrementHeadMail;
use Illuminate\Http\Request;
use App\Models\Employee;
use App\Models\Department;
use App\Models\DepartmentHead;
use App\Models\NonIncrement;
use App\Models\Leave;
use App\Models\Increment;
use App\Models\Promotion;
use App\Models\salaryRevision2025;
use App\Models\SalaryScale;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Barryvdh\DomPDF\Facade\Pdf as FacadePdf;


class NonAcademicController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|head|cc|sc|lc|reg');
    }

    public function index()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $roleID = Auth()->user()->role_id;
        $empNo = Auth()->user()->employee_no;
        $year = now()->year;

        if ($mainBranch == 51) {

            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('non_increments', 'non_increments.employee_no', '=', 'employees.employee_no')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'departments.department_name', 'non_increments.status', 'non_increments.increment_date', 'categories.category_name', 'non_increments.year')
                ->where('employees.main_branch_id', 53)
                ->whereIn('non_increments.status', [0, 1, 2, 3, 4, 5])
                //->where('non_increments.year', $year)
                ->orderBy('employees.increment_date', 'ASC')
                //->limit(5)
                ->get();

            //dd($employees);

        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['sc'])) {

                $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('non_increments', 'non_increments.employee_no', '=', 'employees.employee_no')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'departments.department_name', 'non_increments.status', 'non_increments.increment_date', 'categories.category_name', 'non_increments.year')
                    ->where('employees.main_branch_id', 53)
                    ->where('non_increments.status', 0)
                    //->where('non_increments.year', $year)
                    ->where('employees.assign_ma_user_id', $empNo)
                    ->orderBy('employees.increment_date', 'ASC')
                    ->get();
            } elseif (Auth()->user()->hasRole(['lc'])) {

                $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('non_increments', 'non_increments.employee_no', '=', 'employees.employee_no')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'departments.department_name', 'non_increments.status', 'non_increments.increment_date', 'categories.category_name', 'non_increments.year')
                    ->where('employees.main_branch_id', 53)
                    ->where('non_increments.status', 1)
                    //->where('non_increments.year', $year)
                    ->where('non_increments.leave_clark_emp_no', $empNo)
                    ->orderBy('employees.increment_date', 'ASC')
                    ->get();
            } elseif (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('non_increments', 'non_increments.employee_no', '=', 'employees.employee_no')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'departments.department_name', 'non_increments.status', 'non_increments.increment_date', 'categories.category_name', 'non_increments.year')
                    ->where('employees.main_branch_id', 53)
                    ->wherein('non_increments.status', [2, 4])
                    //->where('non_increments.year', $year)
                    ->orderBy('employees.increment_date', 'ASC')
                    ->get();
            }
        }

        return view('admin.increment.NonAcademic.index', compact('employees'));
    }

    public function reverseList()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $roleID = Auth()->user()->role_id;
        $empNo = Auth()->user()->employee_no;
        $year = now()->year;

        if ($mainBranch == 51) {

            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('non_increments', 'non_increments.employee_no', '=', 'employees.employee_no')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'departments.department_name', 'non_increments.status', 'non_increments.increment_date', 'categories.category_name', 'non_increments.year')
                ->where('employees.main_branch_id', 53)
                ->where('non_increments.status', 0)
                //->where('non_increments.year', $year)
                ->orderBy('employees.increment_date', 'ASC')
                ->get();
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['sc'])) {

                $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('non_increments', 'non_increments.employee_no', '=', 'employees.employee_no')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'departments.department_name', 'non_increments.status', 'non_increments.increment_date', 'categories.category_name', 'non_increments.year')
                    ->where('employees.main_branch_id', 53)
                    ->where('non_increments.status', 0)
                    //->where('non_increments.year', $year)
                    ->where('employees.assign_ma_user_id', $empNo)
                    ->orderBy('employees.increment_date', 'ASC')
                    ->get();
            }
        }

        return view('admin.increment.NonAcademic.index', compact('employees'));
    }

    public function MaLeaveToArList()
    {
        $mainBranch = Auth()->user()->main_branch_id;
        $roleID = Auth()->user()->role_id;
        $empNo = Auth()->user()->employee_no;
        $year = now()->year;

        if ($mainBranch == 51) {

            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('non_increments', 'non_increments.employee_no', '=', 'employees.employee_no')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'departments.department_name', 'non_increments.status', 'non_increments.increment_date', 'categories.category_name', 'non_increments.year')
                ->where('employees.main_branch_id', 53)
                ->where('non_increments.status', 2)
                //->where('non_increments.year', $year)
                ->orderBy('employees.increment_date', 'ASC')
                ->get();
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('non_increments', 'non_increments.employee_no', '=', 'employees.employee_no')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'departments.department_name', 'non_increments.status', 'non_increments.increment_date', 'categories.category_name', 'non_increments.year')
                    ->where('employees.main_branch_id', 53)
                    ->where('non_increments.status', 2)
                    //->where('non_increments.year', $year)
                    ->orderBy('employees.increment_date', 'ASC')
                    ->get();
            }
        }
        return view('admin.increment.NonAcademic.MaLeaveToArList', compact('employees'));
    }

    public function HodToArList()
    {
        $mainBranch = Auth()->user()->main_branch_id;
        $roleID = Auth()->user()->role_id;
        $empNo = Auth()->user()->employee_no;

        if ($mainBranch == 51) {

            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('non_increments', 'non_increments.employee_no', '=', 'employees.employee_no')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'departments.department_name', 'non_increments.status', 'non_increments.increment_date', 'categories.category_name', 'non_increments.year')
                ->where('employees.main_branch_id', 53)
                ->where('non_increments.status', 4)
                //->where('non_increments.year', now()->year)
                ->orderBy('employees.increment_date', 'ASC')
                ->get();
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $employees = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('non_increments', 'non_increments.employee_no', '=', 'employees.employee_no')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'departments.department_name', 'non_increments.status', 'non_increments.increment_date', 'categories.category_name', 'non_increments.year')
                    ->where('employees.main_branch_id', 53)
                    ->where('non_increments.status', 4)
                    //->where('non_increments.year', now()->year)
                    ->orderBy('employees.increment_date', 'ASC')
                    ->get();
            }
        }

        return view('admin.increment.NonAcademic.HodToArList', compact('employees'));
    }


    public function MAView($id, Request $request)
    {
        $empNo = decrypt($id);
        $Year = $request->input('year');
        $level = 1;

        $employees = Employee::where('employee_no', $empNo)->get();
        $departments = Department::whereIn('id', $employees->pluck('department_id'))->get();

        $designations = Employee::selectRaw('CONCAT(designations.designation_name, " - ", categories.category_name) as designation_name')
            ->join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->where('employees.employee_no', $empNo)
            ->first();

        $scales = Employee::select('salary_scales.salary_code', 'salary_scales.salary_scale_txt','salary_scale_versions.id as version_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('salary_scales', 'salary_scales.salary_code', '=', 'designations.salary_code')
            ->join('salary_scale_versions', 'salary_scale_versions.id', '=', 'salary_scales.salary_scale_version_id')
            ->where('salary_scale_versions.status', 1)
            ->where('employees.employee_no', $empNo)
            ->first();

        $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
            ->join('categories as diff', 'increments.decision', '=', 'diff.id')
            ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
            ->select('increments.effective_date', 'increments.increment_type', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
            ->where('increments.emp_no', $empNo)
            ->orderBy('increments.effective_date', 'DESC')
            ->get();

        $leavelclark = User::select('employees.employee_no', 'employees.initials', 'employees.last_name')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->join('employees', 'users.employee_no', '=', 'employees.employee_no')
            ->where('roles.id', '=', '7')
            ->get();

        //$basicData = NonIncrement::where('employee_no', $empNo)->where('year', $Year)->get();

        $basicData = NonIncrement::where('employee_no', $empNo)
            ->where('year', $Year)
            ->where('registar_Approval', '!=', 2)
            ->where('status', '!=', 6)
            ->get();


        return view('admin.increment.NonAcademic.operator', ['employees' => $employees, 'departments' => $departments, 'designations' => $designations, 'level' => $level, 'scales' => $scales, 'basicData' => $basicData, 'increments' => $increments, 'leavelclark' => $leavelclark, 'Year' => $Year]);
    }


    public function leaveClerk($id, Request $request)
    {
        $empNo = decrypt($id);
        $Year = $request->input('year');
        $perviousYear = $Year - 1;
        $level = 2;

        $employees = Employee::where('employee_no', $empNo)->get();
        $departments = Department::whereIn('id', $employees->pluck('department_id'))->get();

        $designations = Employee::selectRaw('CONCAT(designations.designation_name, " - ", categories.category_name) as designation_name')
            ->join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->where('employees.employee_no', $empNo)
            ->first();

        $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
            ->join('categories as diff', 'increments.decision', '=', 'diff.id')
            ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
            ->select('increments.effective_date', 'increments.increment_type', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
            ->where('increments.emp_no', $empNo)
            ->orderBy('increments.effective_date', 'DESC')
            ->get();

        $leaves = Leave::where('emp_no', $empNo)->where('year', $perviousYear)->get();

        $scales = NonIncrement::join('designations', 'designations.id', '=', 'non_increments.designation_id')
            ->join('salary_scales', 'salary_scales.salary_code', '=', 'designations.salary_code')
            ->join('salary_scale_versions', 'salary_scale_versions.id', '=', 'non_increments.salary_scale_version_id')
            ->select('salary_scales.salary_code', 'salary_scales.salary_scale_txt','salary_scale_versions.id as version_id')
            ->where('non_increments.employee_no', $empNo)
            ->where('non_increments.year', $Year)
            ->where('non_increments.registar_Approval', '!=', 2)
            ->where('non_increments.status', '!=', 6)
            ->first();

        $leavelclark = User::select('employees.employee_no', 'employees.initials', 'employees.last_name')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->join('employees', 'users.employee_no', '=', 'employees.employee_no')
            ->where('roles.id', '=', '7')
            ->get();

        $basicData = NonIncrement::where('employee_no', $empNo)
            ->where('year', $Year)
            ->where('registar_Approval', '!=', 2)
            ->where('status', '!=', 6)
            ->get();

        return view('admin.increment.NonAcademic.leaveClerk', ['employees' => $employees, 'departments' => $departments, 'designations' => $designations, 'leaves' => $leaves, 'level' => $level, 'scales' => $scales, 'basicData' => $basicData, 'increments' => $increments, 'leavelclark' => $leavelclark, 'Year' => $Year]);
    }
    public function arToHead($id, Request $request)
    {
        $empNo = decrypt($id);
        $Year = $request->input('year');
        $perviousYear = $Year - 1;
        $level = 3;

        $employees = Employee::where('employee_no', $empNo)->get();
        $departments = Department::whereIn('id', $employees->pluck('department_id'))->get();

        $designations = Employee::selectRaw('CONCAT(designations.designation_name, " - ", categories.category_name) as designation_name')
            ->join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->where('employees.employee_no', $empNo)
            ->first();

        $leaves = Leave::where('emp_no', $empNo)->where('year', $perviousYear)->get();

        $scales = NonIncrement::join('designations', 'designations.id', '=', 'non_increments.designation_id')
            ->join('salary_scales', 'salary_scales.salary_code', '=', 'designations.salary_code')
            ->join('salary_scale_versions', 'salary_scale_versions.id', '=', 'non_increments.salary_scale_version_id')
            ->select('salary_scales.salary_code', 'salary_scales.salary_scale_txt','salary_scale_versions.id as version_id')
            ->where('non_increments.employee_no', $empNo)
            ->where('non_increments.year', $Year)
            ->where('non_increments.registar_Approval', '!=', 2)
            ->where('non_increments.status', '!=', 6)
            ->first();

        $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
            ->join('categories as diff', 'increments.decision', '=', 'diff.id')
            ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
            ->select('increments.effective_date', 'increments.increment_type', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
            ->where('increments.emp_no', $empNo)
            ->orderBy('increments.effective_date', 'DESC')
            ->get();

        $departmentHeadName = Employee::select(DB::raw('CONCAT(categories.category_name, " ", head_employee.initials, " ", head_employee.last_name) AS department_head_name'), 'position.category_name as position', 'departments.department_name as department', 'faculties.faculty_name as faculty', 'faculties.id as facultyid')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->join('department_heads', 'departments.id', '=', 'department_heads.department_id')
            ->join('employees AS head_employee', 'department_heads.emp_no', '=', 'head_employee.employee_no')
            ->join('categories', 'head_employee.title_id', '=', 'categories.id')
            ->join('categories as position', 'department_heads.head_position', '=', 'position.id')
            ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
            ->where('employees.employee_no', '=', $empNo)
            ->first();

        $leavelclark = User::select('employees.employee_no', 'employees.initials', 'employees.last_name')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->join('employees', 'users.employee_no', '=', 'employees.employee_no')
            ->where('roles.id', '=', '7')
            ->get();

        $basicData = NonIncrement::where('employee_no', $empNo)
            ->where('year', $Year)
            ->where('registar_Approval', '!=', 2)
            ->where('status', '!=', 6)
            ->get();

        $mALeave = NonIncrement::where('non_increments.employee_no', $empNo)
            ->where('non_increments.year', $Year)
            ->where('non_increments.registar_Approval', '!=', 2)
            ->where('non_increments.status', '!=', 6)
            ->join('employees', 'non_increments.leave_clark_emp_no', '=', 'employees.employee_no')
            ->selectRaw("CONCAT(employees.initials, ' ', employees.last_name) as mALeave")
            ->first();

        $mApersonalFile = NonIncrement::where('non_increments.employee_no', $empNo)
            ->where('non_increments.year', $Year)
            ->where('non_increments.registar_Approval', '!=', 2)
            ->where('non_increments.status', '!=', 6)
            ->join('employees', 'non_increments.operator', '=', 'employees.employee_no')
            ->selectRaw("CONCAT(employees.initials, ' ', employees.last_name) as mApersonalFile")
            ->first();

        return view('admin.increment.NonAcademic.arToHead', ['employees' => $employees, 'departments' => $departments, 'designations' => $designations, 'leaves' => $leaves, 'level' => $level, 'scales' => $scales, 'basicData' => $basicData, 'increments' => $increments, 'departmentHeadName' => $departmentHeadName, 'leavelclark' => $leavelclark, 'mALeave' => $mALeave, 'mApersonalFile' => $mApersonalFile, 'Year' => $Year]);
    }
    public function head($id, Request $request)
    {
        $empNo = decrypt($id);
        $Year = $request->input('year');
        $perviousYear = $Year - 1;
        $level = 4;

        $employees = Employee::where('employee_no', $empNo)->get();
        $departments = Department::whereIn('id', $employees->pluck('department_id'))->get();
        $designations = Employee::selectRaw('CONCAT(designations.designation_name, " - ", categories.category_name) as designation_name')
            ->join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->where('employees.employee_no', $empNo)
            ->first();

        $leaves = Leave::where('emp_no', $empNo)->where('year', $perviousYear)->get();

        $scales = NonIncrement::join('designations', 'designations.id', '=', 'non_increments.designation_id')
            ->join('salary_scales', 'salary_scales.salary_code', '=', 'designations.salary_code')
            ->join('salary_scale_versions', 'salary_scale_versions.id', '=', 'non_increments.salary_scale_version_id')
            ->select('salary_scales.salary_code', 'salary_scales.salary_scale_txt','salary_scale_versions.id as version_id')
            ->where('non_increments.employee_no', $empNo)
            ->where('non_increments.year', $Year)
            ->where('non_increments.registar_Approval', '!=', 2)
            ->where('non_increments.status', '!=', 6)
            ->first();

        $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
            ->join('categories as diff', 'increments.decision', '=', 'diff.id')
            ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
            ->select('increments.effective_date', 'increments.increment_type', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
            ->where('increments.emp_no', $empNo)
            ->orderBy('increments.effective_date', 'DESC')
            ->get();

        $basicData = NonIncrement::where('employee_no', $empNo)
            ->where('year', $Year)
            ->where('registar_Approval', '!=', 2)
            ->where('status', '!=', 6)
            ->get();

        $departmentHeadName = Employee::select(DB::raw('CONCAT(categories.category_name, " ", head_employee.initials, " ", head_employee.last_name) AS department_head_name'), 'position.category_name as position')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->join('department_heads', 'departments.id', '=', 'department_heads.department_id')
            ->join('employees AS head_employee', 'department_heads.emp_no', '=', 'head_employee.employee_no')
            ->join('categories', 'head_employee.title_id', '=', 'categories.id')
            ->join('categories as position', 'department_heads.head_position', '=', 'position.id')
            ->where('employees.employee_no', '=', $empNo)
            ->first();

        $ar = NonIncrement::join('designations', 'designations.id', '=', 'non_increments.ar_tohead_position')
            ->where('non_increments.registar_Approval', '!=', 2)
            ->where('non_increments.status', '!=', 6)
            ->where('non_increments.employee_no', $empNo)
            ->where('year', $Year)
            ->first();

        return view('admin.increment.NonAcademic.head', ['employees' => $employees, 'departments' => $departments, 'designations' => $designations, 'leaves' => $leaves, 'level' => $level, 'scales' => $scales, 'basicData' => $basicData, 'increments' => $increments, 'basicData' => $basicData, 'departmentHeadName' => $departmentHeadName, 'ar' => $ar, 'Year' => $Year]);
    }
    public function arToRegistar($id, Request $request)
    {
        $empNo = decrypt($id);
        $Year = $request->input('year');
        $perviousYear = $Year - 1;
        $level = 5;

        $employees = Employee::where('employee_no', $empNo)->get();
        $departments = Department::whereIn('id', $employees->pluck('department_id'))->get();

        $designations = Employee::selectRaw('CONCAT(designations.designation_name, " - ", categories.category_name) as designation_name')
            ->join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->where('employees.employee_no', $empNo)
            ->first();

        $leaves = Leave::where('emp_no', $empNo)->where('year', $perviousYear)->get();

        $scales = NonIncrement::join('designations', 'designations.id', '=', 'non_increments.designation_id')
            ->join('salary_scales', 'salary_scales.salary_code', '=', 'designations.salary_code')
            ->join('salary_scale_versions', 'salary_scale_versions.id', '=', 'non_increments.salary_scale_version_id')
            ->select('salary_scales.salary_code', 'salary_scales.salary_scale_txt','salary_scale_versions.id as version_id')
            ->where('non_increments.employee_no', $empNo)
            ->where('non_increments.year', $Year)
            ->where('non_increments.registar_Approval', '!=', 2)
            ->where('non_increments.status', '!=', 6)
            ->first();

        $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
            ->join('categories as diff', 'increments.decision', '=', 'diff.id')
            ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
            ->select('increments.effective_date', 'increments.increment_type', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
            ->where('increments.emp_no', $empNo)
            ->orderBy('increments.effective_date', 'DESC')
            ->get();

        $departmentHeadName = Employee::select(DB::raw('CONCAT(categories.category_name, " ", head_employee.initials, " ", head_employee.last_name) AS department_head_name'), 'position.category_name as position')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->join('department_heads', 'departments.id', '=', 'department_heads.department_id')
            ->join('employees AS head_employee', 'department_heads.emp_no', '=', 'head_employee.employee_no')
            ->join('categories', 'head_employee.title_id', '=', 'categories.id')
            ->join('categories as position', 'department_heads.head_position', '=', 'position.id')
            ->where('employees.employee_no', '=', $empNo)
            ->first();

        $leavelclark = User::select('employees.employee_no', 'employees.initials', 'employees.last_name')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->join('employees', 'users.employee_no', '=', 'employees.employee_no')
            ->where('roles.id', '=', '7')
            ->get();

        $basicData = NonIncrement::where('employee_no', $empNo)
            ->where('year', $Year)
            ->where('registar_Approval', '!=', 2)
            ->where('status', '!=', 6)
            ->get();

        $mALeave = NonIncrement::where('non_increments.employee_no', $empNo)
            ->where('non_increments.year', $Year)
            ->where('non_increments.registar_Approval', '!=', 2)
            ->where('non_increments.status', '!=', 6)
            ->join('employees', 'non_increments.leave_clark_emp_no', '=', 'employees.employee_no')
            ->selectRaw("CONCAT(employees.initials, ' ', employees.last_name) as mALeave")
            ->first();

        $mApersonalFile = NonIncrement::where('non_increments.employee_no', $empNo)
            ->where('non_increments.year', $Year)
            ->where('non_increments.registar_Approval', '!=', 2)
            ->where('non_increments.status', '!=', 6)
            ->join('employees', 'non_increments.operator', '=', 'employees.employee_no')
            ->selectRaw("CONCAT(employees.initials, ' ', employees.last_name) as mApersonalFile")
            ->first();

        $ar = NonIncrement::join('designations', 'designations.id', '=', 'non_increments.ar_tohead_position')
            ->where('non_increments.registar_Approval', '!=', 2)
            ->where('non_increments.status', '!=', 6)
            ->where('non_increments.employee_no', $empNo)
            ->where('year', $Year)
            ->first();


        return view('admin.increment.NonAcademic.arToRegistar', ['employees' => $employees, 'departments' => $departments, 'designations' => $designations, 'leaves' => $leaves, 'level' => $level, 'scales' => $scales, 'basicData' => $basicData, 'increments' => $increments, 'departmentHeadName' => $departmentHeadName, 'leavelclark' => $leavelclark, 'mALeave' => $mALeave, 'mApersonalFile' => $mApersonalFile, 'ar' => $ar, 'Year' => $Year]);
    }

    public function registarToAr($id, Request $request)
    {
        $empNo = decrypt($id);
        $Year = $request->input('year');
        $perviousYear = $Year - 1;
        $level = 5;

        $employees = Employee::where('employee_no', $empNo)->get();
        $departments = Department::whereIn('id', $employees->pluck('department_id'))->get();

        $designations = Employee::selectRaw('CONCAT(designations.designation_name, " - ", categories.category_name) as designation_name')
            ->join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->where('employees.employee_no', $empNo)
            ->first();

        $leaves = Leave::where('emp_no', $empNo)->where('year', $perviousYear)->get();

       $scales = NonIncrement::join('designations', 'designations.id', '=', 'non_increments.designation_id')
            ->join('salary_scales', 'salary_scales.salary_code', '=', 'designations.salary_code')
            ->join('salary_scale_versions', 'salary_scale_versions.id', '=', 'non_increments.salary_scale_version_id')
            ->select('salary_scales.salary_code', 'salary_scales.salary_scale_txt','salary_scale_versions.id as version_id')
            ->where('non_increments.employee_no', $empNo)
            ->where('non_increments.year', $Year)
            ->where('non_increments.registar_Approval', '!=', 2)
            ->where('non_increments.status', '!=', 6)
            ->first();

        $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
            ->join('categories as diff', 'increments.decision', '=', 'diff.id')
            ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
            ->select('increments.effective_date', 'increments.increment_type', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
            ->where('increments.emp_no', $empNo)
            ->orderBy('increments.effective_date', 'DESC')
            ->get();

        $basicData = NonIncrement::where('employee_no', $empNo)
            ->where('year', $Year)
            ->where('registar_Approval', '!=', 2)
            ->where('status', '!=', 6)
            ->get();

        $departmentHeadName = Employee::select(DB::raw('CONCAT(categories.category_name, " ", head_employee.initials, " ", head_employee.last_name) AS department_head_name'), 'position.category_name as position')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->join('department_heads', 'departments.id', '=', 'department_heads.department_id')
            ->join('employees AS head_employee', 'department_heads.emp_no', '=', 'head_employee.employee_no')
            ->join('categories', 'head_employee.title_id', '=', 'categories.id')
            ->join('categories as position', 'department_heads.head_position', '=', 'position.id')
            ->where('employees.employee_no', '=', $empNo)
            ->first();

        $ar = NonIncrement::join('designations', 'designations.id', '=', 'non_increments.ar_tohead_position')
            ->where('non_increments.registar_Approval', '!=', 2)
            ->where('non_increments.status', '!=', 6)
            ->where('non_increments.employee_no', $empNo)
            ->where('year', $Year)
            ->first();

        return view('admin.increment.NonAcademic.registarToAr', ['employees' => $employees, 'departments' => $departments, 'designations' => $designations, 'leaves' => $leaves, 'level' => $level, 'scales' => $scales, 'basicData' => $basicData, 'increments' => $increments, 'departmentHeadName' => $departmentHeadName, 'ar' => $ar, 'Year' => $Year]);
    }

    public function submitOperator(Request $request)
    {

        $employee_no = $request->employee_no;
        $year = $request->year;

        //get employee details
        $employees = Employee::find($employee_no);
        $designation_id = $employees->designation_id;
        $department_id = $employees->department_id;


        $nonIncrement = NonIncrement::where('employee_no', $employee_no)->where('year', $year)->where('registar_Approval', '!=', 2)->where('status', '!=', 6)->first();

        if ($nonIncrement) {

            $nonIncrement->designation_id = $designation_id;
            $nonIncrement->department_id = $department_id;
            $nonIncrement->current_basic = $request->current_basic;
            $nonIncrement->increment_date = $request->dateOfIncrement;
            $nonIncrement->increment_amount = $request->amountOfIncrement;
            $nonIncrement->last_increment_status = $request->lastIncrementStatus;
            $nonIncrement->qulified_exam_status = $request->qualifiedExamStatus;
            $nonIncrement->salary_scale_version_id = $request->salary_scale_version_id;
            $nonIncrement->confirmation_status = $request->confirmationAppointmentStatus;
            $nonIncrement->leave_clark_emp_no = $request->leave_clark_emp;
            $nonIncrement->status = 1;
            $nonIncrement->employeeQualifiedStatus = $request->employeeQualifiedStatus;
            $nonIncrement->unpaid_amount = $request->unpaid_amount;
            $nonIncrement->paid_amount = $request->paid_amount;
            $nonIncrement->operator = auth()->user()->employee_no;
            $nonIncrement->operator_enter_date = now();
            $nonIncrement->save();
        } else {

            $nonIncrement = new NonIncrement();
            $nonIncrement->employee_no = $request->employee_no;
            $nonIncrement->designation_id = $designation_id;
            $nonIncrement->department_id = $department_id;
            $nonIncrement->year = $year;
            $nonIncrement->current_basic = $request->current_basic;
            $nonIncrement->salary_step = 0;
            $nonIncrement->increment_date = $request->dateOfIncrement;
            $nonIncrement->increment_amount = $request->amountOfIncrement;
            $nonIncrement->last_increment_status = $request->lastIncrementStatus;
            $nonIncrement->qulified_exam_status = $request->qualifiedExamStatus;
            $nonIncrement->salary_scale_version_id = $request->salary_scale_version_id;
            $nonIncrement->confirmation_status = $request->confirmationAppointmentStatus;
            $nonIncrement->leave_clark_emp_no = $request->leave_clark_emp;
            $nonIncrement->status = 1;
            $nonIncrement->employeeQualifiedStatus = $request->employeeQualifiedStatus;
            $nonIncrement->unpaid_amount = $request->unpaid_amount;
            $nonIncrement->paid_amount = $request->paid_amount;
            $nonIncrement->operator = auth()->user()->employee_no;
            $nonIncrement->operator_enter_date = now();
            $nonIncrement->save();
        }

        //increment process lock
        $data = Employee::find($request->employee_no);
        $data->increment_process_active = 1;
        $data->save();


        $notification = array(
            'message' => 'Increment Process initiated Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('nonAc.month.list')->with($notification);
    }

    public function submitleaveClerk(Request $request)
    {
        $employeeNo = $request->employee_no;
        $year = $request->year;
        $reference_id = $request->reference_id;


        $nonIncrement = NonIncrement::where('id', $reference_id)->first();

        if ($nonIncrement) {

            $nonIncrement->current_year_from_date = $request->current_year_from_date;
            $nonIncrement->current_year_to_date = $request->current_year_to_date;
            $nonIncrement->current_year_casual = $request->current_year_casual;
            $nonIncrement->current_year_vacation = $request->current_year_vacation;
            $nonIncrement->current_year_halfpay = $request->current_year_halfpay;
            $nonIncrement->current_year_nopay = $request->current_year_nopay;
            $nonIncrement->current_year_other = $request->current_year_other;
            $nonIncrement->current_year_remark = $request->current_year_remark;
            $nonIncrement->previous_year_from_date = $request->previous_year_from_date;
            $nonIncrement->previous_year_to_date = $request->previous_year_to_date;
            $nonIncrement->previous_year_casual = $request->previous_year_casual;
            $nonIncrement->previous_year_vacation = $request->previous_year_vacation;
            $nonIncrement->previous_year_halfpay = $request->previous_year_halfpay;
            $nonIncrement->previous_year_nopay = $request->previous_year_nopay;
            $nonIncrement->previous_year_other = $request->previous_year_other;
            $nonIncrement->previous_year_remark = $request->previous_year_remark;
            $nonIncrement->status = 2;
            $nonIncrement->leave_clark = auth()->user()->employee_no;
            $nonIncrement->leave_clark_enter_date = now();
            $nonIncrement->save();
        }

        $leave = Leave::updateOrCreate(
            [
                'emp_no' => $request->employee_no,
                'year' => $year
            ],
            [
                'casual' => $request->current_year_casual,
                'sick' => 0,
                'medical' => $request->current_year_vacation,
                'nopay' => $request->current_year_nopay,
                'study' => $request->current_year_other,
                'added_user_id' => auth()->user()->employee_no,
                'updated_user_id' => auth()->user()->employee_no,
            ]
        );



        $notification = array(
            'message' => 'Leave Data updated and Forwarded to Admin Officer Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('nonAc.month.list')->with($notification);
    }
    public function submitArToHead(Request $request)
    {
        $employeeNo = $request->employee_no;
        $reference_id = $request->reference_id;

        $ardesignation = Employee::where('employees.employee_no', '=', auth()->user()->employee_no)->first();

        $nonIncrement = NonIncrement::where('id', $reference_id)->first();

        if ($nonIncrement) {
            $nonIncrement->status = 3;
            $nonIncrement->ar_tohead_position = $ardesignation->designation_id;
            $nonIncrement->ar_tohead = auth()->user()->employee_no;
            $nonIncrement->ar_tohead_enter_date = now();
            $nonIncrement->save();
        }

        $applicantName = Employee::select(DB::raw('CONCAT(categories.category_name," " ,employees.initials, " ", employees.last_name,", ",designations.designation_name," - ",staff_grade.category_name) AS applicant_name'))
            ->join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'employees.title_id', '=', 'categories.id')
            ->join('categories as staff_grade', 'designations.staff_grade', '=', 'staff_grade.id')
            ->where('employee_no', $employeeNo)->first();


        $departmentHeadName = Employee::select(DB::raw('CONCAT(categories.category_name," " ,head_employee.initials, " ", head_employee.last_name) AS department_head_name'), 'departments.head_email')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->join('department_heads', 'departments.id', '=', 'department_heads.department_id')
            ->join('employees AS head_employee', 'department_heads.emp_no', '=', 'head_employee.employee_no')
            ->join('categories', 'head_employee.title_id', '=', 'categories.id')
            ->join('categories as position', 'department_heads.head_position', '=', 'position.id')
            ->where('employees.employee_no', '=', $employeeNo)
            ->first();

        $data = [
            'headName' => $departmentHeadName->department_head_name,
            'name' => $applicantName->applicant_name
        ];

        $mail = new IncrementHeadMail($data);
        Mail::to($departmentHeadName->head_email)->send($mail);
        //Mail::to('<EMAIL>')->send($mail);
        $notification = array(
            'message' => 'Increment Forword to department head Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('nonAc.month.MaLeaveToArList')->with($notification);
    }

    public function rejectToClark(Request $request)
    {
        $employeeNo = $request->employee_no;
        $reference_id = $request->reference_id;

        $nonIncrement = NonIncrement::where('id', $reference_id)->first();

        if ($nonIncrement) {
            $nonIncrement->status = 0;
            $nonIncrement->ar_tohead = auth()->user()->employee_no;
            $nonIncrement->ar_tohead_enter_date = now();
            $nonIncrement->save();
        }

        //unlock the lock
        $data = Employee::find($employeeNo);
        $data->lock = 2;
        $data->save();

        $notification = array(
            'message' => 'Increment Back to File Assign Operator Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('nonAc.month.list')->with($notification);
    }
    public function rejectToLeaveClark(Request $request)
    {
        $reference_id = $request->reference_id;

        $nonIncrement = NonIncrement::where('id', $reference_id)->first();

        if ($nonIncrement) {
            $nonIncrement->status = 1;
            $nonIncrement->ar_tohead = auth()->user()->employee_no;
            $nonIncrement->ar_tohead_enter_date = now();
            $nonIncrement->save();
        }

        $notification = array(
            'message' => 'Increment Back to Leave Clark Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('nonAc.month.list')->with($notification);
    }
    public function submitHead(Request $request)
    {

        $reference_id = $request->reference_id;

        $nonIncrement = NonIncrement::where('id', $reference_id)->first();

        $headposition = DepartmentHead::where('emp_no', auth()->user()->employee_no)->where('active_status', 1)->first();

        if ($nonIncrement) {
            $nonIncrement->punctually = $request->row0;
            $nonIncrement->application = $request->row1;
            $nonIncrement->outlook = $request->row2;
            $nonIncrement->output_and_quality = $request->row3;
            $nonIncrement->responsibility = $request->row4;
            $nonIncrement->completion_tasks = $request->row5;
            $nonIncrement->relations = $request->row6;
            $nonIncrement->dealing_students_public = $request->row7;
            $nonIncrement->leadership = $request->row8;
            $nonIncrement->reliability = $request->row9;
            $nonIncrement->teamwork = $request->row10;
            $nonIncrement->commendations_warnings = $request->commendationRadio;
            $nonIncrement->commendations_warnings_remark = $request->commendations_warnings_remark;
            $nonIncrement->head_recommendation = $request->incrementRecommendationRadio;
            $nonIncrement->head_recommendation_remark = $request->head_recommendation_remark;
            $nonIncrement->status = 4;
            $nonIncrement->head = auth()->user()->employee_no;
            $nonIncrement->head_position = $headposition->head_position;
            //$nonIncrement->head_position =239;
            $nonIncrement->head_enter_date = now();
            $nonIncrement->save();
        }

        $notification = array(
            'message' => 'Increment data evaluated Successfully',
            'alert-type' => 'success'
        );

        if (Auth()->user()->hasRole(['administrator', 'super-admin'])) {

            return redirect()->route('nonAc.month.list')->with($notification);
        } else {

            return redirect()->route('head.show.increment')->with($notification);
        }
    }
    public function submitArToRegistar(Request $request)
    {
        $employeeNo = $request->employee_no;
        $reference_id = $request->reference_id;

        $ardesignation = Employee::where('employees.employee_no', '=', auth()->user()->employee_no)->first();

        $nonIncrement = NonIncrement::where('id', $reference_id)->first();

        if ($nonIncrement) {
            $nonIncrement->status = 5;
            $nonIncrement->ar_position = $ardesignation->designation_id;
            $nonIncrement->ar_toRegistar = auth()->user()->employee_no;
            $nonIncrement->ar_toRegistar_enter_date = now();
            $nonIncrement->save();
        }

        $notification = array(
            'message' => 'Increment Forword to Registrar Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('nonAc.month.HodToArList')->with($notification);
    }
    public function submitRegistar(Request $request)
    {
        $employeeNo = $request->employee_no;
        $addUserId = auth()->user()->employee_no;
        $reference_id = $request->reference_id;

        $nonIncrement = NonIncrement::where('id', $reference_id)->first();

        if ($nonIncrement) {
            $nonIncrement->status = 6;
            $nonIncrement->registar = auth()->user()->employee_no;
            $nonIncrement->registar_Approval = $request->incrementRecommendationRadioApproved;
            $nonIncrement->not_approved_reason = $request->incrementDecision;
            $nonIncrement->incrementperiod = $request->incrementperiod;
            $nonIncrement->NewIncrementDate = $request->NewIncrementDate;
            $nonIncrement->registar_enter_date = now();
            $nonIncrement->save();

            $nonIncrements = NonIncrement::join(
                'employees',
                'employees.employee_no',
                '=',
                'non_increments.employee_no'
            )
                ->where('non_increments.employee_no', $employeeNo)
                ->where('non_increments.id', $reference_id)
                ->select([
                    'non_increments.increment_date AS effective_date',
                    DB::raw('
            CASE
                WHEN non_increments.not_approved_reason = 0 THEN 172
                WHEN non_increments.not_approved_reason = 1 THEN 173
                WHEN non_increments.not_approved_reason = 2 THEN 176
                WHEN non_increments.not_approved_reason = 3 THEN 174
                WHEN non_increments.not_approved_reason = 4 THEN 175
            END AS decision
        '),
                    'employees.designation_id AS desgnation_id',
                    DB::raw('(non_increments.current_basic + non_increments.increment_amount) AS basic_sal'),
                    'non_increments.salary_step AS salary_step',
                    'non_increments.increment_amount AS increment_value',
                    'non_increments.salary_scale_version_id'
                ])->first();

            //dd($nonIncrements);


            if ($nonIncrements) {
                // Create a new Increment model instance
                $increment = new Increment();
                $increment->emp_no = $employeeNo;
                $increment->effective_date = $nonIncrements->effective_date;
                $increment->decision = $nonIncrements->decision;
                $increment->desgnation_id = $nonIncrements->desgnation_id;
                $increment->basic_sal = $nonIncrements->basic_sal;
                $increment->salary_step = $nonIncrements->salary_step;
                $increment->increment_value = $nonIncrements->increment_value;
                $increment->description_id = null;
                $increment->reason = null;
                $increment->period = null;
                $increment->add_user_id = $addUserId;
                $increment->updated_user_id = null;
                $increment->created_at = now();
                $increment->updated_at = now();
                $increment->save();
            }

            //update current basic salary
            $employee = Employee::withoutTrashed()->where('employee_no', $employeeNo)->first();

            if ($employee && $nonIncrements->salary_scale_version_id != 1) {

                $newSalary = NonIncrement::where('employee_no', $employeeNo)
                    ->where('non_increments.id', $reference_id)
                    ->where('non_increments.not_approved_reason', 0)
                    ->selectRaw('current_basic + increment_amount as new_salary')
                    ->value('new_salary');

                // Update current basic salary
                if ($newSalary !== null) {
                    $employee->current_basic_salary = $newSalary;
                    $employee->save();
                }

            } else {

                $count = NonIncrement::where('employee_no', $employeeNo)
                    ->where('non_increments.id', $reference_id)
                    ->where('non_increments.not_approved_reason', 0)
                    ->count();

                if ($count == 1) {

                 $serviceHistory = Promotion::where('employee_no', $employeeNo)->where('type_id',369)->delete();

                 $salaryRevisionReset = salaryRevision2025::where('emp_no', $employeeNo)->update([
                    'status' => 0,
                    'checking2_status' => 0,
                    'accept_status' => 0,
                ]);
                }

            }
        }

        //increment process unlock
        $data = Employee::find($request->employee_no);
        $data->increment_process_active = 0;
        $data->save();

        $notification = array(
            'message' => 'Increment Confirmed Successfully',
            'alert-type' => 'success'
        );

        if (Auth()->user()->hasRole(['administrator', 'super-admin'])) {

            return redirect()->route('nonAc.month.list')->with($notification);
        } else {

            return redirect()->route('registar.show.increment')->with($notification);
        }
    }

    public function IncrementSummery(Request $request)
    {
        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $empfetchDatas =  NonIncrement::join('employees', 'non_increments.employee_no', '=', 'employees.employee_no')
                ->select('non_increments.*', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'employees.increment_date')
                ->where('non_increments.year', date('Y'))
                ->get();
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head', 'cc'])) {

                $empfetchDatas =  NonIncrement::join('employees', 'non_increments.employee_no', '=', 'employees.employee_no')
                    ->select('non_increments.*', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'employees.increment_date')
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->where('employees.main_branch_id', 53)
                    ->where('non_increments.year', date('Y'))
                    ->get();
            } elseif (Auth()->user()->hasRole(['sc'])) {

                $empfetchDatas =  NonIncrement::join('employees', 'non_increments.employee_no', '=', 'employees.employee_no')
                    ->select('non_increments.*', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'employees.increment_date')
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->where('employees.main_branch_id', 53)
                    ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                    ->where('non_increments.year', date('Y'))
                    ->get();
            }
        }

        $currentMonth = date('m');
        $currentYear = date('Y');

        return view('admin.increment.NonAcademic.summery', compact('empfetchDatas', 'currentMonth', 'currentYear'));
    }

    public function monthListIncrementSearch(Request $request)
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $empNo = Auth()->user()->employee_no;


        if ($request->year != NULL && $request->month != NULL) {

            if ($mainBranch == 51) {

                $empfetchDatas =  NonIncrement::join('employees', 'non_increments.employee_no', '=', 'employees.employee_no')
                    ->select('non_increments.*', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'employees.increment_date')
                    ->where('employees.increment_date', 'LIKE', str_pad($request->month, 2, '0', STR_PAD_LEFT) . '%')
                    ->where('non_increments.year', $request->year)
                    ->get();
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head', 'cc'])) {

                    $empfetchDatas =  NonIncrement::join('employees', 'non_increments.employee_no', '=', 'employees.employee_no')
                        ->select('non_increments.*', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'employees.increment_date')
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.increment_date', 'LIKE', str_pad($request->month, 2, '0', STR_PAD_LEFT) . '%')
                        ->where('non_increments.year', $request->year)
                        ->get();
                } elseif (Auth()->user()->hasRole(['sc'])) {

                    $empfetchDatas =  NonIncrement::join('employees', 'non_increments.employee_no', '=', 'employees.employee_no')
                        ->select('non_increments.*', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'employees.increment_date')
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->where('employees.increment_date', 'LIKE', str_pad($request->month, 2, '0', STR_PAD_LEFT) . '%')
                        ->where('non_increments.year', $request->year)
                        ->get();
                }
            }
        }
        $currentMonth = $request->month;
        $currentYear = $request->year;

        return view('admin.increment.NonAcademic.summery', compact('empfetchDatas', 'currentMonth', 'currentYear'));
    }
    public function FinalPrintSummery($id, Request $request)
    {
        $empNo = decrypt($id);
        $Year = $request->input('year');
        $perviousYear = $Year - 1;
        $row = $request->input('row');
        $level = 6;

        $employees = Employee::where('employee_no', $empNo)->get();
        $departments = Department::whereIn('id', $employees->pluck('department_id'))->get();
        $designations = Employee::selectRaw('CONCAT(designations.designation_name, " - ", categories.category_name) as designation_name')
            ->join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->where('employees.employee_no', $empNo)
            ->first();

        $leaves = Leave::where('emp_no', $empNo)->where('year', $perviousYear)->get();

        $scales = NonIncrement::join('designations', 'designations.id', '=', 'non_increments.designation_id')
            ->join('salary_scales', 'salary_scales.salary_code', '=', 'designations.salary_code')
            ->join('salary_scale_versions', 'salary_scale_versions.id', '=', 'non_increments.salary_scale_version_id')
            ->select('salary_scales.salary_code', 'salary_scales.salary_scale_txt','salary_scale_versions.id as version_id')
            ->where('non_increments.id', $row)
            ->first();

        $increment = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
            ->join('categories as diff', 'increments.decision', '=', 'diff.id')
            ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
            ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
            ->where('increments.emp_no', $empNo)
            ->where('increments.increment_type', 329)
            ->orderBy('increments.effective_date', 'DESC')
            ->orderBy('id', 'DESC')
            ->skip(1)
            ->take(1)
            ->first();

        //dd($increments);

        $basicData = NonIncrement::where('id', $row)->get();

        $departmentHeadName = NonIncrement::select(DB::raw('CONCAT(categories.category_name, " ", employees.initials, " ", employees.last_name) AS department_head_name'), 'position.category_name as position', 'departments.department_name as department', 'faculties.faculty_name as faculty', 'faculties.id as facultyid')
            ->join('employees', 'non_increments.head', '=', 'employees.employee_no')
            ->join('categories', 'employees.title_id', '=', 'categories.id')
            ->join('departments', 'non_increments.department_id', '=', 'departments.id')
            ->join('faculties', 'departments.faculty_code', '=', 'faculties.id')
            ->join('categories as position', 'non_increments.head_position', '=', 'position.id')
            ->where('non_increments.id', $row)
            ->first();

        $registrars = Employee::select(DB::raw("CONCAT(`initials`, ' ', `last_name`) AS Registrar"))->where('designation_id', '=', '396')->get();

        $ar = NonIncrement::join('employees', 'employees.employee_no', '=', 'non_increments.ar_tohead')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'non_increments.ar_tohead_position')
            ->selectRaw("designations.designation_name, CONCAT(categories.category_name, ' ', employees.initials, ' ', employees.last_name) AS AR")
            ->where('non_increments.id', $row)
            ->get();

        $arToRegistarfoward = NonIncrement::join('employees', 'employees.employee_no', '=', 'non_increments.ar_toRegistar')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'non_increments.ar_position')
            ->selectRaw("designations.designation_name, CONCAT(categories.category_name, ' ', employees.initials, ' ', employees.last_name) AS AR")
            ->where('non_increments.id', $row)
            ->first();

        $mApersonalFile = NonIncrement::where('non_increments.id', $row)
            ->join('employees', 'non_increments.operator', '=', 'employees.employee_no')
            ->selectRaw("CONCAT(employees.initials, ' ', employees.last_name) as mApersonalFile")
            ->first();

        $mALeave = NonIncrement::where('non_increments.id', $row)
            ->join('employees', 'non_increments.leave_clark_emp_no', '=', 'employees.employee_no')
            ->selectRaw("CONCAT(employees.initials, ' ', employees.last_name) as mALeave")
            ->first();

        //update the print stasus
        NonIncrement::where('non_increments.id', $row)->update(['flag' => 1]);


        $pdf = FacadePdf::loadView('admin.increment.NonAcademic.FinalPrintSummery', ['employees' => $employees, 'departments' => $departments, 'designations' => $designations, 'leaves' => $leaves, 'level' => $level, 'scales' => $scales, 'basicData' => $basicData, 'increment' => $increment, 'departmentHeadName' => $departmentHeadName, 'mApersonalFile' => $mApersonalFile, 'mALeave' => $mALeave, 'registrars' => $registrars, 'ar' => $ar, 'arToRegistarfoward' => $arToRegistarfoward, 'year' => $Year])->setPaper('a4');

        return $pdf->stream('Status of Annual Increment of the Non-Academic Staff.pdf');

        // return view('admin.increment.NonAcademic.FinalPrintSummery', ['employees' => $employees, 'departments' => $departments, 'designations' => $designations, 'leaves' => $leaves, 'level' => $level, 'scales' => $scales, 'basicData' => $basicData, 'increments' => $increments, 'departmentHeadName' => $departmentHeadName]);
    }

    public function progressSummery($id, Request $request)
    {
        $empNo = decrypt($id);

        $increment_status = NonIncrement::where('employee_no', '=', $empNo)->where('year', '=', $request->year)->orderBy('id', 'ASC')->get();

        return view('admin.increment.NonAcademic.progress', compact('increment_status'));
    }
}
