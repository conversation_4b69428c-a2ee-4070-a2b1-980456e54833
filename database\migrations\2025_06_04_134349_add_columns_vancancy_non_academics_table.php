<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vancancy_non_academics', function (Blueprint $table) {
            //
            $table->integer('written_exam_status')->default(0)->after('short_list_complete_date');
            $table->integer('written_exam_select_user')->default(0)->after('written_exam_status');
            $table->date('written_exam_select_date')->nullable()->after('written_exam_select_user');
            $table->integer('written_exam_board_id')->default(0)->after('written_exam_select_date');
            $table->integer('written_exam_complete_user')->default(0)->after('written_exam_board_id');
            $table->date('written_exam_complete_date')->nullable()->after('written_exam_complete_user');
            $table->integer('practical_exam_status')->default(0)->after('written_exam_complete_date');
            $table->integer('practical_exam_select_user')->default(0)->after('practical_exam_status');
            $table->date('practical_exam_select_date')->nullable()->after('practical_exam_select_user');
            $table->integer('practical_exam_board_id')->default(0)->after('practical_exam_select_date');
            $table->integer('practical_exam_complete_user')->default(0)->after('practical_exam_board_id');
            $table->date('practical_exam_complete_date')->nullable()->after('practical_exam_complete_user');
            $table->integer('interview_status')->default(0)->after('practical_exam_complete_date');
            $table->integer('interview_select_user')->default(0)->after('interview_status');
            $table->date('interview_select_date')->nullable()->after('interview_select_user');
            $table->integer('interview_board_id')->default(0)->after('interview_select_date');
            $table->integer('interview_complete_user')->default(0)->after('interview_board_id');
            $table->date('interview_complete_date')->nullable()->after('interview_complete_user');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vancancy_non_academics', function (Blueprint $table) {
            //
            $table->dropColumn('written_exam_status');
            $table->dropColumn('written_exam_select_user');
            $table->dropColumn('written_exam_select_date');
            $table->dropColumn('written_exam_board_id');
            $table->dropColumn('written_exam_complete_user');
            $table->dropColumn('written_exam_complete_date');
            $table->dropColumn('practical_exam_status');
            $table->dropColumn('practical_exam_select_user');
            $table->dropColumn('practical_exam_select_date');
            $table->dropColumn('practical_exam_board_id');
            $table->dropColumn('practical_exam_complete_user');
            $table->dropColumn('practical_exam_complete_date');
            $table->dropColumn('interview_status');
            $table->dropColumn('interview_select_user');
            $table->dropColumn('interview_select_date');
            $table->dropColumn('interview_board_id');
            $table->dropColumn('interview_complete_user');
            $table->dropColumn('interview_complete_date');
        });
    }
};
