<?php $__env->startSection('frontend'); ?>
<?php
use Illuminate\Support\Facades\Storage;
$vacancy = App\Models\Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
->join('categories', 'designations.staff_grade', '=', 'categories.id')
->select('vacancies.*','categories.display_name')
->find(session()->get('vacancy_id'));
$appData = App\Models\Application::find(session()->get('reference_no'));
$olSummary = App\Models\OrdinaryLevelResultSummary::where('reference_no','=',session()->get('reference_no'))->where('attempt','=',1)->get();
$olSummary2 = App\Models\OrdinaryLevelResultSummary::where('reference_no','=',session()->get('reference_no'))->where('attempt','=',2)->get();
$alSummary = App\Models\AdvanceLevelResultSummary::where('reference_no','=',session()->get('reference_no'))->where('level','=',1)->get();
$alSummary2 = App\Models\AdvanceLevelResultSummary::where('reference_no','=',session()->get('reference_no'))->where('level','=',2)->get();
$degrees = App\Models\Degree::where('reference_no','=',session()->get('reference_no'))->get();
$degreeCertificates = App\Models\DegreeCertificate::where('reference_no','=',session()->get('reference_no'))->get();
$diplomas = App\Models\Diploma::where('reference_no','=',session()->get('reference_no'))->get();
$diplomaCertificates = App\Models\DiplomaCertificate::where('reference_no','=',session()->get('reference_no'))->get();
$research = App\Models\Research::where('reference_no','=',session()->get('reference_no'))->get();
$specialQulificationList = App\Models\SpecialQulification::where('reference_no','=',session()->get('reference_no'))->get();
$memberships = App\Models\Membership::where('reference_no','=',session()->get('reference_no'))->get();
$professionalQulificationList = App\Models\ProfessionalQualification::where('reference_no','=',session()->get('reference_no'))->get();
$employmentRecords = App\Models\EmploymentRecord::where('reference_no','=',session()->get('reference_no'))->orderBy('start_date')->get();
$employmentRecordCertificates = App\Models\EmploymentRecordCertificate::where('reference_no','=',session()->get('reference_no'))->get();
$employmentBonds = App\Models\Bond::where('reference_no','=',session()->get('reference_no'))->get();
$refereeList = App\Models\Referee::where('reference_no','=',session()->get('reference_no'))->get();
$releaseLetters = App\Models\ReleaseLetter::where('reference_no','=',session()->get('reference_no'))->get();

$degreeSubject1 = App\Models\DegreeSubject::where('reference_no','=',session()->get('reference_no'))->where('subject_number',1)->get();
$degreeSubject2 = App\Models\DegreeSubject::where('reference_no','=',session()->get('reference_no'))->where('subject_number',2)->get();
$degreeSubject3 = App\Models\DegreeSubject::where('reference_no','=',session()->get('reference_no'))->where('subject_number',3)->get();
$degreeSubject4 = App\Models\DegreeSubject::where('reference_no','=',session()->get('reference_no'))->where('subject_number',4)->get();
$degreeSubject5 = App\Models\DegreeSubject::where('reference_no','=',session()->get('reference_no'))->where('subject_number',5)->get();
$degreeSubject6 = App\Models\DegreeSubject::where('reference_no','=',session()->get('reference_no'))->where('subject_number',6)->get();

$photoCount = App\Models\Application::where('reference_no','=',session()->get('reference_no'))->where('profile_photo','!=',NULL)->count();
$firstDegreeCount = App\Models\Degree::where('reference_no','=',session()->get('reference_no'))->where('degree_type',214)->count();
//dd($releaseLetters);
?>

<?php if(session()->has('vacancy_id') && session()->has('reference_no') && $appData->application_decision_id == 33): ?>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<section class="content-header" style="padding-top:10px;">
  <div class="">
    
    <h5 align="center" class="font-weight-bold">
      <?php if($vacancy->subject == '' && $vacancy->faculty_id == '' && $vacancy->department_id == ''): ?>
      <?php echo e(strtoupper($vacancy->designations->designation_name)); ?> <?php if($vacancy->display_name != ''): ?>
      (<?php echo e(strtoupper($vacancy->display_name)); ?>)
      <?php endif; ?>


      <?php elseif($vacancy->subject == '' && $vacancy->faculty_id != '' && $vacancy->department_id == ''): ?>
      <?php echo e(strtoupper($vacancy->designations->designation_name)); ?> <?php if($vacancy->display_name != ''): ?>
      (<?php echo e(strtoupper($vacancy->display_name)); ?>)
      <?php endif; ?> <br>
      <font style="font-size: 16px;"><?php echo e(strtoupper($vacancy->faculties->faculty_name)); ?></font>


      <?php elseif($vacancy->subject == '' && $vacancy->faculty_id != '' && $vacancy->department_id != ''): ?>
      <?php echo e(strtoupper($vacancy->designations->designation_name)); ?> <?php if($vacancy->display_name != ''): ?>
      (<?php echo e(strtoupper($vacancy->display_name)); ?>)
      <?php endif; ?> <br>
      <font style="font-size: 16px;"><?php if($vacancy->departments->name_status == 1): ?> DEPARTMENT OF <?php endif; ?> <?php echo e(strtoupper($vacancy->departments->department_name)); ?> </font> / <font style="font-size: 16px;"><?php echo e(strtoupper($vacancy->faculties->faculty_name)); ?></font>


      <?php elseif($vacancy->subject == '' && $vacancy->faculty_id == '' && $vacancy->department_id != ''): ?>
      <?php echo e(strtoupper($vacancy->designations->designation_name)); ?> <?php if($vacancy->display_name != ''): ?>
      (<?php echo e(strtoupper($vacancy->display_name)); ?>)
      <?php endif; ?> <br>
      <font style="font-size: 16px;"><?php if($vacancy->departments->name_status == 1): ?> DEPARTMENT OF <?php endif; ?> <?php echo e(strtoupper($vacancy->departments->department_name)); ?></font>


      <?php else: ?>
      <?php echo e(strtoupper($vacancy->designations->designation_name)); ?> <?php if($vacancy->display_name != ''): ?>
      (<?php echo e(strtoupper($vacancy->display_name)); ?>)
      <?php endif; ?> - <?php echo e(strtoupper($vacancy->subject)); ?> <br>
      <font style="font-size: 16px;"><?php if($vacancy->departments->name_status == 1): ?> DEPARTMENT OF <?php endif; ?> <?php echo e(strtoupper($vacancy->departments->department_name)); ?></font> / <font style="font-size: 16px;"><?php echo e(strtoupper($vacancy->faculties->faculty_name)); ?></font>


      <?php endif; ?>
    </h5>
    <h6 align="center">
      <?php if($vacancy->main_category_id == 44): ?>
      Application for the Post of Academic Positions
      <?php elseif($vacancy->main_category_id == 45): ?>
      Application for the Post of Academic Support Positions
      <?php elseif($vacancy->main_category_id == 46): ?>
      Application for the Post of Non Academic Positions
      <?php endif; ?>
    </h6>
    
  </div>

  <?php if($errors->count() > 0): ?>
  <br>
  <div class="alert alert-danger" id="success-danger">
    <button type="button" class="close" data-dismiss="alert">x</button>
    <strong>Error!</strong>
    Fill in the compulsorily requried <span class="badge badge-light"><?php echo e($errors->count()); ?></span> fields and submit again.
  </div>
  <?php endif; ?>
</section>
<!-- Main content -->
<section class="content">
  <div class="col-12">
    <form action="<?php echo e(route('vacancy.application.submit')); ?>" method="post" enctype="multipart/form-data" id="applicant_form">
      <?php echo csrf_field(); ?>
      <div class="card card-outline card-danger">
        <div class="card-body">
          <div class="row">
            <input type="hidden" id="reference_no" name="reference_no" value="<?php echo e($appData->reference_no); ?>">
            <div class="col-md-4 col-sm-12">
              <div class="form-group">
                <label>NIC No. : <span style="color: #ff0000;">*</span></label>
                <?php if($appData->active_nic == 1): ?>
                <input type="text" name="nic" class="form-control" id="nic" value="<?php echo e($appData->nic); ?>" readonly />
                <?php elseif($appData->active_nic == 2): ?>
                <input type="text" name="nic" class="form-control" id="nic" value="<?php echo e($appData->new_nic); ?>" readonly />
                <?php endif; ?>
              </div>
            </div>
            <div class="col-md-8 col-sm-12">
              <div class="row"> <!-- Wrapping inputs in a row -->
                <div class="col-md-6"> <!-- Use col-md-6 for two columns on large screens -->
                  <div class="form-group">
                    <label>Title: <span style="color: #ff0000;">*</span></label>
                    <select name="titel_id" id="titel_id" class="select2bs5" style="width: 100%">
                      <option value="" selected disabled>Select Title</option>
                      <?php $__currentLoopData = $titles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $title): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <?php if($appData->titel_id != ''): ?>
                      <option value="<?php echo e($title->id); ?>" <?php echo e($title->id == $appData->titel_id ? 'selected' : ''); ?>><?php echo e(ucfirst($title->category_name)); ?></option>
                      <?php else: ?>
                      <option value="<?php echo e($title->id); ?>" <?php echo e($title->id == old('titel_id') ? 'selected' : ''); ?>><?php echo e(ucfirst($title->category_name)); ?></option>
                      <?php endif; ?>
                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <span class="text-danger"><?php $__errorArgs = ['titel_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                  </div>
                </div>
                <div class="col-md-6"> <!-- Use col-md-6 for two columns on large screens -->
                  <div class="form-group">
                    <label>Initials: <span style="color: #ff0000;">*</span></label>
                    <input type="text" name="initials" class="form-control" id="initials" value="<?php echo e($appData->initials != '' ? $appData->initials : old('initials')); ?>" placeholder="Ex:- A.B.C." maxlength="20" />
                    <span class="text-danger"><?php $__errorArgs = ['initials'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                  </div>
                </div>
              </div>
            </div>

          </div>

          <div class="row">
            <div class="col-md-8 col-sm-12">
              <div class="form-group">
                <label>Name Denoted By Initials : <span style="color: #ff0000;">*</span></label>
                <input type="text" name="name_denoted_by_initials" class="form-control" id="name_denoted_by_initials" value="<?php echo e($appData->name_denoted_by_initials != '' ? $appData->name_denoted_by_initials : old('name_denoted_by_initials')); ?>" />
                <span class="text-danger"><?php $__errorArgs = ['name_denoted_by_initials'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
              </div>
            </div>
            <div class="col-md-4 col-sm-12">
              <div class="form-group">
                <label>Last Name: <span style="color: #ff0000;">*</span></label>
                <input type="text" name="last_name" class="form-control" id="last_name" value="<?php echo e($appData->last_name != '' ? $appData->last_name : old('last_name')); ?>" />
                <span class="text-danger"><?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4 col-sm-12">
              <div class="form-group">
                <label>Telephone No. - (Mobile): <span style="color: #ff0000;">*</span></label>
                <input type="text" name="mobile_no" class="form-control" id="mobile_no" value="<?php echo e($appData->mobile_no); ?>" readonly />
              </div>
            </div>
            <div class="col-md-4 col-sm-12">
              <div class="form-group">
                <label>Telephone No. - (Residence): </label>
                <input type="text" name="phone_no" class="form-control" id="phone_no" value="<?php echo e($appData->phone_no != '' ?  $appData->phone_no : old('phone_no')); ?>" placeholder="Ex:-0112222222" />
                <span class="text-danger"><?php $__errorArgs = ['phone_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
              </div>

            </div>
            <div class="col-md-4 col-sm-12">
              <div class="form-group">
                <label>Email Address: <span style="color: #ff0000;">*</span></label>
                <input type="text" name="email" class="form-control" id="email" value="<?php echo e($appData->email); ?>" readonly />
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-4 col-sm-12">
              <div class="form-group">
                <label>Date of Birth: <span style="color: #ff0000;">*</span></label>
                <div class="input-group date" id="date_of_birth" data-target-input="nearest">
                  <?php if($appData->date_of_birth != '' && $appData->date_of_birth != '1970-01-01'): ?>
                  <input type="text" class="form-control" data-target="#date_of_birth" name="date_of_birth" value="<?php echo e(date("d-M-Y", strtotime($appData->date_of_birth))); ?>" disabled>
                  <?php endif; ?>
                  <div class="input-group-append" data-target="#date_of_birth" data-toggle="datetimepicker">
                    <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                  </div>
                </div>
                <span class="text-danger"><?php $__errorArgs = ['date_of_birth'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
              </div>
            </div>

            <div class="col-md-4 col-sm-12">
              <div class="form-group">
                <label>Gender: <span style="color: #ff0000;">*</span></label>
                <select name="gender_id" id="gender_id" class="select2bs5" style="width: 100%" disabled>
                  <option value="" selected disabled>Select Gender</option>
                  <?php $__currentLoopData = $genders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gender): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <?php if($appData->gender_id != 0): ?>
                  <option value="<?php echo e($gender->id); ?>" <?php echo e($gender->id == $appData->gender_id  ? 'selected' : ''); ?>><?php echo e(ucfirst($gender->category_name)); ?></option>
                  <?php else: ?>
                  <option value="<?php echo e($gender->id); ?>" <?php echo e($gender->id == old('gender_id')  ? 'selected' : ''); ?>><?php echo e(ucfirst($gender->category_name)); ?></option>
                  <?php endif; ?>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <span class="text-danger"><?php $__errorArgs = ['gender_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
              </div>
            </div>
            <div class="col-md-4 col-sm-12">
              <div class="form-group">
                <label>Civil Status: <span style="color: #ff0000;">*</span></label>
                <select name="civil_status_id" id="civil_status_id" class="select2bs5" style="width: 100%">
                  <option value="" selected disabled>Select Civil Status</option>
                  <?php $__currentLoopData = $civilStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $civilStatus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <?php if($appData->civil_status_id != 0): ?>
                  <option value="<?php echo e($civilStatus->id); ?>" <?php echo e($civilStatus->id == $appData->civil_status_id ? 'selected' : ''); ?>><?php echo e(ucfirst($civilStatus->category_name)); ?></option>
                  <?php else: ?>
                  <option value="<?php echo e($civilStatus->id); ?>" <?php echo e($civilStatus->id == old('civil_status_id')  ? 'selected' : ''); ?>><?php echo e(ucfirst($civilStatus->category_name)); ?></option>
                  <?php endif; ?>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <span class="text-danger"><?php $__errorArgs = ['civil_status_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
              </div>
            </div>

          </div>
          
  <div class="row">
    <div class="col-md-12 col-sm-12">
      <label><strong>Permanent Address</strong></label>
    </div>
  </div>
  <div class="row">
    <div class="col-md-3 col-sm-12">
      <div class="form-group">
        <label class="font-weight-normal">Address Line 1: <span style="color: #ff0000;">*</span></label>
        <input type="text" name="permanent_add1" class="form-control" id="permanent_add1" value="<?php echo e($appData->permanent_add1 != '' ?  $appData->permanent_add1 : old('permanent_add1')); ?>" placeholder="Ex:-325B"/>
        <span class="text-danger"><?php $__errorArgs = ['permanent_add1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
      </div>
    </div>
    <div class="col-md-3 col-sm-12">
      <div class="form-group">
        <label class="font-weight-normal">Address Line 2:</label>
        <input type="text" name="permanent_add2" class="form-control" id="permanent_add2" value="<?php echo e($appData->permanent_add2 != '' ?  $appData->permanent_add2 : old('permanent_add2')); ?>" placeholder="Ex:-Temple Road" />
      </div>
    </div>
    <div class="col-md-3 col-sm-12">
      <div class="form-group">
        <label class="font-weight-normal">Address Line 3:</label>
        <input type="text" name="permanent_add3" class="form-control" id="permanent_add3" value="<?php echo e($appData->permanent_add3 != '' ?  $appData->permanent_add3 : old('permanent_add3')); ?>" />
      </div>
    </div>
    <div class="col-md-3 col-sm-12">
      <div class="form-group">
        <label class="font-weight-normal">Address City: <span style="color: #ff0000;">*</span></label>
        <select name="permanent_city_id" id="permanent_city_id" class="select2bs5" style="width: 100%">
          <option value="" selected disabled>Select City</option>
          <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <?php if($appData->permanent_city_id != ''): ?>
          <option value="<?php echo e($city->id); ?>" <?php echo e($city->id == $appData->permanent_city_id ? 'selected' : ''); ?>><?php echo e(ucfirst($city->name_en)); ?></option>
          <?php else: ?>
          <option value="<?php echo e($city->id); ?>" <?php echo e($city->id == old('permanent_city_id') ? 'selected' : ''); ?>><?php echo e(ucfirst($city->name_en)); ?></option>
          <?php endif; ?>

          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <span class="text-danger"><?php $__errorArgs = ['permanent_city_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-md-12 col-sm-12">
      <label class="font-weight-normal">Same as Permanent Address:</label> <input type="checkbox" id="checkbox1">
    </div>
  </div>
  <div class="row">
    <div class="col-md-12 col-sm-12">
      <label><strong>Postal Address</strong></label>
    </div>
  </div>
  <div class="row">
    <div class="col-md-3 col-sm-12">
      <div class="form-group">
        <label class="font-weight-normal">Address Line 1: <span style="color: #ff0000;">*</span></label>
        <input type="text" name="postal_add1" class="form-control" id="postal_add1" value="<?php echo e($appData->postal_add1 != '' ?  $appData->postal_add1 : old('postal_add1')); ?>" placeholder="Ex:-325B"  />
        <span class="text-danger"><?php $__errorArgs = ['postal_add1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
      </div>
    </div>
    <div class="col-md-3 col-sm-12">
      <div class="form-group">
        <label class="font-weight-normal">Address Line 2:</label>
        <input type="text" name="postal_add2" class="form-control" id="postal_add2" value="<?php echo e($appData->postal_add2 != '' ?  $appData->postal_add2 : old('postal_add2')); ?>" placeholder="Ex:-325B Temple Road"  />
      </div>
    </div>
    <div class="col-md-3 col-sm-12">
      <div class="form-group">
        <label class="font-weight-normal">Address Line 3:</label>
        <input type="text" name="postal_add3" class="form-control" id="postal_add3" value="<?php echo e($appData->postal_add3 != '' ?  $appData->postal_add3 : old('postal_add3')); ?>"  />
      </div>
    </div>
    <div class="col-md-3 col-sm-12">
      <div class="form-group">
        <label class="font-weight-normal">Address City: <span style="color: #ff0000;">*</span></label>
        <select name="postal_city_id" id="postal_city_id" class="select2bs5" style="width: 100%">
          <option value="" selected>Select City</option>
          <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <?php if($appData->postal_city_id != 0): ?>
          <option value="<?php echo e($city->id); ?>" <?php echo e($city->id == $appData->postal_city_id ? 'selected' : ''); ?>><?php echo e(ucfirst($city->name_en)); ?></option>
          <?php else: ?>
          <option value="<?php echo e($city->id); ?>" <?php echo e($city->id == old('postal_city_id') ? 'selected' : ''); ?>><?php echo e(ucfirst($city->name_en)); ?></option>
          <?php endif; ?>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <span class="text-danger"><?php $__errorArgs = ['postal_city_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-4 col-sm-12">
      <div class="form-group">
        <label>Applicant's Citizenship: <span style="color: #ff0000;">*</span></label>
        <input type="text" name="citizenship" class="form-control" id="citizenship" placeholder="Ex:-Sri Lankan" value="Sri Lankan" readonly />
        <span class="text-danger"><?php $__errorArgs = ['citizenship'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
      </div>
    </div>
    <div class="col-md-4 col-sm-12">
      <div class="form-group">
        <label>If a Citizen of Sri Lanka, How Obtained :<span style="color: #ff0000;">*</span></label>
        <select name="state_of_citizenship_id" id="state_of_citizenship_id" class="select2bs4" style="width: 100%">
          <option value="" selected disabled>Select Citizenship Type</option>
          <?php $__currentLoopData = $citizenships; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $citizenship): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <?php if($appData->state_of_citizenship_id != ''): ?>
          <option value="<?php echo e($citizenship->id); ?>" <?php echo e($citizenship->id == $appData->state_of_citizenship_id ? 'selected' : ''); ?>><?php echo e(ucfirst($citizenship->category_name)); ?></option>
          <?php else: ?>
          <option value="<?php echo e($citizenship->id); ?>" <?php echo e($citizenship->id == old('state_of_citizenship_id') ? 'selected' : ''); ?>><?php echo e(ucfirst($citizenship->category_name)); ?></option>
          <?php endif; ?>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <span class="text-danger"><?php $__errorArgs = ['state_of_citizenship_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
      </div>
    </div>
    <?php if($appData->state_of_citizenship_id == 25 || $appData->state_of_citizenship_id == NULL): ?>
    <div class="col-md-4 col-sm-12">
      <div class="form-group" id="reg_num" style="display:none">
        <label>Citizen Registration No.: <span style="color: #ff0000;">*</span></label>
        <input type="text" name="citizen_registration_no" class="form-control" id="citizen_registration_no" value="<?php echo e($appData->citizen_registration_no != '' ?  $appData->citizen_registration_no : old('citizen_registration_no')); ?>" />
        <span class="text-danger"><?php $__errorArgs = ['citizen_registration_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
      </div>
    </div>
    <?php else: ?>
    <div class="col-md-4 col-sm-12">
      <div class="form-group" id="reg_num">
        <label>Citizen Registration No.: <span style="color: #ff0000;">*</span></label>
        <input type="text" name="citizen_registration_no" class="form-control" id="citizen_registration_no" value="<?php echo e($appData->citizen_registration_no != '' ?  $appData->citizen_registration_no : old('citizen_registration_no')); ?>" />
        <span class="text-danger"><?php $__errorArgs = ['citizen_registration_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
      </div>
    </div>
    <?php endif; ?>
  </div>
  <div class="row">
    <div class="col-md-8 col-sm-12">
      <div class="form-group">
        <label>Highest Academic Qualification: <span style="color: #ff0000;">*</span></label>
        <select name="emp_highest_edu_level" id="emp_highest_edu_level" class="select2bs5" style="width: 100%">
          <option value="" selected disabled>Select Highest Academic Qualification</option>
          <?php $__currentLoopData = $educationLevels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $educationLevel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <?php if($appData->emp_highest_edu_level != ''): ?>
          <option value="<?php echo e($educationLevel->id); ?>" <?php echo e($educationLevel->id == $appData->emp_highest_edu_level ? 'selected' : ''); ?>><?php echo e(ucfirst($educationLevel->category_name )); ?></option>
          <?php else: ?>
          <option value="<?php echo e($educationLevel->id); ?>" <?php echo e($educationLevel->id ==  old('emp_highest_edu_level') ? 'selected' : ''); ?>><?php echo e(ucfirst($educationLevel->category_name )); ?></option>
          <?php endif; ?>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <span class="text-danger"><?php $__errorArgs = ['emp_highest_edu_level'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-md-6 col-sm-12">
      <div class="form-group">
        <label for="exampleInputFile">Photograph (JPG,PNG Formats Only):<span style="color: #ff0000;">*</span></label>
        <div class="input-group">
          <div class="custom-file">
            <input type="file" class="custom-file-input" id="profile_photo" name="profile_photo" accept="image/jpg, image/jpeg, image/png">
            <label class="custom-file-label" for="profile_photo">Choose file</label>
          </div>
        </div>
        <span id="error_messages"></span>
        <span class="text-danger"><?php $__errorArgs = ['profile_photo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
        <br>
        <p style="font-size:13px;">
          The photograph should be 45.0 mm × 35.0 mm (H x W) in size (Passport Size) and full color on plain white background, with full front face orientation, eyes looking directly into the camera, both ears visible, no hats, no coverings on the head, eyes and both edges of the face should be visible as shown in the sample.
        </p>
      </div>
    </div>
    <div class="col-md-4 col-sm-12">
      <div class="form-group">
        <div class="row">
          <div class="col-6">
            <div class="controls">
              <img id="showImage" src="<?php echo e((!empty($appData->profile_photo))? Storage::url($appData->profile_photo) :url('backend/dist/img/images.png')); ?>" style="width: 100px; height: 125px; border: 1px solid #000000;">
            </div>
          </div>
          <div class="col-6">
            <div class="d-flex flex-column h-100">
              <div class="controls flex-grow-1 d-flex flex-column text-center">
                <?php if($appData->gender_id != 0 || old('gender_id') != 0): ?>
                <?php if($appData->gender_id == 1 || old('gender_id') == 1): ?>
                <img id="showImage2" src="<?php echo e(url('backend/dist/img/sample.png')); ?>" style="width: 100px; height: 125px; border: 1px solid #000000;">
                <?php else: ?>
                <img id="showImage2" src="<?php echo e(url('backend/dist/img/sample2.png')); ?>" style="width: 100px; height: 125px; border: 1px solid #000000;">
                <?php endif; ?>
                <?php else: ?>
                <img id="showImage2" src="<?php echo e(url('backend/dist/img/default.png')); ?>" style="width: 100px; height: 125px; border: 1px solid #000000;">
                <?php endif; ?>
              </div>
              <p class="text-dark" style="font-size:13px;"><b>Sample photograph</b></p>
            </div>
          </div>

        </div>
      </div>
    </div>


    <div class="col-md-2 col-sm-12">
      <div class="form-group">

      </div>
    </div>
  </div>

  <?php if($vacancy->main_category_id == 46): ?>

  <?php echo $__env->make('frontend.application.components.ol', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  <?php echo $__env->make('frontend.application.components.al', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  <?php echo $__env->make('frontend.application.components.diploma', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  <?php echo $__env->make('frontend.application.components.degree', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

  <?php endif; ?>

  <?php if($vacancy->main_category_id == 44 || $vacancy->main_category_id == 45): ?>

  <?php echo $__env->make('frontend.application.components.degree', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  <?php echo $__env->make('frontend.application.components.diploma', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  <?php echo $__env->make('frontend.application.components.publication', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  <?php echo $__env->make('frontend.application.components.squalification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  <?php echo $__env->make('frontend.application.components.membership', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  <?php echo $__env->make('frontend.application.components.pqualification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  <?php echo $__env->make('frontend.application.components.experience', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  <?php echo $__env->make('frontend.application.components.bond', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  <?php echo $__env->make('frontend.application.components.referance', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  <?php echo $__env->make('frontend.application.components.public_sector', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

  <?php endif; ?>

  <div class="card card-outline card-danger">
    <div class="card-body">
      <div class="icheck-danger">
        <input type="checkbox" id="agreeTerms" name="terms" value="1">
        <label for="agreeTerms">
          I hereby declare that the particulars furnished by me in the application are true and accurate. I am aware that if any particulars contained herein are found to be false or incorrect, I am liable to disqualification if the inaccuracy is discovered before the selection and dismissal without any compensation if the inaccuracy is discovered after the appointment.
        </label>
      </div>
      <span class="text-danger"><?php $__errorArgs = ['terms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><br><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
      <br>
      <?php if($appData->application_decision_id == 33): ?>
      <div class="row">
        <div class="col-md-6 col-sm-12">
          <input type="submit" class="btn btn-warning btn-lg submitButton" name="save1" value="Save & Leave">

          
          <input type="button" class="btn btn-danger btn-lg submitButton" value="Submit" style="background-color: #990000;" onclick="checkFields()">
        </div>
      </div>
      <?php elseif($appData->application_decision_id == 34): ?>
      <div class="row">
        <div class="col-md-6 col-sm-12">
          
          <a class="btn btn-secondary btn-lg" href="<?php echo e(route('application.final')); ?>" role="button">Cancel</a>
        </div>
      </div>
      <?php endif; ?>
    </div>
  </div>
  <?php if($appData->application_decision_id == 33): ?>
  <div class="floating-action-container">
    <input type="submit" class="btn btn-danger btn-md submitButton1" name="save" value="Save as Draft">
  </div>
  <?php endif; ?>
  </form>

  </div>

  <div class="modal fade" id="exampleModalCenter3" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-md" role="document">
      <div class="modal-content">
        
        <div class="modal-body text-center">
          <p style="font-size:30px;color: #595959"><b>Are you sure that you want to submit the application?</b></p>
          <p style="font-size:15px;">You will not be able to edit the application after submitting it.</p>
        </div>
        <div class="modal-footer text-center d-flex justify-content-center">
          <a type="button" class="btn btn-warning btn-lg submitButton" data-dismiss="modal">Cancel</a>
          <input type="submit" class="btn btn-danger btn-lg submitButton" name="submit1" value="Submit" style="background-color: #990000;">
        </div>

      </div>
    </div>
  </div>
</section>
<script type="text/javascript">
  $(document).ready(function() {

    $('#applicant_form select[name="state_of_citizenship_id"]').change(function() {
      if ($('#applicant_form select[name="state_of_citizenship_id"] option:selected').val() == 26) {
        $('#reg_num').show();
      } else {
        $('#reg_num').hide();
      }
    });


    var imageURL = "<?php echo e(url('backend/dist/img/sample.png')); ?>";
    var imageURL2 = "<?php echo e(url('backend/dist/img/sample2.png')); ?>";

    $('#applicant_form select[name="gender_id"]').change(function() {
      if ($('#applicant_form select[name="gender_id"] option:selected').val() == 1) {
        var imageElement = document.getElementById("showImage2");
        imageElement.src = imageURL;
      } else {
        var imageElement = document.getElementById("showImage2");
        imageElement.src = imageURL2;
      }
    });

    $("#checkbox1").on("change", function() {

      if (this.checked) {
        $("#postal_add1").val($("#permanent_add1").val());
        $("#postal_add2").val($("#permanent_add2").val());
        $("#postal_add3").val($("#permanent_add3").val());

        var skillsSelect = document.getElementById("permanent_city_id");
        var selectedText = skillsSelect.options[skillsSelect.selectedIndex].text;
        var value = $("#permanent_city_id option:selected").val();

        $("#postal_city_id").append("<option value='" + value + "' selected>" + selectedText + "</option>");

        $('#postal_add1').attr('readonly', true);
        $('#postal_add2').attr('readonly', true);
        $('#postal_add3').attr('readonly', true);
        //$('#postal_city_id').attr('selected', true);

      } else {

        $('#postal_add1').val("");
        $('#postal_add2').val("");
        $('#postal_add3').val("");
        document.getElementById("postal_city_id").removeChild(document.getElementById("postal_city_id").lastElementChild); //remove the last append option

        $('#postal_add1').attr('readonly', false);
        $('#postal_add2').attr('readonly', false);
        $('#postal_add3').attr('readonly', false);
        //$('#postal_city_id').attr('selected', false);

      }

    });

    $('#profile_photo').change(function(e) {
      var reader = new FileReader();
      reader.onload = function(e) {
        $('#showImage').attr('src', e.target.result);
      }
      reader.readAsDataURL(e.target.files['0']);
    });

    $('#degree_type').on('change', function() {
      var degree_type = $(this).val();
      //alert(degree_type);
      if (degree_type) {
        $.ajax({
          url: "<?php echo e(url('/degree/class/load/ajax')); ?>/" + degree_type,
          type: "GET",
          dataType: "json",
          success: function(data) {
            var d = $('select[name="degree_class_1"]').empty();
            $.each(data, function(key, value) {
              $('select[name="degree_class_1"]').append('<option value="' + value.id + '">' + value.category_name + '</option>');
            });

          },
        });
      }
    });

    $('textarea').each(function() {
      $(this).val($(this).val().trim());
    });



    $(document).on('click', '#btn-submit', function(e) {
      e.preventDefault();

      Swal.fire({
        title: 'Are you sure that you want to submit the application?',
        text: "You will not be able to edit the application after submitting it.",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ffc107',
        cancelButtonColor: '#990000',
        confirmButtonText: 'Cancel',
        cancelButtonText: 'Submit',
        cancelButtonTextColor: 'black',
      }).then((result) => {
        if (result.isConfirmed) {
          // User clicked the Cancel button, do nothing, or perform any other action.
        } else {
          // User clicked the Submit button, proceed with form submission.
          // const form = document.querySelector("#applicant_form");
          // const submitButton = form.querySelector("[name='submit1']");
          // form.setAttribute("submit", "submit1"); // Set the 'submit' attribute to 'save1'
          // form.submit(); // Submit the form using the 'save1' button

          //document.querySelector("#applicant_form [name='submit1']").click();
        }
      });
    });




    $('.delete-record').on('click', function(e) {
      e.preventDefault();
      var recordId = $(this).data('record-id');
      var url = "<?php echo e(route('degree.delete', ':id')); ?>";
      url = url.replace(':id', recordId);
      var row = $(this).closest('tr');

      //alert(row);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-old').on('click', function(e) {
      e.preventDefault();
      var key = $(this).data('record-old-id');
      var url = "<?php echo e(route('degree.old.delete', ':id')); ?>";
      url = url.replace(':id', key);
      var row = $(this).closest('tr');

      //alert(url);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-subject').on('click', function(e) {
      e.preventDefault();
      var recordId = $(this).data('record-id');
      var url = "<?php echo e(route('degree.subject.delete', ':id')); ?>";
      url = url.replace(':id', recordId);
      var div = $(this).closest('div');

      //alert(row);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          div.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-subject-old').on('click', function(e) {
      e.preventDefault();
      var key = $(this).data('record-old-id');
      var url = "<?php echo e(route('degree.subject.old.delete', ':id')); ?>";
      url = url.replace(':id', key);
      var div = $(this).closest('div');

      //alert(url);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          div.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-certificate').on('click', function(e) {
      e.preventDefault();
      var recordId = $(this).data('record-id');
      var url = "<?php echo e(route('application.data.delete.degree', ':id')); ?>";
      url = url.replace(':id', recordId);
      var div = $(this).closest('div');

      //alert(url);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          div.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-diploma').on('click', function(e) {
      e.preventDefault();
      var recordId = $(this).data('record-id');
      var url = "<?php echo e(route('diploma.delete', ':id')); ?>";
      url = url.replace(':id', recordId);
      var row = $(this).closest('tr');

      //alert(row);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-old-diploma').on('click', function(e) {
      e.preventDefault();
      var key = $(this).data('record-old-id');
      var url = "<?php echo e(route('diploma.old.delete', ':id')); ?>";
      url = url.replace(':id', key);
      var row = $(this).closest('tr');

      //alert(url);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-certificate-diploma').on('click', function(e) {
      e.preventDefault();
      var recordId = $(this).data('record-id');
      var url = "<?php echo e(route('application.data.delete.diploma', ':id')); ?>";
      url = url.replace(':id', recordId);
      var div = $(this).closest('div');

      //alert(url);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          div.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-squlification').on('click', function(e) {
      e.preventDefault();
      var recordId = $(this).data('record-id');
      var url = "<?php echo e(route('squlification.delete', ':id')); ?>";
      url = url.replace(':id', recordId);
      var row = $(this).closest('tr');

      //alert(row);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-old-squlification').on('click', function(e) {
      e.preventDefault();
      var key = $(this).data('record-old-id');
      var url = "<?php echo e(route('squlification.old.delete', ':id')); ?>";
      url = url.replace(':id', key);
      var row = $(this).closest('tr');

      //alert(url);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-membership').on('click', function(e) {
      e.preventDefault();
      var recordId = $(this).data('record-id');
      var url = "<?php echo e(route('membership.delete', ':id')); ?>";
      url = url.replace(':id', recordId);
      var row = $(this).closest('tr');

      //alert(row);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-old-membership').on('click', function(e) {
      e.preventDefault();
      var key = $(this).data('record-old-id');
      var url = "<?php echo e(route('membership.old.delete', ':id')); ?>";
      url = url.replace(':id', key);
      var row = $(this).closest('tr');

      //alert(url);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-pqulification').on('click', function(e) {
      e.preventDefault();
      var recordId = $(this).data('record-id');
      var url = "<?php echo e(route('pqulification.delete', ':id')); ?>";
      url = url.replace(':id', recordId);
      var row = $(this).closest('tr');

      //alert(row);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-old-pqulification').on('click', function(e) {
      e.preventDefault();
      var key = $(this).data('record-old-id');
      var url = "<?php echo e(route('pqulification.old.delete', ':id')); ?>";
      url = url.replace(':id', key);
      var row = $(this).closest('tr');

      //alert(url);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });


    $('.delete-record-employmentrecord').on('click', function(e) {
      e.preventDefault();
      var recordId = $(this).data('record-id');
      var url = "<?php echo e(route('employment.record.delete', ':id')); ?>";
      url = url.replace(':id', recordId);
      var row = $(this).closest('tr');

      //alert(row);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-old-employmentrecord').on('click', function(e) {
      e.preventDefault();
      var key = $(this).data('record-old-id');
      var url = "<?php echo e(route('employment.record.old.delete', ':id')); ?>";
      url = url.replace(':id', key);
      var row = $(this).closest('tr');

      //alert(url);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-certificate-employment-record').on('click', function(e) {
      e.preventDefault();
      var recordId = $(this).data('record-id');
      var url = "<?php echo e(route('application.data.delete.employment.record', ':id')); ?>";
      url = url.replace(':id', recordId);
      var div = $(this).closest('div');

      //alert(url);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          div.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-bondrecord').on('click', function(e) {
      e.preventDefault();
      var recordId = $(this).data('record-id');
      var url = "<?php echo e(route('bond.record.delete', ':id')); ?>";
      url = url.replace(':id', recordId);
      var row = $(this).closest('tr');

      //alert(row);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-old-bondrecord').on('click', function(e) {
      e.preventDefault();
      var key = $(this).data('record-old-id');
      var url = "<?php echo e(route('bond.record.old.delete', ':id')); ?>";
      url = url.replace(':id', key);
      var row = $(this).closest('tr');

      //alert(url);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-referee').on('click', function(e) {
      e.preventDefault();
      var recordId = $(this).data('record-id');
      var url = "<?php echo e(route('referee.record.delete', ':id')); ?>";
      url = url.replace(':id', recordId);
      var row = $(this).closest('tr');

      //alert(row);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

    $('.delete-record-old-referee').on('click', function(e) {
      e.preventDefault();
      var key = $(this).data('record-old-id');
      var url = "<?php echo e(route('referee.record.old.delete', ':id')); ?>";
      url = url.replace(':id', key);
      var row = $(this).closest('tr');

      //alert(url);

      $.ajax({
        url: url,
        type: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Handle success response, update the UI accordingly
          // For example, remove the deleted record from the table
          row.remove();
          console.log(response);
        },
        error: function(xhr) {
          // Handle error response, display an error message
          console.log(xhr.responseText);
        }
      });
    });

  });

  //boostrap file name show
  $('#certificate1').on('change', function() {
    //get the file name
    var fileName = document.getElementById("certificate1").files[0].name;
    //replace the "Choose a file" label
    $(this).next('.custom-file-label').html(fileName);
  });

  $('#certificate2').on('change', function() {
    //get the file name
    var fileName = document.getElementById("certificate2").files[0].name;
    //replace the "Choose a file" label
    $(this).next('.custom-file-label').html(fileName);
  });

  $('#al_certificate1').on('change', function() {
    //get the file name
    var fileName = document.getElementById("al_certificate1").files[0].name;
    //replace the "Choose a file" label
    $(this).next('.custom-file-label').html(fileName);
  });

  $('#al_certificate2').on('change', function() {
    //get the file name
    var fileName = document.getElementById("al_certificate2").files[0].name;
    //replace the "Choose a file" label
    $(this).next('.custom-file-label').html(fileName);
  });

  $('#profile_photo').on('change', function() {
    //get the file name
    var fileName = document.getElementById("profile_photo").files[0].name;
    //replace the "Choose a file" label
    $(this).next('.custom-file-label').html(fileName);
  });

  $('#public_sector_letter').on('change', function() {
    //get the file name
    var fileName = document.getElementById("public_sector_letter").files[0].name;
    //replace the "Choose a file" label
    $(this).next('.custom-file-label').html(fileName);
  });


  //   $(".alert").delay(100000).slideUp(200, function() {
  //     $(this).alert('close');
  // });

  function append_row() {

    var table1 = document.getElementById('degrees');
    var lastRow = table1.rows.length - 1;

    var degree_type = $("#degree_type option:selected").text();
    var degree_type_val = $("#degree_type option:selected").val();
    var degree_titel = document.getElementById('degree_titel').value;
    var degree_university = document.getElementById('degree_university').value;
    var degree_class = $("#degree_class option:selected").text();
    var degree_class_val = $("#degree_class option:selected").val();
    var eff_date = document.getElementById('eff_date').value;
    var degree_index_number = document.getElementById('degree_index_number').value;
    var degree_start = document.getElementById('degree_start').value;
    var degree_end = document.getElementById('degree_end').value;
    var reference_no = document.getElementById('reference_no').value;
    var currentDate = new Date();
    var currentYear = currentDate.getFullYear();

    //alert(parseInt(degree_start.slice(-4)));

    if (reference_no == '') {
      event.preventDefault(); // Prevent default behavior
      Swal.fire({
        icon: 'error',
        title: 'Error in Data Adding',
        text: 'Please login for application',
        confirmButtonColor: '#ffc107',
      });

    } else {

      if (degree_type_val == '0') {
        event.preventDefault(); // Prevent default behavior
        Swal.fire({
          icon: 'error',
          title: 'Error in Data Adding',
          text: 'Please select type of the degree',
          confirmButtonColor: '#ffc107',
        });

      } else {

        if (degree_titel == "") {
          event.preventDefault(); // Prevent default behavior
          Swal.fire({
            icon: 'error',
            title: 'Error in Data Adding',
            text: 'Please enter name of the degree',
            confirmButtonColor: '#ffc107',
          });

        } else {

          if (!validateName(degree_titel)) {
            event.preventDefault(); // Prevent default behavior
            Swal.fire({
              icon: 'error',
              title: 'Error in Data Adding',
              text: 'Please enter valid name of the degree [Allowed letters, spaces, dots and hyphens only]',
              confirmButtonColor: '#ffc107',
            });
          } else {

            if (degree_university == "") {
              event.preventDefault(); // Prevent default behavior
              Swal.fire({
                icon: 'error',
                title: 'Error in Data Adding',
                text: 'Please enter university/institution',
                confirmButtonColor: '#ffc107',
              });

            } else {


              if (!validateName(degree_university)) {
                event.preventDefault(); // Prevent default behavior
                Swal.fire({
                  icon: 'error',
                  title: 'Error in Data Adding',
                  text: 'Please enter valid degree university/institution [Allowed letters, spaces, dots and hyphens only]',
                  confirmButtonColor: '#ffc107',
                });
              } else {

                if (degree_class_val == "0") {
                  event.preventDefault(); // Prevent default behavior
                  Swal.fire({
                    icon: 'error',
                    title: 'Error in Data Adding',
                    text: 'Please select class of the degree',
                    confirmButtonColor: '#ffc107',
                  });

                } else {

                  if (eff_date == "") {
                    event.preventDefault(); // Prevent default behavior
                    Swal.fire({
                      icon: 'error',
                      title: 'Error in Data Adding',
                      text: 'Please select effective date of the degree',
                      confirmButtonColor: '#ffc107',
                    });

                  } else {

                    if (!isDateTodayOrBefore(eff_date)) {
                      event.preventDefault(); // Prevent default behavior
                      Swal.fire({
                        icon: 'error',
                        title: 'Error in Data Adding',
                        text: 'Degree effective date must not be a future date',
                        confirmButtonColor: '#ffc107',
                      });

                    } else {

                      if (degree_index_number == "") {
                        event.preventDefault(); // Prevent default behavior
                        Swal.fire({
                          icon: 'error',
                          title: 'Error in Data Adding',
                          text: 'Please enter a valid index number',
                          confirmButtonColor: '#ffc107',
                        });

                      } else {

                        if (degree_start == "") {
                          event.preventDefault(); // Prevent default behavior
                          Swal.fire({
                            icon: 'error',
                            title: 'Error in Data Adding',
                            text: 'Please select degree start year and month',
                            confirmButtonColor: '#ffc107',
                          });

                        } else {

                          if (degree_end == "") {
                            event.preventDefault(); // Prevent default behavior
                            Swal.fire({
                              icon: 'error',
                              title: 'Error in Data Adding',
                              text: 'Please select degree end year and month',
                              confirmButtonColor: '#ffc107',
                            });

                          } else {

                            if (parseInt(degree_start.slice(-4)) >= currentYear) {
                              event.preventDefault(); // Prevent default behavior
                              Swal.fire({
                                icon: 'error',
                                title: 'Error in Data Adding',
                                text: 'Degree start year should be less than current year',
                                confirmButtonColor: '#ffc107',
                              });

                            } else if (parseInt(degree_end.slice(-4)) > currentYear) {
                              event.preventDefault(); // Prevent default behavior
                              Swal.fire({
                                icon: 'error',
                                title: 'Error in Data Adding',
                                text: 'Degree end year should be less than current year',
                                confirmButtonColor: '#ffc107',
                              });

                            } else if (parseInt(degree_start.slice(-4)) > parseInt(degree_end.slice(-4))) {
                              event.preventDefault(); // Prevent default behavior
                              Swal.fire({
                                icon: 'error',
                                title: 'Error in Data Adding',
                                text: 'Degree start year should be less than the degree end year',
                                confirmButtonColor: '#ffc107',
                              });


                            } else if (parseInt(degree_start.slice(-4)) > parseInt(eff_date.slice(-4))) {
                              event.preventDefault(); // Prevent default behavior
                              Swal.fire({
                                icon: 'error',
                                title: 'Error in Data Adding',
                                text: 'Degree effective date year cannot be less than degree start year',
                                confirmButtonColor: '#ffc107',
                              });


                            } else {
                              // Create a new row

                              var Row1 = '<td><textarea class="form-control readOnlyInput full-width-textarea" rows="3" name="degree_type[]" readonly>' + degree_type + '</textarea><input type="hidden" name="degree_type_val[]" class="form-control" value="' + degree_type_val + '" readonly></td>';
                              var Row2 = '<td><textarea class="form-control readOnlyInput full-width-textarea" name="degree_titel[]" rows="3" readonly >' + degree_titel + '</textarea></td>';
                              var Row3 = '<td><textarea class="form-control readOnlyInput full-width-textarea" name="degree_university[]" rows="3" readonly >' + degree_university + '</textarea></td>';
                              var Row4 = '<td><textarea class="form-control readOnlyInput full-width-textarea" rows="3" name="degree_class[]" readonly >' + degree_class + '</textarea><input type="hidden" name="degree_class_val[]" class="form-control" value="' + degree_class_val + '" readonly></td>';
                              var Row5 = '<td><textarea class="form-control readOnlyInput full-width-textarea" name="eff_date[]" rows="2" readonly >' + eff_date + '</textarea><b>From -</b><textarea class="form-control readOnlyInput full-width-textarea" name="degree_start[]" rows="1" readonly >' + degree_start + '</textarea><b>To -</b><textarea class="form-control readOnlyInput full-width-textarea" name="degree_end[]" rows="1" readonly >' + degree_end + '</textarea></td>';
                              var Row6 = '<td><textarea class="form-control readOnlyInput full-width-textarea" name="degree_index_number[]" rows="3" readonly >' + degree_index_number + '</textarea></td>';
                              // var Row7 = '<td></td>';
                              // var Row8 = '<td></td>';
                              var Row7 = '<td><input type="button" value="Remove" class="btn btn-sm btn-danger remove-button"></td>'


                              // Update the DataTable instance with the new data
                              degreesdataTable.row.add([
                                Row1, Row2, Row3, Row4, Row5, Row6, Row7
                              ]).draw();

                              $('#degree_type').val('0').trigger('change');
                              $('#degree_titel').val('');
                              $('#degree_university').val('');
                              $('#degree_class').val('0').trigger('change');
                              $('#eff_date').val('');
                              $('#degree_index_number').val('');
                              $('#degree_start').val('');
                              $('#degree_end').val('');

                            }


                          }

                        }

                      }

                    }

                  }

                }

              }

            }

          }
        }
      }
    }

  }

  function append_row1() {

    var table1 = document.getElementById('diplomas');
    var lastRow = table1.rows.length - 1;

    var diploma_type = $("#diploma_type option:selected").text();
    var diploma_type_val = $("#diploma_type option:selected").val();
    var diploma_titel = document.getElementById('diploma_titel').value;
    var diploma_university = document.getElementById('diploma_university').value;
    var diploma_duration = document.getElementById('diploma_duration').value;
    var diploma_year = document.getElementById('diploma_year').value;
    var reference_no = document.getElementById('reference_no').value;
    var currentDate = new Date();
    var currentYear = currentDate.getFullYear();

    //alert(eff_date);

    if (reference_no == '') {
      event.preventDefault(); // Prevent default behavior
      Swal.fire({
        icon: 'error',
        title: 'Error in Data Adding',
        text: 'Please login for application',
        confirmButtonColor: '#ffc107',
      });

    } else {

      if (diploma_type_val == '0') {
        event.preventDefault(); // Prevent default behavior
        Swal.fire({
          icon: 'error',
          title: 'Error in Data Adding',
          text: 'Please select type of course',
          confirmButtonColor: '#ffc107',
        });

      } else {

        if (diploma_titel == "") {
          event.preventDefault(); // Prevent default behavior
          Swal.fire({
            icon: 'error',
            title: 'Error in Data Adding',
            text: 'Please enter name of the course',
            confirmButtonColor: '#ffc107',
          });

        } else {

          if (!validateName(diploma_titel)) {
            event.preventDefault(); // Prevent default behavior
            Swal.fire({
              icon: 'error',
              title: 'Error in Data Adding',
              text: 'Please enter valid name of the course [Allowed letters, spaces, dots and hyphens only]',
              confirmButtonColor: '#ffc107',
            });
          } else {

            if (diploma_university == "") {
              event.preventDefault(); // Prevent default behavior
              Swal.fire({
                icon: 'error',
                title: 'Error in Data Adding',
                text: 'Please enter university/institution',
                confirmButtonColor: '#ffc107',
              });

            } else {

              if (!validateName(diploma_university)) {
                event.preventDefault(); // Prevent default behavior
                Swal.fire({
                  icon: 'error',
                  title: 'Error in Data Adding',
                  text: 'Please enter valid university/institution [Allowed letters, spaces, dots and hyphens only]',
                  confirmButtonColor: '#ffc107',
                });
              } else {

                if (diploma_duration == "") {
                  event.preventDefault(); // Prevent default behavior
                  Swal.fire({
                    icon: 'error',
                    title: 'Error in Data Adding',
                    text: 'Please enter course duration',
                    confirmButtonColor: '#ffc107',
                  });

                } else {

                  if (diploma_year == "") {
                    event.preventDefault(); // Prevent default behavior
                    Swal.fire({
                      icon: 'error',
                      title: 'Error in Data Adding',
                      text: 'Please enter course completed year',
                      confirmButtonColor: '#ffc107',
                    });

                  } else {

                    if (!isNumeric(diploma_year)) {
                      event.preventDefault(); // Prevent default behavior
                      Swal.fire({
                        icon: 'error',
                        title: 'Course Data Add',
                        text: 'Please enter valid course completed year',
                        confirmButtonColor: '#ffc107',
                      });

                    } else {

                      if (diploma_year > currentYear) {
                        event.preventDefault(); // Prevent default behavior
                        Swal.fire({
                          icon: 'error',
                          title: 'Course Data Add',
                          text: 'Course completed year should be less than current year',
                          confirmButtonColor: '#ffc107',
                        });

                      } else {

                        // Create a new row

                        var Row1 = '<td><textarea class="form-control readOnlyInput" rows="3" name="diploma_type[]" readonly>' + diploma_type + '</textarea><input type="hidden" name="diploma_type_val[]" class="form-control" value="' + diploma_type_val + '" readonly></td>';
                        var Row2 = '<td><textarea class="form-control readOnlyInput" name="diploma_titel[]" rows="3" readonly >' + diploma_titel + '</textarea></td>';
                        var Row3 = '<td><textarea class="form-control readOnlyInput" name="diploma_university[]" rows="3" readonly >' + diploma_university + '</textarea></td>';
                        var Row4 = '<td><textarea class="form-control readOnlyInput" name="diploma_duration[]" rows="2" readonly >' + diploma_duration + '</textarea></td>';
                        var Row5 = '<td><textarea class="form-control readOnlyInput" name="diploma_year[]" rows="2" readonly >' + diploma_year + '</textarea></td>';
                        var Row6 = '<td><input type="button" value="Remove" class="btn btn-sm btn-danger remove-button-1"></td>';

                        diplomasdataTable.row.add([
                          Row1, Row2, Row3, Row4, Row5, Row6
                        ]).draw();



                        $('#diploma_type').val('0').trigger('change');
                        $('#diploma_titel').val('');
                        $('#diploma_university').val('');
                        $('#diploma_duration').val('');
                        $('#diploma_year').val('');

                      }

                    }

                  }

                }

              }

            }
          }
        }
      }
    }

  }

  function append_row2() {

    var table2 = document.getElementById('special_qulifications');
    var lastRow = table2.rows.length - 1;

    var qulification_type = $("#qulification_type option:selected").text();
    var qulification_type_val = $("#qulification_type option:selected").val();
    var qulification_summary = document.getElementById('qulification_summary').value;
    var reference_no = document.getElementById('reference_no').value;


    //alert(qulification_summary);

    if (reference_no == '') {
      event.preventDefault(); // Prevent default behavior
      Swal.fire({
        icon: 'error',
        title: 'Error in Data Adding',
        text: 'Please login for application',
        confirmButtonColor: '#ffc107',
      });

    } else {

      if (qulification_type_val == '0') {
        event.preventDefault(); // Prevent default behavior
        Swal.fire({
          icon: 'error',
          title: 'Error in Data Adding',
          text: 'Please select type of the special achievement',
          confirmButtonColor: '#ffc107',
        });

      } else {

        if (qulification_summary == "") {
          event.preventDefault(); // Prevent default behavior
          Swal.fire({
            icon: 'error',
            title: 'Error in Data Adding',
            text: 'Please enter details of the special achievement',
            confirmButtonColor: '#ffc107',
          });

        } else {

          var Row1 = '<td><textarea class="form-control readOnlyInput" rows="2" name="qulification_type[]" readonly>' + qulification_type + '</textarea><input type="hidden" name="qulification_type_val[]" class="form-control" value="' + qulification_type_val + '" readonly></td>';
          var Row2 = '<td><textarea class="form-control readOnlyInput" name="qulification_summary[]" rows="2" readonly >' + qulification_summary + '</textarea></td>';
          var Row3 = '<td><input type="button" value="Remove" class="btn btn-sm btn-danger remove-button-2"></td>';

          specialqulificationdataTable.row.add([
            Row1, Row2, Row3
          ]).draw();



          $('#qulification_type').val('0').trigger('change');
          $('#qulification_summary').val('');

        }

      }

    }

  }

  function append_row3() {

    var table3 = document.getElementById('memberships');
    var lastRow = table3.rows.length - 1;

    var membership_type = $("#membership_type option:selected").text();
    var membership_type_val = $("#membership_type option:selected").val();
    var membership_university = document.getElementById('membership_university').value;
    var membership_start_year = document.getElementById('membership_start_year').value;
    var membership_active_status = $("#membership_active_status option:selected").text();
    var membership_active_status_val = $("#membership_active_status option:selected").val();
    var membership_end_year = document.getElementById('membership_end_year').value;
    var reference_no = document.getElementById('reference_no').value;
    var currentDate = new Date();
    var currentYear = currentDate.getFullYear();


    //alert(membership_active_status);

    if (reference_no == '') {
      event.preventDefault(); // Prevent default behavior
      Swal.fire({
        icon: 'error',
        title: 'Error in Data Adding',
        text: 'Please login for application',
        confirmButtonColor: '#ffc107',
      });

    } else {

      if (membership_type_val == '0') {
        event.preventDefault(); // Prevent default behavior
        Swal.fire({
          icon: 'error',
          title: 'Error in Data Adding',
          text: 'Please select type of the membership',
          confirmButtonColor: '#ffc107',
        });

      } else {

        if (membership_university == "") {
          event.preventDefault(); // Prevent default behavior
          Swal.fire({
            icon: 'error',
            title: 'Error in Data Adding',
            text: 'Please enter university/institution',
            confirmButtonColor: '#ffc107',
          });

        } else {

          if (!validateName(membership_university)) {
            event.preventDefault(); // Prevent default behavior
            Swal.fire({
              icon: 'error',
              title: 'Error in Data Adding',
              text: 'Please enter valid membership university [Allowed letters, spaces, dots and hyphens only]',
              confirmButtonColor: '#ffc107',
            });
          } else {

            if (membership_start_year == "") {
              event.preventDefault(); // Prevent default behavior
              Swal.fire({
                icon: 'error',
                title: 'Error in Data Adding',
                text: 'Please enter membership start year',
                confirmButtonColor: '#ffc107',
              });

            } else {

              if (!isNumeric(membership_start_year)) {
                event.preventDefault(); // Prevent default behavior
                Swal.fire({
                  icon: 'error',
                  title: 'Error in Data Adding',
                  text: 'Please enter valid membership start year',
                  confirmButtonColor: '#ffc107',
                });

              } else {

                if (membership_active_status_val == '0') {
                  event.preventDefault(); // Prevent default behavior
                  Swal.fire({
                    icon: 'error',
                    title: 'Error in Data Adding',
                    text: 'Please select membership current status',
                    confirmButtonColor: '#ffc107',
                  });

                } else {

                  if (membership_start_year > currentYear) {
                    event.preventDefault(); // Prevent default behavior
                    Swal.fire({
                      icon: 'error',
                      title: 'Error in Data Adding',
                      text: 'Memberships start year should less than current year',
                      confirmButtonColor: '#ffc107',
                    });

                  } else if (membership_active_status_val == 48 && membership_end_year == "") {

                    event.preventDefault(); // Prevent default behavior
                    Swal.fire({
                      icon: 'error',
                      title: 'Error in Data Adding',
                      text: 'Please enter membership end year',
                      confirmButtonColor: '#ffc107',
                    });

                  } else if (membership_active_status_val == 48 && membership_end_year != "" && !isNumeric(membership_end_year)) {

                    event.preventDefault(); // Prevent default behavior
                    Swal.fire({
                      icon: 'error',
                      title: 'Error in Data Adding',
                      text: 'Please enter valid membership start year',
                      confirmButtonColor: '#ffc107',
                    });

                  } else if (membership_active_status_val == 48 && membership_end_year != "" && (membership_end_year > currentYear)) {

                    event.preventDefault(); // Prevent default behavior
                    Swal.fire({
                      icon: 'error',
                      title: 'Error in Data Adding',
                      text: 'Memberships end year should be less than current year',
                      confirmButtonColor: '#ffc107',
                    });
                  } else if (membership_active_status_val == 48 && membership_end_year != "" && (membership_start_year > membership_end_year)) {

                    event.preventDefault(); // Prevent default behavior
                    Swal.fire({
                      icon: 'error',
                      title: 'Error in Data Adding',
                      text: 'Memberships start year should be less than end year',
                      confirmButtonColor: '#ffc107',
                    });

                  } else {

                    var Row1 = '<td><textarea class="form-control readOnlyInput" rows="2" name="membership_type[]" readonly>' + membership_type + '</textarea><input type="hidden" name="membership_type_val[]" class="form-control" value="' + membership_type_val + '" readonly></td>';
                    var Row2 = '<td><textarea class="form-control readOnlyInput" name="membership_university[]" rows="2" readonly >' + membership_university + '</textarea></td>';
                    var Row3 = '<td><textarea class="form-control readOnlyInput" name="membership_start_year[]" rows="2" readonly >' + membership_start_year + '</textarea></td>';
                    var Row4 = '<td><textarea class="form-control readOnlyInput" name="membership_end_year[]" rows="2" readonly >' + membership_end_year + '</textarea><input type="hidden" name="membership_active_status_val[]" class="form-control" value="' + membership_active_status_val + '" readonly></td>';
                    var Row5 = '<td><input type="button" value="Remove" class="btn btn-sm btn-danger remove-button-3"></td>';

                    membershipdataTable.row.add([
                      Row1, Row2, Row3, Row4, Row5
                    ]).draw();



                    $('#membership_type').val('0').trigger('change');
                    $('#membership_university').val('');
                    $('#membership_start_year').val('');
                    $('#membership_end_year').val('');
                    $('#membership_active_status').val('0').trigger('change');
                  }

                  //}

                }

              }

            }

          }

        }

      }

    }

  }

  function append_row4() {

    var table4 = document.getElementById('memberships');
    var lastRow = table4.rows.length - 1;

    var pqualification_name = document.getElementById('pqualification_name').value;
    var pqualification_university = document.getElementById('pqualification_university').value;
    var pqualification_start_year = document.getElementById('pqualification_start_year').value;
    var pqualification_end_year = document.getElementById('pqualification_end_year').value;
    var reference_no = document.getElementById('reference_no').value;
    var currentDate = new Date();
    var currentYear = currentDate.getFullYear();


    //alert(qulification_summary);

    if (reference_no == '') {
      event.preventDefault(); // Prevent default behavior
      Swal.fire({
        icon: 'error',
        title: 'Error in Data Adding',
        text: 'Please login for application',
        confirmButtonColor: '#ffc107',
      });

    } else {

      if (pqualification_name == "") {
        event.preventDefault(); // Prevent default behavior
        Swal.fire({
          icon: 'error',
          title: 'Error in Data Adding',
          text: 'Please enter professional qualification',
          confirmButtonColor: '#ffc107',
        });

      } else {

        if (!validateName(pqualification_name)) {
          event.preventDefault(); // Prevent default behavior
          Swal.fire({
            icon: 'error',
            title: 'Error in Data Adding',
            text: 'Please enter valid professional qualification [Allowed letters, spaces, dots and hyphens only]',
            confirmButtonColor: '#ffc107',
          });
        } else {

          if (pqualification_university == "") {
            event.preventDefault(); // Prevent default behavior
            Swal.fire({
              icon: 'error',
              title: 'Error in Data Adding',
              text: 'Please enter university/institution',
              confirmButtonColor: '#ffc107',
            });

          } else {

            if (!validateName(pqualification_university)) {
              event.preventDefault(); // Prevent default behavior
              Swal.fire({
                icon: 'error',
                title: 'Error in Data Adding',
                text: 'Please enter valid university/institution [Allowed letters, spaces, dots and hyphens only]',
                confirmButtonColor: '#ffc107',
              });
            } else {

              if (pqualification_start_year == "") {
                event.preventDefault(); // Prevent default behavior
                Swal.fire({
                  icon: 'error',
                  title: 'Error in Data Adding',
                  text: 'Please enter professional qualification start year',
                  confirmButtonColor: '#ffc107',
                });

              } else {

                if (!isNumeric(pqualification_start_year)) {
                  event.preventDefault(); // Prevent default behavior
                  Swal.fire({
                    icon: 'error',
                    title: 'Error in Data Adding',
                    text: 'Please enter valid professional qualification start year',
                    confirmButtonColor: '#ffc107',
                  });

                } else {

                  if (pqualification_end_year == "") {
                    event.preventDefault(); // Prevent default behavior
                    Swal.fire({
                      icon: 'error',
                      title: 'Error in Data Adding',
                      text: 'Please enter professional qualification end year',
                      confirmButtonColor: '#ffc107',
                    });

                  } else {

                    if (!isNumeric(pqualification_end_year)) {
                      event.preventDefault(); // Prevent default behavior
                      Swal.fire({
                        icon: 'error',
                        title: 'Error in Data Adding',
                        text: 'Please enter valid professional qualification end year',
                        confirmButtonColor: '#ffc107',
                      });

                    } else {

                      if (pqualification_start_year > currentYear) {
                        event.preventDefault(); // Prevent default behavior
                        Swal.fire({
                          icon: 'error',
                          title: 'Error in Data Adding',
                          text: 'Professional qualification start year should be less than current year',
                          confirmButtonColor: '#ffc107',
                        });

                      } else if (pqualification_end_year > currentYear) {
                        event.preventDefault(); // Prevent default behavior
                        Swal.fire({
                          icon: 'error',
                          title: 'Error in Data Adding',
                          text: 'Professional qualification end year should be less than current year',
                          confirmButtonColor: '#ffc107',
                        });

                      } else if (pqualification_end_year < pqualification_start_year) {

                        event.preventDefault(); // Prevent default behavior
                        Swal.fire({
                          icon: 'error',
                          title: 'Error in Data Adding',
                          text: 'Professional qulification start year should be less than end year',
                          confirmButtonColor: '#ffc107',
                        });

                      } else {

                        var Row1 = '<td><textarea class="form-control readOnlyInput" rows="2" name="pqualification_name[]" readonly>' + pqualification_name + '</textarea></td>';
                        var Row2 = '<td><textarea class="form-control readOnlyInput" name="pqualification_university[]" rows="2" readonly >' + pqualification_university + '</textarea></td>';
                        var Row3 = '<td><textarea class="form-control readOnlyInput" name="pqualification_start_year[]" rows="2" readonly >' + pqualification_start_year + '</textarea></td>';
                        var Row4 = '<td><textarea class="form-control readOnlyInput" name="pqualification_end_year[]" rows="2" readonly >' + pqualification_end_year + '</textarea></td>';
                        var Row5 = '<td><input type="button" value="Remove" class="btn btn-sm btn-danger remove-button-4"></td>';

                        pqualificationdataTable.row.add([
                          Row1, Row2, Row3, Row4, Row5
                        ]).draw();



                        $('#pqualification_name').val('');
                        $('#pqualification_university').val('');
                        $('#pqualification_start_year').val('');
                        $('#pqualification_end_year').val('');

                      }

                    }

                  }
                }
              }

            }

          }
        }
      }

    }

  }


  function append_row5() {

    var table5 = document.getElementById('experiences');
    var lastRow = table5.rows.length - 1;

    var experience_designation = document.getElementById('experience_designation').value;
    var experience_institution = document.getElementById('experience_institution').value;
    var experience_start_date = document.getElementById('experience_start_date').value;
    var experience_end_date = document.getElementById('experience_end_date').value;
    var experience_last_month_salary = document.getElementById('experience_last_month_salary').value;
    var reference_no = document.getElementById('reference_no').value;
    var currently_working = document.getElementById('currently_working').checked;
    //var experience_last_month_salary = 0;

    alert(currently_working);

    if (reference_no == '') {
      event.preventDefault(); // Prevent default behavior
      Swal.fire({
        icon: 'error',
        title: 'Error in Data Adding',
        text: 'Please login for application',
        confirmButtonColor: '#ffc107',
      });

    } else {

      if (experience_designation == "") {
        event.preventDefault(); // Prevent default behavior
        Swal.fire({
          icon: 'error',
          title: 'Error in Data Adding',
          text: 'Please enter designation',
          confirmButtonColor: '#ffc107',
        });

      } else {

        if (!validateName(experience_designation)) {
          event.preventDefault(); // Prevent default behavior
          Swal.fire({
            icon: 'error',
            title: 'Error in Data Adding',
            text: 'Please enter valid designation [Allowed letters, spaces, dots and hyphens only]',
            confirmButtonColor: '#ffc107',
          });
        } else {

          if (experience_institution == "") {
            event.preventDefault(); // Prevent default behavior
            Swal.fire({
              icon: 'error',
              title: 'Error in Data Adding',
              text: 'Please enter institution',
              confirmButtonColor: '#ffc107',
            });

          } else {

            if (!validateName(experience_institution)) {
              event.preventDefault(); // Prevent default behavior
              Swal.fire({
                icon: 'error',
                title: 'Error in Data Adding',
                text: 'Please enter valid institution [Allowed letters, spaces, dots and hyphens only]',
                confirmButtonColor: '#ffc107',
              });
            } else {

              if (experience_start_date == "") {
                event.preventDefault(); // Prevent default behavior
                Swal.fire({
                  icon: 'error',
                  title: 'Error in Data Adding',
                  text: 'Please select employment start date',
                  confirmButtonColor: '#ffc107',
                });

              } else {

                if (!isDateTodayOrBefore(experience_start_date)) {
                  event.preventDefault(); // Prevent default behavior
                  Swal.fire({
                    icon: 'error',
                    title: 'Error in Data Adding',
                    text: 'Employment start date must not be a future date',
                    confirmButtonColor: '#ffc107',
                  });

                } else {

                  // if (experience_end_date == "") {
                  //     event.preventDefault(); // Prevent default behavior
                  //      Swal.fire({
                  //          icon: 'error',
                  //          title: 'Error in Data Adding',
                  //          text: 'Please enter job end date'
                  //        });

                  // } else {

                  if (experience_end_date != "" && !isDateTodayOrBefore(experience_end_date)) {
                    event.preventDefault(); // Prevent default behavior
                    Swal.fire({
                      icon: 'error',
                      title: 'Error in Data Adding',
                      text: 'Employment end date must not be a future date',
                      confirmButtonColor: '#ffc107',
                    });

                  } else {

                    if (experience_end_date != "" && !isStartDateBeforeEndDate(experience_start_date, experience_end_date)) {
                      event.preventDefault(); // Prevent default behavior
                      Swal.fire({
                        icon: 'error',
                        title: 'Error in Data Adding',
                        text: 'Employment start date should be less than employment end date',
                        confirmButtonColor: '#ffc107',
                      });

                    } else {

                      var Row1 = '<td><textarea class="form-control readOnlyInput" rows="2" name="experience_designation[]" readonly>' + experience_designation + '</textarea></td>';
                      var Row2 = '<td><textarea class="form-control readOnlyInput" name="experience_institution[]" rows="2" readonly >' + experience_institution + '</textarea></td>';
                      var Row3 = '<td><b>From -</b><textarea class="form-control readOnlyInput" name="experience_start_date[]" rows="2" readonly >' + experience_start_date + '</textarea><b>To -</b><textarea class="form-control readOnlyInput" name="experience_end_date[]" rows="2" readonly >' + experience_end_date + '</textarea></td>';
                      // var Row4 = '<td></td>';
                      //var experience_last_month_salary = 0;
                      var Row4 = '<td><textarea class="form-control readOnlyInput" name="experience_last_month_salary[]" rows="2" readonly >' + (Number(experience_last_month_salary) != 0 ? Number(experience_last_month_salary).toFixed(2) : "") + '</textarea></td>';
                      var Row5 = '<td><input type="button" value="Remove" class="btn btn-sm btn-danger remove-button-5"></td>';

                      employmentrecorddataTable.row.add([
                        Row1, Row2, Row3, Row4, Row5
                      ]).draw();



                      $('#experience_designation').val('');
                      $('#experience_institution').val('');
                      $('#experience_start_date').val('');
                      $('#experience_end_date').val('');
                      $('#experience_last_month_salary').val('');


                    }

                  }

                }

              }

            }

          }

        }

      }

    }

  }

  function append_row6() {

    var table6 = document.getElementById('bonds');
    var lastRow = table6.rows.length - 1;

    var bond_institution = document.getElementById('bond_institution').value;
    var bond_start_date = document.getElementById('bond_start_date').value;
    var bond_end_date = document.getElementById('bond_end_date').value;
    var bond_total_value = document.getElementById('bond_total_value').value;
    var reference_no = document.getElementById('reference_no').value;


    //alert(experience_designation);

    if (reference_no == '') {
      event.preventDefault(); // Prevent default behavior
      Swal.fire({
        icon: 'error',
        title: 'Error in Data Adding',
        text: 'Please login for application',
        confirmButtonColor: '#ffc107',
      });

    } else {

      if (bond_institution == "") {
        event.preventDefault(); // Prevent default behavior
        Swal.fire({
          icon: 'error',
          title: 'Error in Data Adding',
          text: 'Please enter bond institution',
          confirmButtonColor: '#ffc107',
        });

      } else {

        if (!validateName(bond_institution)) {
          event.preventDefault(); // Prevent default behavior
          Swal.fire({
            icon: 'error',
            title: 'Error in Data Adding',
            text: 'Please enter valid bond institution [Allowed letters, spaces, dots and hyphens only]',
            confirmButtonColor: '#ffc107',
          });
        } else {

          if (bond_total_value == "") {
            event.preventDefault(); // Prevent default behavior
            Swal.fire({
              icon: 'error',
              title: 'Error in Data Adding',
              text: 'Please enter total bond value',
              confirmButtonColor: '#ffc107',
            });


          } else {

            if (bond_start_date == "") {
              event.preventDefault(); // Prevent default behavior
              Swal.fire({
                icon: 'error',
                title: 'Error in Data Adding',
                text: 'Please select obligatory service start date',
                confirmButtonColor: '#ffc107',
              });

            } else {


              if (bond_end_date == "") {

                event.preventDefault(); // Prevent default behavior
                Swal.fire({
                  icon: 'error',
                  title: 'Error in Data Adding',
                  text: 'Please select obligatory service end date',
                  confirmButtonColor: '#ffc107',
                });


              } else {

                if (!isStartDateBeforeEndDate(bond_start_date, bond_end_date)) {
                  event.preventDefault(); // Prevent default behavior
                  Swal.fire({
                    icon: 'error',
                    title: 'Error in Data Adding',
                    text: 'Obligatory service start date should be less than obligatory service end date',
                    confirmButtonColor: '#ffc107',
                  });

                } else {

                  // var Row1 = '<td><textarea class="form-control readOnlyInput" rows="2" name="experience_designation[]" readonly>'+ experience_designation +'</textarea></td>';
                  var Row1 = '<td><textarea class="form-control readOnlyInput" name="bond_institution[]" rows="2" readonly >' + bond_institution + '</textarea></td>';
                  var Row2 = '<td><b>From -</b><textarea class="form-control readOnlyInput" name="bond_start_date[]" rows="2" readonly >' + bond_start_date + '</textarea><b>To -</b><textarea class="form-control readOnlyInput" name="bond_end_date[]" rows="2" readonly >' + bond_end_date + '</textarea></td>';
                  // var Row3 = '<td></td>';
                  var Row3 = '<td><textarea class="form-control readOnlyInput" name="bond_total_value[]" rows="2" readonly >' + Number(bond_total_value).toFixed(2) + '</textarea></td>';
                  var Row4 = '<td><input type="button" value="Remove" class="btn btn-sm btn-danger remove-button-6"></td>';

                  bondrecorddataTable.row.add([
                    Row1, Row2, Row3, Row4
                  ]).draw();


                  $('#bond_institution').val('');
                  $('#bond_start_date').val('');
                  $('#bond_end_date').val('');
                  $('#bond_total_value').val('');

                }

              }

            }

          }

        }

      }

    }

  }

  function append_row7() {

    var table7 = document.getElementById('referees');
    var lastRow = table7.rows.length - 1;

    var referee_name = document.getElementById('referee_name').value;
    var referee_designation = document.getElementById('referee_designation').value;
    var referee_address = document.getElementById('referee_address').value;
    var referee_phome = document.getElementById('referee_phome').value;
    var referee_email = document.getElementById('referee_email').value;
    var reference_no = document.getElementById('reference_no').value;

    //alert(eff_date);

    if (reference_no == '') {
      event.preventDefault(); // Prevent default behavior
      Swal.fire({
        icon: 'error',
        title: 'Error in Data Adding',
        text: 'Please login for application',
        confirmButtonColor: '#ffc107',
      });

    } else {

      if (referee_name == '') {
        event.preventDefault(); // Prevent default behavior
        Swal.fire({
          icon: 'error',
          title: 'Error in Data Adding',
          text: 'Please enter referee name',
          confirmButtonColor: '#ffc107',
        });

      } else {

        if (referee_designation == "") {
          event.preventDefault(); // Prevent default behavior
          Swal.fire({
            icon: 'error',
            title: 'Error in Data Adding',
            text: 'Please enter referee designation',
            confirmButtonColor: '#ffc107',
          });

        } else {

          if (!validateName(referee_designation)) {
            event.preventDefault(); // Prevent default behavior
            Swal.fire({
              icon: 'error',
              title: 'Error in Data Adding',
              text: 'Please enter a valid designation [Allowed letters, spaces, dots and hyphens only]',
              confirmButtonColor: '#ffc107',
            });
          } else {

            if (referee_phome == "") {
              event.preventDefault(); // Prevent default behavior
              Swal.fire({
                icon: 'error',
                title: 'Error in Data Adding',
                text: 'Please enter referee phone number',
                confirmButtonColor: '#ffc107',
              });

            } else {

              //     if (!isValidPhoneNumber(referee_phome)) {
              //     event.preventDefault(); // Prevent default behavior
              //     Swal.fire({
              //      icon: 'error',
              //      title: 'Error in Data Adding',
              //      text: 'Please enter a valid referee phone number',
              //      confirmButtonColor: '#ffc107',
              //    });
              // } else {

              if (referee_email == "") {
                event.preventDefault(); // Prevent default behavior
                Swal.fire({
                  icon: 'error',
                  title: 'Error in Data Adding',
                  text: 'Please enter referee email',
                  confirmButtonColor: '#ffc107',
                });

              } else {

                if (!isValidEmail(referee_email)) {
                  event.preventDefault(); // Prevent default behavior
                  Swal.fire({
                    icon: 'error',
                    title: 'Error in Data Adding',
                    text: 'Please enter a valid referee email',
                    confirmButtonColor: '#ffc107',
                  });
                } else {

                  if (referee_address == "") {
                    event.preventDefault(); // Prevent default behavior
                    Swal.fire({
                      icon: 'error',
                      title: 'Error in Data Adding',
                      text: 'Please enter referee address',
                      confirmButtonColor: '#ffc107',
                    });

                  } else {

                    // Create a new row

                    var Row1 = '<td><textarea class="form-control readOnlyInput" name="referee_name[]" rows="2" readonly >' + referee_name + '</textarea></td>';
                    var Row2 = '<td><textarea class="form-control readOnlyInput" name="referee_designation[]" rows="2" readonly >' + referee_designation + '</textarea></td>';
                    // var Row3 = '<td></td>';
                    var Row3 = '<td><b>Phone No-</b><textarea class="form-control readOnlyInput" name="referee_phome[]" rows="2" readonly >' + referee_phome + '</textarea><b>Email-</b><textarea class="form-control readOnlyInput" name="referee_email[]" rows="2" readonly >' + referee_email + '</textarea><b>Address-</b><textarea class="form-control readOnlyInput" name="referee_address[]" rows="4" readonly >' + referee_address + '</textarea></td>';
                    // var Row5 = '<td></td>';
                    var Row4 = '<td><input type="button" value="Remove" class="btn btn-sm btn-danger remove-button-7"></td>';

                    refereerecorddataTable.row.add([
                      Row1, Row2, Row3, Row4
                    ]).draw();



                    $('#referee_name').val('');
                    $('#referee_designation').val('');
                    $('#referee_address').val('');
                    $('#referee_phome').val('');
                    $('#referee_email').val('');

                  }
                }

              }

              //}

            }

          }
        }
      }
    }

  }

  function deleteRow(btn) {
    var row = btn.parentNode.parentNode;
    row.parentNode.removeChild(row);
  }

  function initializeDataTable() {

    $('#degrees').on('click', '.remove-button', function() {
      var row = $(this).closest('tr');
      degreesdataTable.row(row).remove().draw();
    });
  }

  function initializeDataTable1() {

    $('#diplomas').on('click', '.remove-button-1', function() {
      var row = $(this).closest('tr');
      diplomasdataTable.row(row).remove().draw();
    });
  }

  function initializeDataTable2() {

    $('#special_qulifications').on('click', '.remove-button-2', function() {
      var row = $(this).closest('tr');
      specialqulificationdataTable.row(row).remove().draw();
    });
  }

  function initializeDataTable3() {

    $('#memberships').on('click', '.remove-button-3', function() {
      var row = $(this).closest('tr');
      membershipdataTable.row(row).remove().draw();
    });
  }

  function initializeDataTable4() {

    $('#pqualifications').on('click', '.remove-button-4', function() {
      var row = $(this).closest('tr');
      pqualificationdataTable.row(row).remove().draw();
    });
  }

  function initializeDataTable5() {

    $('#experiences').on('click', '.remove-button-5', function() {
      var row = $(this).closest('tr');
      employmentrecorddataTable.row(row).remove().draw();
    });
  }

  function initializeDataTable6() {

    $('#bonds').on('click', '.remove-button-6', function() {
      var row = $(this).closest('tr');
      bondrecorddataTable.row(row).remove().draw();
    });
  }

  function initializeDataTable7() {

    $('#referees').on('click', '.remove-button-7', function() {
      var row = $(this).closest('tr');
      refereerecorddataTable.row(row).remove().draw();
    });
  }

  $(document).ready(function() {

    initializeDataTable();
    initializeDataTable1();
    initializeDataTable2();
    initializeDataTable3();
    initializeDataTable4();
    initializeDataTable5();
    initializeDataTable6();
    initializeDataTable7();
  });

  let fileList = [];


  function updateFileList() {
    var fileListContainer = document.getElementById('file-list');
    fileListContainer.innerHTML = '';

    for (var i = 0; i < fileList.length; i++) {
      var file = fileList[i];
      var listItem = document.createElement('li');
      listItem.innerHTML = file.name;
      fileListContainer.appendChild(listItem);
    }
  }

  document.getElementById('degree_certificate').addEventListener('change', function() {
    fileList = Array.from(document.getElementById('degree_certificate').files);
    updateFileList();
  });

  /******************************************************************************/

  let fileList1 = [];


  function updateFileList1() {
    var fileListContainer = document.getElementById('file-list1');
    fileListContainer.innerHTML = '';

    for (var i = 0; i < fileList1.length; i++) {
      var file = fileList1[i];
      var listItem1 = document.createElement('li');
      listItem1.innerHTML = file.name;
      fileListContainer.appendChild(listItem1);
    }
  }

  document.getElementById('diploma_certificate').addEventListener('change', function() {
    fileList1 = Array.from(document.getElementById('diploma_certificate').files);
    updateFileList1();
  });


  let fileList2 = [];


  function updateFileList2() {
    var fileListContainer = document.getElementById('file-list2');
    fileListContainer.innerHTML = '';

    for (var i = 0; i < fileList2.length; i++) {
      var file = fileList2[i];
      var listItem2 = document.createElement('li');
      listItem2.innerHTML = file.name;
      fileListContainer.appendChild(listItem2);
    }
  }

  document.getElementById('employment_record_certificate').addEventListener('change', function() {
    fileList2 = Array.from(document.getElementById('employment_record_certificate').files);
    updateFileList2();
  });

  /***********************************************************************/

  const tagInputField = document.getElementById('tag-input-field');
  const tagContainer = document.getElementById('tag-container');

  tagInputField.addEventListener('keydown', function(event) {
    if (event.key === ',') {
      event.preventDefault();
      const tagText = tagInputField.value.trim();

      if (tagText) {
        addTag(tagText);
        tagInputField.value = '';
      }
    }
  });

  function addTag(tagText) {
    const tag = document.createElement('div');
    tag.classList.add('tag');
    tag.innerHTML = '<span>' + tagText + '</span><input type="hidden" name="degree_main_subject[]" class="form-control" value="' + tagText + '" readonly><span class="remove-tag btn btn-sm btn-danger text-white" onclick="removeTag(event);">x</span>';
    tagContainer.appendChild(tag);
  }

  function removeTag(event) {
    const tag = event.target.parentNode;
    tag.parentNode.removeChild(tag);
  }

  /******************************************************************************/

  function bondability_ckeck() {


    if (document.getElementById("bondabilityYes").checked) {
      document.getElementById("skillComm").style.display = "block";
      document.getElementById("skillComm3").style.display = "block";

    } else if (document.getElementById("bondabilityNo").checked) {
      document.getElementById("skillComm").style.display = "none";
      document.getElementById("skillComm3").style.display = "none";

    }
  }

  function public_sector_ckeck() {


    if (document.getElementById("publicSectorYes").checked) {
      document.getElementById("skillComm2").style.display = "block";

    } else if (document.getElementById("publicSectorNo").checked) {
      document.getElementById("skillComm2").style.display = "none";

    }
  }

  function validateName(name) {
    // Regular expression pattern to match letters, spaces, hyphens, dots, slashes, parentheses, double quotes, and single quotes
    var pattern = /^[a-zA-Z\s-.\/()"'']+$/;

    // Test if the name matches the pattern
    return pattern.test(name);
  }



  function isDateTodayOrBefore(date) {

    var today = new Date();
    today.setHours(0, 0, 0, 0); // Set today's time to midnight for accurate comparison

    // Parse the input date string into a Date object
    var parts = date.split("-");
    var inputDate = new Date(parts[2], getMonthIndex(parts[1]), parts[0]);

    return inputDate < today;
  }

  function isStartDateBeforeEndDate(startDateString, endDateString) {

    var startDateParts = startDateString.split("-");
    var endDateParts = endDateString.split("-");

    var startDate = new Date(startDateParts[2], getMonthIndex(startDateParts[1]), startDateParts[0]);
    var endDate = new Date(endDateParts[2], getMonthIndex(endDateParts[1]), endDateParts[0]);

    return startDate < endDate;
  }

  // Helper function to get the month index based on the month abbreviation
  function getMonthIndex(monthAbbreviation) {
    var months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    return months.indexOf(monthAbbreviation);
  }

  function isNumeric(value) {
    return /^[0-9]+$/.test(value);
  }

  function isValidPhoneNumber(phoneNumber) {

    // Regular expression pattern for phone number starting with "+94" or "0" followed by 9 digits
    var pattern = /^(?:0|94|\+94|0094)?(?:(11|21|23|24|25|26|27|31|32|33|34|35|36|37|38|41|45|47|51|52|54|55|57|63|65|66|67|81|91)([0|2|3|4|5|7|9])|7([0|1|2|4|5|6|7|8])\d)\d{6}$/;

    // Test if the phoneNumber matches the pattern
    return pattern.test(phoneNumber);
  }

  function isValidEmail(email) {
    // Regular expression pattern for email validation
    var pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    // Test if the email matches the pattern
    return pattern.test(email);
  }

  function isValidInitial(initial) {
    // Regular expression pattern for email validation
    var pattern = /^([A-Z]\.)+$/;

    // Test if the email matches the pattern
    return pattern.test(initial);
  }

  function isValidPhone(phone) {
    // Regular expression pattern for email validation
    var pattern = /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/;

    // Test if the email matches the pattern
    return pattern.test(phone);
  }

  function fileTypecheck(files) {

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileName = file.name;
      const fileSize = file.size / 1024; // Size in KB

      if (file.type !== 'application/pdf') {

        return true;
      }

    }
  }

  function fileSizecheck(files) {

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileName = file.name;
      const fileSize = file.size / 1024; // Size in KB


      if (fileSize > 5120) { // 10 MB limit
        return true;
      }

    }

  }

  function isValidOCRID(ocdid) {
    // Regular expression pattern for email validation
    var pattern = /^https:\/\/orcid\.org\/[a-zA-Z\d]{4}-[a-zA-Z\d]{4}-[a-zA-Z\d]{4}-[a-zA-Z\d]{4}$/;

    // Test if the email matches the pattern
    return pattern.test(ocdid);
  }

  function firstDegreeCheck(degrees) {
    var targetValue = 214;

    for (var i = 0; i < degrees.length; i++) {
      if (parseInt(degrees[i].value) == targetValue) {
        return true;
      }
    }
    return false; // You should also return false if the value is not found
  }
</script>
<script>
  //Detect Ctrl+P key combination
  document.addEventListener('keydown', function(event) {
    if (event.ctrlKey && event.keyCode === 80) { // 80 is the keycode for 'P'
      event.preventDefault(); // Prevent default behavior
      Swal.fire({
        icon: 'error',
        title: 'No Printing Allowed',
        text: 'You have no permission to print this screen.after submit application you can get printout',
        confirmButtonColor: '#ffc107',
      });
    }
  });

  document.addEventListener('keydown', function(event) {
    // Check if the Ctrl key (or Command key on Mac) is pressed along with the 'u' key
    if ((event.ctrlKey || event.metaKey) && (event.key === 'u' || event.keyCode === 85)) {
      // Prevent the default behavior (opening "View Page Source")
      event.preventDefault();

    }
  });



  document.addEventListener('contextmenu', function(event) {
    event.preventDefault();
  });
</script>

<script>
  function checkFields() {

    //personal detail validation
    var titel_id = $("#titel_id option:selected").val();
    var initials = document.getElementById('initials').value;
    var name_denoted_by_initials = document.getElementById('name_denoted_by_initials').value;
    var last_name = document.getElementById('last_name').value;
    var phone_no = document.getElementById('phone_no').value;
    var civil_status_id = $("#civil_status_id option:selected").val();
    var permanent_add1 = document.getElementById('permanent_add1').value;
    var permanent_city_id = $("#permanent_city_id option:selected").val();
    var postal_add1 = document.getElementById('postal_add1').value;
    var postal_city_id = $("#postal_city_id option:selected").val();
    var state_of_citizenship_id = $("#state_of_citizenship_id option:selected").val();
    var citizen_registration_no = document.getElementById('citizen_registration_no').value;
    var emp_highest_edu_level = $("#emp_highest_edu_level option:selected").val();
    var agreeTerms = document.getElementById("agreeTerms");

    var photoCount = '<?php echo e($photoCount); ?>';
    var profile_photo1 = document.getElementById("profile_photo");
    var profile_photo = profile_photo1.files[0];
    const allowedMimeTypes = ['image/png', 'image/jpeg'];
    const maxFileSize = 2 * 1024 * 1024; // in bytes

    var main_category = '<?php echo e($vacancy->main_category_id); ?>';
    var min_qualification = '<?php echo e($vacancy->min_qualification); ?>';

    var diplomaCount = '<?php echo e(count($diplomas)); ?>';
    var diplomas = document.querySelector('input[name="diploma_type_val[]"]');


    var diplomaCertificateCount = '<?php echo e(count($diplomaCertificates)); ?>';
    var diploma_certificate = document.getElementById("diploma_certificate");
    var diploma_certificates = diploma_certificate.files;

    var firstDegreeCount = '<?php echo e($firstDegreeCount); ?>';
    var degrees = document.querySelectorAll('input[name="degree_type_val[]"]');


    var degreeSubject = document.getElementById('degree_main_subject1');
    var degreeSubjectCount = '<?php echo e(count($degreeSubject1)); ?>';
    //alert(degreeSubject.value);

    var degreeCertificateCount = '<?php echo e(count($degreeCertificates)); ?>';
    var degree_certificate = document.getElementById("degree_certificate");
    var degree_certificates = degree_certificate.files;


    var bondability = document.querySelector('input[name="bondability"]:checked');
    var bondCount = '<?php echo e(count($employmentBonds)); ?>';

    var refereeListCount = '<?php echo e(count($refereeList)); ?>';
    var referees = document.querySelectorAll('textarea[name="referee_name[]"]');
    var values = [];

    if (referees.length == 0) {

      var firstreferees = '';
      var secondreferees = '';

    } else if (referees.length == 1) {

      values.push(referees[0].value);
      var firstreferees = values[0];
      var secondreferees = '';

    } else if (referees.length == 2) {

      values.push(referees[0].value);
      values.push(referees[1].value);
      var firstreferees = values[0];
      var secondreferees = values[1];
    }

    var publicSector = document.querySelector('input[name="publicSector"]:checked');
    var releaseLetterCount = '<?php echo e(count($releaseLetters)); ?>';
    var public_sector_letter = document.getElementById("public_sector_letter");
    var public_sector_letters = public_sector_letter.files;

    var employment_record_certificate = document.getElementById("employment_record_certificate");
    var employment_record_certificates = employment_record_certificate.files;

    var orcid = document.getElementById('orcid').value;

    // Get input field values
    var degree_type_val = $("#degree_type option:selected").val();
    var degree_titel = document.getElementById('degree_titel').value;
    var degree_university = document.getElementById('degree_university').value;
    var degree_class_val = $("#degree_class option:selected").val();
    var eff_date = document.getElementById('eff_date').value;
    var degree_index_number = document.getElementById('degree_index_number').value;
    var degree_start = document.getElementById('degree_start').value;
    var degree_end = document.getElementById('degree_end').value;
    /*********************************************************/

    var diploma_type = $("#diploma_type option:selected").text();
    var diploma_type_val = $("#diploma_type option:selected").val();
    var diploma_titel = document.getElementById('diploma_titel').value;
    var diploma_university = document.getElementById('diploma_university').value;
    var diploma_duration = document.getElementById('diploma_duration').value;
    var diploma_year = document.getElementById('diploma_year').value;
    /*********************************************/

    var qulification_type_val = $("#qulification_type option:selected").val();
    var qulification_summary = document.getElementById('qulification_summary').value;
    /************************************************************/

    var membership_type_val = $("#membership_type option:selected").val();
    var membership_university = document.getElementById('membership_university').value;
    var membership_start_year = document.getElementById('membership_start_year').value;
    //var membership_end_year = document.getElementById('membership_end_year').value;
    var membership_active_status_val = $("#membership_active_status option:selected").val();
    /**************************************************************/

    var pqualification_name = document.getElementById('pqualification_name').value;
    var pqualification_university = document.getElementById('pqualification_university').value;
    var pqualification_start_year = document.getElementById('pqualification_start_year').value;
    var pqualification_end_year = document.getElementById('pqualification_end_year').value;
    /**********************************************************/

    var experience_designation = document.getElementById('experience_designation').value;
    var experience_institution = document.getElementById('experience_institution').value;
    var experience_start_date = document.getElementById('experience_start_date').value;
    var experience_end_date = document.getElementById('experience_end_date').value;
    var experience_last_month_salary = document.getElementById('experience_last_month_salary').value;
    /**********************************************************/

    var bond_institution = document.getElementById('bond_institution').value;
    var bond_start_date = document.getElementById('bond_start_date').value;
    var bond_end_date = document.getElementById('bond_end_date').value;
    var bond_total_value = document.getElementById('bond_total_value').value;
    /**************************************************************************/

    var referee_name = document.getElementById('referee_name').value;
    var referee_designation = document.getElementById('referee_designation').value;
    var referee_address = document.getElementById('referee_address').value;
    var referee_phome = document.getElementById('referee_phome').value;
    var referee_email = document.getElementById('referee_email').value;

    if (titel_id == '') {

      event.preventDefault(); // Prevent form submission)

      var titel_id = document.getElementById('titel_id');
      titel_id.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // // Show the new error message next to the missing field
      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'The title must be selected';
      // messageElement.id = 'error_message';
      // titel_id.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'The title must be selected',
        confirmButtonColor: '#ffc107',
      });

    } else if (initials == "") {

      event.preventDefault(); // Prevent form submission)
      var initials = document.getElementById('initials');
      initials.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'the name initials required';
      // messageElement.id = 'error_message';
      // initials.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Initials required',
        confirmButtonColor: '#ffc107',
      });

    } else if (!isValidInitial(initials)) {

      event.preventDefault(); // Prevent form submission)
      var initials = document.getElementById('initials');
      initials.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'initials only can include capital letters and dots.Ex:- A.B.C.';
      // messageElement.id = 'error_message';
      // initials.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Can include capital letters and dots only for initials. Ex:- A.B.C.',
        confirmButtonColor: '#ffc107',
      });

    } else if (name_denoted_by_initials == "") {

      event.preventDefault(); // Prevent form submission)
      var name_denoted_by_initials = document.getElementById('name_denoted_by_initials');
      name_denoted_by_initials.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'the initials denoted name required';
      // messageElement.id = 'error_message';
      // name_denoted_by_initials.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Names denoted by initials are required',
        confirmButtonColor: '#ffc107',
      });

    } else if (!validateName(name_denoted_by_initials)) {

      event.preventDefault(); // Prevent form submission)
      var name_denoted_by_initials = document.getElementById('name_denoted_by_initials');
      name_denoted_by_initials.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'the initials denoted name have invalid character (Only allowed letters, spaces)';
      // messageElement.id = 'error_message';
      // name_denoted_by_initials.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Names denoted by initials have invalid character (Only allowed letters, spaces)',
        confirmButtonColor: '#ffc107',
      });

    } else if (last_name == "") {

      event.preventDefault(); // Prevent form submission)

      var last_name = document.getElementById('last_name');
      last_name.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'the last name required';
      // messageElement.id = 'error_message';
      // last_name.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Last name required',
        confirmButtonColor: '#ffc107',
      });


    } else if (!isValidPhone(phone_no) && phone_no != '') {

      event.preventDefault(); // Prevent form submission)

      var phone_no = document.getElementById('phone_no');
      phone_no.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'phone number is invalid format';
      // messageElement.id = 'error_message';
      // phone_no.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Telephone number is invalid format',
        confirmButtonColor: '#ffc107',
      });

    } else if (civil_status_id == '') {

      event.preventDefault(); // Prevent form submission)

      var civil_status_id = document.getElementById('civil_status_id');
      civil_status_id.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // // Show the new error message next to the missing field
      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'the civil status required';
      // messageElement.id = 'error_message';
      // civil_status_id.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Civil status required',
        confirmButtonColor: '#ffc107',
      });

    } else if (permanent_add1 == '') {

      event.preventDefault(); // Prevent form submission)
      var permanent_add1 = document.getElementById('permanent_add1');
      permanent_add1.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // // Show the new error message next to the missing field
      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'the permanent address required';
      // messageElement.id = 'error_message';
      // permanent_add1.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Permanent address required',
        confirmButtonColor: '#ffc107',
      });


    } else if (permanent_city_id == '') {

      event.preventDefault(); // Prevent form submission)
      var permanent_city_id = document.getElementById('permanent_city_id');
      permanent_city_id.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // // Show the new error message next to the missing field
      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'the permanent city required';
      // messageElement.id = 'error_message';
      // permanent_city_id.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Permanent address city required',
        confirmButtonColor: '#ffc107',
      });

    } else if (postal_add1 == '') {

      event.preventDefault(); // Prevent form submission)
      var postal_add1 = document.getElementById('postal_add1');
      postal_add1.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // // Show the new error message next to the missing field
      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'the postal address required';
      // messageElement.id = 'error_message';
      // postal_add1.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Postal address required',
        confirmButtonColor: '#ffc107',
      });


    } else if (postal_city_id == '') {

      event.preventDefault(); // Prevent form submission)
      var postal_city_id = document.getElementById('postal_city_id');
      postal_city_id.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // // Show the new error message next to the missing field
      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'the postal city required';
      // messageElement.id = 'error_message';
      // postal_city_id.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Postal address city required',
        confirmButtonColor: '#ffc107',
      });

    } else if (state_of_citizenship_id == "") {

      event.preventDefault(); // Prevent form submission)
      var state_of_citizenship_id = document.getElementById('state_of_citizenship_id');
      state_of_citizenship_id.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // // Show the new error message next to the missing field
      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'the citizenship type required';
      // messageElement.id = 'error_message';
      // state_of_citizenship_id.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Citizenship type required',
        confirmButtonColor: '#ffc107',
      });

    } else if (citizen_registration_no == "" && state_of_citizenship_id == 26) {

      event.preventDefault(); // Prevent form submission)
      var citizen_registration_no = document.getElementById('citizen_registration_no');
      citizen_registration_no.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // // Show the new error message next to the missing field
      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'the citizenship registration number required';
      // messageElement.id = 'error_message';
      // citizen_registration_no.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Citizenship registration number required',
        confirmButtonColor: '#ffc107',
      });

    } else if (emp_highest_edu_level == '') {

      event.preventDefault(); // Prevent form submission)
      var emp_highest_edu_level = document.getElementById('emp_highest_edu_level');
      emp_highest_edu_level.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // // Show the new error message next to the missing field
      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'the highest academic qulification required';
      // messageElement.id = 'error_message';
      // emp_highest_edu_level.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Highest academic qulification required',
        confirmButtonColor: '#ffc107',
      });

    } else if (photoCount == 0 && !profile_photo) {

      event.preventDefault(); // Prevent form submission)
      var profile_photo = document.getElementById("profile_photo");
      profile_photo.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_messages');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'you must upload profile image';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'You must upload photograph',
        confirmButtonColor: '#ffc107',
      });


    } else if (profile_photo && !allowedMimeTypes.includes(profile_photo.type)) {

      const customFileLabel = document.querySelector('.custom-file-label');
      customFileLabel.innerHTML = '';
      profile_photo1.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_messages');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'Invalid file type. Please select a PNG or JPG file.';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Invalid file type. Please select a PNG or JPG file.',
        confirmButtonColor: '#ffc107',
      });


    } else if (profile_photo && profile_photo.size > maxFileSize) {

      const customFileLabel = document.querySelector('.custom-file-label');
      customFileLabel.innerHTML = '';
      profile_photo1.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_messages');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'photo size must be under 2mb';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'photograph size must be under 5Mb',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && (min_qualification == 73 || min_qualification == 74 || min_qualification == 183 || min_qualification == 184) && diplomaCount == 0 && diplomas == null) {

      event.preventDefault(); // Prevent form submission)
      var focusButton1 = document.getElementById("focusButton1");
      focusButton1.focus();

      // // Check if an error message already exists
      // var existingMessage = document.getElementById('error_message');
      // if (existingMessage) {
      // // Remove the existing error message
      //  existingMessage.parentNode.removeChild(existingMessage);
      // }


      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message1');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'you must add at least one diploma/cretificate data';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'You must add at least one diploma/cretificate data',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && (min_qualification == 73 || min_qualification == 74 || min_qualification == 183 || min_qualification == 184) && diplomaCertificateCount == 0 && diploma_certificates.length === 0) {

      event.preventDefault(); // Prevent form submission)
      var diploma_certificate = document.getElementById("diploma_certificate");
      diploma_certificate.focus();




      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message2');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'you must upload the scanned copies of the certificates mentioned above';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Diploma certificate/s must be uploaded',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && (min_qualification == 73 || min_qualification == 74 || min_qualification == 183 || min_qualification == 184) && diplomaCertificateCount == 0 && diploma_certificates.length !== 0 && fileTypecheck(diploma_certificates)) {

      event.preventDefault(); // Prevent form submission)
      var diploma_certificate = document.getElementById("diploma_certificate");
      diploma_certificate.focus();


      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message2');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'diploma certificates must be pdf format';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Diploma certificate/s must be in PDF format',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && (min_qualification == 73 || min_qualification == 74 || min_qualification == 183 || min_qualification == 184) && diplomaCertificateCount == 0 && diploma_certificates.length !== 0 && fileSizecheck(diploma_certificates)) {

      event.preventDefault(); // Prevent form submission)
      var diploma_certificate = document.getElementById("diploma_certificate");
      diploma_certificate.focus();


      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message2');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'diploma certificates must be pdf size must be under 5Mb';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Diploma certificate/s file size must be under 5Mb',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && (min_qualification == 73 || min_qualification == 74 || min_qualification == 183 || min_qualification == 184) && fileTypecheck(degree_certificates)) {

      event.preventDefault(); // Prevent form submission)
      var degree_certificate = document.getElementById("degree_certificate");
      degree_certificate.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message4');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'degree certificates must be pdf format';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Degree certificates/trascripts must be in PDF format',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && (min_qualification == 73 || min_qualification == 74 || min_qualification == 183 || min_qualification == 184) && fileTypecheck(degree_certificates)) {

      event.preventDefault(); // Prevent form submission)
      var degree_certificate = document.getElementById("degree_certificate");
      degree_certificate.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message4');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'degree certificates must be pdf size must be under 5Mb';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Degree certificates/trascripts file size must be under 5Mb',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && (min_qualification == 75 || min_qualification == 76 || min_qualification == 77 || min_qualification == 78 || min_qualification == 79 || min_qualification == 80 || min_qualification == 81 || min_qualification == 82) && firstDegreeCount == 0 && degrees.length == 0) {

      event.preventDefault(); // Prevent form submission)
      var focusButton2 = document.getElementById("focusButton2");
      focusButton2.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message3');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'you must add first degree data';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'You must be add first degree data',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && (min_qualification == 75 || min_qualification == 76 || min_qualification == 77 || min_qualification == 78 || min_qualification == 79 || min_qualification == 80 || min_qualification == 81 || min_qualification == 82) && firstDegreeCount == 0 && !firstDegreeCheck(degrees) && degrees.length != 0) {

      event.preventDefault(); // Prevent form submission)
      var focusButton2 = document.getElementById("focusButton2");
      focusButton2.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message3');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'you must add first degree data';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'You must be add first degree data',
        confirmButtonColor: '#ffc107',
      });


    } else if ((main_category == 44 || main_category == 45) && (min_qualification == 75 || min_qualification == 76 || min_qualification == 77 || min_qualification == 78 || min_qualification == 79 || min_qualification == 80 || min_qualification == 81 || min_qualification == 82) && degreeSubject.value == '' && degreeSubjectCount == 0) {

      event.preventDefault(); // Prevent form submission)
      var degreeSubject = document.getElementById('degree_main_subject1');
      degreeSubject.focus();


      // // Show the new error message next to the missing field
      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'main subjects of the first degree is required';
      // messageElement.id = 'error_message';
      // degreeSubject.parentNode.appendChild(messageElement);

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Main subjects of the first degree is required',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && (min_qualification == 75 || min_qualification == 76 || min_qualification == 77 || min_qualification == 78 || min_qualification == 79 || min_qualification == 80 || min_qualification == 81 || min_qualification == 82) && degreeCertificateCount == 0 && degree_certificates.length === 0) {

      event.preventDefault(); // Prevent form submission)
      var degree_certificate = document.getElementById("degree_certificate");
      degree_certificate.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message4');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'you must upload the scanned copies of the certificates mentioned above';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Degree certificates/transcripts must be uploaded',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && (min_qualification == 75 || min_qualification == 76 || min_qualification == 77 || min_qualification == 78 || min_qualification == 79 || min_qualification == 80 || min_qualification == 81 || min_qualification == 82) && degreeCertificateCount == 0 && degree_certificates.length !== 0 && fileTypecheck(degree_certificates)) {

      event.preventDefault(); // Prevent form submission)
      var degree_certificate = document.getElementById("degree_certificate");
      degree_certificate.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message4');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'degree certificates must be pdf format';
      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Degree certificates/trascripts must be in PDF format',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && (min_qualification == 75 || min_qualification == 76 || min_qualification == 77 || min_qualification == 78 || min_qualification == 79 || min_qualification == 80 || min_qualification == 81 || min_qualification == 82) && degreeCertificateCount == 0 && degree_certificates.length !== 0 && fileSizecheck(degree_certificates)) {

      event.preventDefault(); // Prevent form submission)
      var degree_certificate = document.getElementById("degree_certificate");
      degree_certificate.focus();

      // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message4');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'degree certificates must be pdf size must be under 5Mb';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Degree certificates/trascripts file size must be under 5Mb',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && (min_qualification == 75 || min_qualification == 76 || min_qualification == 77 || min_qualification == 78 || min_qualification == 79 || min_qualification == 80 || min_qualification == 81 || min_qualification == 82) && fileTypecheck(diploma_certificates)) {

      event.preventDefault(); // Prevent form submission)
      var diploma_certificate = document.getElementById("diploma_certificate");
      diploma_certificate.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message2');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'diploma certificates must be pdf format';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Diploma certificate/s must be in PDF format',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && (min_qualification == 75 || min_qualification == 76 || min_qualification == 77 || min_qualification == 78 || min_qualification == 79 || min_qualification == 80 || min_qualification == 81 || min_qualification == 82) && fileTypecheck(diploma_certificates)) {

      event.preventDefault(); // Prevent form submission)
      var diploma_certificate = document.getElementById("diploma_certificate");
      diploma_certificate.focus();

      // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message2');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'diploma certificates must be pdf size must be under 5Mb';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Diploma certificate/s file size must be under 5Mb',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && bondability.value == 1 && bondCount == 0) {

      event.preventDefault(); // Prevent form submission)
      var bondability = document.querySelector('input[name="bondability"]');
      bondability.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message5');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'you must add at least one bond data';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'You must be add at least one bond data',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && firstreferees == '' && secondreferees == '' && refereeListCount == 0) {

      event.preventDefault(); // Prevent form submission)
      var focusButton3 = document.getElementById("focusButton3");
      focusButton3.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message6');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'you must add non related first referees data';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'You must be add non related first referee data',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && firstreferees == '' && secondreferees == '' && refereeListCount == 1) {

      event.preventDefault(); // Prevent form submission)
      var focusButton3 = document.getElementById("focusButton3");
      focusButton3.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message6');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'you must add non related second referees data';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'You must be add non related second referee data',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && firstreferees != '' && secondreferees == '' && refereeListCount == 0) {

      event.preventDefault(); // Prevent form submission)
      var focusButton3 = document.getElementById("focusButton3");
      focusButton3.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message6');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'you must add non related second referees data';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'You must be add non related second referee data',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && publicSector.value == 1 && releaseLetterCount == 0 && public_sector_letters.length == 0) {

      event.preventDefault(); // Prevent form submission)
      var public_sector_letter = document.getElementById("public_sector_letter");
      public_sector_letter.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message7');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'you must upload the consent to release letter certified by the head of the institution/department';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'You must be upload the consent to release letter certified by the head of the institution/department',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && public_sector_letters.length != 0 && fileTypecheck(public_sector_letters)) {

      event.preventDefault(); // Prevent form submission)
      var public_sector_letter = document.getElementById("public_sector_letter");
      public_sector_letter.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message7');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'the consent letter must be pdf';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Consent to release letter must be PDF format',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && public_sector_letters.length != 0 && fileSizecheck(public_sector_letters)) {

      event.preventDefault(); // Prevent form submission)
      var public_sector_letter = document.getElementById("public_sector_letter");
      public_sector_letter.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message7');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'the consent letter file size must be under 5Mb';

      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Consent to release letter must be under 5Mb',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && fileTypecheck(employment_record_certificates)) {

      event.preventDefault(); // Prevent form submission)
      var employment_record_certificate = document.getElementById("employment_record_certificate");
      employment_record_certificate.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message8');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'employment record must be pdf format';
      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Employment record/s must be PDF format',
        confirmButtonColor: '#ffc107',
      });


    } else if ((main_category == 44 || main_category == 45) && fileSizecheck(employment_record_certificates)) {

      event.preventDefault(); // Prevent form submission)
      var employment_record_certificate = document.getElementById("employment_record_certificate");
      employment_record_certificate.focus();

      // // Show the new error message next to the missing field
      // var errorMessageElement = document.getElementById('error_message8');
      // errorMessageElement.style.color = 'red';
      // errorMessageElement.textContent = 'employment record file file under 5Mb';
      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'Employment record/s file must be under 5Mb',
        confirmButtonColor: '#ffc107',
      });

    } else if ((main_category == 44 || main_category == 45) && orcid != '' && !isValidOCRID(orcid)) {

      event.preventDefault(); // Prevent form submission)
      var orcid = document.getElementById("orcid");
      orcid.focus();

      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'The OCRID id is invalid format';
      // messageElement.id = 'error_message';
      // orcid.parentNode.appendChild(messageElement);
      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'The OCRID id is invalid format',
        confirmButtonColor: '#ffc107',
      });

    } else if (!agreeTerms.checked) {

      event.preventDefault(); // Prevent form submission)
      var agreeTerms = document.getElementById('agreeTerms');
      agreeTerms.focus();

      // // Show the new error message next to the missing field
      // var messageElement = document.createElement('span');
      // messageElement.style.color = 'red';
      // messageElement.innerHTML = 'you must accept the declaration';
      // messageElement.id = 'error_message';
      // agreeTerms.parentNode.appendChild(messageElement);
      Swal.fire({
        icon: 'error',
        title: 'Error in Data Submitting',
        text: 'You must be accept the declaration',
        confirmButtonColor: '#ffc107',
      });

    } else if (degree_type_val !== '0' && degree_titel !== '' && degree_university !== '' && degree_class_val !== '0' && eff_date !== '' && degree_index_number !== '' && degree_start !== '' && degree_end !== '') {

      event.preventDefault(); // Prevent form submission
      Swal.fire({
        icon: 'warning',
        title: 'Error in Data Adding',
        text: 'Degree data should be added to the table before submitting or saving',
        confirmButtonColor: '#ffc107',
      });

    } else if (diploma_type_val !== '0' && diploma_titel !== '' && diploma_university !== '' && diploma_duration !== '' && diploma_year !== '') {

      event.preventDefault(); // Prevent form submission
      Swal.fire({
        icon: 'warning',
        title: 'Course Data Add',
        text: 'Certificate/diploma data should be added to the table before submitting or saving',
        confirmButtonColor: '#ffc107',
      });

    } else if (qulification_type_val !== '0' && qulification_summary !== '') {

      event.preventDefault(); // Prevent form submission
      Swal.fire({
        icon: 'warning',
        title: 'Error in Data Adding',
        text: 'Special qualification data should be added to the table before submitting or saving',
        confirmButtonColor: '#ffc107',
      });

    } else if (membership_type_val !== '0' && membership_university !== '' && membership_start_year !== '' && membership_active_status_val !== '0') {

      event.preventDefault(); // Prevent form submission
      Swal.fire({
        icon: 'warning',
        title: 'Error in Data Adding',
        text: 'Membership data should be added to the table before submitting or saving',
        confirmButtonColor: '#ffc107',
      });

    } else if (pqualification_name !== '' && pqualification_university !== '' && pqualification_start_year !== '' && pqualification_end_year !== '') {

      event.preventDefault(); // Prevent form submission
      Swal.fire({
        icon: 'warning',
        title: 'Error in Data Adding',
        text: 'Professional qualification data should be added to the table before submitting or saving',
        confirmButtonColor: '#ffc107',
      });

    } else if (experience_designation !== '' && experience_institution !== '' && experience_start_date !== '') {

      event.preventDefault(); // Prevent form submission
      Swal.fire({
        icon: 'warning',
        title: 'Error in Data Adding',
        text: 'Employment record data should be added to the table before submitting or saving',
        confirmButtonColor: '#ffc107',
      });

    } else if (bond_institution !== '' && bond_start_date !== '' && bond_end_date !== '' && bond_total_value !== '') {

      event.preventDefault(); // Prevent form submission
      Swal.fire({
        icon: 'warning',
        title: 'Error in Data Adding',
        text: 'Employment bond data should be added to the table before submitting or saving',
        confirmButtonColor: '#ffc107',
      });

    } else if (referee_name !== '' && referee_designation !== '' && referee_address !== '' && referee_phome !== '' && referee_email !== '') {

      event.preventDefault(); // Prevent form submission
      Swal.fire({
        icon: 'warning',
        title: 'Error in Data Adding',
        text: 'Referees data should be added to the table before submitting or saving',
        confirmButtonColor: '#ffc107',
      });

    } else {
      $('#exampleModalCenter3').modal('show'); // Show the modal
      //$('#applicant_form').submit();
    }













  }

  function displayErrorMessage(message) {
    const errorMessage = document.createElement('p');
    errorMessage.textContent = message;
    errorMessages.appendChild(errorMessage);
  }
</script>

<script>
  document.getElementById('applicant_form').addEventListener('submit', function(event) {

    // Get the button that triggered the form submission
    const submitterButton = event.submitter;

    // Get input field values
    var degree_type_val = $("#degree_type option:selected").val();
    var degree_titel = document.getElementById('degree_titel').value;
    var degree_university = document.getElementById('degree_university').value;
    var degree_class_val = $("#degree_class option:selected").val();
    var eff_date = document.getElementById('eff_date').value;
    var degree_index_number = document.getElementById('degree_index_number').value;
    var degree_start = document.getElementById('degree_start').value;
    var degree_end = document.getElementById('degree_end').value;
    /*********************************************************/

    var diploma_type = $("#diploma_type option:selected").text();
    var diploma_type_val = $("#diploma_type option:selected").val();
    var diploma_titel = document.getElementById('diploma_titel').value;
    var diploma_university = document.getElementById('diploma_university').value;
    var diploma_duration = document.getElementById('diploma_duration').value;
    var diploma_year = document.getElementById('diploma_year').value;
    /*********************************************/

    var qulification_type_val = $("#qulification_type option:selected").val();
    var qulification_summary = document.getElementById('qulification_summary').value;
    /************************************************************/

    var membership_type_val = $("#membership_type option:selected").val();
    var membership_university = document.getElementById('membership_university').value;
    var membership_start_year = document.getElementById('membership_start_year').value;
    //var membership_end_year = document.getElementById('membership_end_year').value;
    var membership_active_status_val = $("#membership_active_status option:selected").val();
    /**************************************************************/

    var pqualification_name = document.getElementById('pqualification_name').value;
    var pqualification_university = document.getElementById('pqualification_university').value;
    var pqualification_start_year = document.getElementById('pqualification_start_year').value;
    var pqualification_end_year = document.getElementById('pqualification_end_year').value;
    /**********************************************************/

    var experience_designation = document.getElementById('experience_designation').value;
    var experience_institution = document.getElementById('experience_institution').value;
    var experience_start_date = document.getElementById('experience_start_date').value;
    var experience_end_date = document.getElementById('experience_end_date').value;
    var experience_last_month_salary = document.getElementById('experience_last_month_salary').value;
    /**********************************************************/

    var bond_institution = document.getElementById('bond_institution').value;
    var bond_start_date = document.getElementById('bond_start_date').value;
    var bond_end_date = document.getElementById('bond_end_date').value;
    var bond_total_value = document.getElementById('bond_total_value').value;
    /**************************************************************************/

    var referee_name = document.getElementById('referee_name').value;
    var referee_designation = document.getElementById('referee_designation').value;
    var referee_address = document.getElementById('referee_address').value;
    var referee_phome = document.getElementById('referee_phome').value;
    var referee_email = document.getElementById('referee_email').value;


    if ((submitterButton && submitterButton.name === 'save') || (submitterButton && submitterButton.name === 'save1')) {

      // Check if input fields are filled
      if (degree_type_val !== '0' && degree_titel !== '' && degree_university !== '' && degree_class_val !== '0' && eff_date !== '' && degree_index_number !== '' && degree_start !== '' && degree_end !== '') {

        event.preventDefault(); // Prevent form submission
        Swal.fire({
          icon: 'warning',
          title: 'Error in Data Adding',
          text: 'Degree data should be added to the table before submitting or saving',
          confirmButtonColor: '#ffc107',
        });

      } else if (diploma_type_val !== '0' && diploma_titel !== '' && diploma_university !== '' && diploma_duration !== '' && diploma_year !== '') {

        event.preventDefault(); // Prevent form submission
        Swal.fire({
          icon: 'warning',
          title: 'Course Data Add',
          text: 'Certificate/diploma data should be added to the table before submitting or saving',
          confirmButtonColor: '#ffc107',
        });

      } else if (qulification_type_val !== '0' && qulification_summary !== '') {

        event.preventDefault(); // Prevent form submission
        Swal.fire({
          icon: 'warning',
          title: 'Error in Data Adding',
          text: 'Special qualification data should be added to the table before submitting or saving',
          confirmButtonColor: '#ffc107',
        });

      } else if (membership_type_val !== '0' && membership_university !== '' && membership_start_year !== '' && membership_active_status_val !== '0') {

        event.preventDefault(); // Prevent form submission
        Swal.fire({
          icon: 'warning',
          title: 'Error in Data Adding',
          text: 'Membership data should be added to the table before submitting or saving',
          confirmButtonColor: '#ffc107',
        });

      } else if (pqualification_name !== '' && pqualification_university !== '' && pqualification_start_year !== '' && pqualification_end_year !== '') {

        event.preventDefault(); // Prevent form submission
        Swal.fire({
          icon: 'warning',
          title: 'Error in Data Adding',
          text: 'Professional qualification data should be added to the table before submitting or saving',
          confirmButtonColor: '#ffc107',
        });

      } else if (experience_designation !== '' && experience_institution !== '' && experience_start_date !== '') {

        event.preventDefault(); // Prevent form submission
        Swal.fire({
          icon: 'warning',
          title: 'Error in Data Adding',
          text: 'Employment record data should be added to the table before submitting or saving',
          confirmButtonColor: '#ffc107',
        });

      } else if (bond_institution !== '' && bond_start_date !== '' && bond_end_date !== '' && bond_total_value !== '') {

        event.preventDefault(); // Prevent form submission
        Swal.fire({
          icon: 'warning',
          title: 'Error in Data Adding',
          text: 'Employment bond data should be added to the table before submitting or saving',
          confirmButtonColor: '#ffc107',
        });

      } else if (referee_name !== '' && referee_designation !== '' && referee_address !== '' && referee_phome !== '' && referee_email !== '') {

        event.preventDefault(); // Prevent form submission
        Swal.fire({
          icon: 'warning',
          title: 'Error in Data Adding',
          text: 'Referees data should be added to the table before submitting or saving',
          confirmButtonColor: '#ffc107',
        });

      } else {
        $('#applicant_form').submit();
      }

    }


  });
</script>
<script>
  document.addEventListener("keydown", function(event) {
    // Check if 'Ctrl' (or 'Meta' for Mac) and 's' keys are pressed for Save as Draft
    if ((event.ctrlKey || event.metaKey) && event.key === "s") {
      event.preventDefault(); // Prevent the default 'save page' behavior
      document.querySelector("#applicant_form [name='save']").click(); // Trigger the click event of Save as Draft button
    } else if (event.key === "Enter") { // For Submit on Enter key
      event.preventDefault(); // Prevent the default form submission behavior
      //document.querySelector("#applicant_form [name='submit1']").click(); // Trigger the click event of Submit button
    }
  });
</script>
<?php $__env->stopSection(); ?>
<?php elseif(session()->has('vacancy_id') && session()->has('reference_no') && $appData->application_decision_id == 34): ?>
<script type="text/javascript">
  window.location = "<?php echo e(route('application.final')); ?>"; //here double curly bracket
</script>
<?php else: ?>
<script type="text/javascript">
  window.location = "<?php echo e(route('home')); ?>"; //here double curly bracket
</script>
<?php endif; ?>

<?php echo $__env->make('frontend.frontend_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Development\hr_system\resources\views/frontend/application/index.blade.php ENDPATH**/ ?>