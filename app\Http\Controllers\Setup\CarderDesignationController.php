<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\CarderDesignation;
use App\Models\Designation;
use Illuminate\Http\Request;

class CarderDesignationController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function CarderDesignationAssignIndex(){

        $data = CarderDesignation::where('active_status',1)->get();
        return view('admin.setups.carder_designation_assign.index', compact('data'));

    }

    public function CarderDesignationAssignAdd($id){

        $mainBranch = Auth()->user()->main_branch_id;
        if ($mainBranch == 51) {

            $designations = Designation::whereNotIn('salary_code', ['ON CONTRACT', 'Allowance','ON ASSIGNMENT BASIS','ON TEMPORY'])
                           ->where('active_status',1)
                           ->where('carder_designation_id',0)
                           ->orderBy('salary_code')
                           ->get();

        }elseif ($mainBranch == 52) {

            $designations = Designation::where('designation_division',52)
                            ->whereNotIn('salary_code', ['ON CONTRACT', 'Allowance','ON ASSIGNMENT BASIS','ON TEMPORY'])
                            ->where('active_status',1)
                            ->where('carder_designation_id',0)
                            ->where('designation_division',52)
                            ->orderBy('salary_code')
                            ->get();

        }elseif ($mainBranch == 53) {

            $designations = Designation::where('designation_division',53)
                            ->whereNotIn('salary_code', ['ON CONTRACT', 'Allowance','ON ASSIGNMENT BASIS','ON TEMPORY'])
                            ->where('active_status',1)
                            ->where('carder_designation_id',0)
                            ->where('designation_division',53)
                            ->orderBy('salary_code')
                            ->get();

        }
        $carderDesignations = CarderDesignation::find($id);
        return view('admin.setups.carder_designation_assign.add', compact('carderDesignations','designations'));
    }

    public function CarderDesignationAssignStore(Request $request){

        $request->validate([
            'designation_id' => 'required',
            'carder_designation_id' => 'required',
        ]);

        $data = Designation::find($request->designation_id);
        $data->carder_designation_id = $request->carder_designation_id;
        $data->save();

        $notification = array(
            'message' => 'New Carder Designation Assign Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('carder.designation.assign.index')->with($notification);

    }

    public function CarderDesignationAssignEdit($id){

        $data = CarderDesignation::find($id);
        $designations = Designation::where('carder_designation_id',$id)->get();


        return view('admin.setups.carder_designation_assign.edit', compact('data','designations'));
    }

    public function CarderDesignationAssignDelete($id)
    {
        $data = Designation::find($id);

        if (!$data) {
            return redirect()->back()->with([
                'message' => 'Designation not found',
                'alert-type' => 'error'
            ]);
        }

        $carderDesignationId = $data->carder_designation_id;

        $data->carder_designation_id = 0;
        $data->save();

        $notification = [
            'message' => 'Carder Designation Assign Deleted Successfully',
            'alert-type' => 'success'
        ];

        return redirect()->route('carder.designation.assign.edit', ['id' => $carderDesignationId])->with($notification);
    }

}
