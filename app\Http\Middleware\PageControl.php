<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class PageControl
{
    public $attributes;
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next,$num)
    {
     
//     if( $request->session()->get('checkReturn') != null)
// {
//     if( $request->session()->get('checkReturn')){
//       //return redirect($request->session()->get('special_callback_url'));
//       // return redirect(route("nac.promo.head.approval.open"));
     
        
//      }
// }       
//return redirect(route("admin.dashboard"));

      if($num == 1){
        
        return $next($request);

      }else if($request->session()->get('special_value')){
    
        return $next($request);
    
      }else if($request->session()->get('checkReturn') && (! $request->session()->get('special_value') || $request->session()->get('special_value') !== null)){
      //}else if($request->session()->get('checkReturn') && $request->session()->get('special_value')){
    
        return redirect($request->session()->get('special_callback_url'));
    
      }else{
        
        return $next($request);
      }
       }
}
