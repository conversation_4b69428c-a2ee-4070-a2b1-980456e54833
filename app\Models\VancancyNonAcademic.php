<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class VancancyNonAcademic extends Model
{
    use HasFactory,LogsActivity;

    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('hrms_vacancies_non_academic')
        ->logOnlyDirty()
        ->dontLogIfAttributesChangedOnly(['updated_at'])
        ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    public function designations()
    {
        return $this->belongsTo(Designation::class,'designation_id');
    }

    public function visibility()
    {
        return $this->belongsTo(Category::class,'vacancy_visibility_status');
    }

    public function operatorName()
    {
        return $this->belongsTo(Employee::class,'published_user_id')->with('category');
    }

    public function officerName()
    {
        return $this->belongsTo(Employee::class,'updated_user_id')->with('category');
    }

    public function vacancyExtenderName()
    {
        return $this->belongsTo(Employee::class,'deadline_extented_emp')->with('category');
    }

    public function vacancyStatusType()
    {
        return $this->belongsTo(Category::class,'vacancy_status_type_id');
    }
}
