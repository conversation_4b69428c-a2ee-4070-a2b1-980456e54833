<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\CarderDesignation;
use App\Models\Designation;
use Illuminate\Http\Request;

class CarderController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }
    public function CarderDesignationIndex()
    {
        $data = CarderDesignation::all();
        return view('admin.setups.carder_designation.index', compact('data'));
    }

    public function CarderDesignationAdd()
    {
        return view('admin.setups.carder_designation.add');
    }

    public function CarderDesignationStore(Request $request)
    {
        $request->validate([
            'name' => 'required|unique:carder_designations,name',
        ]);

        CarderDesignation::create($request->all());

        $notification = array(
            'message' => 'New Carder Designation Inserted Successfully',
            'alert-type' => 'success'
        );
        return redirect()->route('carder.designation.index')->with($notification);
    }

    public function CarderDesignationEdit($id)
    {
        $data = CarderDesignation::find($id);
        return view('admin.setups.carder_designation.edit', compact('data'));
    }

    public function CarderDesignationUpdate(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|unique:carder_designations,name,' . $id,
        ]);

        $data = CarderDesignation::find($id);
        $data->name = $request->name;
        $data->active_status = $request->active_status;
        $data->save();

        if ($request->active_status == 0) {
            Designation::where('carder_designation_id', $id)->update(['carder_designation_id' => 0]);
        }

        $notification = array(
            'message' => 'Carder Designation Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('carder.designation.index')->with($notification);
    }

    public function CarderDesignationDelete($id)
    {
        CarderDesignation::find($id)->delete();

        Designation::where('carder_designation_id', $id)->update(['carder_designation_id' => 0]);

        $notification = array(
            'message' => 'Carder Designation Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('carder.designation.index')->with($notification);
    }
}
