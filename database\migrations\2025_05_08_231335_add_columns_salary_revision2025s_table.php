<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('salary_revision2025s', function (Blueprint $table) {
            //
            $table->integer('checking2_status')->default(0)->after('check_date');
            $table->integer('checking2_user')->default(0)->after('checking2_status');
            $table->date('checking2_date')->nullable()->after('checking2_user');
            $table->integer('accept_status')->default(0)->after('checking2_date');
            $table->integer('accept_user')->default(0)->after('accept_status');
            $table->date('accept_date')->nullable()->after('accept_user');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('salary_revision2025s', function (Blueprint $table) {
            //
            $table->dropColumn('checking2_status');
            $table->dropColumn('checking2_user');
            $table->dropColumn('checking2_date');
            $table->dropColumn('accept_status');
            $table->dropColumn('accept_user');
            $table->dropColumn('accept_date');
        });
    }
};
