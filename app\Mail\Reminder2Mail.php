<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class Reminder2Mail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;
    public function __construct($data)
    {
        $this->data = $data;
    }


    public function envelope()
    {
        return new Envelope(
            subject: 'Incomplete Application for the Vacancy at USJ',
            tags: ['reminder'],
        );
    }


    public function content()
    {
        return new Content(
            markdown: 'emails.reminder2',
        );
    }


    public function attachments()
    {
        return [];
    }
}
