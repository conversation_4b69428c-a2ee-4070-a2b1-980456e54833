<?php

namespace App\Console\Commands;

use App\Models\Vacancy;
use Illuminate\Support\Facades\DB;

use Illuminate\Console\Command;

class ClosingDateCheckCron extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auto:closedate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check vacancy is expired or not';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Update vacancies where date_closed is less than today and status is 27
        $vacanciesToClose = Vacancy::whereDate('date_closed', '<', date('Y-m-d'))
        ->where('vacancy_status_type_id', 27)
        ->get();

        foreach ($vacanciesToClose as $vacancy) {
            $vacancy->vacancy_status_type_id = 28;
            $vacancy->save();
        }

        // Update vacancies where date_closed is greater than today and status is 28
        $vacanciesToReopen = Vacancy::whereDate('date_closed', '>', date('Y-m-d'))
        ->where('vacancy_status_type_id', 28)
        ->get();

        foreach ($vacanciesToReopen as $vacancy) {
            $vacancy->vacancy_status_type_id = 27;
            $vacancy->save();
        }


        return 0;
    }
}
