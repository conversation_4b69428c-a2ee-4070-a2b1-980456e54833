<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use App\Models\CategoryType;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rule;

class CategoryTypeController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator');
    }

    public function CategoryTypeIndex()
    {
        $categoryTypes =  CategoryType::all();

        return view('admin.setups.category_type.index', compact('categoryTypes'));
    }

    public function CategoryTypeAdd()
    {
        return view('admin.setups.category_type.add');
    }

    public function CategoryTypeStore(Request $request)
    {
        $validatedData = $request->validate([
            'category_type_name' => 'required|unique:category_types,category_type_name',
        ]);

        $data = new CategoryType();
        $data->category_type_name = $request->category_type_name;
        $data->created_at = Carbon::now();
        $data->save();

        $notification = array(
            'message' => 'Category Type Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('category.type.index')->with($notification);
    }

    public function CategoryTypeEdit($id)
    {
        $editData = CategoryType::find($id);
        return view('admin.setups.category_type.edit', compact('editData'));
    }

    public function CategoryTypeUpdate(Request $request, $id)
    {

        $validatedData = $request->validate([
            'category_type_name' => ['required', Rule::unique('category_types')->ignore($id)],
        ]);

        $data = CategoryType::find($id);
        $data->category_type_name = $request->category_type_name;
        $data->save();

        $notification = array(
            'message' => 'Category Type Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('category.type.index')->with($notification);
    }

    public function CategoryTypeDelete($id)
    {

        $catagory = CategoryType::find($id);
        $catagory->delete();

        $notification = array(
            'message' => 'Category Type Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('category.type.index')->with($notification);
    }

    public function CategoryList($id)
    {

        $categoryType = CategoryType::find($id);
        $categories = Category::where('category_type_id', $id)->get();

        return view('admin.setups.category_type.list', compact('categories', 'categoryType'));
    }
}
