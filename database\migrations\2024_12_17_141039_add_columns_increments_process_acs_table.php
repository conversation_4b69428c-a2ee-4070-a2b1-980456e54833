<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('increments_process_acs', function (Blueprint $table) {
            //
            $table->date('ma_date')->nullable()->after('ma_status');
            $table->integer('ma_user_id')->default(0)->after('ma_date');
            $table->integer('finance_list_status')->default(0)->after('ma_user_id');
            $table->integer('finance_list_id')->default(0)->after('finance_list_status');
            $table->date('finance_list_date')->nullable()->after('finance_list_id');
            $table->date('completed_date')->nullable()->after('finance_list_date');
            $table->integer('completed_user')->default(0)->after('completed_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('increments_process_acs', function (Blueprint $table) {
            //
            $table->dropColumn('ma_date');
            $table->dropColumn('ma_user_id');
            $table->dropColumn('finance_list_status');
            $table->dropColumn('finance_list_id');
            $table->dropColumn('finance_list_date');
            $table->dropColumn('completed_date');
            $table->dropColumn('completed_user');
        });
    }
};
