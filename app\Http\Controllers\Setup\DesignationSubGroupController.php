<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\DesignationMainGroup;
use App\Models\DesignationSubGroup;
use Illuminate\Validation\Rule;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class DesignationSubGroupController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator');
    }

    public function designationSubGroupIndex()
    {
        Log::info('DesignationSubGroupController -> designation sub group index started');
        $designationSubGroups = DesignationSubGroup::all();
        $trashDesignationSubGroups = DesignationSubGroup::onlyTrashed()->get();
        return view('admin.setups.designation_sub.index', compact('designationSubGroups', 'trashDesignationSubGroups'));
        Log::notice('DesignationSubGroupController -> designation sub group Count - ' . $designationSubGroups->count());
        Log::info('DesignationSubGroupController -> designation sub group index ended');
    }

    public function designationSubGroupAdd()
    {
        Log::info('DesignationSubGroupController -> designation sub group add started');

        $designationMainGroups = DesignationMainGroup::all();
        return view('admin.setups.designation_sub.add', compact('designationMainGroups'));

        Log::info('DesignationSubGroupController -> designation sub group add ended');
    }

    public function designationSubGroupStore(Request $request)
    {
        Log::info('DesignationSubGroupController -> designation sub group store started');
        $validatedData = $request->validate([
            'name' => 'required|unique:designation_main_groups,name',
            'designation_main_group_id' => 'required'
        ], [
            'name.required' => 'designation main group name required',
        ]);

        $data = new DesignationSubGroup();
        $data->name = $request->name;
        $data->designation_main_group_id = $request->designation_main_group_id;
        $data->created_at = Carbon::now();
        $data->save();

        Log::notice('DesignationSubGroupController -> Created designation sub group id - ' . $data->id . ' created by ' . auth()->user()->id);
        Log::info('DesignationSubGroupController -> designation sub group store ended');

        $notification = array(
            'message' => 'Designation Sub Group Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('designation.sub.group.index')->with($notification);
    }

    public function designationSubGroupEdit($id)
    {
        Log::info('DesignationSubGroupController -> designation sub group edit started');
        $editData = DesignationSubGroup::find($id);
        $designationMainGroups = DesignationMainGroup::all();
        return view('admin.setups.designation_sub.edit', compact('editData', 'designationMainGroups'));
        Log::notice('DesignationSubGroupController -> edit designation sub group id - ' . $editData->id . ' edited by ' . auth()->user()->id);
        Log::info('DesignationSubGroupController -> designation sub group edit ended');
    }

    public function designationSubGroupUpdate(Request $request, $id)
    {

        Log::info('DesignationSubGroupController -> designation sub group edit started');

        $validatedData = $request->validate([
            'name' => ['required', Rule::unique('designation_main_groups')->ignore($id)],
            'designation_main_group_id' => 'required'
        ], [
            'name.required' => 'designation main group name required',
        ]);

        $data = DesignationSubGroup::find($id);
        $data->name = $request->name;
        $data->designation_main_group_id = $request->designation_main_group_id;
        $data->updated_at = Carbon::now();
        $data->save();

        Log::notice('DesignationSubGroupController -> update designation sub group id - ' . $data->id . ' updated by ' . auth()->user()->id);
        Log::info('DesignationSubGroupController -> designation sub group update ended');

        $notification = array(
            'message' => 'Designation Sub Group Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('designation.sub.group.index')->with($notification);
    }

    public function designationSubGroupSoftdelete($id)
    {

        Log::info('DesignationSubGroupController -> designation sub group soft delete started');
        $designationSubGroup = DesignationSubGroup::find($id);
        $designationSubGroup->delete();

        $notification = array(
            'message' => 'Designation Main Group Deleted Successfully',
            'alert-type' => 'warning'
        );

        return redirect()->route('designation.sub.group.index')->with($notification);

        Log::notice('DesignationSubGroupController -> soft delete designation sub group id - ' . $designationSubGroup->id . ' deleted by ' . auth()->user()->id);
        Log::info('DesignationSubGroupController -> designation sub group soft delete ended');
    }

    public function designationSubGroupRestore($id)
    {

        Log::info('DesignationSubGroupController -> designation sub group restore started');

        $designationSubGroup = DesignationSubGroup::withTrashed()->find($id);
        $designationSubGroup->restore();

        $notification = array(
            'message' => 'Designation Sub Group Restore Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('designation.sub.group.index')->with($notification);

        Log::notice('DesignationSubGroupController -> restore designation sub group id - ' . $designationSubGroup->id . ' deleted by ' . auth()->user()->id);
        Log::info('DesignationSubGroupController -> designation sub group restore ended');
    }

    public function designationSubGroupDelete($id)
    {

        Log::info('DesignationSubGroupController -> designation sub group delete started');

        $designationSubGroup = DesignationSubGroup::onlyTrashed()->find($id);
        $designationSubGroup->forceDelete();

        $notification = array(
            'message' => 'Designation Sub Group Permenetly Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('designation.sub.group.index')->with($notification);

        Log::emergency('DesignationSubGroupController -> delete designation sub group id - ' . $designationSubGroup->id . ' deleted by ' . auth()->user()->id);
        Log::info('DesignationSubGroupController -> designation sub group delete ended');
    }
}
