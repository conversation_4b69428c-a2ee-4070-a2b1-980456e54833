<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('annual_leaves', function (Blueprint $table) {
            $table->id();
            $table->integer('empNo');
            $table->date('from_date');
            $table->date('to_date');
            $table->float('leave_count')->default(0);
            $table->integer('leave_type')->default(0);
            $table->string('ma_remark')->nullable();
            $table->integer('year');
            $table->integer('month');
            $table->integer('month_code')->default(0);
            $table->integer('dept_code')->default(0);
            $table->integer('faculty_code')->default(0);
            $table->integer('submitted_status')->default(0);
            $table->integer('ma_forward_status')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('annual_leaves');
    }
};
