<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('non_increments', function (Blueprint $table) {
            $table->decimal('paid_amount', 10, 2)->nullable()->after('salary_scale_version_id');
            $table->decimal('unpaid_amount', 10, 2)->nullable()->after('paid_amount');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('non_increments', function (Blueprint $table) {
            $table->dropColumn('paid_amount');
            $table->dropColumn('unpaid_amount');
        });
    }
};
