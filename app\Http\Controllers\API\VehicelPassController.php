<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class VehicelPassController extends Controller
{
    public function empDetailsGet(Request $request)
    {

        $validPassword = '7mQM4qwM7QcilNd';

        // Check if the request contains a password
        if (!$request->has('password')) {
            return response()->json(['error' => 'Password required'], 400);
        }

        // Extract password from the request
        $password = $request->input('password');

        // Check if the provided password matches the hardcoded password
        if ($password !== $validPassword) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // If the password is correct, proceed to fetch employee details
        $data = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as title', 'employees.title_id', '=', 'title.id')
            ->join('categories as employee_type', 'employees.employee_work_type', '=', 'employee_type.id')
            ->leftJoin('faculties', 'employees.faculty_id', '=', 'faculties.id') // Using leftJoin instead of join
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->select(
                'employees.employee_no',
                'employees.email',
                'employees.last_name',
                'employees.name_denoted_by_initials',
                'employees.nic',
                'designations.designation_name',
                DB::raw('CASE WHEN employees.faculty_id IN (50, 51) THEN "None" ELSE faculties.faculty_name END AS faculty_name'), // Using a CASE statement to handle the condition
                'departments.department_name',
                'employees.initials',
                'employees.mobile_no',
                'title.category_name as title',
                'employee_type.category_name as employee_type',
                'employees.employee_work_type',
                'employees.salary_termination_date_1 as termination_date_service',
                'employees.salary_termination_date_2 as termination_date_leave'
            )
            ->where('employees.employee_no', $request->empNo)
            //->where('employees.employee_status_id', 110)
            ->get();


        return $data;
    }

    public function allEmp(Request $request)
    {
        $empIDs = $request->input('empIDs', []);

        $data = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->leftJoin('faculties', function ($join) {
                $join->on('employees.faculty_id', '=', 'faculties.id')
                    ->whereNotIn('faculties.id', [50, 51]); // Exclude faculty_id 50 and 51
            })
            ->select(
                'employees.employee_no',
                'employees.initials',
                'employees.last_name',
                'employees.name_denoted_by_initials',
                'categories.category_name as title',
                'employees.nic',
                'departments.department_name',
                DB::raw('CASE WHEN employees.faculty_id IN (50, 51) THEN "None" ELSE faculties.faculty_name END AS faculty_name'),
                'employees.mobile_no',
                'employees.employee_status_id'
            )
            ->whereIn('employees.employee_no', $empIDs)
            ->get();


        return $data;
    }

    public function eligibilityCount()
    {
        $data =  Employee::where('employees.employee_status_id', 110)->count();

        return $data;
    }
}
