<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateConEmployeeRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }


    public function rules()
    {
        $rules['employee_no'] = 'required';
        $rules['nic'] = ['required','regex:/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/m'];
        $rules['titel_id'] = 'required';
        $rules['initials'] = 'required|regex:/^([A-Z]\.)+$/';
        $rules['name_denoted_by_initials'] = 'required';
        $rules['last_name'] = 'required';
        $rules['mobile_no'] = 'required|max:12|min:10';
        $rules['phone_no'] = 'nullable|max:12|min:10';
        $rules['personal_email'] = 'nullable|email:rfc,dns';
        $rules['email'] = 'nullable|email:rfc,dns|ends_with:@sjp.ac.lk';
        $rules['personal_email'] = 'nullable|email:rfc,dns';
        $rules['date_of_birth'] = 'required|date|before:-18 years';
        $rules['gender_id'] = 'required';
        $rules['civil_status_id'] = 'required';
        //$rules['race_id'] = 'required';
        //$rules['religion_id'] = 'required';
        $rules['permanent_add1'] = 'required';
        $rules['permanent_city_id'] = 'required';
        $rules['postal_add1'] = 'required';
        $rules['postal_city_id'] = 'required';
        $rules['citizenship'] = 'required';
        $rules['state_of_citizenship_id'] = 'required';

        if($this->get('state_of_citizenship_id') == 26){

            $rules['citizen_registration_no'] = 'required';

        }

        $rules['main_branch_id'] = 'required';
        $rules['faculty_id'] = 'required';
        $rules['department_id'] = 'required';
        $rules['carder_faculty_id'] = 'required';
        $rules['carder_department_id'] = 'required';
        $rules['designation_id'] = 'required';
        $rules['current_basic_salary'] = ['required','regex:/^\d{1,13}(\.\d{1,4})?$/'];
        $rules['current_appointment_date'] = 'required|date|before_or_equal:today';
        $rules['salary_termination_date_1'] = 'required|date';
        $rules['emp_highest_edu_level'] = 'required';
        $rules['employee_status_id'] = 'required';
        $rules['employee_status_type_id'] = 'required';
        //$rules['employee_work_type'] = 'required';
        $rules['sabbatical_start'] = 'nullable|date|before_or_equal:today';
        $rules['sabbatical_end'] = 'nullable|date';
        $rules['salary_payment_type'] = 'required';

        return $rules;

    }

    public function messages()
    {
        return [
            'employee_no.required' => 'the employee number required',
            'nic.required' => 'the employee nic required',
            'mobile_no.required' => 'the mobile number required',
            'mobile_no.integer' => 'the mobile only be numbers',
            'mobile_no.min' => 'you entered phone number in invalid format',
            'mobile_no.max' => 'you entered phone number in invalid format',
            'email.ends_with' => 'accept university personal mail only',
            'titel_id.required' => 'the title required',
            'initials.required' => 'the name initials required',
            'initials.regex' => 'name initials incorrect formatted',
            'name_denoted_by_initials.required' => 'the initials denoted name required',
            'last_name.required' => 'the last name required',
            'phone_no.integer' => 'the phone number only be numbers',
            'phone_no.max' => 'you entered phone number in invalid format',
            'phone_no.min' => 'you entered phone number in invalid format',
            'date_of_birth.required' => 'the birthday required',
            'date_of_birth.date' => 'the birthday must be date',
            'date_of_birth.before' => 'Age must be 18 or more',
            'gender_id.required' => 'the gender required',
            'civil_status_id.required' => 'the civil status required',
            'race_id.required' => 'the race required',
            'religion_id.required' => 'the religion required',
            'permanent_add1.required' => 'the permanent address required',
            'permanent_city_id.required' => 'the permanent city required',
            'postal_add1.required' => 'the postal address required',
            'postal_city_id.required' => 'the postal city required',
            'citizenship.required' => 'the citizenship required',
            'state_of_citizenship_id.required' => 'the citizenship type required',
            'main_branch_id.required' => 'employee main branch required',
            'designation_id.required' => 'employee designation required',
            'faculty_id.required' => 'employee working faculty required',
            'department_id.required' => 'employee working department required',
            'carder_faculty_id.required' => 'employee carder faculty required',
            'carder_department_id.required' => 'employee carder department required',
            'current_basic_salary.required' => 'employee total salary required',
            'current_appointment_date.required' => 'contrat start date required',
            'current_appointment_date.date' => 'contract start date must be valid date',
            'salary_termination_date_1.required' => 'contract end date required',
            'salary_termination_date_1.date' => 'contract end date must be valid date',
            'emp_highest_edu_level.required' => 'highest educational qualification required',
            'sabbatical_start.date' => 'sabbatical start date must be valid date',
            'sabbatical_end.date' => 'sabbatical end date must be valid date',
            'employee_status_id.required' => 'employee current working status required',
            'employee_status_type_id.required' => 'employee current working status type required',
            'employee_work_type.required' => 'employee current working type required',
            'salary_payment_type.required' => 'employee salary paymenmt type required',
        ];

    }
}
