<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\LoginHistory;
use App\Providers\RouteServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session as FacadesSession;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('auth.admin_login');
    }

    /**
     * Handle an incoming authentication request.
     *
     * @param  \App\Http\Requests\Auth\LoginRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(LoginRequest $request)
    {
        $request->authenticate();

        $request->session()->regenerate();

        LoginHistory::create([
            'user_id' => auth()->user()->id,
            'employee_no' => auth()->user()->employee_no,
            'email' => auth()->user()->email,
            'ip' => trim(shell_exec("dig +short myip.opendns.com @resolver1.opendns.com")),
            //'ip' => '*************',
            'login_time' => now(),
        ]);

        return redirect()->intended(RouteServiceProvider::HOME);
    }

    /**
     * Destroy an authenticated session.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        //FacadesSession::flush();

        LoginHistory::where('user_id', '=', auth()->user()->id)->orderBy('created_at', 'desc')->limit(1)->update([
            'logout_time' => now(),
            'updated_at' => now(),
        ]);

        Auth::guard('web')->logout();

        if (config('app.env') === 'production'){
            $response = Http::get("https://hrms.sjp.ac.lk" .  "/user_logout");
        }



        $request->session()->invalidate();

        $request->session()->regenerateToken();

        if (config('app.env') === 'production'){
        return redirect('/sso/login');
        }else{
        return redirect('/admin/login');
        }

    }
}
