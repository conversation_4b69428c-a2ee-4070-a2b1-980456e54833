<?php

namespace App\Http\Controllers\Backend\Transfer;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Models\InternalTransfer;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class InternalTransferNacController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function eligibleListOpen(Request $request)
    {
        if (isset($request->effDate) && isset($request->years)) {

            $pass_date = Carbon::parse($request->effDate);
            $pass_years = $request->years;

            if (Auth()->user()->hasRole(['administrator', 'est-head'])) {

                $emp_text = Employee::join('categories as title_table', 'title_table.id', '=', 'employees.title_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                    ->select(
                        'employees.employee_no',
                        'employees.initials',
                        'employees.last_name',
                        'employees.faculty_id',
                        'title_table.category_name as title',
                        'designations.designation_name',
                        'g.category_name as grade',
                        'departments.department_name',
                        'faculties.faculty_name'
                    )
                    ->where('employees.main_branch_id', 53)
                    ->where('employees.employee_status_id', 110)
                    ->get();

            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $emp_text = Employee::join('categories as title_table', 'title_table.id', '=', 'employees.title_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                    ->select(
                        'employees.employee_no',
                        'employees.initials',
                        'employees.last_name',
                        'employees.faculty_id',
                        'title_table.category_name as title',
                        'designations.designation_name',
                        'g.category_name as grade',
                        'departments.department_name',
                        'faculties.faculty_name'
                    )
                    ->where('employees.main_branch_id', 53)
                    ->where('employees.employee_status_id', 110)
                    ->where('employees.assign_ma_user_id', '=', auth()->user()->employee_no)
                    ->get();
            } else {
                $emp_text = array();
            }

            if (count($emp_text) > 0) {

                $data = [];

                foreach ($emp_text as $emp_texts) {

                    // $tran_text = InternalTransfer::select('emp_no', 'MAX(transfer_date) AS last_transfer_date')
                    //     ->where('emp_no', '=', $emp_texts->employee_no)
                    //     ->get();

                    // if (count($tran_text) > 0) {
                    //     foreach ($tran_text as $tran_texts) {
                    //         $trans_date = Carbon::parse($tran_texts->last_transfer_date);
                    //         $diffInYears = $trans_date->diffInYears($pass_date);
                    //     }
                    // } else {
                    //     $diffInYears = 0;
                    // }

                    // if ($diffInYears >= $pass_years) {

                    //     // $dataSEt = new \stdClass();

                    //     // $dataSEt->empNo = $emp_texts->employee_no;
                    //     // $dataSEt->name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                    //     // $dataSEt->designation = $emp_texts->designation_name . ' ' . $emp_texts->grade;
                    //     // $dataSEt->dep = $emp_texts->department_name;
                    //     // $dataSEt->fac = $emp_texts->faculty_name;
                    //     // $dataSEt->tDate = $trans_date;
                    //     // $dataSEt->years = $diffInYears;

                    //     // $data[] = $dataSEt;
                    // }
                }
            } else {
                $data = [];
            }
        } else {
            $pass_date = '';
            $pass_years = '';
            $data = [];
            $emp_text = '';
        }

        return view('admin.transfer.nac_internal_transfer.eligibility', compact('pass_date', 'pass_years', 'data', 'emp_text'));
    }
}
