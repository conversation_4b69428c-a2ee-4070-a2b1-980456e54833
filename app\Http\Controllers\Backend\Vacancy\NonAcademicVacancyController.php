<?php

namespace App\Http\Controllers\Backend\Vacancy;

use App\Http\Controllers\Controller;
use App\Models\AdvanceLevelResult;
use App\Models\AdvanceLevelResultSummary;
use App\Models\ApplicationForm;
use App\Models\City;
use App\Models\Designation;
use App\Models\nav_experience;
use App\Models\nav_higher_eduacation_qulification;
use App\Models\nav_pdf_upload_path;
use App\Models\nav_professional_qulifications;
use App\Models\OrdinaryLevelResult;
use App\Models\OrdinaryLevelResultSummary;
use App\Models\User;
use App\Models\VancancyNonAcademic;
use App\Models\VancancyOperatorNonAcademic;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf as FacadePdf;

class NonAcademicVacancyController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc|head');
    }

    public function VacancyIndex()
    {
        Log::info('VacancyController -> vacancy index started');

        $mainBranch = Auth()->user()->main_branch_id;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {

            $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                         ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                         ->select('vancancy_non_academics.*','categories.display_name')
                         ->orderByDesc('vancancy_non_academics.id')
                         ->get();
            //dd($vacancies);

            Log::info('VacancyController -> vacancies Count - ' . $vacancies->count());
            Log::info('VacancyController -> vacancy index ended');

        }  elseif ($mainBranch == 53) {

            $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                        ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                        ->select('vancancy_non_academics.*','categories.display_name')
                        ->orderByDesc('vancancy_non_academics.id')
                        ->get();

            Log::info('VacancyController -> vacancies Count - ' . $vacancies->count());
            Log::info('VacancyController -> vacancy index ended');
        }

        return view('admin.nonacademic_vacancy.index', compact('vacancies'));
    }

    public function VacancyAdd()
    {
        Log::info('VacancyController -> vacancy add started');
        $categories = $this->getCategories([34]);
        $vacancyVisibility = $categories->where('category_type_id', '34');
        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->where('designations.active_status',1)
                          ->where('designations.designation_division',53)
                          ->get();

        }  elseif ($mainBranch == 53) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->where('designations.active_status',1)
                          ->where('designations.designation_division',53)
                          ->get();
        }

        if ($mainBranch == 51) {

            $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                        ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                        ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                        ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->where('users.main_branch_id',53)
                        ->whereIn('roles.id', [5, 6])
                        ->distinct()
                        ->get();


        }else if($mainBranch == 53){

            if (Auth()->user()->hasRole(['est-head'])) {

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                            ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                            ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                            ->where('users.main_branch_id',53)
                            ->whereIn('roles.id', [5, 6])
                            ->distinct()
                            ->get();

            }

        }

        Log::info('VacancyController -> vacancy add ended');
        return view('admin.nonacademic_vacancy.add', compact('designations', 'vacancyVisibility','operators'));
    }

    public function VacancyStore(Request $request)
    {
        $request->validate([
            'designation_id' => 'required',
            'designation_category' => 'required',
            'vacancy_visibility_status' => 'required',
            'date_opened' => 'required|date|before_or_equal:date_closed',
            'date_closed' => 'required|date|after_or_equal:date_opened',
            'min_age' =>'required',
            'max_age' =>'required',
            'operator' => 'required'
        ], [
            'main_category_id.required' => 'The category field is required.',
            'designation_id.required' => 'The designation field is required.',
            'vacancy_visibility_status.required' => 'The visibility status field is required.',
            'date_opened.required' => 'The opening date field is required.',
            'date_opened.before_or_equal' => 'The opening date must be before or equal to closing date.',
            'date_closed.required' => 'The closing date field is required.',
            'date_closed.after_or_equal' => 'The closing date must be after or equal to opening date.',
            'operator.required' => 'At least one operator must be selected.',
            'designation_category.required' => 'The designation category field is required.',
            'min_age.required' => 'The minimum age field is required.',
            'max_age.required' => 'The maximum age field is required.',
        ]);

        Log::info('VacancyController -> Vacancy store started');

        $maxnumber = DB::table('vancancy_non_academics')
                    ->select(DB::raw('MAX(id) as value'))
                    ->get();

        $maxValue = json_decode($maxnumber, true);
        $nextId = $maxValue[0]["value"] + 1;

        $data = new VancancyNonAcademic();
        $data->designation_id = $request->designation_id;
        $data->designation_category = $request->designation_category;
        $data->vacancy_visibility_status = $request->vacancy_visibility_status;
        $data->date_opened = date("Y-m-d", strtotime($request->date_opened));
        $data->date_closed = date("Y-m-d", strtotime($request->date_closed));
        $data->min_age = $request->min_age;
        $data->max_age = $request->max_age;
        $data->published_user_id = Auth()->user()->employee_no;
        $data->published_date = date("Y-m-d");
        $data->vacancy_status_id = 0;
        $data->save();

        for ($i = 0; $i < count($request->operator); $i++) {

        $data1 = new VancancyOperatorNonAcademic();
        $data1->vacancy_id = $data->id;
        $data1->employee_no = $request->operator[$i];
        $data1->save();
        }

        Log::notice('VacancyController -> Created vacancy id - ' . $data->id . ' created by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> Vacancy create ended');

        $notification = array(
            'message' => 'New Vacancy Created Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('nonacademic.vacancy.add')->with($notification);
    }

    public function VacancyEdit($id)
    {
        Log::info('VacancyController -> vacancy edit started');

        $vacancyId = decrypt($id);
        $editData = VancancyNonAcademic::find($vacancyId);

        $categories = $this->getCategories([34]);
        $vacancyVisibility = $categories->where('category_type_id', '34');
        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->where('designations.active_status',1)
                          ->get();

        }  elseif ($mainBranch == 53) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->where('designations.active_status',1)
                          ->where('designations.designation_division',53)
                          ->get();

        }

        $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                        ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                        ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                        ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->where('users.main_branch_id',53)
                        ->whereIn('roles.id', [5, 6])
                        ->distinct()
                        ->get();




        $oldOperators = VancancyOperatorNonAcademic::where('vacancy_id',$editData->id)->get();


        Log::notice('VacancyController -> edit vacancy id - ' . $vacancyId . ' edited by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy edit ended');


        return view('admin.nonacademic_vacancy.edit', compact('editData','designations', 'vacancyVisibility','operators','oldOperators'));
    }

    public function VacancyUpdate(Request $request, $id)
    {

        Log::info('VacancyController -> vacancy update started');

        $request->validate([
            'designation_id' => 'required',
            'designation_category' => 'required',
            'vacancy_visibility_status' => 'required',
            'date_opened' => 'required|date|before_or_equal:date_closed',
            'date_closed' => 'required|date|after_or_equal:date_opened',
            'min_age' =>'required',
            'max_age' =>'required',
            //'operator' => 'required'
        ], [
            'main_category_id.required' => 'The category field is required.',
            'designation_id.required' => 'The designation field is required.',
            'vacancy_visibility_status.required' => 'The visibility status field is required.',
            'date_opened.required' => 'The opening date field is required.',
            'date_opened.before_or_equal' => 'The opening date must be before or equal to closing date.',
            'date_closed.required' => 'The closing date field is required.',
            'date_closed.after_or_equal' => 'The closing date must be after or equal to opening date.',
            'designation_category.required' => 'The designation category field is required.',
            'min_age.required' => 'The minimum age field is required.',
            'max_age.required' => 'The maximum age field is required.',
        ]);

        $data = VancancyNonAcademic::find($id);
        $data->designation_category = $request->designation_category;
        $data->designation_id = $request->designation_id;
        $data->vacancy_visibility_status = $request->vacancy_visibility_status;
        $data->date_opened = date("Y-m-d", strtotime($request->date_opened));
        $data->date_closed = date("Y-m-d", strtotime($request->date_closed));
        $data->min_age = $request->min_age;
        $data->max_age = $request->max_age;
        $data->updated_user_id = Auth()->user()->employee_no;
        $data->updated_date = date("Y-m-d");
        $data->updated_at = Carbon::now();
        $data->save();

        if ($request->newoperator != null) {

        for ($i = 0; $i < count($request->newoperator); $i++) {

            $data1 = new VancancyOperatorNonAcademic();
            $data1->vacancy_id = $data->id;
            $data1->employee_no = $request->newoperator[$i];
            $data1->save();
        }

        }

        Log::notice('VacancyController -> update vacancy id - ' . $data->id . ' updated by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy update ended');

        $notification = array(
            'message' => 'Vacancy data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('nonacademic.vacancy.index')->with($notification);
    }

    public function VacancyDelete($id)
    {
        try {
            DB::beginTransaction();
            Log::info('VacancyController -> vacancy soft delete started');

            $vacancyId = decrypt($id);
            $vacancy = VancancyNonAcademic::find($vacancyId);

            // Delete associated operators first
            VancancyOperatorNonAcademic::where('vacancy_id', $vacancy->id)->delete();

            // Then delete the vacancy
            $vacancy->delete();

            DB::commit();

            $notification = array(
                'message' => 'Vacancy and Associated Operators Deleted Successfully',
                'alert-type' => 'warning'
            );

            Log::notice('VacancyController -> delete vacancy id - ' . $vacancy->id . ' and its operators deleted by ' . auth()->user()->employee_no);
            Log::info('VacancyController -> vacancy soft delete ended');

            return redirect()->route('nonacademic.vacancy.index')->with($notification);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('VacancyController -> Error deleting vacancy: ' . $e->getMessage());

            $notification = array(
                'message' => 'Error Deleting Vacancy',
                'alert-type' => 'error'
            );
            return redirect()->route('nonacademic.vacancy.index')->with($notification);
        }
    }

    public function VacancyShow($id){

        $vacancyId = decrypt($id);
        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
        ->join('categories', 'designations.staff_grade', '=', 'categories.id')
        ->select('vancancy_non_academics.*','categories.display_name')
        ->find($vacancyId);

        $vacancyOperator = VancancyOperatorNonAcademic::where('vacancy_id',$vacancyId)->get();

        $completeApplications = ApplicationForm::where('vacancy_id',$vacancyId)->where('protect_application',1)->get();

        return view('admin.nonacademic_vacancy.show', compact('vacancy','vacancyOperator','completeApplications'));

    }

    public function VacancyOperatorDelete($id)
    {
        try {
            DB::beginTransaction();

            $operator = VancancyOperatorNonAcademic::findOrFail($id);
            $operator->delete();

            DB::commit();
            $notification = array(
                'message' => 'Operator Deleted Successfully',
                'alert-type' => 'success'
            );

            Log::info('NonAcademicVacancyController -> Operator deleted successfully. ID: ' . $id);
            return redirect()->back()->with($notification);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('NonAcademicVacancyController -> Error deleting operator: ' . $e->getMessage());

            $notification = array(
                'message' => 'Error Deleting Operator',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
        }
    }

    public function VacancyInactive($id)
    {

        Log::info('VacancyController -> vacancy inactive started');
        $vacancyId = decrypt($id);
        $vacancy = VancancyNonAcademic::find($vacancyId);
        $vacancy->vacancy_status_id = 0;
        $vacancy->save();

        $notification = array(
            'message' => 'Academic Support Vacancy Inactive successfully',
            'alert-type' => 'success'
        );

        Log::notice('VacancyController -> inactive vacancy id - ' . $vacancyId . ' inactivate by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> Vacancy inactive ended');

        return redirect()->back()->with($notification);
    } //end method

    public function VacancyActive($id)
    {

        Log::info('VacancyController -> vacancy active started');
        $vacancyId = decrypt($id);
        $vacancy = VancancyNonAcademic::find($vacancyId);
        $vacancy->vacancy_status_id = 1;
        $vacancy->save();

        $notification = array(
            'message' => 'Academic Support Vacancy Active successfully',
            'alert-type' => 'success'
        );

        Log::notice('VacancyController -> active vacancy id - ' . $vacancyId . ' activate by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy active ended');


        return redirect()->back()->with($notification);
    } //end method

    public function VacancyExtend($id)
    {
        Log::info('VacancyController -> vacancy extend started');

        $vacancyId = decrypt($id);
        $editData = VancancyNonAcademic::find($vacancyId);
        $categories = $this->getCategories([34]);
        $vacancyVisibility = $categories->where('category_type_id', '34');
        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->get();

        } elseif ($mainBranch == 53) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->get();
        }

        Log::notice('VacancyController -> extend vacancy id - ' . $vacancyId . ' edited by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy extend ended');


        return view('admin.nonacademic_vacancy.extend', compact('editData', 'designations', 'vacancyVisibility'));
    }

    public function VacancyExtendUpdate(Request $request, $id)
    {

        Log::info('VacancyController -> vacancy extend update started');

        $validatedData = $request->validate([

            'date_closed' => 'required|date|after:today'
        ]);

        $data = VancancyNonAcademic::find($id);
        $data->date_closed = date("Y-m-d", strtotime($request->date_closed));
        $data->vacancy_status_type_id = 259;
        $data->deadline_extented_status = 1;
        $data->deadline_extented_emp = Auth()->user()->employee_no;
        $data->deadline_extented_date = date("Y-m-d");
        $data->updated_at = Carbon::now();
        $data->save();


        Log::notice('VacancyController ->vacancy date extend id - ' . $data->id . ' updated by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy extend update ended');

        $notification = array(
            'message' => 'Vacancy date extended Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('nonacademic.vacancy.index')->with($notification);
    }

    public function VacancyOperatorView()
    {
        $mainBranch = Auth()->user()->main_branch_id;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {

            $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                         ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                         ->select('vancancy_non_academics.*','categories.display_name')
                         ->orderByDesc('vancancy_non_academics.id')
                         ->get();


        } elseif ($mainBranch == 53) {

            $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                        ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                        ->select('vancancy_non_academics.*','categories.display_name')
                        ->orderByDesc('vancancy_non_academics.id')
                        ->get();

        }

        return view('admin.nonacademic_vacancy.operator', compact('vacancies'));
    }

    public function VacancyOperatorList($id){

        $vacancyId = decrypt($id);
        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                   ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                   ->select('vancancy_non_academics.*','categories.display_name')
                   ->find($vacancyId);
        $vacancyOperator = VancancyOperatorNonAcademic::where('vacancy_id',$vacancyId)->get();

        return view('admin.nonacademic_vacancy.operator_list', compact('vacancyOperator','vacancy'));
    }

    public function VacancyOperatorAdd($id){

        $vacancyId = decrypt($id);
        $mainBranch = Auth()->user()->main_branch_id;

        $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                         ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                         ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                         ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                         ->whereIn('roles.id', [5, 6])
                         ->where('users.main_branch_id', 53)
                         ->leftJoin('vancancy_operator_non_academics as vo', function($join) use ($vacancyId) {
                                  $join->on('employees.employee_no', '=', 'vo.employee_no')
                                  ->where('vo.vacancy_id', $vacancyId);
                          })->whereNull('vo.employee_no')
                          ->distinct()
                          ->get();



        return view('admin.nonacademic_vacancy.operator_add', compact('operators','vacancyId'));

    }

    public function VacancyOperatorStore(Request $request){

        $validatedData = $request->validate([
            'operator' => 'required',
        ]);

        $data = new VancancyOperatorNonAcademic();
        $data->vacancy_id = $request->vacancy_id;
        $data->employee_no = $request->operator;
        $data->save();

        $notification = array(
            'message' => 'New Vacancy Operator Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('nonacademic.vacancy.operator.view')->with($notification);
    }

    public function VacancyOperatorNewDelete($id)
    {

        Log::info('VacancyController -> vacancy operator delete started');

        //$operastorId = decrypt();
        $data = VancancyOperatorNonAcademic::find($id);
        $data->delete();

        Log::emergency('VacancyController -> delete vacancy operator id - ' . $data->id . ' deleted by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy operator delete ended');

        return response()->json(['message' => 'Record deleted successfully']);
    }

    public function VacancyApplicationShow(Request $request)
    {
        $appID = $request->input('app_id');
        // Find the application
        $applicationForm = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->select(
                'application_forms.*',
                't.category_name as title_name',
                'cs.category_name as civil_status_name'
            )
            ->where('application_forms.id', $appID)
            ->first();

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        // Get vacancy information with designation name
        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'designations.designation_name', 'categories.display_name')
            ->find($applicationForm->vacancy_id);

        // Get O/L results
        $olResults = OrdinaryLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 1)->get();

        $olResults2 = OrdinaryLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 2)->get();

        // Get O/L summaries
        $olSummary = OrdinaryLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 1)->first();

        $olSummary2 = OrdinaryLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 2)->first();

        // Get A/L results
        $alResults = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 1)->get();

        $alResults2 = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 2)->get();

        $alResults3 = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 3)->get();

        // Get A/L summaries
        $alSummary = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 1)->first();

        $alSummary2 = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 2)->first();

        $alSummary3 = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 3)->first();

        // Get higher education qualifications
        $higherEducation = nav_higher_eduacation_qulification::where('result_slot_id', $applicationForm->id)->get();

        // Get professional qualifications
        $proQualifications = nav_professional_qulifications::where('user_id', $applicationForm->id)->get();

        // Get experience information
        $experiences = nav_experience::where('user_id', $applicationForm->id)->get();

        // Get extra curricular data
        $extraCurricular = DB::table('nav_extra_curricular')->where('user_id', $applicationForm->id)->first();

        // Get all uploaded documents grouped by category
        $documents = nav_pdf_upload_path::where('user_id', $applicationForm->id)->get();

        $documentsByCategory = [
            'higher_education' => $documents->where('nav_cat_type', 1),
            'professional_qualifications' => $documents->where('nav_cat_type', 2),
            'experience' => $documents->where('nav_cat_type', 3),
            'extra_curricular' => $documents->where('nav_cat_type', 4),
            'public_sector' => $documents->where('nav_cat_type', 5),
            'driver_licenses' => $documents->where('nav_cat_type', 6),
        ];

        // Load reference data
        $titles = DB::table('categories')->where('category_type_id', '=', 5)->get();
        $civilStatuses = DB::table('categories')->where('category_type_id', '4')->get();
        $cities = City::all();
        $degreeTypes = DB::table('categories')->where('category_type_id', '16')->get();
        $alStreams = DB::table('categories')->where('category_type_id', '15')->get();

        return view('admin.nonacademic_vacancy.application_web_view', compact(
            'appID',
            'applicationForm',
            'vacancy',
            'olResults',
            'olResults2',
            'olSummary',
            'olSummary2',
            'alResults',
            'alResults2',
            'alResults3',
            'alSummary',
            'alSummary2',
            'alSummary3',
            'higherEducation',
            'proQualifications',
            'experiences',
            'extraCurricular',
            'documents',
            'documentsByCategory',
            'titles',
            'civilStatuses',
            'cities',
            'degreeTypes',
            'alStreams'
        ));
    }

    public function applicationReportDownload(Request $request)
    {
        $appID = $request->input('app_id');

        // Get application form data with relationships
        $applicationForm = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->select(
                'application_forms.*',
                't.category_name as title_name',
                'cs.category_name as civil_status_name'
            )
            ->where('application_forms.id', $appID)
            ->first();

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        // Get vacancy information with designation name
        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'designations.designation_name', 'categories.display_name')
            ->find($applicationForm->vacancy_id);

        // Get O/L results
        $olResults = OrdinaryLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 1)->get();
        $olResults2 = OrdinaryLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 2)->get();

        // Get O/L summaries
        $olSummary = OrdinaryLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 1)->first();
        $olSummary2 = OrdinaryLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 2)->first();

        // Get A/L results
        $alResults = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 1)->get();
        $alResults2 = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 2)->get();
        $alResults3 = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 3)->get();

        // Get A/L summaries
        $alSummary = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 1)->first();
        $alSummary2 = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 2)->first();
        $alSummary3 = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 3)->first();

        // Get higher education qualifications
        $higherEducation = nav_higher_eduacation_qulification::where('result_slot_id', $applicationForm->id)->get();

        // Get professional qualifications
        $proQualifications = nav_professional_qulifications::where('user_id', $applicationForm->id)->get();

        // Get experience information
        $experiences = nav_experience::where('user_id', $applicationForm->id)->get();

        // Get extra curricular data
        $extraCurricular = DB::table('nav_extra_curricular')->where('user_id', $applicationForm->id)->first();

        // Get all uploaded documents grouped by category
        $documents = nav_pdf_upload_path::where('user_id', $applicationForm->id)->get();
        $documentsByCategory = [
            'higher_education' => $documents->where('nav_cat_type', 1),
            'professional_qualifications' => $documents->where('nav_cat_type', 2),
            'experience' => $documents->where('nav_cat_type', 3),
            'extra_curricular' => $documents->where('nav_cat_type', 4),
            'public_sector' => $documents->where('nav_cat_type', 5),
            'driver_licenses' => $documents->where('nav_cat_type', 6),
        ];

        // Load reference data
        $titles = DB::table('categories')->where('category_type_id', '=', 5)->get();
        $civilStatuses = DB::table('categories')->where('category_type_id', '4')->get();
        $cities = City::all();
        $degreeTypes = DB::table('categories')->where('category_type_id', '16')->get();
        $alStreams = DB::table('categories')->where('category_type_id', '15')->get();


        $data = [
            'applicationForm' => $applicationForm,
            'vacancy' => $vacancy,
            'olResults' => $olResults,
            'olResults2' => $olResults2,
            'olSummary' => $olSummary,
            'olSummary2' => $olSummary2,
            'alResults' => $alResults,
            'alResults2' => $alResults2,
            'alResults3' => $alResults3,
            'alSummary' => $alSummary,
            'alSummary2' => $alSummary2,
            'alSummary3' => $alSummary3,
            'higherEducation' => $higherEducation,
            'proQualifications' => $proQualifications,
            'experiences' => $experiences,
            'extraCurricular' => $extraCurricular,
            'documentsByCategory' => $documentsByCategory,
            'titles' => $titles,
            'civilStatuses' => $civilStatuses,
            'cities' => $cities,
            'degreeTypes' => $degreeTypes,
            'alStreams' => $alStreams
        ];

        $pdf = FacadePdf::loadView('admin.nonacademic_vacancy.application_pdf_view', $data)->setPaper('a4');

        return $pdf->stream($applicationForm->reference_no . ' - Application.pdf');
    }
}
