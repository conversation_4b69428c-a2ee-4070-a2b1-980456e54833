<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('scale_version_designations', function (Blueprint $table) {
            $table->id();
            $table->integer('desig_id');
            $table->integer('scale_id');
            $table->integer('version_id');
            $table->timestamps();

            $table->unique(['desig_id', 'scale_id', 'version_id'], 'unique_designation_scale_version');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('scale_version_designations');
    }
};
