<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('academic_years', function (Blueprint $table) {
            $table->dropColumn('sem1_start_date');
            $table->dropColumn('sem1_end_date');
            $table->dropColumn('sem2_start_date');
            $table->dropColumn('sem2_end_date');   
            $table->date('start_date')->nullable()->after('academic_year_name'); 
            $table->date('end_date')->nullable()->after('start_date');        
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('academic_years', function (Blueprint $table) {
            //
            $table->date('sem1_start_date');
            $table->date('sem1_end_date');
            $table->date('sem2_start_date');
            $table->date('sem2_end_date');
            $table->dropColumn('start_date'); 
            $table->dropColumn('end_date');
        });
    }
};
