<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class DutyAssumeEmployee extends Notification implements ShouldQueue
{
    use Queueable;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }


    public function via($notifiable)
    {
        return ['database','mail'];
    }


    public function toMail($notifiable)
    {
        return (new MailMessage)
                    ->from('<EMAIL>', 'USJ HRM SYSTEM')
                    ->greeting('Applicant to Employee')
                    ->line('Convert Applicant to Employee with Referance Number - '.$this->data['reference_no'])
                    ->action('Applicant Convert as Employee', url('https://hrms.sjp.ac.lk/vacancy/interview/completed/list'))
                    ->line('Thank you for using our application!');
    }


    public function toArray($notifiable)
    {
        return [
            'headline' => 'Applicant to Employee',
            'message' => 'Convert Applicant to Employee with Referance Number - '.$this->data['reference_no'],
            'data' => $this->data,
            'type' => 290
         ];
    }
}
