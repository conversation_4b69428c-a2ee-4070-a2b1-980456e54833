<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vancancy_non_academics', function (Blueprint $table) {

            $table->decimal('written_exam_contribution_percentage')->default(0)->after('written_exam_complete_date');
            $table->decimal('practical_exam_contribution_percentage')->default(0)->after('practical_exam_complete_date');
            $table->decimal('interview_contribution_percentage')->default(0)->after('interview_complete_date');
            $table->decimal('written_exam_cut_off_mark')->default(0)->after('written_exam_contribution_percentage');
            $table->decimal('practical_exam_cut_off_mark')->default(0)->after('practical_exam_contribution_percentage');
            $table->decimal('interview_cut_off_mark')->default(0)->after('interview_contribution_percentage');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vancancy_non_academics', function (Blueprint $table) {

            $table->dropColumn('written_exam_cut_off_mark');
            $table->dropColumn('practical_exam_cut_off_mark');
            $table->dropColumn('interview_cut_off_mark');
            $table->dropColumn('written_exam_contribution_percentage');
            $table->dropColumn('practical_exam_contribution_percentage');
            $table->dropColumn('interview_contribution_percentage');

        });
    }
};
