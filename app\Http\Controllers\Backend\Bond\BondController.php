<?php

namespace App\Http\Controllers\Backend\Bond;

use App\Http\Controllers\Controller;
use App\Models\BondEmp;
use App\Models\BondInstitute;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BondController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function oldBondIndex(Request $request)
    {
        if (isset($request->employee_no)) {

            $mainBranch = Auth()->user()->main_branch_id;
            $search_emp_no = $request->employee_no;

            //admin user data collection
            if ($mainBranch == 51) {

                $employee_bonds = BondEmp::where('emp_no', $search_emp_no)
                    ->where('emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('effictive_date')
                    ->get();

                $bond_institues = BondInstitute::where('emp_no', $search_emp_no)
                    ->where('emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('bond_effictive_date')
                    ->get();

                $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                    ->where('employees.employee_no', $search_emp_no)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->get();

                $row_count = $empfetchDatas->count();
            }
            //academic devision data collection
            elseif ($mainBranch == 52) {

                $employee_bonds = BondEmp::where('emp_no', $search_emp_no)
                    ->where('emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('effictive_date')
                    ->get();

                $bond_institues = BondInstitute::where('emp_no', $search_emp_no)
                    ->where('emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('bond_effictive_date')
                    ->get();

                if (Auth()->user()->hasRole(['est-head','cc'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                } elseif (Auth()->user()->hasRole(['sc'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();
            }
            //non academic division data collection
            elseif ($mainBranch == 53) {

                $employee_bonds = BondEmp::where('emp_no', $search_emp_no)
                    ->where('emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('effictive_date')
                    ->get();

                $bond_institues = BondInstitute::where('emp_no', $search_emp_no)
                    ->where('emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('bond_effictive_date')
                    ->get();


                if (Auth()->user()->hasRole(['est-head','cc'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                } elseif (Auth()->user()->hasRole(['sc'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();
            }

            /********************************************************************************* */

            if ($row_count > 0) {

                foreach ($empfetchDatas as $empData) {

                    $emp_no = $empData->employee_no;
                    $emp_name = $empData->titleName . " " . $empData->initials . " " . $empData->last_name;
                    $designation_name = $empData->designation_name . " " . $empData->gradeName;
                    $department_name = $empData->department_name;
                }
            } else {

                $emp_no = '';
                $emp_name = '';
                $designation_name = '';
                $department_name = '';
                $employee_bonds = array();
                $bond_institues = array();

                $notification = array(
                    'message' => 'employee number not found or employee profile lock',
                    'alert-type' => 'error'
                );

                return redirect()->back()->with($notification);
            }

            $pass_employee_no = $request->employee_no;
        } else {

            $pass_employee_no = '';
            $emp_no = '';
            $emp_name = '';
            $designation_name = '';
            $department_name = '';
            $employee_bonds = array();
            $bond_institues = array();
        }

        $categories = $this->getCategories([24, 25]);
        $bondTypes = $categories->where('category_type_id', '24');
        $bondCategories = $categories->where('category_type_id', '25');

        return view("admin.bond.bond_history", compact('pass_employee_no', 'emp_no', 'emp_name', 'designation_name', 'department_name', 'employee_bonds', 'bondTypes', 'bondCategories', 'bond_institues'));
    }

    public function oldBondStore(Request $request)
    {
        Log::info('BondController -> old employee bond store started');

        if (!isset($request->emp_no)) {
            $notification = array(
                'message' => 'Please Search Employee Before Submit',
                'alert-type' => 'error'
            );

            return redirect()->route('old.bond.index')->with($notification);
        }

        $request->validate([
            'emp_no' => 'required',
            'bond_category_id' => 'required',
            'salary_value' => 'required|numeric',
            'obligated_service_period_year' => 'required|numeric',

        ], [
            'emp_no.required' => 'Please search employee before the enter old bond data'
        ]);

        $data = new BondEmp();
        $data->emp_no = $request->emp_no;
        $data->bond_category_id = $request->bond_category_id;
        $data->bond_type = $request->bond_type;
        $data->salary_value = $request->salary_value;
        $data->scholorship_value = $request->scholorship_value;
        $data->other_value = $request->other_value;
        $data->other_bond_description = $request->other_bond_description;
        $data->total_value = $request->total_value;
        $data->bond_violation_fee = $request->bond_violation_fee;
        $data->obligated_service_period_year = $request->obligated_service_period_year;
        $data->obligated_service_period_month = $request->obligated_service_period_month;
        $data->effictive_date = date("Y-m-d", strtotime($request->effictive_date));

        if ($request->effictive_date != '') {

            if ($request->obligated_service_period_month == 0) {

                $data->end_date = Carbon::createFromFormat('Y-m-d', date("Y-m-d", strtotime($request->effictive_date)))->addYear($request->obligated_service_period_year);
            } else {

                $data->end_date = Carbon::createFromFormat('Y-m-d', date("Y-m-d", strtotime($request->effictive_date)))->addYear($request->obligated_service_period_year)->addMonths($request->obligated_service_period_month);
            }
        } else {

            $data->end_date = "1970-01-01";
        }

        $data->created_at = Carbon::now();
        $data->user_id = Auth()->user()->employee_no;
        if ($data->end_date == "1970-01-01") {
            $data->status = 2;
        } else if ($data->end_date > date("Y-m-d")) {
            $data->status = 1;
        } else {
            $data->status = 0;
        }

        $data->save();


        if ($request->bond_value != null) {

            for ($i = 0; $i < count($request->bond_value); $i++) {

                $bond = new BondInstitute();
                $bond->emp_no = $data->emp_no;
                $bond->bond_id = $data->id;
                $bond->institute_name = $request->institute_name[$i];
                $bond->bond_value = $request->bond_value[$i];
                $bond->bond_year = $request->bond_year[$i];
                $bond->bond_month = $request->bond_month[$i];
                $bond->bond_effictive_date = date("Y-m-d", strtotime($request->bond_effictive_date[$i]));
                $bond->user_id = auth()->user()->employee_no;
                $bond->save();
            }

            Log::notice('BondController -> Created employee bond institute data employee number - ' . $data->emp_no . ' created by ' . auth()->user()->employee_no);
        }

        Log::notice('BondController -> Created employee bond data employee number - ' . $data->emp_no . ' created by ' . auth()->user()->employee_no);
        Log::info('BondController -> old employee bond store ended');

        $notification = array(
            'message' => 'Old Bond data Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('old.bond.index', ['employee_no' => $data->emp_no])->with($notification);
        //return response()->json([ 'success'=> 'Form is successfully submitted!']);
    }

    public function oldBondEdit($id)
    {

        Log::info('BondController -> old bond data edit started');
        $BondId = decrypt($id);
        $categories = $this->getCategories([24, 25]);
        $bondTypes = $categories->where('category_type_id', '24');
        $bondCategories = $categories->where('category_type_id', '25');
        $editData = BondEmp::find($BondId);

        Log::notice('BondController -> edit old bond data id - ' . $editData->id . ' edited by ' . auth()->user()->employee_no);
        Log::info('BondController -> old bond edit ended');

        return view('admin.bond.edit', compact('editData', 'bondTypes', 'bondCategories'));
    }

    public function oldBondUpdate(Request $request, $id)
    {

        Log::info('BondController -> old bond data update started');

        $validatedData = $request->validate([
            'bond_category_id' => 'required',
            //'bond_type' => 'required',
            'salary_value' => 'required|numeric',
            'obligated_service_period_year' => 'required|numeric',
        ]);


        $data = BondEmp::find($id);
        $data->emp_no = $request->emp_no;
        $data->bond_category_id = $request->bond_category_id;
        $data->bond_type = $request->bond_type;
        $data->salary_value = $request->salary_value;
        $data->scholorship_value = $request->scholorship_value;
        $data->other_value = $request->other_value;
        $data->other_bond_description = $request->other_bond_description;
        $data->total_value = $request->total_value;
        $data->bond_violation_fee = $request->bond_violation_fee;
        $data->obligated_service_period_year = $request->obligated_service_period_year;
        $data->obligated_service_period_month = $request->obligated_service_period_month;
        $data->effictive_date = date("Y-m-d", strtotime($request->effictive_date));

        if ($request->effictive_date != '') {

            if ($request->obligated_service_period_month == 0) {

                $data->end_date = Carbon::createFromFormat('Y-m-d', date("Y-m-d", strtotime($request->effictive_date)))->addYear($request->obligated_service_period_year);
            } else {

                $data->end_date = Carbon::createFromFormat('Y-m-d', date("Y-m-d", strtotime($request->effictive_date)))->addYear($request->obligated_service_period_year)->addMonths($request->obligated_service_period_month);
            }
        } else {

            $data->end_date = "1970-01-01";
        }

        $data->created_at = Carbon::now();
        $data->updated_user_id = Auth()->user()->employee_no;
        if ($data->end_date == "1970-01-01") {
            $data->status = 2;
        } else if ($data->end_date > date("Y-m-d")) {
            $data->status = 1;
        } else {
            $data->status = 0;
        }

        $data->save();

        Log::warning('BondController -> update old bond data employee number - ' . $data->emp_no . ' updated by ' . auth()->user()->employee_no);
        Log::info('BondController -> old bond data update ended');

        $notification = array(
            'message' => 'Bond data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('old.bond.index', ['employee_no' => $data->emp_no])->with($notification);
    }

    public function oldBondDelete($id)
    {

        Log::info('BondController -> bond data delete started');
        $BondId = decrypt($id);
        $bondInstitute = BondInstitute::where('bond_id', $BondId);
        $bondInstitute->delete();

        $bond = BondEmp::find($BondId);
        $bond->delete();

        Log::emergency('BondController -> delete bond employee number - ' . $bond->emp_no . ' deleted by ' . auth()->user()->employee_no);
        Log::info('BondController -> bond data delete ended');


        $notification = array(
            'message' => 'Bond data Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('old.bond.index', ['employee_no' => $bond->emp_no])->with($notification);
    }

    public function oldBondInstituteEdit($id)
    {

        Log::info('BondController -> old bond institute data edit started');

        $BondInstituteId = decrypt($id);
        $editData = BondInstitute::find($BondInstituteId);

        Log::notice('BondController -> edit old bond institute data id - ' . $editData->id . ' edited by ' . auth()->user()->employee_no);
        Log::info('BondController -> old bond institute edit ended');

        return view('admin.bond.bond_institute_edit', compact('editData'));
    }

    public function oldBondInstituteUpdate(Request $request, $id)
    {

        Log::info('BondController -> old bond institute data update started');

        $validatedData = $request->validate([
            'institute_name' => 'required',
            'bond_value' => 'required|numeric',
            'bond_year' => 'required|integer',
        ]);


        $bond = BondInstitute::find($id);
        $bond->emp_no = $request->emp_no;
        $bond->bond_id = $request->bond_id;
        $bond->institute_name = $request->institute_name;
        $bond->bond_value = $request->bond_value;
        $bond->bond_year = $request->bond_year;
        $bond->bond_month = $request->bond_month;
        $bond->bond_effictive_date = date("Y-m-d", strtotime($request->bond_effictive_date));
        $bond->updated_user_id = auth()->user()->employee_no;
        $bond->save();


        Log::warning('BondController -> update old bond institute data employee number - ' . $bond->emp_no . ' updated by ' . auth()->user()->employee_no);
        Log::info('BondController -> old bond institute data update ended');

        $notification = array(
            'message' => 'Bond Institute data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('old.bond.index', ['employee_no' => $bond->emp_no])->with($notification);
    }

    public function oldBondInstituteDelete($id)
    {

        Log::info('BondController -> bond institute data delete started');
        $BondInstituteId = decrypt($id);
        $bond = BondInstitute::find($BondInstituteId);
        $bond->delete();

        Log::emergency('BondController -> delete bond institute employee number - ' . $bond->emp_no . ' deleted by ' . auth()->user()->employee_no);
        Log::info('BondController -> bond data delete ended');


        $notification = array(
            'message' => 'Bond Institute data Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('old.bond.index', ['employee_no' => $bond->emp_no])->with($notification);
    }

    public function oldBondInstituteAdd($id)
    {

        Log::info('BondController -> old bond institute data add started');

        $bondId = decrypt($id);
        //$bondId = $id;

        //get head email
        $empNo = BondEmp::where('id', $bondId)->select('emp_no as value')->get();
        $empNo = json_decode($empNo, true);
        $empNo = $empNo[0]["value"];

        Log::notice('BondController -> add old bond institute bond add view employee number - ' . $empNo . ' added by ' . auth()->user()->employee_no);
        Log::info('BondController -> old bond institute add ended');

        return view('admin.bond.bond_institute_add', compact('bondId', 'empNo'));
    }

    public function oldBondInstituteStore(Request $request)
    {

        Log::info('BondController -> old bond institute data store started');

        $validatedData = $request->validate([
            'institute_name' => 'required',
            'bond_value' => 'required|numeric',
            'bond_year' => 'required|integer',
        ]);


        $bond = new BondInstitute();
        $bond->emp_no = $request->emp_no;
        $bond->bond_id = $request->bond_id;
        $bond->institute_name = $request->institute_name;
        $bond->bond_value = $request->bond_value;
        $bond->bond_year = $request->bond_year;
        $bond->bond_month = $request->bond_month;
        $bond->bond_effictive_date = date("Y-m-d", strtotime($request->bond_effictive_date));
        $bond->user_id = auth()->user()->employee_no;
        $bond->save();


        Log::warning('BondController -> store old bond institute data employee number - ' . $bond->emp_no . ' added by ' . auth()->user()->employee_no);
        Log::info('BondController -> old bond institute data store ended');

        $notification = array(
            'message' => 'Bond Institute data Inserted Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('old.bond.index', ['employee_no' => $bond->emp_no])->with($notification);
    }
}
