<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('increments_process_acs', function (Blueprint $table) {
            //
            $table->integer('eb_status')->default(3)->after('desig_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('increments_process_acs', function (Blueprint $table) {
            //
            $table->dropColumn('eb_status');
        });
    }
};
