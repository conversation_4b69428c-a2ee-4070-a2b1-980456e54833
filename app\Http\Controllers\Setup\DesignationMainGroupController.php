<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\DesignationMainGroup;
use App\Models\DesignationSubGroup;
use Illuminate\Validation\Rule;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class DesignationMainGroupController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator');
    }

    public function designationMainGroupIndex()
    {
        Log::info('DesignationMainGroupController -> designation main group index started');
        $designationMainGroups = DesignationMainGroup::all();
        $trashDesignationMainGroups = DesignationMainGroup::onlyTrashed()->get();
        return view('admin.setups.designation_main.index', compact('designationMainGroups', 'trashDesignationMainGroups'));
        Log::notice('DesignationMainGroupController -> designation main group Count - ' . $designationMainGroups->count());
        Log::info('DesignationMainGroupController -> designation main group index ended');
    }

    public function designationMainGroupAdd()
    {
        Log::info('DesignationMainGroupController -> designation main group add started');

        return view('admin.setups.designation_main.add');

        Log::info('DesignationMainGroupController -> designation main group add ended');
    }

    public function designationMainGroupStore(Request $request)
    {
        Log::info('DesignationMainGroupController -> designation main group store started');
        $validatedData = $request->validate([
            'name' => 'required|unique:designation_main_groups,name',
        ], [
            'name.required' => 'designation main group name required',
        ]);

        $data = new DesignationMainGroup();
        $data->name = $request->name;
        $data->created_at = Carbon::now();
        $data->save();

        Log::notice('DesignationMainGroupController -> Created designation main group id - ' . $data->id . ' created by ' . auth()->user()->id);
        Log::info('DesignationMainGroupController -> designation main group store ended');

        $notification = array(
            'message' => 'Designation Main Group Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('designation.main.group.index')->with($notification);
    }

    public function designationMainGroupEdit($id)
    {
        Log::info('DesignationMainGroupController -> designation main group edit started');
        $editData = DesignationMainGroup::find($id);
        return view('admin.setups.designation_main.edit', compact('editData'));
        Log::notice('DesignationMainGroupController -> edit designation main group id - ' . $editData->id . ' edited by ' . auth()->user()->id);
        Log::info('DesignationMainGroupController -> designation main group edit ended');
    }

    public function designationMainGroupUpdate(Request $request, $id)
    {

        Log::info('DesignationMainGroupController -> designation main group edit started');

        $validatedData = $request->validate([
            'name' => ['required', Rule::unique('designation_main_groups')->ignore($id)],
        ], [
            'name.required' => 'designation main group name required',
        ]);

        $data = DesignationMainGroup::find($id);
        $data->name = $request->name;
        $data->updated_at = Carbon::now();
        $data->save();

        Log::notice('DesignationMainGroupController -> update designation main group id - ' . $data->id . ' updated by ' . auth()->user()->id);
        Log::info('DesignationMainGroupController -> designation main group update ended');

        $notification = array(
            'message' => 'Designation Main Group Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('designation.main.group.index')->with($notification);
    }

    public function designationMainGroupSoftdelete($id)
    {

        Log::info('DesignationMainGroupController -> designation main group soft delete started');
        $designationMainGroup = DesignationMainGroup::find($id);
        $designationMainGroup->delete();

        $notification = array(
            'message' => 'Designation Main Group Deleted Successfully',
            'alert-type' => 'warning'
        );

        return redirect()->route('designation.main.group.index')->with($notification);

        Log::notice('DesignationMainGroupController -> soft delete designation main group id - ' . $designationMainGroup->id . ' deleted by ' . auth()->user()->id);
        Log::info('DesignationMainGroupController -> designation main group soft delete ended');
    }

    public function designationMainGroupRestore($id)
    {

        Log::info('DesignationMainGroupController -> designation main group restore started');

        $designationMainGroup = DesignationMainGroup::withTrashed()->find($id);
        $designationMainGroup->restore();

        $notification = array(
            'message' => 'Designation Main Group Restore Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('designation.main.group.index')->with($notification);

        Log::notice('DesignationMainGroupController -> restore designation main group id - ' . $designationMainGroup->id . ' deleted by ' . auth()->user()->id);
        Log::info('DesignationMainGroupController -> designation main group restore ended');
    }

    public function designationMainGroupDelete($id)
    {

        Log::info('DesignationMainGroupController -> designation main group delete started');

        $designationMainGroup = DesignationMainGroup::onlyTrashed()->find($id);
        $designationMainGroup->forceDelete();

        $notification = array(
            'message' => 'Designation Main Group Permenetly Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('designation.main.group.index')->with($notification);

        Log::emergency('DesignationMainGroupController -> delete designation main group id - ' . $designationMainGroup->id . ' deleted by ' . auth()->user()->id);
        Log::info('DesignationMainGroupController -> designation main group delete ended');
    }

    public function designationSubGroupList($id)
    {

        $designationMainGroup = DesignationMainGroup::find($id);
        //dd($faculty);
        $designationSubGroups = DesignationSubGroup::where('designation_main_group_id', $id)->get();
        return view('admin.setups.designation_main.list', compact('designationSubGroups', 'designationMainGroup'));
    }
}
