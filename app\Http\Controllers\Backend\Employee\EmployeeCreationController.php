<?php

namespace App\Http\Controllers\Backend\Employee;

use App\Http\Controllers\Controller;
use App\Http\Requests\EmployeeCreationRequest;
use App\Mail\UJSWelcomeMail;
use App\Models\City;
use App\Models\ContractEmployee;
use App\Models\Department;
use App\Models\DepartmentSub;
use App\Models\Designation;
use App\Models\Employee;
use App\Models\EmployeeSalaryStatus;
use App\Models\Faculty;
use App\Models\NewEmployee;
use App\Models\PermanentEmployee;
use App\Models\TemporyEmployee;
use App\Models\User;
use App\Notifications\AdminOfficerNewEmployee;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Session;

class EmployeeCreationController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }


    public function GenerateAddList()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $empNo = Auth()->user()->employee_no;

        if ($mainBranch == 51) {

            $pendingnewEmployees = NewEmployee::where('completion_status', 1)->orderBy('created_at', 'ASC')->get();
            $completednewEmployees = NewEmployee::where('completion_status', 2)->orderBy('updated_at', 'DESC')->get();
        } else if ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $pendingnewEmployees = NewEmployee::where('completion_status', 1)
                    ->where('main_branch_id', 52)
                    ->orderBy('created_at', 'ASC')->get();
                $completednewEmployees = NewEmployee::where('completion_status', 2)
                    ->where('main_branch_id', 52)
                    ->orderBy('updated_at', 'DESC')->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $pendingnewEmployees = NewEmployee::where('completion_status', 1)
                    ->where('main_branch_id', 52)
                    ->where('added_ma_id', $empNo)
                    ->orderBy('created_at', 'ASC')->get();
                $completednewEmployees = NewEmployee::where('completion_status', 2)
                    ->where('main_branch_id', 52)
                    ->where('added_ma_id', $empNo)
                    ->orderBy('updated_at', 'DESC')->get();
            }
        } else if ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $pendingnewEmployees = NewEmployee::where('completion_status', 1)
                    ->where('main_branch_id', 53)
                    ->orderBy('created_at', 'ASC')->get();
                $completednewEmployees = NewEmployee::where('completion_status', 2)
                    ->where('main_branch_id', 53)
                    ->orderBy('updated_at', 'DESC')->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $pendingnewEmployees = NewEmployee::where('completion_status', 1)
                    ->where('main_branch_id', 53)
                    ->where('added_ma_id', $empNo)
                    ->orderBy('created_at', 'ASC')->get();
                $completednewEmployees = NewEmployee::where('completion_status', 2)
                    ->where('main_branch_id', 53)
                    ->where('added_ma_id', $empNo)
                    ->orderBy('updated_at', 'DESC')->get();
            }
        }

        return view('admin.employee.create.list', compact('pendingnewEmployees', 'completednewEmployees'));
    }

    public function GenerateList($notification_id)
    {


        auth()->user()->notifications()->where('id', $notification_id)->update(['read_at' => now()]);

        $mainBranch = Auth()->user()->main_branch_id;
        $empNo = Auth()->user()->employee_no;

        if ($mainBranch == 51) {

            $pendingnewEmployees = NewEmployee::where('completion_status', 1)->orderBy('created_at', 'ASC')->get();
            $completednewEmployees = NewEmployee::where('completion_status', 2)->orderBy('updated_at', 'DESC')->get();
        } else if ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $pendingnewEmployees = NewEmployee::where('completion_status', 1)
                    ->where('main_branch_id', 52)
                    ->orderBy('created_at', 'ASC')->get();
                $completednewEmployees = NewEmployee::where('completion_status', 2)
                    ->where('main_branch_id', 52)
                    ->orderBy('updated_at', 'DESC')->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $pendingnewEmployees = NewEmployee::where('completion_status', 1)
                    ->where('main_branch_id', 52)
                    ->where('added_ma_id', $empNo)
                    ->orderBy('created_at', 'ASC')->get();
                $completednewEmployees = NewEmployee::where('completion_status', 2)
                    ->where('main_branch_id', 52)
                    ->where('added_ma_id', $empNo)
                    ->orderBy('updated_at', 'DESC')->get();
            }
        } else if ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $pendingnewEmployees = NewEmployee::where('completion_status', 1)
                    ->where('main_branch_id', 53)
                    ->orderBy('created_at', 'ASC')->get();
                $completednewEmployees = NewEmployee::where('completion_status', 2)
                    ->where('main_branch_id', 53)
                    ->orderBy('updated_at', 'DESC')->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $pendingnewEmployees = NewEmployee::where('completion_status', 1)
                    ->where('main_branch_id', 53)
                    ->where('added_ma_id', $empNo)
                    ->orderBy('created_at', 'ASC')->get();
                $completednewEmployees = NewEmployee::where('completion_status', 2)
                    ->where('main_branch_id', 53)
                    ->where('added_ma_id', $empNo)
                    ->orderBy('updated_at', 'DESC')->get();
            }
        }

        return view('admin.employee.create.list', compact('pendingnewEmployees', 'completednewEmployees'));
    }

    public function GenerateAddView()
    {
        return view('admin.employee.create.index');
    }

    public function generateAddSubmit(Request $request)
    {

        if ($request->employee_type == 138) {

            return redirect()->route('employee.generate.add.permanent');
        } elseif ($request->employee_type == 140) {

            return redirect()->route('employee.generate.add.tempory');
        } elseif ($request->employee_type == 141) {

            return redirect()->route('employee.generate.add.contract');
        } elseif ($request->employee_type == 142) {

            return redirect()->route('employee.generate.add.assignment.basis');
        }
    }

    public function generateAddPermanent()
    {

        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');

        $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(138, 139));
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
        $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

        $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        return view('admin.employee.create.permanent_add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'CarderDepartment', 'CarderSubDepartment'));
    }

    public function generateAddTempory()
    {

        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');

        $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(140));
        $salaryPaymentTypes = $categories->where('category_type_id', '48');
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
        $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

        $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        return view('admin.employee.create.temporary_add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));
    }

    public function generateAddContract()
    {
        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');

        $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(141));
        $salaryPaymentTypes = $categories->where('category_type_id', '48');
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
        $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

        $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        return view('admin.employee.create.contract_add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));
    }

    public function generateAddAssignmentBasis()
    {

        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');

        $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(140));
        $salaryPaymentTypes = $categories->where('category_type_id', '48');
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
        $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

        $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        return view('admin.employee.create.assignment_basis_add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));
    }


    public function generateAddStore(EmployeeCreationRequest $request)
    {
        if ($request->employee_work_type == 138) {

            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

            $nicData = json_decode($empDetails->body(), true);

            $employee_data = new NewEmployee();
            $employee_data->file_reference_number = strtoupper($request->file_reference_number);
            $employee_data->main_branch_id = $request->main_branch_id;
            $employee_data->designation_id = $request->designation_id;
            $employee_data->faculty_id = $request->faculty_id;
            $employee_data->department_id = $request->department_id;
            $employee_data->sub_department_id = $request->sub_department_id;

            $employee_data->carder_faculty_id = $request->carder_faculty_id;
            $employee_data->carder_department_id = $request->carder_department_id;
            $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

            $employee_data->initials = strtoupper($request->initials);
            $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
            $employee_data->last_name = ucwords($request->last_name);
            $employee_data->civil_status_id = $request->civil_status_id;
            $employee_data->gender_id = $request->gender_id;
            $employee_data->race_id = $request->race_id;
            $employee_data->religion_id = $request->religion_id;
            $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
            $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
            $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
            $employee_data->permanent_city_id = $request->permanent_city_id;
            $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
            $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
            $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
            $employee_data->postal_city_id = $request->postal_city_id;
            $employee_data->personal_email = $request->personal_email;
            $employee_data->email = $request->email;
            $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
            $employee_data->citizen_registration_no = $request->citizen_registration_no;
            $employee_data->initial_appointment_date = date("Y-m-d", strtotime($request->initial_appointment_date));
            $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
            $employee_data->gratuity_cal_date = date("Y-m-d", strtotime($request->gratuity_cal_date));
            $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
            $employee_data->salary_termination_date_2 = date("Y-m-d", strtotime($request->salary_termination_date_2));
            $employee_data->retirement_date = date("Y-m-d", strtotime($request->retirement_date));
            $employee_data->current_basic_salary = $request->current_basic_salary;
            $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
            $employee_data->added_ma_id = Auth()->user()->employee_no;
            $employee_data->added_ma_date = date('Y-m-d');
            $employee_data->completion_status = 1;
            $employee_data->employee_work_type = 138;

            if ($request->increment_date == '') {
                $employee_data->increment_date = NULL;
            } else {
                $employee_data->increment_date = date("m-d", strtotime($request->increment_date));
            }

            $employee_data->mobile_no = $request->mobile_no;
            $employee_data->telephone_no = $request->phone_no;
            $employee_data->nic = strtoupper($request->nic);

            $employee_data->nic_old = $nicData['oldnic'] ?? null;
            $employee_data->nic_new = $nicData['newnic'] ?? null;
            $employee_data->active_nic = $nicData['activenic'] ?? null;
            $employee_data->dob_gen = $nicData['dob'] ?? null;

            $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
            $employee_data->title_id = $request->titel_id;
            $employee_data->salary_payment_type = 266;
            $employee_data->save();

            //send notification
            $users = User::join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('users.*')
                ->where('users.main_branch_id', Auth()->user()->main_branch_id)
                ->whereIn('roles.id', [4])
                ->distinct()
                ->get();


            $emailData = [
                'employee_work_type' => 'Permenant',
                'nic' => $employee_data->nic
            ];

            Notification::send($users, new AdminOfficerNewEmployee($emailData));


            $notification = array(
                'message' => 'New Permanent Employee Inserted Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('employee.generate.add.list')->with($notification);
        } elseif ($request->employee_work_type == 140) {


            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

            $nicData = json_decode($empDetails->body(), true);

            $employee_data = new NewEmployee();
            $employee_data->file_reference_number = strtoupper($request->file_reference_number);
            $employee_data->main_branch_id = $request->main_branch_id;
            $employee_data->designation_id = $request->designation_id;
            $employee_data->faculty_id = $request->faculty_id;
            $employee_data->department_id = $request->department_id;
            $employee_data->sub_department_id = $request->sub_department_id;

            $employee_data->carder_faculty_id = $request->carder_faculty_id;
            $employee_data->carder_department_id = $request->carder_department_id;
            $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

            $employee_data->initials = strtoupper($request->initials);
            $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
            $employee_data->last_name = ucwords($request->last_name);
            $employee_data->civil_status_id = $request->civil_status_id;
            $employee_data->gender_id = $request->gender_id;
            $employee_data->race_id = $request->race_id;
            $employee_data->religion_id = $request->religion_id;
            $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
            $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
            $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
            $employee_data->permanent_city_id = $request->permanent_city_id;
            $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
            $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
            $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
            $employee_data->postal_city_id = $request->postal_city_id;
            $employee_data->personal_email = $request->personal_email;
            $employee_data->email = $request->email;
            $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
            $employee_data->citizen_registration_no = $request->citizen_registration_no;
            $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
            $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
            $employee_data->current_basic_salary = $request->current_basic_salary;
            $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
            $employee_data->added_ma_id = Auth()->user()->employee_no;
            $employee_data->added_ma_date = date('Y-m-d');
            $employee_data->completion_status = 1;
            $employee_data->employee_work_type = 140;

            $employee_data->mobile_no = $request->mobile_no;
            $employee_data->telephone_no = $request->phone_no;
            $employee_data->nic = strtoupper($request->nic);

            $employee_data->nic_old = $nicData['oldnic'] ?? null;
            $employee_data->nic_new = $nicData['newnic'] ?? null;
            $employee_data->active_nic = $nicData['activenic'] ?? null;
            $employee_data->dob_gen = $nicData['dob'] ?? null;

            $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
            $employee_data->title_id = $request->titel_id;
            $employee_data->salary_payment_type = $request->salary_payment_type;
            $employee_data->save();

            //send notification
            $users = User::join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('users.*')
                ->where('users.main_branch_id', Auth()->user()->main_branch_id)
                ->whereIn('roles.id', [4])
                ->distinct()
                ->get();

            //$users = User::where('id',1)->get();


            $emailData = [
                'employee_work_type' => 'Tempory',
                'nic' => $employee_data->nic
            ];

            Notification::send($users, new AdminOfficerNewEmployee($emailData));

            $notification = array(
                'message' => 'New Tempory Employee Inserted Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('employee.generate.add.list')->with($notification);
        } elseif ($request->employee_work_type == 142) {

            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

            $nicData = json_decode($empDetails->body(), true);

            $employee_data = new NewEmployee();
            $employee_data->file_reference_number = strtoupper($request->file_reference_number);
            $employee_data->main_branch_id = $request->main_branch_id;
            $employee_data->designation_id = $request->designation_id;
            $employee_data->faculty_id = $request->faculty_id;
            $employee_data->department_id = $request->department_id;
            $employee_data->sub_department_id = $request->sub_department_id;

            $employee_data->carder_faculty_id = $request->carder_faculty_id;
            $employee_data->carder_department_id = $request->carder_department_id;
            $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

            $employee_data->initials = strtoupper($request->initials);
            $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
            $employee_data->last_name = ucwords($request->last_name);
            $employee_data->civil_status_id = $request->civil_status_id;
            $employee_data->gender_id = $request->gender_id;
            $employee_data->race_id = $request->race_id;
            $employee_data->religion_id = $request->religion_id;
            $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
            $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
            $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
            $employee_data->permanent_city_id = $request->permanent_city_id;
            $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
            $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
            $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
            $employee_data->postal_city_id = $request->postal_city_id;
            $employee_data->personal_email = $request->personal_email;
            $employee_data->email = $request->email;
            $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
            $employee_data->citizen_registration_no = $request->citizen_registration_no;
            $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
            $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
            $employee_data->current_basic_salary = $request->current_basic_salary;
            $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
            $employee_data->added_ma_id = Auth()->user()->employee_no;
            $employee_data->added_ma_date = date('Y-m-d');
            $employee_data->completion_status = 1;
            $employee_data->employee_work_type = 142;


            $employee_data->mobile_no = $request->mobile_no;
            $employee_data->telephone_no = $request->phone_no;
            $employee_data->nic = strtoupper($request->nic);

            $employee_data->nic_old = $nicData['oldnic'] ?? null;
            $employee_data->nic_new = $nicData['newnic'] ?? null;
            $employee_data->active_nic = $nicData['activenic'] ?? null;
            $employee_data->dob_gen = $nicData['dob'] ?? null;

            $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
            $employee_data->title_id = $request->titel_id;
            $employee_data->salary_payment_type = $request->salary_payment_type;
            $employee_data->save();

            //send notification
            $users = User::join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('users.*')
                ->where('users.main_branch_id', Auth()->user()->main_branch_id)
                ->whereIn('roles.id', [4])
                ->distinct()
                ->get();


            $emailData = [
                'employee_work_type' => 'Assignment Basis',
                'nic' => $employee_data->nic
            ];

            Notification::send($users, new AdminOfficerNewEmployee($emailData));

            $notification = array(
                'message' => 'New Assignment Basis Employee Inserted Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('employee.generate.add.list')->with($notification);
        } elseif ($request->employee_work_type == 141) {

            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

            $nicData = json_decode($empDetails->body(), true);

            $employee_data = new NewEmployee();
            $employee_data->file_reference_number = strtoupper($request->file_reference_number);
            $employee_data->main_branch_id = $request->main_branch_id;
            $employee_data->designation_id = $request->designation_id;
            $employee_data->faculty_id = $request->faculty_id;
            $employee_data->department_id = $request->department_id;
            $employee_data->sub_department_id = $request->sub_department_id;

            $employee_data->carder_faculty_id = $request->carder_faculty_id;
            $employee_data->carder_department_id = $request->carder_department_id;
            $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

            $employee_data->initials = strtoupper($request->initials);
            $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
            $employee_data->last_name = ucwords($request->last_name);
            $employee_data->civil_status_id = $request->civil_status_id;
            $employee_data->gender_id = $request->gender_id;
            $employee_data->race_id = $request->race_id;
            $employee_data->religion_id = $request->religion_id;
            $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
            $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
            $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
            $employee_data->permanent_city_id = $request->permanent_city_id;
            $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
            $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
            $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
            $employee_data->postal_city_id = $request->postal_city_id;
            $employee_data->personal_email = $request->personal_email;
            $employee_data->email = $request->email;
            $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
            $employee_data->citizen_registration_no = $request->citizen_registration_no;
            $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
            $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
            $employee_data->sabbatical_start = date("Y-m-d", strtotime($request->sabbatical_start));
            $employee_data->sabbatical_end = date("Y-m-d", strtotime($request->sabbatical_end));
            $employee_data->current_basic_salary = $request->current_basic_salary;
            $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
            $employee_data->added_ma_id = Auth()->user()->employee_no;
            $employee_data->added_ma_date = date('Y-m-d');
            $employee_data->completion_status = 1;
            $employee_data->employee_work_type = 141;

            $employee_data->mobile_no = $request->mobile_no;
            $employee_data->telephone_no = $request->phone_no;
            $employee_data->nic = strtoupper($request->nic);

            $employee_data->nic_old = $nicData['oldnic'] ?? null;
            $employee_data->nic_new = $nicData['newnic'] ?? null;
            $employee_data->active_nic = $nicData['activenic'] ?? null;
            $employee_data->dob_gen = $nicData['dob'] ?? null;

            $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
            $employee_data->title_id = $request->titel_id;
            $employee_data->salary_payment_type = $request->salary_payment_type;
            $employee_data->save();

            //send notification
            $users = User::join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('users.*')
                ->where('users.main_branch_id', Auth()->user()->main_branch_id)
                ->whereIn('roles.id', [4])
                ->distinct()
                ->get();


            $emailData = [
                'employee_work_type' => 'Contract',
                'nic' => $employee_data->nic
            ];

            Notification::send($users, new AdminOfficerNewEmployee($emailData));

            $notification = array(
                'message' => 'New Contract Employee Inserted Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('employee.generate.add.list')->with($notification);
        }
    }

    public function generateEditPermanent($id)
    {
        $empNo = decrypt($id);
        $employee = NewEmployee::find($empNo);
        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');

        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(138, 139));
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', $employee->faculty_id)->get();
        $subDepartments = DepartmentSub::where('department_code', '=', $employee->department_id)->get();

        $CarderDepartment = Department::where('faculty_code', '=', $employee->carder_faculty_id)->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', $employee->carder_department_id)->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        return view('admin.employee.create.permanent_edit', compact('employee', 'genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'educationLevels', 'departments', 'subDepartments', 'CarderDepartment', 'CarderSubDepartment'));
    }

    public function generateEditTempory($id)
    {
        $empNo = decrypt($id);
        $employee = NewEmployee::find($empNo);
        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');

        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(140));
        $salaryPaymentTypes = $categories->where('category_type_id', '48');
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', $employee->faculty_id)->get();
        $subDepartments = DepartmentSub::where('department_code', '=', $employee->department_id)->get();

        $CarderDepartment = Department::where('faculty_code', '=', $employee->carder_faculty_id)->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', $employee->carder_department_id)->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        return view('admin.employee.create.temporary_edit', compact('employee', 'genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'educationLevels', 'departments', 'subDepartments', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));
    }

    public function generateEditContract($id)
    {
        $empNo = decrypt($id);
        $employee = NewEmployee::find($empNo);
        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');

        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(141));
        $salaryPaymentTypes = $categories->where('category_type_id', '48');
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', $employee->faculty_id)->get();
        $subDepartments = DepartmentSub::where('department_code', '=', $employee->department_id)->get();

        $CarderDepartment = Department::where('faculty_code', '=', $employee->carder_faculty_id)->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', $employee->carder_department_id)->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        return view('admin.employee.create.contract_edit', compact('employee', 'genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'educationLevels', 'departments', 'subDepartments', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));
    }

    public function generateEditAssignmentBasis($id)
    {
        $empNo = decrypt($id);
        $employee = NewEmployee::find($empNo);
        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $educationLevels = $categories->where('category_type_id', '16');
        $employeeStatusIds = $categories->where('category_type_id', '21');

        $employeeTypes = $categories->where('category_type_id', '23')->whereIn('id', array(140));
        $salaryPaymentTypes = $categories->where('category_type_id', '48');
        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', $employee->faculty_id)->get();
        $subDepartments = DepartmentSub::where('department_code', '=', $employee->department_id)->get();

        $CarderDepartment = Department::where('faculty_code', '=', $employee->carder_faculty_id)->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', $employee->carder_department_id)->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        } elseif ($mainBranch == 52) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 52)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            $designations =  Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                ->where('designations.deleted_at', '=', NULL)
                ->where('designation_division', 53)
                ->where('active_status', 1)
                ->get();

            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }

        return view('admin.employee.create.assignment_basis_edit', compact('employee', 'genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'educationLevels', 'departments', 'subDepartments', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));
    }

    public function generateUpdate(EmployeeCreationRequest $request, $id)
    {

        if ($request->has('update')) {

            if ($request->employee_work_type == 138) {

                //get nic information
                $empDetails = Http::withHeaders([
                    'Content-Type' => 'application/json',
                ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

                $nicData = json_decode($empDetails->body(), true);

                $employee_data = NewEmployee::find($id);
                $employee_data->file_reference_number = strtoupper($request->file_reference_number);
                $employee_data->main_branch_id = $request->main_branch_id;
                $employee_data->designation_id = $request->designation_id;
                $employee_data->faculty_id = $request->faculty_id;
                $employee_data->department_id = $request->department_id;
                $employee_data->sub_department_id = $request->sub_department_id;

                $employee_data->carder_faculty_id = $request->carder_faculty_id;
                $employee_data->carder_department_id = $request->carder_department_id;
                $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

                $employee_data->initials = strtoupper($request->initials);
                $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
                $employee_data->last_name = ucwords($request->last_name);
                $employee_data->civil_status_id = $request->civil_status_id;
                $employee_data->gender_id = $request->gender_id;
                $employee_data->race_id = $request->race_id;
                $employee_data->religion_id = $request->religion_id;
                $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
                $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
                $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
                $employee_data->permanent_city_id = $request->permanent_city_id;
                $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
                $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
                $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
                $employee_data->postal_city_id = $request->postal_city_id;
                $employee_data->personal_email = $request->personal_email;
                $employee_data->email = $request->email;
                $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
                $employee_data->citizen_registration_no = $request->citizen_registration_no;
                $employee_data->initial_appointment_date = date("Y-m-d", strtotime($request->initial_appointment_date));
                $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
                $employee_data->gratuity_cal_date = date("Y-m-d", strtotime($request->gratuity_cal_date));
                $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
                $employee_data->salary_termination_date_2 = date("Y-m-d", strtotime($request->salary_termination_date_2));
                $employee_data->retirement_date = date("Y-m-d", strtotime($request->retirement_date));
                $employee_data->current_basic_salary = $request->current_basic_salary;
                $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
                $employee_data->added_ma_id = Auth()->user()->employee_no;
                $employee_data->added_ma_date = date('Y-m-d');
                $employee_data->completion_status = 1;
                $employee_data->employee_work_type = 138;

                if ($request->increment_date == '') {
                    $employee_data->increment_date = NULL;
                } else {
                    $employee_data->increment_date = date("m-d", strtotime($request->increment_date));
                }

                $employee_data->mobile_no = $request->mobile_no;
                $employee_data->telephone_no = $request->phone_no;
                $employee_data->nic = strtoupper($request->nic);

                $employee_data->nic_old = $nicData['oldnic'] ?? null;
                $employee_data->nic_new = $nicData['newnic'] ?? null;
                $employee_data->active_nic = $nicData['activenic'] ?? null;
                $employee_data->dob_gen = $nicData['dob'] ?? null;

                $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
                $employee_data->title_id = $request->titel_id;
                $employee_data->salary_payment_type = 266;
                $employee_data->save();


                $notification = array(
                    'message' => 'New Permanent Employee Inserted Successfully',
                    'alert-type' => 'success'
                );

                return redirect()->route('employee.generate.add.list')->with($notification);
            } elseif ($request->employee_work_type == 140) {


                //get nic information
                $empDetails = Http::withHeaders([
                    'Content-Type' => 'application/json',
                ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

                $nicData = json_decode($empDetails->body(), true);

                $employee_data = NewEmployee::find($id);
                $employee_data->file_reference_number = strtoupper($request->file_reference_number);
                $employee_data->main_branch_id = $request->main_branch_id;
                $employee_data->designation_id = $request->designation_id;
                $employee_data->faculty_id = $request->faculty_id;
                $employee_data->department_id = $request->department_id;
                $employee_data->sub_department_id = $request->sub_department_id;

                $employee_data->carder_faculty_id = $request->carder_faculty_id;
                $employee_data->carder_department_id = $request->carder_department_id;
                $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

                $employee_data->initials = strtoupper($request->initials);
                $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
                $employee_data->last_name = ucwords($request->last_name);
                $employee_data->civil_status_id = $request->civil_status_id;
                $employee_data->gender_id = $request->gender_id;
                $employee_data->race_id = $request->race_id;
                $employee_data->religion_id = $request->religion_id;
                $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
                $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
                $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
                $employee_data->permanent_city_id = $request->permanent_city_id;
                $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
                $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
                $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
                $employee_data->postal_city_id = $request->postal_city_id;
                $employee_data->personal_email = $request->personal_email;
                $employee_data->email = $request->email;
                $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
                $employee_data->citizen_registration_no = $request->citizen_registration_no;
                $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
                $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
                $employee_data->current_basic_salary = $request->current_basic_salary;
                $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
                $employee_data->added_ma_id = Auth()->user()->employee_no;
                $employee_data->added_ma_date = date('Y-m-d');
                $employee_data->completion_status = 1;
                $employee_data->employee_work_type = 140;

                $employee_data->mobile_no = $request->mobile_no;
                $employee_data->telephone_no = $request->phone_no;
                $employee_data->nic = strtoupper($request->nic);

                $employee_data->nic_old = $nicData['oldnic'] ?? null;
                $employee_data->nic_new = $nicData['newnic'] ?? null;
                $employee_data->active_nic = $nicData['activenic'] ?? null;
                $employee_data->dob_gen = $nicData['dob'] ?? null;

                $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
                $employee_data->title_id = $request->titel_id;
                $employee_data->salary_payment_type = $request->salary_payment_type;
                $employee_data->save();

                $notification = array(
                    'message' => 'New Tempory Employee Inserted Successfully',
                    'alert-type' => 'success'
                );

                return redirect()->route('employee.generate.add.list')->with($notification);
            } elseif ($request->employee_work_type == 142) {

                //get nic information
                $empDetails = Http::withHeaders([
                    'Content-Type' => 'application/json',
                ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

                $nicData = json_decode($empDetails->body(), true);

                $employee_data = NewEmployee::find($id);
                $employee_data->file_reference_number = strtoupper($request->file_reference_number);
                $employee_data->main_branch_id = $request->main_branch_id;
                $employee_data->designation_id = $request->designation_id;
                $employee_data->faculty_id = $request->faculty_id;
                $employee_data->department_id = $request->department_id;
                $employee_data->sub_department_id = $request->sub_department_id;

                $employee_data->carder_faculty_id = $request->carder_faculty_id;
                $employee_data->carder_department_id = $request->carder_department_id;
                $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

                $employee_data->initials = strtoupper($request->initials);
                $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
                $employee_data->last_name = ucwords($request->last_name);
                $employee_data->civil_status_id = $request->civil_status_id;
                $employee_data->gender_id = $request->gender_id;
                $employee_data->race_id = $request->race_id;
                $employee_data->religion_id = $request->religion_id;
                $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
                $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
                $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
                $employee_data->permanent_city_id = $request->permanent_city_id;
                $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
                $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
                $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
                $employee_data->postal_city_id = $request->postal_city_id;
                $employee_data->personal_email = $request->personal_email;
                $employee_data->email = $request->email;
                $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
                $employee_data->citizen_registration_no = $request->citizen_registration_no;
                $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
                $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
                $employee_data->current_basic_salary = $request->current_basic_salary;
                $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
                $employee_data->added_ma_id = Auth()->user()->employee_no;
                $employee_data->added_ma_date = date('Y-m-d');
                $employee_data->completion_status = 1;
                $employee_data->employee_work_type = 142;


                $employee_data->mobile_no = $request->mobile_no;
                $employee_data->telephone_no = $request->phone_no;
                $employee_data->nic = strtoupper($request->nic);

                $employee_data->nic_old = $nicData['oldnic'] ?? null;
                $employee_data->nic_new = $nicData['newnic'] ?? null;
                $employee_data->active_nic = $nicData['activenic'] ?? null;
                $employee_data->dob_gen = $nicData['dob'] ?? null;

                $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
                $employee_data->title_id = $request->titel_id;
                $employee_data->salary_payment_type = $request->salary_payment_type;
                $employee_data->save();

                $notification = array(
                    'message' => 'New Assignment Basis Employee Inserted Successfully',
                    'alert-type' => 'success'
                );

                return redirect()->route('employee.generate.add.list')->with($notification);
            } elseif ($request->employee_work_type == 141) {

                //get nic information
                $empDetails = Http::withHeaders([
                    'Content-Type' => 'application/json',
                ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

                $nicData = json_decode($empDetails->body(), true);

                $employee_data = NewEmployee::find($id);
                $employee_data->file_reference_number = strtoupper($request->file_reference_number);
                $employee_data->main_branch_id = $request->main_branch_id;
                $employee_data->designation_id = $request->designation_id;
                $employee_data->faculty_id = $request->faculty_id;
                $employee_data->department_id = $request->department_id;
                $employee_data->sub_department_id = $request->sub_department_id;

                $employee_data->carder_faculty_id = $request->carder_faculty_id;
                $employee_data->carder_department_id = $request->carder_department_id;
                $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

                $employee_data->initials = strtoupper($request->initials);
                $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
                $employee_data->last_name = ucwords($request->last_name);
                $employee_data->civil_status_id = $request->civil_status_id;
                $employee_data->gender_id = $request->gender_id;
                $employee_data->race_id = $request->race_id;
                $employee_data->religion_id = $request->religion_id;
                $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
                $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
                $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
                $employee_data->permanent_city_id = $request->permanent_city_id;
                $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
                $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
                $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
                $employee_data->postal_city_id = $request->postal_city_id;
                $employee_data->personal_email = $request->personal_email;
                $employee_data->email = $request->email;
                $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
                $employee_data->citizen_registration_no = $request->citizen_registration_no;
                $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
                $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
                $employee_data->sabbatical_start = date("Y-m-d", strtotime($request->sabbatical_start));
                $employee_data->sabbatical_end = date("Y-m-d", strtotime($request->sabbatical_end));
                $employee_data->current_basic_salary = $request->current_basic_salary;
                $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
                $employee_data->added_ma_id = Auth()->user()->employee_no;
                $employee_data->added_ma_date = date('Y-m-d');
                $employee_data->completion_status = 1;
                $employee_data->employee_work_type = 141;

                $employee_data->mobile_no = $request->mobile_no;
                $employee_data->telephone_no = $request->phone_no;
                $employee_data->nic = strtoupper($request->nic);

                $employee_data->nic_old = $nicData['oldnic'] ?? null;
                $employee_data->nic_new = $nicData['newnic'] ?? null;
                $employee_data->active_nic = $nicData['activenic'] ?? null;
                $employee_data->dob_gen = $nicData['dob'] ?? null;

                $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
                $employee_data->title_id = $request->titel_id;
                $employee_data->salary_payment_type = $request->salary_payment_type;
                $employee_data->save();

                $notification = array(
                    'message' => 'New Contract Employee Inserted Successfully',
                    'alert-type' => 'success'
                );

                return redirect()->route('employee.generate.add.list')->with($notification);
            }
        }

        if ($request->has('create')) {

            if ($request->employee_work_type == 138) {

                //get nic information
                $empDetails = Http::withHeaders([
                    'Content-Type' => 'application/json',
                ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

                $nicData = json_decode($empDetails->body(), true);

                $employee_data = NewEmployee::find($id);
                $employee_data->file_reference_number = strtoupper($request->file_reference_number);
                $employee_data->main_branch_id = $request->main_branch_id;
                $employee_data->designation_id = $request->designation_id;
                $employee_data->faculty_id = $request->faculty_id;
                $employee_data->department_id = $request->department_id;
                $employee_data->sub_department_id = $request->sub_department_id;

                $employee_data->carder_faculty_id = $request->carder_faculty_id;
                $employee_data->carder_department_id = $request->carder_department_id;
                $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

                $employee_data->initials = strtoupper($request->initials);
                $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
                $employee_data->last_name = ucwords($request->last_name);
                $employee_data->civil_status_id = $request->civil_status_id;
                $employee_data->gender_id = $request->gender_id;
                $employee_data->race_id = $request->race_id;
                $employee_data->religion_id = $request->religion_id;
                $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
                $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
                $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
                $employee_data->permanent_city_id = $request->permanent_city_id;
                $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
                $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
                $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
                $employee_data->postal_city_id = $request->postal_city_id;
                $employee_data->personal_email = $request->personal_email;
                $employee_data->email = $request->email;
                $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
                $employee_data->citizen_registration_no = $request->citizen_registration_no;
                $employee_data->initial_appointment_date = date("Y-m-d", strtotime($request->initial_appointment_date));
                $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
                $employee_data->gratuity_cal_date = date("Y-m-d", strtotime($request->gratuity_cal_date));
                $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
                $employee_data->salary_termination_date_2 = date("Y-m-d", strtotime($request->salary_termination_date_2));
                $employee_data->retirement_date = date("Y-m-d", strtotime($request->retirement_date));
                $employee_data->current_basic_salary = $request->current_basic_salary;
                $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
                $employee_data->completion_status = 1;
                $employee_data->employee_work_type = 138;

                if ($request->increment_date == '') {
                    $employee_data->increment_date = NULL;
                } else {
                    $employee_data->increment_date = date("m-d", strtotime($request->increment_date));
                }

                $employee_data->mobile_no = $request->mobile_no;
                $employee_data->telephone_no = $request->phone_no;
                $employee_data->nic = strtoupper($request->nic);

                $employee_data->nic_old = $nicData['oldnic'] ?? null;
                $employee_data->nic_new = $nicData['newnic'] ?? null;
                $employee_data->active_nic = $nicData['activenic'] ?? null;
                $employee_data->dob_gen = $nicData['dob'] ?? null;

                $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
                $employee_data->title_id = $request->titel_id;
                $employee_data->salary_payment_type = 266;
                $employee_data->save();


                $empSimilarData = Employee::where(function ($query) use ($request, $nicData) {
                    $query->where('nic', strtoupper($request->nic))
                        ->orWhere('nic_old', $nicData['oldnic'])
                        ->orWhere('nic_new', $nicData['newnic']);
                })
                    ->where('employee_status_id', 110)
                    ->first();

                if ($empSimilarData) {
                    //return view('admin.employee.create.similar_nic', compact('empSimilarData'));
                    return redirect()->back()->withErrors(['nic' => 'An active employee with this NIC already exists in system.']);
                } else {

                    DB::transaction(function () use ($request, $nicData, $employee_data) {

                        // create employee number
                        $data = new PermanentEmployee();
                        if ($employee_data->active_nic == 1) {
                            $data->nic = strtoupper($employee_data->nic);
                        } else {
                            $data->nic = strtoupper($employee_data->nic_new);
                        }
                        $data->main_branch = Auth()->user()->main_branch_id;
                        $data->assign_ma_user_id = $employee_data->added_ma_id;
                        $data->assign_ma_date = date("Y-m-d");
                        $data->employee_status_id = 110;
                        $data->employee_status_type_id = 112;
                        $data->employee_work_type = 138;
                        $data->added_user_id = auth()->user()->employee_no;
                        $data->added_date = date("Y-m-d");
                        $data->save();

                        //get next table id
                        $maxnumber = Employee::lockForUpdate()->max('id');
                        $nextId = $maxnumber + 1;

                        //get nic information
                        $empDetails = Http::withHeaders([
                            'Content-Type' => 'application/json',
                        ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

                        $nicData = json_decode($empDetails->body(), true);

                        // Check for existing employees with the same NIC
                        $empSimilarData = Employee::where(function ($query) use ($request, $nicData) {
                            $query->where('nic', strtoupper($request->nic))
                                  ->orWhere('nic_old', $nicData['oldnic'])
                                  ->orWhere('nic_new', $nicData['newnic']);
                        })
                        ->where('employee_status_id', 110)
                        ->first();

                        if ($empSimilarData) {
                            throw new \Exception('An active employee with this NIC already exists in the system.');
                        }

                        $emp_data = new Employee();
                        $emp_data->id = $nextId;
                        $emp_data->employee_no = $data->id;
                        $emp_data->file_reference_number = strtoupper($request->file_reference_number);
                        $emp_data->main_branch_id = $request->main_branch_id;
                        $emp_data->designation_id = $request->designation_id;
                        $emp_data->faculty_id = $request->faculty_id;
                        $emp_data->department_id = $request->department_id;
                        $emp_data->sub_department_id = $request->sub_department_id;

                        $emp_data->carder_faculty_id = $request->carder_faculty_id;
                        $emp_data->carder_department_id = $request->carder_department_id;
                        $emp_data->carder_sub_department_id = $request->carder_sub_department_id;

                        $emp_data->initials = strtoupper($request->initials);
                        $emp_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
                        $emp_data->last_name = ucwords($request->last_name);
                        $emp_data->civil_status_id = $request->civil_status_id;
                        $emp_data->gender_id = $request->gender_id;
                        $emp_data->race_id = $request->race_id;
                        $emp_data->religion_id = $request->religion_id;
                        $emp_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
                        $emp_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
                        $emp_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
                        $emp_data->permanent_city_id = $request->permanent_city_id;
                        $emp_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
                        $emp_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
                        $emp_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
                        $emp_data->postal_city_id = $request->postal_city_id;
                        $emp_data->personal_email = $request->personal_email;
                        $emp_data->email = $request->email;
                        $emp_data->state_of_citizenship_id = $request->state_of_citizenship_id;
                        $emp_data->citizen_registration_no = $request->citizen_registration_no;
                        $emp_data->initial_appointment_date = date("Y-m-d", strtotime($request->initial_appointment_date));
                        $emp_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
                        $emp_data->gratuity_cal_date = date("Y-m-d", strtotime($request->gratuity_cal_date));
                        $emp_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
                        $emp_data->salary_termination_date_2 = date("Y-m-d", strtotime($request->salary_termination_date_2));
                        $emp_data->retirement_date = date("Y-m-d", strtotime($request->retirement_date));
                        $emp_data->current_basic_salary = $request->current_basic_salary;
                        $emp_data->emp_highest_edu_level = $request->emp_highest_edu_level;
                        $emp_data->added_ma_user_id = $employee_data->added_ma_id;
                        $emp_data->added_ma_date = date('Y-m-d');
                        $emp_data->assign_ma_user_id = $employee_data->added_ma_id;
                        $emp_data->assign_ma_date = date('Y-m-d');
                        $emp_data->approved_ar_user_id = auth()->user()->employee_no;
                        $emp_data->approved_ar_date = date('Y-m-d');
                        $emp_data->status_id = 1;
                        $emp_data->employee_status_id = 110;
                        $emp_data->employee_status_type_id = 112;
                        $emp_data->employee_work_type = 138;
                        if ($request->increment_date == '') {
                            $emp_data->increment_date = NULL;
                        } else {
                            $emp_data->increment_date = date("m-d", strtotime($request->increment_date));
                        }
                        $emp_data->emp_decision_id = 41;
                        $emp_data->mobile_no = $request->mobile_no;
                        $emp_data->telephone_no = $request->telephone_no;
                        $emp_data->nic = strtoupper($request->nic);

                        $emp_data->nic_old = $nicData['oldnic'] ?? null;
                        $emp_data->nic_new = $nicData['newnic'] ?? null;
                        $emp_data->active_nic = $nicData['activenic'] ?? null;
                        $emp_data->dob_gen = $nicData['dob'] ?? null;

                        $emp_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
                        $emp_data->title_id = $request->titel_id;
                        $emp_data->salary_payment_type = 266;
                        $emp_data->save();

                        $data = PermanentEmployee::find($emp_data->employee_no);
                        $data->completion_status = 1;
                        $data->updated_at = Carbon::now();
                        $data->save();

                        $statusRecord = new EmployeeSalaryStatus();
                        $statusRecord->employee_no = $emp_data->employee_no;
                        $statusRecord->status = 1;
                        $statusRecord->created_date = Carbon::now();
                        $statusRecord->save();

                        $data = NewEmployee::find($employee_data->id);
                        $data->employee_no = $emp_data->employee_no;
                        $data->completion_status = 2;
                        $data->approved_ar_id = auth()->user()->employee_no;
                        $data->approved_ar_date = Carbon::now();
                        $data->save();

                        Log::notice('EmployeeController -> Created new permanent employee number - ' . $emp_data->employee_no . ' created by ' . auth()->user()->employee_no);
                        Log::info('EmployeeController -> new permanent employee creation ended');

                        $mailData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                            ->select('last_name', 'initials', 'category_name', 'employee_no', 'personal_email')
                            ->where('employee_no', $emp_data->employee_no)
                            ->first();

                        if ($mailData) {
                            $email = $mailData->personal_email;

                            // Check if the personal_email field is not empty and is a valid email address
                            if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                                $data = [
                                    'name' => $mailData->category_name . ' ' . $mailData->initials . ' ' . $mailData->last_name,
                                    'empNo' => $mailData->employee_no
                                ];

                                $mail = new UJSWelcomeMail($data);

                                DB::afterCommit(function () use ($mail, $email, $mailData) {
                                    Mail::to($email)->send($mail);

                                    $employee = Employee::find($mailData->employee_no);
                                    $employee->welcome_mail = 1;
                                    $employee->save();
                                });
                            } else {

                                Log::alert('Invalid or empty personal_email for employee: ' . $emp_data->employee_no);
                            }
                        } else {

                            Log::alert('Employee data not found for employee: ' . $emp_data->employee_no);
                        }

                        //session data
                        session()->get('employee_no');
                        session()->forget('employee_no');
                        $session = Session::put('employee_no', $emp_data->employee_no);
                    });

                    $mainBranch = Auth()->user()->main_branch_id;
                    $notification = array(
                        'message' => 'New Permanent Employee Inserted Successfully',
                        'alert-type' => 'success'
                    );

                    if ($mainBranch == 51 || $mainBranch == 52) {

                        return redirect()->route('employee.generate.complete')->with($notification);
                    } else {

                        return redirect()->route('employee.generate.add.list')->with($notification);
                    }
                }
            } elseif ($request->employee_work_type == 140) {

                //get nic information
                $empDetails = Http::withHeaders([
                    'Content-Type' => 'application/json',
                ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

                $nicData = json_decode($empDetails->body(), true);

                $employee_data = NewEmployee::find($id);
                $employee_data->file_reference_number = strtoupper($request->file_reference_number);
                $employee_data->main_branch_id = $request->main_branch_id;
                $employee_data->designation_id = $request->designation_id;
                $employee_data->faculty_id = $request->faculty_id;
                $employee_data->department_id = $request->department_id;
                $employee_data->sub_department_id = $request->sub_department_id;

                $employee_data->carder_faculty_id = $request->carder_faculty_id;
                $employee_data->carder_department_id = $request->carder_department_id;
                $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

                $employee_data->initials = strtoupper($request->initials);
                $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
                $employee_data->last_name = ucwords($request->last_name);
                $employee_data->civil_status_id = $request->civil_status_id;
                $employee_data->gender_id = $request->gender_id;
                $employee_data->race_id = $request->race_id;
                $employee_data->religion_id = $request->religion_id;
                $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
                $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
                $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
                $employee_data->permanent_city_id = $request->permanent_city_id;
                $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
                $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
                $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
                $employee_data->postal_city_id = $request->postal_city_id;
                $employee_data->personal_email = $request->personal_email;
                $employee_data->email = $request->email;
                $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
                $employee_data->citizen_registration_no = $request->citizen_registration_no;
                $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
                $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
                $employee_data->current_basic_salary = $request->current_basic_salary;
                $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
                $employee_data->completion_status = 1;
                $employee_data->employee_work_type = 140;

                $employee_data->mobile_no = $request->mobile_no;
                $employee_data->telephone_no = $request->phone_no;
                $employee_data->nic = strtoupper($request->nic);

                $employee_data->nic_old = $nicData['oldnic'] ?? null;
                $employee_data->nic_new = $nicData['newnic'] ?? null;
                $employee_data->active_nic = $nicData['activenic'] ?? null;
                $employee_data->dob_gen = $nicData['dob'] ?? null;

                $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
                $employee_data->title_id = $request->titel_id;
                $employee_data->salary_payment_type = $request->salary_payment_type;
                $employee_data->save();


                $empSimilarData = Employee::where(function ($query) use ($request, $nicData) {
                    $query->where('nic', strtoupper($request->nic))
                        ->orWhere('nic_old', $nicData['oldnic'])
                        ->orWhere('nic_new', $nicData['newnic']);
                })
                    ->where('employee_status_id', 110)
                    ->first();

                if ($empSimilarData) {
                    //return view('admin.employee.create.similar_nic', compact('empSimilarData'));
                    return redirect()->back()->withErrors(['nic' => 'An active employee with this NIC already exists in system.']);
                } else {

                    DB::transaction(function () use ($request, $nicData, $employee_data) {
                        // create employee number


                        $data = new TemporyEmployee();
                        if ($employee_data->active_nic == 1) {
                            $data->nic = strtoupper($employee_data->nic);
                        } else {
                            $data->nic = strtoupper($employee_data->nic_new);
                        }
                        $data->main_branch = Auth()->user()->main_branch_id;
                        $data->assign_ma_user_id = $employee_data->added_ma_id;
                        $data->assign_ma_date = date("Y-m-d");
                        $data->employee_status_id = 110;
                        $data->employee_status_type_id = 112;
                        $data->employee_work_type = 140;
                        $data->added_user_id = auth()->user()->employee_no;
                        $data->added_date = date("Y-m-d");
                        $data->save();

                        //get next table id
                        $maxnumber = Employee::lockForUpdate()->max('id');
                        $nextId = $maxnumber + 1;

                        //get nic information
                        $empDetails = Http::withHeaders([
                            'Content-Type' => 'application/json',
                        ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

                        $nicData = json_decode($empDetails->body(), true);

                        // Check for existing employees with the same NIC
                        $empSimilarData = Employee::where(function ($query) use ($request, $nicData) {
                            $query->where('nic', strtoupper($request->nic))
                                  ->orWhere('nic_old', $nicData['oldnic'])
                                  ->orWhere('nic_new', $nicData['newnic']);
                        })
                        ->where('employee_status_id', 110)
                        ->first();

                        if ($empSimilarData) {
                            throw new \Exception('An active employee with this NIC already exists in the system.');
                        }

                        $emp_data = new Employee();
                        $emp_data->id = $nextId;
                        $emp_data->employee_no = $data->id;
                        $emp_data->file_reference_number = strtoupper($request->file_reference_number);
                        $emp_data->main_branch_id = $request->main_branch_id;
                        $emp_data->designation_id = $request->designation_id;
                        $emp_data->faculty_id = $request->faculty_id;
                        $emp_data->department_id = $request->department_id;
                        $emp_data->sub_department_id = $request->sub_department_id;

                        $emp_data->carder_faculty_id = $request->carder_faculty_id;
                        $emp_data->carder_department_id = $request->carder_department_id;
                        $emp_data->carder_sub_department_id = $request->carder_sub_department_id;

                        $emp_data->initials = strtoupper($request->initials);
                        $emp_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
                        $emp_data->last_name = ucwords($request->last_name);
                        $emp_data->civil_status_id = $request->civil_status_id;
                        $emp_data->gender_id = $request->gender_id;
                        $emp_data->race_id = $request->race_id;
                        $emp_data->religion_id = $request->religion_id;
                        $emp_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
                        $emp_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
                        $emp_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
                        $emp_data->permanent_city_id = $request->permanent_city_id;
                        $emp_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
                        $emp_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
                        $emp_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
                        $emp_data->postal_city_id = $request->postal_city_id;
                        $emp_data->personal_email = $request->personal_email;
                        $emp_data->email = $request->email;
                        $emp_data->state_of_citizenship_id = $request->state_of_citizenship_id;
                        $emp_data->citizen_registration_no = $request->citizen_registration_no;
                        $emp_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
                        $emp_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
                        $emp_data->current_basic_salary = $request->current_basic_salary;
                        $emp_data->emp_highest_edu_level = $request->emp_highest_edu_level;
                        $emp_data->added_ma_user_id = $employee_data->added_ma_id;
                        $emp_data->added_ma_date = date('Y-m-d');
                        $emp_data->assign_ma_user_id = $employee_data->added_ma_id;
                        $emp_data->assign_ma_date = date('Y-m-d');
                        $emp_data->approved_ar_user_id = auth()->user()->employee_no;
                        $emp_data->approved_ar_date = date('Y-m-d');
                        $emp_data->status_id = 1;
                        $emp_data->employee_status_id = 110;
                        $emp_data->employee_status_type_id = 112;
                        $emp_data->employee_work_type = 140;
                        $emp_data->emp_decision_id = 41;
                        $emp_data->mobile_no = $request->mobile_no;
                        $emp_data->telephone_no = $request->telephone_no;
                        $emp_data->nic = strtoupper($request->nic);

                        $emp_data->nic_old = $nicData['oldnic'] ?? null;
                        $emp_data->nic_new = $nicData['newnic'] ?? null;
                        $emp_data->active_nic = $nicData['activenic'] ?? null;
                        $emp_data->dob_gen = $nicData['dob'] ?? null;

                        $emp_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
                        $emp_data->title_id = $request->titel_id;
                        $emp_data->salary_payment_type = $request->salary_payment_type;
                        $emp_data->save();

                        $data = TemporyEmployee::find($emp_data->employee_no);
                        $data->completion_status = 1;
                        $data->updated_at = Carbon::now();
                        $data->save();

                        $statusRecord = new EmployeeSalaryStatus();
                        $statusRecord->employee_no = $emp_data->employee_no;
                        $statusRecord->status = 1;
                        $statusRecord->created_date = Carbon::now();
                        $statusRecord->save();

                        $data = NewEmployee::find($employee_data->id);
                        $data->employee_no = $emp_data->employee_no;
                        $data->completion_status = 2;
                        $data->approved_ar_id = auth()->user()->employee_no;
                        $data->approved_ar_date = Carbon::now();
                        $data->save();

                        Log::notice('EmployeeController -> Created new tempory employee number - ' . $emp_data->employee_no . ' created by ' . auth()->user()->employee_no);
                        Log::info('EmployeeController -> new tempory employee creation ended');

                        $mailData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                            ->select('last_name', 'initials', 'category_name', 'employee_no', 'personal_email')
                            ->where('employee_no', $emp_data->employee_no)
                            ->first();

                        if ($mailData) {
                            $email = $mailData->personal_email;

                            // Check if the personal_email field is not empty and is a valid email address
                            if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                                $data = [
                                    'name' => $mailData->category_name . ' ' . $mailData->initials . ' ' . $mailData->last_name,
                                    'empNo' => $mailData->employee_no
                                ];

                                $mail = new UJSWelcomeMail($data);

                                DB::afterCommit(function () use ($mail, $email, $mailData) {
                                    Mail::to($email)->send($mail);

                                    $employee = Employee::find($mailData->employee_no);
                                    $employee->welcome_mail = 1;
                                    $employee->save();
                                });
                            } else {

                                Log::alert('Invalid or empty personal_email for employee: ' . $emp_data->employee_no);
                            }
                        } else {

                            Log::alert('Employee data not found for employee: ' . $emp_data->employee_no);
                        }

                        //session data
                        session()->get('employee_no');
                        session()->forget('employee_no');
                        $session = Session::put('employee_no', $emp_data->employee_no);
                    });
                    $mainBranch = Auth()->user()->main_branch_id;
                    $notification = array(
                        'message' => 'New Tempory Employee Inserted Successfully',
                        'alert-type' => 'success'
                    );


                    if ($mainBranch == 51 || $mainBranch == 52) {

                        return redirect()->route('employee.generate.complete')->with($notification);
                    } else {

                        return redirect()->route('employee.generate.add.list')->with($notification);
                    }
                }
            } elseif ($request->employee_work_type == 142) {

                //get nic information
                $empDetails = Http::withHeaders([
                    'Content-Type' => 'application/json',
                ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

                $nicData = json_decode($empDetails->body(), true);

                $employee_data = NewEmployee::find($id);
                $employee_data->file_reference_number = strtoupper($request->file_reference_number);
                $employee_data->main_branch_id = $request->main_branch_id;
                $employee_data->designation_id = $request->designation_id;
                $employee_data->faculty_id = $request->faculty_id;
                $employee_data->department_id = $request->department_id;
                $employee_data->sub_department_id = $request->sub_department_id;

                $employee_data->carder_faculty_id = $request->carder_faculty_id;
                $employee_data->carder_department_id = $request->carder_department_id;
                $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

                $employee_data->initials = strtoupper($request->initials);
                $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
                $employee_data->last_name = ucwords($request->last_name);
                $employee_data->civil_status_id = $request->civil_status_id;
                $employee_data->gender_id = $request->gender_id;
                $employee_data->race_id = $request->race_id;
                $employee_data->religion_id = $request->religion_id;
                $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
                $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
                $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
                $employee_data->permanent_city_id = $request->permanent_city_id;
                $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
                $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
                $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
                $employee_data->postal_city_id = $request->postal_city_id;
                $employee_data->personal_email = $request->personal_email;
                $employee_data->email = $request->email;
                $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
                $employee_data->citizen_registration_no = $request->citizen_registration_no;
                $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
                $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
                $employee_data->current_basic_salary = $request->current_basic_salary;
                $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
                $employee_data->completion_status = 1;
                $employee_data->employee_work_type = 142;


                $employee_data->mobile_no = $request->mobile_no;
                $employee_data->telephone_no = $request->phone_no;
                $employee_data->nic = strtoupper($request->nic);

                $employee_data->nic_old = $nicData['oldnic'] ?? null;
                $employee_data->nic_new = $nicData['newnic'] ?? null;
                $employee_data->active_nic = $nicData['activenic'] ?? null;
                $employee_data->dob_gen = $nicData['dob'] ?? null;

                $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
                $employee_data->title_id = $request->titel_id;
                $employee_data->salary_payment_type = $request->salary_payment_type;
                $employee_data->save();

                /********************************************************************* */


                $empSimilarData = Employee::where(function ($query) use ($request, $nicData) {
                    $query->where('nic', strtoupper($request->nic))
                          ->orWhere('nic_old', $nicData['oldnic'])
                          ->orWhere('nic_new', $nicData['newnic']);
                })
                ->where('employee_status_id', 110)
                ->first();

                if ($empSimilarData) {
                    //return view('admin.employee.create.similar_nic', compact('empSimilarData'));
                    return redirect()->back()->withErrors(['nic' => 'An active employee with this NIC already exists in system.']);
                } else {

                    DB::transaction(function () use ($request, $nicData, $employee_data) {
                        // create employee number
                        $mainBranch = Auth()->user()->main_branch_id;

                        $data = new TemporyEmployee();
                        if ($employee_data->active_nic == 1) {
                            $data->nic = strtoupper($employee_data->nic);
                        } else {
                            $data->nic = strtoupper($employee_data->nic_new);
                        }
                        $data->main_branch = Auth()->user()->main_branch_id;
                        $data->assign_ma_user_id = $employee_data->added_ma_id;
                        $data->assign_ma_date = date("Y-m-d");
                        $data->employee_status_id = 110;
                        $data->employee_status_type_id = 112;
                        $data->employee_work_type = 142;
                        $data->added_user_id = auth()->user()->employee_no;
                        $data->added_date = date("Y-m-d");
                        $data->save();

                        //get next table id
                        $maxnumber = Employee::lockForUpdate()->max('id');
                        $nextId = $maxnumber + 1;

                        //get nic information
                        $empDetails = Http::withHeaders([
                            'Content-Type' => 'application/json',
                        ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

                        $nicData = json_decode($empDetails->body(), true);

                        // Check for existing employees with the same NIC
                        $empSimilarData = Employee::where(function ($query) use ($request, $nicData) {
                            $query->where('nic', strtoupper($request->nic))
                                  ->orWhere('nic_old', $nicData['oldnic'])
                                  ->orWhere('nic_new', $nicData['newnic']);
                        })
                        ->where('employee_status_id', 110)
                        ->first();

                        if ($empSimilarData) {
                            throw new \Exception('An active employee with this NIC already exists in the system.');
                        }

                        $emp_data = new Employee();
                        $emp_data->id = $nextId;
                        $emp_data->employee_no = $data->id;
                        $emp_data->file_reference_number = strtoupper($request->file_reference_number);
                        $emp_data->main_branch_id = $request->main_branch_id;
                        $emp_data->designation_id = $request->designation_id;
                        $emp_data->faculty_id = $request->faculty_id;
                        $emp_data->department_id = $request->department_id;
                        $emp_data->sub_department_id = $request->sub_department_id;

                        $emp_data->carder_faculty_id = $request->carder_faculty_id;
                        $emp_data->carder_department_id = $request->carder_department_id;
                        $emp_data->carder_sub_department_id = $request->carder_sub_department_id;

                        $emp_data->initials = strtoupper($request->initials);
                        $emp_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
                        $emp_data->last_name = ucwords($request->last_name);
                        $emp_data->civil_status_id = $request->civil_status_id;
                        $emp_data->gender_id = $request->gender_id;
                        $emp_data->race_id = $request->race_id;
                        $emp_data->religion_id = $request->religion_id;
                        $emp_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
                        $emp_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
                        $emp_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
                        $emp_data->permanent_city_id = $request->permanent_city_id;
                        $emp_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
                        $emp_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
                        $emp_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
                        $emp_data->postal_city_id = $request->postal_city_id;
                        $emp_data->personal_email = $request->personal_email;
                        $emp_data->email = $request->email;
                        $emp_data->state_of_citizenship_id = $request->state_of_citizenship_id;
                        $emp_data->citizen_registration_no = $request->citizen_registration_no;
                        $emp_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
                        $emp_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
                        $emp_data->current_basic_salary = $request->current_basic_salary;
                        $emp_data->emp_highest_edu_level = $request->emp_highest_edu_level;
                        $emp_data->added_ma_user_id = $employee_data->added_ma_id;
                        $emp_data->added_ma_date = date('Y-m-d');
                        $emp_data->assign_ma_user_id = $employee_data->added_ma_id;
                        $emp_data->assign_ma_date = date('Y-m-d');
                        $emp_data->approved_ar_user_id = auth()->user()->employee_no;
                        $emp_data->approved_ar_date = date('Y-m-d');
                        $emp_data->status_id = 1;
                        $emp_data->employee_status_id = 110;
                        $emp_data->employee_status_type_id = 112;
                        $emp_data->employee_work_type = 142;
                        $emp_data->emp_decision_id = 41;
                        $emp_data->mobile_no = $request->mobile_no;
                        $emp_data->telephone_no = $request->telephone_no;
                        $emp_data->nic = strtoupper($request->nic);

                        $emp_data->nic_old = $nicData['oldnic'] ?? null;
                        $emp_data->nic_new = $nicData['newnic'] ?? null;
                        $emp_data->active_nic = $nicData['activenic'] ?? null;
                        $emp_data->dob_gen = $nicData['dob'] ?? null;

                        $emp_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
                        $emp_data->title_id = $request->titel_id;
                        $emp_data->salary_payment_type = $request->salary_payment_type;
                        $emp_data->save();

                        $data = TemporyEmployee::find($emp_data->employee_no);
                        $data->completion_status = 1;
                        $data->updated_at = Carbon::now();
                        $data->save();

                        $statusRecord = new EmployeeSalaryStatus();
                        $statusRecord->employee_no = $emp_data->employee_no;
                        $statusRecord->status = 1;
                        $statusRecord->created_date = Carbon::now();
                        $statusRecord->save();

                        $data = NewEmployee::find($employee_data->id);
                        $data->employee_no = $emp_data->employee_no;
                        $data->completion_status = 2;
                        $data->approved_ar_id = auth()->user()->employee_no;
                        $data->approved_ar_date = Carbon::now();
                        $data->save();

                        Log::notice('EmployeeController -> Created new assignment basis employee number - ' . $emp_data->employee_no . ' created by ' . auth()->user()->employee_no);
                        Log::info('EmployeeController -> new assignment basis employee creation ended');

                        $mailData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                            ->select('last_name', 'initials', 'category_name', 'employee_no', 'personal_email')
                            ->where('employee_no', $emp_data->employee_no)
                            ->first();

                        if ($mailData) {
                            $email = $mailData->personal_email;

                            // Check if the personal_email field is not empty and is a valid email address
                            if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                                $data = [
                                    'name' => $mailData->category_name . ' ' . $mailData->initials . ' ' . $mailData->last_name,
                                    'empNo' => $mailData->employee_no
                                ];

                                $mail = new UJSWelcomeMail($data);

                                DB::afterCommit(function () use ($mail, $email, $mailData) {
                                    Mail::to($email)->send($mail);

                                    $employee = Employee::find($mailData->employee_no);
                                    $employee->welcome_mail = 1;
                                    $employee->save();
                                });
                            } else {

                                Log::alert('Invalid or empty personal_email for employee: ' . $emp_data->employee_no);
                            }
                        } else {

                            Log::alert('Employee data not found for employee: ' . $emp_data->employee_no);
                        }

                        //session data
                        session()->get('employee_no');
                        session()->forget('employee_no');
                        $session = Session::put('employee_no', $emp_data->employee_no);
                    });

                    $mainBranch = Auth()->user()->main_branch_id;
                    $notification = array(
                        'message' => 'New Assignment Basis Employee Inserted Successfully',
                        'alert-type' => 'success'
                    );

                    if ($mainBranch == 51 || $mainBranch == 52) {

                        return redirect()->route('employee.generate.complete')->with($notification);
                    } else {

                        return redirect()->route('employee.generate.add.list')->with($notification);
                    }
                }
            } elseif ($request->employee_work_type == 141) {

                //get nic information
                $empDetails = Http::withHeaders([
                    'Content-Type' => 'application/json',
                ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

                $nicData = json_decode($empDetails->body(), true);

                $employee_data = NewEmployee::find($id);
                $employee_data->file_reference_number = strtoupper($request->file_reference_number);
                $employee_data->main_branch_id = $request->main_branch_id;
                $employee_data->designation_id = $request->designation_id;
                $employee_data->faculty_id = $request->faculty_id;
                $employee_data->department_id = $request->department_id;
                $employee_data->sub_department_id = $request->sub_department_id;

                $employee_data->carder_faculty_id = $request->carder_faculty_id;
                $employee_data->carder_department_id = $request->carder_department_id;
                $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

                $employee_data->initials = strtoupper($request->initials);
                $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
                $employee_data->last_name = ucwords($request->last_name);
                $employee_data->civil_status_id = $request->civil_status_id;
                $employee_data->gender_id = $request->gender_id;
                $employee_data->race_id = $request->race_id;
                $employee_data->religion_id = $request->religion_id;
                $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
                $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
                $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
                $employee_data->permanent_city_id = $request->permanent_city_id;
                $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
                $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
                $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
                $employee_data->postal_city_id = $request->postal_city_id;
                $employee_data->personal_email = $request->personal_email;
                $employee_data->email = $request->email;
                $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
                $employee_data->citizen_registration_no = $request->citizen_registration_no;
                $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
                $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
                $employee_data->sabbatical_start = date("Y-m-d", strtotime($request->sabbatical_start));
                $employee_data->sabbatical_end = date("Y-m-d", strtotime($request->sabbatical_end));
                $employee_data->current_basic_salary = $request->current_basic_salary;
                $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
                $employee_data->completion_status = 1;
                $employee_data->employee_work_type = 141;

                $employee_data->mobile_no = $request->mobile_no;
                $employee_data->telephone_no = $request->phone_no;
                $employee_data->nic = strtoupper($request->nic);

                $employee_data->nic_old = $nicData['oldnic'] ?? null;
                $employee_data->nic_new = $nicData['newnic'] ?? null;
                $employee_data->active_nic = $nicData['activenic'] ?? null;
                $employee_data->dob_gen = $nicData['dob'] ?? null;

                $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
                $employee_data->title_id = $request->titel_id;
                $employee_data->salary_payment_type = $request->salary_payment_type;
                $employee_data->save();

                /********************************************************************* */

                $empSimilarData = Employee::where(function ($query) use ($request, $nicData) {
                    $query->where('nic', strtoupper($request->nic))
                        ->orWhere('nic_old', $nicData['oldnic'])
                        ->orWhere('nic_new', $nicData['newnic']);
                })
                    ->where('employee_status_id', 110)
                    ->first();

                if ($empSimilarData) {
                    // return view('admin.employee.create.similar_nic', compact('empSimilarData'));
                    return redirect()->back()->withErrors(['nic' => 'An active employee with this NIC already exists in system.']);
                } else {

                    DB::transaction(function () use ($request, $nicData, $employee_data) {
                        // create employee number

                        $data = new ContractEmployee();
                        if ($employee_data->active_nic == 1) {
                            $data->nic = strtoupper($employee_data->nic);
                        } else {
                            $data->nic = strtoupper($employee_data->nic_new);
                        }
                        $data->main_branch = Auth()->user()->main_branch_id;
                        $data->assign_ma_user_id = $employee_data->added_ma_id;
                        $data->assign_ma_date = date("Y-m-d");
                        $data->employee_status_id = 110;
                        $data->employee_status_type_id = 112;
                        $data->employee_work_type = 141;
                        $data->added_user_id = auth()->user()->employee_no;
                        $data->added_date = date("Y-m-d");
                        $data->save();

                        //get next table id
                        $maxnumber = Employee::lockForUpdate()->max('id');
                        $nextId = $maxnumber + 1;

                        //get nic information
                        $empDetails = Http::withHeaders([
                            'Content-Type' => 'application/json',
                        ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

                        $nicData = json_decode($empDetails->body(), true);

                        // Check for existing employees with the same NIC
                        $empSimilarData = Employee::where(function ($query) use ($request, $nicData) {
                            $query->where('nic', strtoupper($request->nic))
                                  ->orWhere('nic_old', $nicData['oldnic'])
                                  ->orWhere('nic_new', $nicData['newnic']);
                        })
                        ->where('employee_status_id', 110)
                        ->first();

                        if ($empSimilarData) {
                            throw new \Exception('An active employee with this NIC already exists in the system.');
                        }

                        $emp_data = new Employee();
                        $emp_data->id = $nextId;
                        $emp_data->employee_no = $data->id;
                        $emp_data->file_reference_number = strtoupper($request->file_reference_number);
                        $emp_data->main_branch_id = $request->main_branch_id;
                        $emp_data->designation_id = $request->designation_id;
                        $emp_data->faculty_id = $request->faculty_id;
                        $emp_data->department_id = $request->department_id;
                        $emp_data->sub_department_id = $request->sub_department_id;

                        $emp_data->carder_faculty_id = $request->carder_faculty_id;
                        $emp_data->carder_department_id = $request->carder_department_id;
                        $emp_data->carder_sub_department_id = $request->carder_sub_department_id;

                        $emp_data->initials = strtoupper($request->initials);
                        $emp_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
                        $emp_data->last_name = ucwords($request->last_name);
                        $emp_data->civil_status_id = $request->civil_status_id;
                        $emp_data->gender_id = $request->gender_id;
                        $emp_data->race_id = $request->race_id;
                        $emp_data->religion_id = $request->religion_id;
                        $emp_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
                        $emp_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
                        $emp_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
                        $emp_data->permanent_city_id = $request->permanent_city_id;
                        $emp_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
                        $emp_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
                        $emp_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
                        $emp_data->postal_city_id = $request->postal_city_id;
                        $emp_data->personal_email = $request->personal_email;
                        $emp_data->email = $request->email;
                        $emp_data->state_of_citizenship_id = $request->state_of_citizenship_id;
                        $emp_data->citizen_registration_no = $request->citizen_registration_no;
                        $emp_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
                        $emp_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
                        $emp_data->sabbatical_start = date("Y-m-d", strtotime($request->sabbatical_start));
                        $emp_data->sabbatical_end = date("Y-m-d", strtotime($request->sabbatical_end));
                        $emp_data->current_basic_salary = $request->current_basic_salary;
                        $emp_data->emp_highest_edu_level = $request->emp_highest_edu_level;
                        $emp_data->added_ma_user_id = $employee_data->added_ma_id;
                        $emp_data->added_ma_date = date('Y-m-d');
                        $emp_data->assign_ma_user_id = $employee_data->added_ma_id;
                        $emp_data->assign_ma_date = date('Y-m-d');
                        $emp_data->approved_ar_user_id = auth()->user()->employee_no;;
                        $emp_data->approved_ar_date = date('Y-m-d');
                        $emp_data->status_id = 1;
                        $emp_data->employee_status_id = 110;
                        $emp_data->employee_status_type_id = 112;
                        $emp_data->employee_work_type = 141;
                        $emp_data->emp_decision_id = 41;
                        $emp_data->mobile_no = $request->mobile_no;
                        $emp_data->telephone_no = $request->telephone_no;
                        $emp_data->nic = strtoupper($request->nic);

                        $emp_data->nic_old = $nicData['oldnic'] ?? null;
                        $emp_data->nic_new = $nicData['newnic'] ?? null;
                        $emp_data->active_nic = $nicData['activenic'] ?? null;
                        $emp_data->dob_gen = $nicData['dob'] ?? null;

                        $emp_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
                        $emp_data->title_id = $request->titel_id;
                        $emp_data->salary_payment_type = $request->salary_payment_type;
                        $emp_data->save();

                        $data = ContractEmployee::find($emp_data->employee_no);
                        $data->completion_status = 1;
                        $data->updated_at = Carbon::now();
                        $data->save();

                        $statusRecord = new EmployeeSalaryStatus();
                        $statusRecord->employee_no = $emp_data->employee_no;
                        $statusRecord->status = 1;
                        $statusRecord->created_date = Carbon::now();
                        $statusRecord->save();

                        $data = NewEmployee::find($employee_data->id);
                        $data->employee_no = $emp_data->employee_no;
                        $data->completion_status = 2;
                        $data->approved_ar_id = auth()->user()->employee_no;
                        $data->approved_ar_date = Carbon::now();
                        $data->save();

                        Log::notice('EmployeeController -> Created new contract employee number - ' . $emp_data->employee_no . ' created by ' . auth()->user()->employee_no);
                        Log::info('EmployeeController -> new contract employee creation ended');

                        $mailData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                            ->select('last_name', 'initials', 'category_name', 'employee_no', 'personal_email')
                            ->where('employee_no', $emp_data->employee_no)
                            ->first();

                        if ($mailData) {
                            $email = $mailData->personal_email;

                            // Check if the personal_email field is not empty and is a valid email address
                            if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                                $data = [
                                    'name' => $mailData->category_name . ' ' . $mailData->initials . ' ' . $mailData->last_name,
                                    'empNo' => $mailData->employee_no
                                ];

                                $mail = new UJSWelcomeMail($data);

                                DB::afterCommit(function () use ($mail, $email, $mailData) {
                                    Mail::to($email)->send($mail);

                                    $employee = Employee::find($mailData->employee_no);
                                    $employee->welcome_mail = 1;
                                    $employee->save();
                                });
                            } else {

                                Log::alert('Invalid or empty personal_email for employee: ' . $emp_data->employee_no);
                            }
                        } else {

                            Log::alert('Employee data not found for employee: ' . $emp_data->employee_no);
                        }

                        //session data
                        session()->get('employee_no');
                        session()->forget('employee_no');
                        $session = Session::put('employee_no', $emp_data->employee_no);
                    });

                    $mainBranch = Auth()->user()->main_branch_id;
                    $notification = array(
                        'message' => 'New Contract Employee Inserted Successfully',
                        'alert-type' => 'success'
                    );

                    if ($mainBranch == 51 || $mainBranch == 52) {

                        return redirect()->route('employee.generate.complete')->with($notification);
                    } else {

                        return redirect()->route('employee.generate.add.list')->with($notification);
                    }
                }
            }
        }
    }

    public function generateDelete($id)
    {

        $empNo = decrypt($id);
        $employee = NewEmployee::find($empNo);
        $employee->delete();

        $notification = array(
            'message' => 'New Employee Deleted Successfully',
            'alert-type' => 'warning'
        );

        return redirect()->route('employee.generate.add.list')->with($notification);
    }

    public function generateComplete()
    {

        if (!session()->get('employee_no')) {

            $notification = array(
                'message' => 'Employee Number Generation Failed',
                'alert-type' => 'error'
            );

            return redirect()->route('employee.generate.add.list')->with($notification);
        }

        $empSimilarData = Employee::find(session()->get('employee_no'));

        return view('admin.employee.create.final', compact('empSimilarData'));
    }
}
