<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\Category;
use App\Models\City;
use App\Models\Department;
use App\Models\DepartmentSub;
use App\Models\Designation;
use App\Models\DesignationSubGroup;
use App\Models\District;
use App\Models\User;
use App\Models\Vacancy;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Http\Request;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public function getCategories(array $categoryIds)
    {
        $results = Category::whereIn('category_type_id', $categoryIds)->orderBy('sorting_order', 'ASC')->get();
        return $results;
    }/*get category type related category data*/

    public function subDepartmentLoad($department_id){

        $subDepartmentList = DepartmentSub::where('department_code',$department_id)->orderBy('sub_departmet_name','ASC')->get();
        return json_encode($subDepartmentList);
    }/*get sub department list according to the department*/

    public function departmentLoad($faculty_id){

        $departmentList = Department::where('faculty_code',$faculty_id)->orderBy('department_name','ASC')->get();
        return json_encode($departmentList);
    }/*get department list according to the faculty*/

    public function employeeStatusTypeLoad($employee_status_id){

        $categories = $this->getCategories([22]);
        $employeeStatusTypes = $categories->where('category_type_id','22')->where('category_code',$employee_status_id);
        return json_encode($employeeStatusTypes);

    }/*get employee status type list according to the employee status*/

    public function designationMainGroupLoad($designation_id){

        $maingroup = Designation::where('id',$designation_id)->where('active_status',1)->get();
        return json_encode($maingroup);
    }/*get department list according to the faculty*/

    public function subDesignationGroupLoad($designation_main_id){

        $maingroup = DesignationSubGroup::where('designation_main_group_id',$designation_main_id)->get();
        return json_encode($maingroup);
    }/*get sub designation list according to the main designation*/

    public function DesignationLoad($designation_main_id){

        $designation = Designation::where('designation_main_id',$designation_main_id)->where('active_status',1)->get();
        return json_encode($designation);
    }/*get sub designation list according to the main designation*/

    public function EmployeeGroupLoad($main_branch_id){

        $categories = $this->getCategories([19]);
        $employeeGroups = $categories->where('category_type_id','19')->where('category_code',$main_branch_id);
        return json_encode($employeeGroups);
    }/*get employee group according to the employee main branch*/

    public function districtLoad($province_id){

        $districts = District::where('province_id',$province_id)->get();
        return json_encode($districts);
    }/*get district list according to the province*/

    public function cityLoad($district_id){

        $cities = City::where('district_id',$district_id)->get();
        return json_encode($cities);
    }/*get cities list according to the district*/

    public function EmployeeDegreeClass($degree_type){

        $categories = $this->getCategories([40]);
        $degreeTypes = $categories->where('category_type_id','40')->where('category_code',$degree_type);
        return json_encode($degreeTypes);

    }

    public function departmentAllLoad(){

        $departmentList = Department::orderBy('department_name','ASC')->get();
        return json_encode($departmentList);
    }/*get department list according to the faculty*/

    public function vacancyDepartmentLoad($faculty_id){

        $currentDate = date('Y-m-d');
        $departments = Vacancy::where('date_closed', '>=', $currentDate)
                      ->where('date_opened', '<=', $currentDate)
                      ->pluck('department_id')->unique();
        $departmentList = Department::whereIn('id', $departments)->where('faculty_code',$faculty_id)->orderBy('department_name','ASC')->get();
        return json_encode($departmentList);
    }/*get sub department list according to the department*/

    public function selectSearch(Request $request)
    {

        $data = Application::select("nic as value", "reference_no as reference_no","new_nic as new_nic")
                ->where('nic', 'LIKE', '%' . $request->get('search') . '%')
                ->orWhere('new_nic', 'LIKE', '%' . $request->get('search') . '%')
                ->orWhere('reference_no', 'LIKE', '%' . $request->get('search') . '%')
                ->get();

          return response()->json($data);
    }

    public function checkApplicationStatusCount(Request $request)
    {

    $vacancyId = $request->input('vacancy_id');

    // Use the Application model to count applications with status 0 for the specific vacancy
    $count = Application::where('vacancy_id', $vacancyId)
            ->where('application_decision_id', 34)
            ->where('ma_check_status',0)
            ->count();

       return response()->json(['count' => $count]);
   }

   public function checkApplicationDepartmentInfo(Request $request)
    {

    $vacancyId = $request->input('vacancy_id');

    $text = Vacancy::join('departments', 'vacancies.department_id', '=', 'departments.id')
            ->join('department_heads', 'department_heads.department_id', '=', 'departments.id')
            ->join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('categories as head_pos', 'department_heads.head_position', '=', 'head_pos.id')
            ->select('employees.*', 'categories.category_name as category_name','head_pos.category_name as head_position','departments.department_name')
            ->where('vacancies.id', $vacancyId)
            ->where('department_heads.active_status', '=', 1)
            ->get();


     if(count($text) > 0){

        foreach ($text as $dephs) {

            $headName = $dephs->category_name . " " . $dephs->initials . " " . $dephs->last_name;
            $headposition = $dephs->head_position;
            $deptName = ucfirst($dephs->department_name);
        }

        return response()->json(['count' => count($text),
                                 'headName' => $headName,
                                 'headposition' => $headposition,
                                  'deptName' => $deptName]);

     }else{

        $text1 = Vacancy::join('departments', 'vacancies.department_id', '=', 'departments.id')
               ->select('departments.department_name')
               ->where('vacancies.id', $vacancyId)
               ->get();

        foreach ($text1 as $dephs) {

                $headName = '';
                $headposition = '';
                $deptName = ucfirst($dephs->department_name);
        }

        return response()->json(['count' => count($text),
                                 'headName' => $headName,
                                 'headposition' => $headposition,
                                  'deptName' => $deptName]);

     }

   }

   public function checkApplicationHeadStatusCount(Request $request)
    {

    $vacancyId = $request->input('vacancy_id');

    // Use the Application model to count applications with status 0 for the specific vacancy
    $count = Application::where('vacancy_id', $vacancyId)
            ->where('application_decision_id', 34)
            ->whereIn('head_check_status', array(0))
            ->count();

       return response()->json(['count' => $count]);
   }

   public function checkApplicationFinalizeStatusCount(Request $request)
    {

    $vacancyId = $request->input('vacancy_id');

    // Use the Application model to count applications with status 0 for the specific vacancy
    $count = Application::where('vacancy_id', $vacancyId)
            ->whereIn('application_decision_id', array(35))
            ->whereIn('head_check_status', array(1))
            ->count();

       return response()->json(['count' => $count]);
   }


   public function checkApplicationVacancyFinal(Request $request)
    {

    $vacancyId = $request->input('vacancy_id');

    // Use the Application model to count applications with status 0 for the specific vacancy
    $allPending =  Application::where('vacancy_id', $vacancyId)
                   ->where('application_decision_id', 37)
                   ->where('interview_status', 2)
                   ->count();

    $dutyAssumePending = Application::where('vacancy_id', $vacancyId)
                         ->where('application_decision_id', 37)
                         ->where('duty_assume_status', 0)
                         ->count();

    $dutyAssumeReject = Application::where('vacancy_id', $vacancyId)
                         ->where('application_decision_id', 37)
                         ->where('duty_assume_status', 1)
                         ->count();

    $dutyAssumeApproved = Application::where('vacancy_id', $vacancyId)
                         ->where('application_decision_id', 37)
                         ->where('duty_assume_status', 2)
                         ->count();

    $applicantToEmployee = Application::where('vacancy_id', $vacancyId)
                         ->where('application_decision_id', 37)
                         ->where('duty_assume_status', 3)
                         ->count();

       return response()->json(['allPending' => $allPending,
                                'dutyAssumePending' => $dutyAssumePending,
                                'dutyAssumeReject' => $dutyAssumeReject,
                                'dutyAssumeApproved' => $dutyAssumeApproved,
                                'applicantToEmployee' => $applicantToEmployee
                            ]);
   }
}
