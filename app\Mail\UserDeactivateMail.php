<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class UserDeactivateMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function envelope()
    {
        return new Envelope(
            subject: 'HRMS USJ - USER DEACTIVATION',
            tags: ['deactivation'],
        );
    }


    public function content()
    {
        return new Content(
            markdown: 'emails.user_deactivate',
            with: [
                'data' => $this->data
            ],
        );
    }

    public function attachments()
    {
        return [];
    }
}
