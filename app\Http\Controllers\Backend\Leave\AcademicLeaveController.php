<?php

namespace App\Http\Controllers\Backend\Leave;

use App\Http\Controllers\Controller;
use App\Models\AcademicYear;
use App\Models\Category;
use App\Models\Employee;
use App\Models\leaveAcademicSummery;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule as ValidationRule;

class AcademicLeaveController extends Controller
{

    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|sc|lc|cc');
    }

    public function historyOpen(Request $request)
    {

        $mainBranch = Auth()->user()->main_branch_id;
        // $empNo = Auth()->user()->employee_no;

        if (Auth()->user()->hasRole(['administrator'])) {

            $emp_text = Employee::join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('designations', 'designations.id', '=', 'employees.designation_id')
                ->select(
                    'employees.employee_no',
                    'employees.initials',
                    'employees.last_name',
                    't.category_name as title'
                )
                ->where('employees.main_branch_id', 52)
                ->where('employees.employee_status_id', 110)
                ->where('designations.ugc_mis', 135)
                ->get();
        } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

            $emp_text = Employee::join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('designations', 'designations.id', '=', 'employees.designation_id')
                ->select(
                    'employees.employee_no',
                    'employees.initials',
                    'employees.last_name',
                    't.category_name as title'
                )
                ->where('employees.main_branch_id', 52)
                ->where('employees.employee_status_id', 110)
                ->where('designations.ugc_mis', 135)
                ->where('assign_ma_user_id', Auth()->user()->employee_no)
                ->get();
        } else {
            $emp_text = array();
        }

        if (isset($request->emp)) {

            $emp_search = Employee::join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->select(
                    'employees.employee_no',
                    'employees.initials',
                    'employees.last_name',
                    't.category_name as title',
                    'g.category_name as grade',
                    'designations.designation_name',
                    'departments.department_name',
                    'faculties.faculty_name',
                    'faculties.id as fac_id'
                )
                ->where('employees.employee_no', '=', $request->emp)
                ->get();

            if (count($emp_search) > 0) {
                foreach ($emp_search as $emp_searchs) {
                    $empNo = $emp_searchs->employee_no;
                    $name = $emp_searchs->title . ' ' . $emp_searchs->initials . ' ' . $emp_searchs->last_name;
                    $desig =  $emp_searchs->designation_name . ' ' .  $emp_searchs->grade;
                    $dep = $emp_searchs->department_name;
                    $fac = $emp_searchs->faculty_name;
                    $fac_id = $emp_searchs->fac_id;
                }

                $leave_type_text = Category::where('category_type_id', 54)->get();

                // $ac_year_text = AcademicYear::where('fac_id', $fac_id)->get();
                // Get the current year
                $currentYear = date('Y');

                // Create the academic years dataset
                $academicYears = [];
                for ($i = 4; $i >= 0; $i--) {
                    $startYear = $currentYear - $i;
                    $endYear = $currentYear - ($i - 1);
                    $academicYears[] = "{$startYear}/{$endYear}";
                }

                $leave_text = leaveAcademicSummery::
                    join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->join('categories as salPay', 'salPay.id', '=', 'leave_academic_summeries.sal_pay_type')
                    ->select(
                        'leave_academic_summeries.*',
                        
                        'categories.category_name as lave_type',
                        'salPay.category_name as salPayType'
                    )
                    ->where('leave_academic_summeries.emp_no', $request->emp)
                    ->where('leave_academic_summeries.status', 0)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();

                $sal_pay_type = Category::where('category_type_id', 55)->get();
            } else {
                $empNo = '';
                $name = '';
                $desig = '';
                $dep = '';
                $fac = '';
                $leave_type_text = array();
                // $ac_year_text = array();
                $leave_text = array();
                $sal_pay_type = array();
                $academicYears = [];
            }
        } else {
            $empNo = '';
            $name = '';
            $desig = '';
            $dep = '';
            $fac = '';
            $leave_type_text = array();
            // $ac_year_text = array();
            $leave_text = array();
            $sal_pay_type = array();
            $academicYears = [];
        }

        return view('admin.leave.academic.history', compact(
            'emp_text',
            'empNo',
            'name',
            'desig',
            'dep',
            'fac',
            'leave_type_text',
            'academicYears',
            'leave_text',
            'sal_pay_type'
        ));
    }

    public function historyStore(Request $request)
    {
        $validatedData = $request->validate(
            [
                'leaveType' => 'required',
                'formDate' => 'required|date',
                'toDate' => 'required|date',
                'salPayType' => 'required',
                'acYears' => [
                    ValidationRule::requiredIf(function () use ($request) {
                        return (($request->leaveType != 313) && ($request->leaveType != 314));
                    }),
                ]
            ],
            [
                'leaveType.required' => 'Please select leave type',
                'formDate.required' => 'Please select the date',
                'formDate.date' => 'incorrect format',
                'toDate.required' => 'Please select the date',
                'toDate.date' => 'incorrect format',
            ]
        );

        if ($request->years == '' && $request->months == '' && $request->days == '') {
        } else {
            $due_years = $request->input('years') ?? 0;
            $due_months = $request->input('months') ?? 0;
            $due_days = $request->input('days') ?? 0;

            $leave_summery = new leaveAcademicSummery();
            $leave_summery->emp_no = $request->empNo;
            $leave_summery->leave_type = $request->leaveType;
            $leave_summery->ac_year_id = $request->acYears;
            $leave_summery->starting_date = date("Y-m-d", strtotime($request->formDate));
            $leave_summery->end_date = date("Y-m-d", strtotime($request->toDate));
            $leave_summery->sal_pay_type = $request->salPayType;
            $leave_summery->duration_year = $due_years;
            $leave_summery->duration_month = $due_months;
            $leave_summery->duration_day = $due_days;
            $leave_summery->purpose = $request->purpose;
            $leave_summery->add_user = Auth()->user()->employee_no;
            $leave_summery->add_date = today();
            $leave_summery->save();

            $notification = array(
                'message' => 'Successfully submitted',
                'alert-type' => 'success'
            );

            return redirect()->route('leave.ac.history.open');
        }
    }
}
