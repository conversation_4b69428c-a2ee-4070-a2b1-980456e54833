<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class PromotionApplicationsNonacademic extends Model
{
    use HasFactory,LogsActivity;
    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('hrms_promotion_applications_nonacademics')
        ->logOnlyDirty()
        ->dontLogIfAttributesChangedOnly(['updated_at'])
        ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    public function getCurrentDesignation()
    {
        return $this->belongsTo(Designation::class,'current_desigantion_id');
    }

    public function getPromotionDesignation()
    {
        return $this->belongsTo(Designation::class,'promotion_designation_id');
    }

    public function getPromotionPerformanceRate()
    {
        return $this->belongsTo(Category::class,'performance');
    }

    public function getEmployeeDetail(){

        return $this->belongsTo(Employee::class,'emp_no');
    }

}
