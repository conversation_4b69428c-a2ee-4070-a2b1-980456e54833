<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewEmployeeCreation extends Notification implements ShouldQueue
{
    use Queueable;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }


    public function via($notifiable)
    {
        return ['database','mail'];
    }


    public function toMail($notifiable)
    {
        return (new MailMessage)
                    ->from('<EMAIL>', 'USJ HRM SYSTEM')
                    ->greeting('New '. $this->data['employee_work_type'].' Employee Assigned')
                    ->line('Assign new employee with employee number '. $this->data['employee_no'] .' and nic number ' .$this->data['nic'])
                    ->action('Complete Employee Data', url('https://hrms.sjp.ac.lk/employee/new/check/index'))
                    ->line('Thank you for using our application!');
    }


    public function toArray($notifiable)
    {
        return [
           'headline' => 'New '. $this->data['employee_work_type'].' Employee Assigned',
           'message' => 'Assign new employee with employee number '. $this->data['employee_no'] .' and nic number ' .$this->data['nic'],
           'data' => $this->data,
           'type' => 268
        ];
    }
}
