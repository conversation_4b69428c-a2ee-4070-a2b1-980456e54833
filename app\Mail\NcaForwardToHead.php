<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NcaForwardToHead extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }


    public function envelope()
    {
        return new Envelope(
            subject: 'HRMS USJ - NonAcademic Promotion',
            tags: ['promotion'],
        );
    }


    public function content()
    {
        return new Content(
            markdown: 'emails.nca_forwar_to_head',
            with: [
                'data' => $this->data
            ],
        );
    }


    public function attachments()
    {
        return [];
    }
}
