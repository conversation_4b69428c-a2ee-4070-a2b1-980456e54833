<?php

namespace App\Http\Controllers\Setting;

use App\Http\Controllers\Controller;
use App\Mail\MaNotifyMail;
use App\Mail\UJSWelcomeMail;
use App\Models\Employee;
use App\Models\NonIncrement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class EmailController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        //$this->middleware('role:super-admin');
    }

    public function sendTestEmail($empNumber)
    {
        $data = Employee::find($empNumber);

        if (empty($data)) {
            echo 'Employee not found';
            return;
        }
        $emailData = [
            'name' => $data->initials . ' ' . $data->last_name,
        ];

        $mail = new MaNotifyMail($emailData);

        try {
            // Attempt to send the email
            Mail::to($data->email)->send($mail);
            // Log success message
            echo ('Email sent successfully to ' . $data->email);
        } catch (\Exception $e) {
            // Log error message
            echo ('Failed to send email to ' . $data->email . '. Error: ' . $e->getMessage());
        }
    }

    public function nicGenerate()
    {
        $data = Employee::whereNull('nic_old')
            ->whereNull('nic_new')
            ->whereNull('active_nic')
            ->whereNull('dob_gen')
            ->select('nic', 'employee_no')
            ->limit(100)
            ->get();
        //dd(count($data));

        foreach ($data as $key => $value) {
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => $value->nic]);

            $nicData = json_decode($empDetails->body(), true);

            if (!empty($nicData)) {

                $employee = Employee::where('employee_no', $value->employee_no)->first();

                if ($employee) {
                    $employee->nic_old = $nicData['oldnic'] ?? null;
                    $employee->nic_new = $nicData['newnic'] ?? null;
                    $employee->active_nic = $nicData['activenic'] ?? null;
                    $employee->dob_gen = $nicData['dob'] ?? null;
                    $employee->save();
                }

            }
        }
    }

    public function zoomStatusUpdate()
    {
        $data = DB::table('test')
            ->select('EmpNo', 'Email')
            ->get();
        //dd(count($data));

        foreach ($data as $key => $value) {


            if (!empty($data)) {
                Employee::where('employee_no', $value->EmpNo)->update([
                    'zoom_active_status' => 1,
                ]);
            }
        }
    }

    public function emailTest($empNo)
    {

        $mailData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select('last_name', 'initials', 'category_name', 'employee_no', 'personal_email')
            ->where('employee_no', $empNo)
            ->first();

        $data = [
            'name' => $mailData->category_name . ' ' . $mailData->initials . ' ' . $mailData->last_name,
            'empNo' => $mailData->employee_no
        ];

        $mail = new UJSWelcomeMail($data);

        Mail::to($mailData->personal_email)->send($mail);
    }

    public function incrementHeadUpdate()
    {

        $data = NonIncrement::join('employees', 'non_increments.employee_no', '=', 'employees.employee_no')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('department_heads', 'departments.id', '=', 'department_heads.department_id')
            //->where('department_heads.active_status', )
            ->whereIn('employees.faculty_id', [50, 51])
            ->select('employees.employee_no', 'department_heads.head_position')
            ->get();

        //dd($data);

        foreach ($data as $key => $value) {
            $nonIncrement = NonIncrement::where('employee_no', $value->employee_no)->first();

            if ($nonIncrement) {
                $nonIncrement->head_position = $value->head_position;
                $nonIncrement->save();
            }
        }

    }

    public function incrementHeadUpdate1()
    {

        $data = NonIncrement::join('employees', 'non_increments.employee_no', '=', 'employees.employee_no')
            ->whereIn('employees.department_id', [101, 201, 301, 401, 501, 801, 901, 1001, 1101, 1201, 1301])
            ->select('employees.employee_no')
            ->get();

        foreach ($data as $key => $value) {

            $nonIncrement = NonIncrement::where('employee_no', $value->employee_no)->first();

            if ($nonIncrement) {
                $nonIncrement->head_position = 238;
                $nonIncrement->save();
            }
        }
    }

    public function incrementDeptUpdate()
    {

        $data = NonIncrement::join('employees', 'non_increments.employee_no', '=', 'employees.employee_no')
            ->select('employees.employee_no', 'employees.designation_id', 'employees.department_id')
            ->get();

        foreach ($data as $key => $value) {

            $nonIncrement = NonIncrement::where('employee_no', $value->employee_no)->first();

            if ($nonIncrement) {
                $nonIncrement->designation_id = $value->designation_id;
                $nonIncrement->department_id = $value->department_id;
                $nonIncrement->save();
            }
        }
    }
}
