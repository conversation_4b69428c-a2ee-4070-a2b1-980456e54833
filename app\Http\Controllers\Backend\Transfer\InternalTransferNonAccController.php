<?php

namespace App\Http\Controllers\Backend\Transfer;

use App\Http\Controllers\Controller;
use App\Models\Department;
use App\Models\Employee;
use App\Models\EmployeeChangeHistory;
use App\Models\InternalTransfer;
use App\Models\Faculty;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InternalTransferNonAccController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }


    public function indexNonAcInternal(Request $request)
    {
        if (isset($request->employee_no)) {
            Log::info('InternalTransferNonAccController -> Internal transfer data getAll started');
            $mainBranch = Auth()->user()->main_branch_id;
            $search_emp_no = $request->employee_no;


            if ($mainBranch == 51) {

                $emp_inter_trans = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                    ->select('internal_transfers.*', 'departments.department_name')
                    ->where('internal_transfers.emp_no', $search_emp_no)
                    ->where('internal_transfers.emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('internal_transfers.transfer_date')
                    ->get();

                $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->select(
                        'title.category_name as titleName',
                        'employees.employee_no',
                        'employees.initials',
                        'employees.last_name',
                        'designations.designation_name',
                        'grade.category_name as gradeName',
                        'departments.department_name',
                        'employees.lock'
                    )
                    ->where('employees.employee_no', $search_emp_no)
                    //->where('employees.lock', 1)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->get();

            }

            //academic devision data collection
            elseif ($mainBranch == 52) {

                $emp_inter_trans = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                    ->select('internal_transfers.*', 'departments.department_name')
                    ->where('internal_transfers.emp_no', $search_emp_no)
                    ->where('internal_transfers.emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('internal_transfers.transfer_date')
                    ->get();



                if (Auth()->user()->hasRole(['est-head'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'employees.lock', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.lock', 1)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        ->get();

                } elseif (Auth()->user()->hasRole(['cc','sc'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.lock', 1)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->get();
                }
            }
            //non academic division data collection
            elseif ($mainBranch == 53) {

                $emp_inter_trans = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                    ->select('internal_transfers.*', 'departments.department_name')
                    ->where('internal_transfers.emp_no', $search_emp_no)
                    ->where('internal_transfers.emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('internal_transfers.transfer_date')
                    ->get();

                if (Auth()->user()->hasRole(['est-head'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.lock', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.lock', 1)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->get();

                } elseif (Auth()->user()->hasRole(['cc','sc'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.lock', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.lock', 1)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->get();
                }
            }
            if (count($empfetchDatas) > 0) {
                foreach ($empfetchDatas as $empData) {
                    $emp_no = $empData->employee_no;
                    $emp_name = $empData->titleName . " " . $empData->initials . " " . $empData->last_name;
                    $designation_name = $empData->designation_name . " " . $empData->gradeName;
                    $department_name = $empData->department_name;
                }
            } else {
                $emp_no = '';
                $emp_name = '';
                $designation_name = '';
                $department_name = '';
                $emp_inter_trans = array();

                $notification = array(
                    'message' => 'employee number not found or employee profile not lock',
                    'alert-type' => 'error'
                );

                return redirect()->back()->with($notification);
            }

            $pass_employee_no = $request->employee_no;
        } else {
            $pass_employee_no = '';
            $emp_no = '';
            $emp_name = '';
            $designation_name = '';
            $department_name = '';
            $faculties = '';
            $emp_inter_trans = array();
        }

        $fac = DB::table('faculties')->where('deleted_at', '=', NULL)->orderBy('faculty_name')->get();
        $dep = Department::join('faculties', 'departments.faculty_code', '=', 'faculties.id')
            //->join('faculties', 'departments.faculty_code', '=', 'faculties.id')
            ->select('departments.*', 'faculties.id')
            //->where( 'departments.faculty_code')
            ->orderBy('department_name')
            ->get();

        $trans_data = DB::table('employee_change_histories')->get();
        $trans_data = DB::table('employee_change_histories')
            ->join('employees', 'employees.employee_no', '=', 'employee_change_histories.emp_no')
            ->where('type', '=', '346')
            ->where('employee_change_histories.approved_status')
            ->get();



        Log::info('InternalTransferNonAccController -> Internal transfer data getAll ended');

        return view('admin.transfer.nac_internal_transfer.non_academic_transfer', compact('pass_employee_no', 'emp_no', 'emp_name', 'designation_name', 'department_name', 'emp_inter_trans', 'dep', 'fac', 'trans_data'));
    }

    public function storeInternal(Request $request)
    {

        Log::info('InternalTransferNonAccController -> Internal transfer data store started');

        if (!isset($request->emp_no)) {


            $notification = array(
                'message' => 'Please enter employee number',
                'alert-type' => 'error'
            );
            return redirect()->route('transfer.internal.nonaccedemic')->with($notification);
        } else {

            $perRequest = EmployeeChangeHistory::where('emp_no', $request->emp_no)->where('type', 346)->where('approved_status', 0)->first();
            if ($perRequest) {

                $notification = array(
                    'message' => 'You already send a request for internal transfer update request',
                    'alert-type' => 'error'
                );

                return redirect()->route('transfer.internal.nonaccedemic')->with($notification);
            }

            $NonAceInterTransfer = new EmployeeChangeHistory();
            $NonAceInterTransfer->type = 346; //346
            $NonAceInterTransfer->emp_no = $request->emp_no;

            //$depName = $this->getDepartmentName($id);
            $remark = $request->description2;
            $NonAceInterTransfer->pervious_record = json_encode([
                [
                    'key' => $request->dep_id,
                    'value' => $request->dep_id,
                    'text' => 'Previous Department'
                ],
                [
                    'key' => '',
                    'value' => '',
                    'text' => 'Effective Date'
                ],

                [
                    'key' => '',
                    'value' => '',
                    'text' => 'Remark'
                ]
            ]);

            $transfer_date = date("Y-m-d", strtotime($request->effDate1));
            $NonAceInterTransfer->new_record = json_encode([
                [
                    'key' => $request->depList2,
                    'value' => $this->getDepartmentName($request->depList2),
                    'text' => 'New Department'
                ],
                [
                    'key' => $transfer_date,
                    'value' => $transfer_date,
                    'text' => 'Effective Date'
                ],
                [
                    'key' => $remark,
                    'value' => $remark,
                    'text' => 'Remark'
                ]
            ]);
            $NonAceInterTransfer->date = date("Y-m-d");
            $NonAceInterTransfer->updated_user_id = auth()->user()->employee_no;
            $NonAceInterTransfer->approvability = 1;
            $NonAceInterTransfer->approved_status = 0;
            $NonAceInterTransfer->created_at = Carbon::now();
            $NonAceInterTransfer->save();

            // $NonAceInterTransfer = new InternalTransfer();
            // $NonAceInterTransfer->emp_no = $request->emp_no;
            // $NonAceInterTransfer->transfer_date = date("Y-m-d", strtotime($request->effDate1));
            // $NonAceInterTransfer->dep_id = $request->depList2;
            // $NonAceInterTransfer->descriptions = $request->description2;
            // $NonAceInterTransfer->user_id = auth()->user()->employee_no;
            // $NonAceInterTransfer->save();

            $notification = array(
                'message' => 'Successfully Submitted for Transfer',
                'alert-type' => 'success'
            );
            return redirect()->route('transfer.internal.nonaccedemic')->with($notification);
        }
    }

    public function internalNonAceTransferApprove(Request $request)
    {
        //$trans_data = DB::table('employee_change_histories')->get();
        $trans_data = DB::table('employee_change_histories')->orderBy('id')->get();
        $trans_data = Employee::find($request->emp_no);

        $trans_data->approved_status == 1;



        $NonAceInterTransfer = new InternalTransfer();
        $NonAceInterTransfer->emp_no = $request->emp_no;
        $NonAceInterTransfer->transfer_date = date("Y-m-d", strtotime($request->effDate1));
        $NonAceInterTransfer->dep_id = $request->depList2;
        $NonAceInterTransfer->descriptions = $request->description2;
        $NonAceInterTransfer->user_id = auth()->user()->employee_no;
        $NonAceInterTransfer->save();


        return redirect()->route('transfer.nonacedemic.approve');

        // //$NonAceInterTransfer = InternalTransfer::find($request->id);
        // $NonAceInterTransfer = new InternalTransfer();
        // $NonAceInterTransfer->emp_no = $request->emp_no;
        // $NonAceInterTransfer->transfer_date = date("Y-m-d", strtotime($request->effDate1));
        // $NonAceInterTransfer->dep_id = $request->depList2;
        // $NonAceInterTransfer->descriptions = $request->description2;
        // $NonAceInterTransfer->user_id = auth()->user()->employee_no;
        // $NonAceInterTransfer->save();
        //return redirect()->route('employee.change')->with($notification);
        //return redirect()->route('transfer.nonacedemic.list', ['employee_no' => $NonAceInterTransfer->emp_no])->with($notification);

        //$employee = Employee::find($request->emp_no);



        // $NonAceInterTransfer = new InternalTransfer();
        // $NonAceInterTransfer->emp_no = $request->emp_no;
        // $NonAceInterTransfer->transfer_date = date("Y-m-d", strtotime($request->effDate1));
        // $NonAceInterTransfer->dep_id = $request->depList2;
        // $NonAceInterTransfer->descriptions = $request->description2;
        // $NonAceInterTransfer->user_id = auth()->user()->employee_no;
        // $NonAceInterTransfer->save();

        //return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
    }


    public function internalNonAccTransferList()
    {



        $trans_data = DB::table('employee_change_histories')
            //     ->where('type', '=', '346')
            //     ->orderBy('id')
            ->get();

        // $employee_name = DB::table('employees')
        //     ->join('departments', 'departments.id', '=', 'employees.department_id')
        //     ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'departments.id as department_id')
        //     ->where('employees.employee_no', '!=', auth()->user()->employee_no)
        //     ->get();
        // $employee_name = DB::table('employees')->get();
        // $trans_data = DB::table('internal_transfers')
        //     ->join('employees', 'employees.employee_no', '=', 'internal_transfers.emp_no')
        //     ->select('internal_transfers.*', 'employees.initials', 'employees.last_name')
        //     ->orderBy('internal_transfers.id')
        //     ->get();
        //previous work
        // $trans_data = DB::table('internal_transfers')
        //     ->join('employees', 'employees.employee_no', '=', 'internal_transfers.emp_no')
        //     ->join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
        //     ->select('internal_transfers.*', 'employees.initials', 'employees.last_name', 'departments.department_name')
        //     ->orderBy('internal_transfers.id')
        //     ->get();

        $trans_data = DB::table('employee_change_histories')
            ->join('employees', 'employees.employee_no', '=', 'employee_change_histories.emp_no')
            ->where('type', '=', '346')
            //->join('departments', 'departments.id', '=', 'employee_change_histories.new_record')
            //->select('employee_change_histories.*', 'employees.initials', 'employees.last_name', 'departments.department_name')
            //->orderBy('employee_change_histories.id')
            ->get();


        return view('admin.transfer.nac_internal_transfer.non_academic_transfer_list', compact('trans_data'));
    }

    public function internalNonAceTransferDelete($id)
    {
        Log::info('InternalTransferController -> internal transfer data delete started');
        $NonAceInterTransfer = InternalTransfer::find($id);

        $NonAceInterTransfer->delete();

        //return view('admin.transfer.nac_internal_transfer.non_academic_transfer_list');

        $notification = array(
            'message' => 'Transfer Rejected',
            'alert-type' => 'error'
        );

        return redirect()->route('transfer.nonacedemic.list', ['employee_no' => $NonAceInterTransfer->emp_no])->with($notification);
    }

    private function getDepartmentName($id)
    {
        $depName = Department::where('id', '=', $id)->first();

        return $depName ? $depName->department_name : NULL;
    }
}
