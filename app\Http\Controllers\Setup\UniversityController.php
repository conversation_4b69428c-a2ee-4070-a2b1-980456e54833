<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\University;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class UniversityController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator');
    }

    public function universityIndex()
    {
        Log::info('UniversityController -> university index started');
        $universities = University::all();
        $trashUniversities = University::onlyTrashed()->get();
        return view('admin.setups.university.index', compact('universities', 'trashUniversities'));

        Log::notice('UniversityController -> university Count - ' . $universities->count());
        Log::info('UniversityController -> university index ended');
    }

    public function universityAdd()
    {
        Log::info('UniversityController -> university add started');

        $categories = $this->getCategories([26]);
        $universityTypes = $categories->where('category_type_id', '26');

        return view('admin.setups.university.add', compact('universityTypes'));

        Log::info('UniversityController -> university add ended');
    }

    public function universityStore(Request $request)
    {
        Log::info('UniversityController -> university store started');

        $validatedData = $request->validate([
            'uni_name' => 'required|unique:universities,uni_name',
            'type' => 'required',
        ], [
            'type.required' => 'select the relavent university type',
            'uni_name.required' => 'you must enter the universit/institute name',
            'uni_name.unique' => 'universit/institute name already in the system'
        ]);

        $data = new University();
        $data->uni_name = ucwords($request->uni_name);
        $data->type = $request->type;
        $data->created_at = Carbon::now();
        $data->save();

        Log::notice('UniversityController -> Created university id - ' . $data->id . ' created by ' . auth()->user()->id);
        Log::info('UniversityController -> university create ended');

        $notification = array(
            'message' => 'New University Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('university.index')->with($notification);
    }

    public function universityEdit($id)
    {
        Log::info('UniversityController -> city edit started');
        $editData = University::find($id);
        $categories = $this->getCategories([26]);
        $universityTypes = $categories->where('category_type_id', '26');

        return view('admin.setups.university.edit', compact('editData', 'universityTypes'));

        Log::notice('UniversityController -> edit university id - ' . $editData->id . ' edited by ' . auth()->user()->id);
        Log::info('UniversityController -> university edit ended');
    }

    public function universityUpdate(Request $request, $id)
    {

        Log::info('UniversityController -> university update started');
        $validatedData = $request->validate([
            'uni_name' => ['required', Rule::unique('universities')->ignore($id)],
            'type' => 'required',
        ]);


        $data = University::find($id);
        $data->uni_name = ucwords($request->uni_name);
        $data->type = $request->type;
        $data->updated_at = Carbon::now();
        $data->save();

        Log::notice('UniversityController -> update university id - ' . $data->id . ' updated by ' . auth()->user()->id);
        Log::info('UniversityController -> university update ended');

        $notification = array(
            'message' => 'University data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('university.index')->with($notification);
    }

    public function universitySoftdelete($id)
    {

        Log::info('UniversityController -> university soft delete started');
        $university = University::find($id);
        $university->delete();

        $notification = array(
            'message' => 'University Deleted Successfully',
            'alert-type' => 'warning'
        );

        return redirect()->route('university.index')->with($notification);

        Log::notice('UniversityController -> soft delete university id - ' . $university->id . ' deleted by ' . auth()->user()->id);
        Log::info('UniversityController -> university soft delete ended');
    }

    public function universityRestore($id)
    {

        Log::info('UniversityController -> university restore started');

        $university = University::withTrashed()->find($id);
        $university->restore();

        $notification = array(
            'message' => 'University Restore Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('university.index')->with($notification);

        Log::notice('UniversityController -> restore university id - ' . $university->id . ' deleted by ' . auth()->user()->id);
        Log::info('UniversityController -> university restore ended');
    }

    public function universityDelete($id)
    {

        Log::info('UniversityController -> university delete started');

        $university = University::onlyTrashed()->find($id);
        $university->forceDelete();

        $notification = array(
            'message' => 'University Permenetly Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('university.index')->with($notification);

        Log::emergency('UniversityController -> delete university id - ' . $university->id . ' deleted by ' . auth()->user()->id);
        Log::info('UniversityController -> university delete ended');
    }
}
