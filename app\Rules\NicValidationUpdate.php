<?php

namespace App\Rules;

use App\Models\NewEmployee;
use Illuminate\Contracts\Validation\Rule;

class NicValidationUpdate implements Rule
{
    protected $currentId;
    public function __construct($currentId = null)
    {
        $this->currentId = $currentId;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $exists = NewEmployee::where('nic', $value)
                             ->where('completion_status', '=', 1)
                             ->when($this->currentId, function ($query) {
                                 // Exclude the current record by its ID
                                 return $query->where('id', '!=', $this->currentId);
                             })
                             ->exists();

        // Fail validation if such a record exists
        return !$exists;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'This NIC is already associated with a incomplete record.';
    }
}
