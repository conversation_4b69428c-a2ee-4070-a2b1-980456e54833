<?php

namespace App\Http\Controllers\InterviewPanel;

use App\Http\Controllers\Controller;
use App\Models\Application;
use App\Models\ApplicationForm;
use App\Models\Designation;
use App\Models\InterviewPanel;
use App\Models\interviewPanelMember;
use App\Models\PromotionApplicationsNonacademic;
use App\Models\Vacancy;
use App\Models\VancancyNonAcademic;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class InterviewPanelController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function addInterPanelOpen()
    {
        $mainBranch = Auth()->user()->main_branch_id;
        $empNo = Auth()->user()->employee_no;
        $categories = $this->getCategories([36]);
        $interType = $categories->where('category_type_id', '36');

        $desig = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('designations.id', 'designations.designation_name', 'categories.category_name', 'designations.salary_code')
            ->where('designations.deleted_at', '=', NULL)
            ->get();


        if ($mainBranch == 51) {

            $panel = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                ->join('designations', 'designations.id', '=', 'interview_panels.designation_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('interview_panels.*', 'type.category_name as interType', 'designations.designation_name', 'categories.category_name')
                ->where('interview_panels.interview_date', '>=', today())
                ->get();

            $panel1 = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                ->join('vacancies', 'vacancies.id', '=', 'interview_panels.vacancy_id')
                ->join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('interview_panels.id as pid', 'interview_panels.panel_id', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue', 'type.category_name as interType', 'vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id')
                ->where('interview_panels.interview_date', '>=', today())
                ->where('interview_panels.division_id', 52)
                ->get();

            $panel2 = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                ->join('vancancy_non_academics', 'vancancy_non_academics.id', '=', 'interview_panels.vacancy_id')
                ->join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('interview_panels.id as pid', 'interview_panels.panel_id', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue', 'type.category_name as interType', 'vancancy_non_academics.id', 'vancancy_non_academics.designation_id','categories.display_name','vancancy_non_academics.vacancy_status_type_id')
                ->where('interview_panels.interview_date', '>=', today())
                ->where('interview_panels.division_id', 53)
                ->get();

            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancies.vacancy_status_type_id',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 35 THEN 1 ELSE 0 END), 0) as booked'),
                    DB::raw('COALESCE(SUM(CASE WHEN applications.head_check_status = 1 THEN 1 ELSE 0 END), 0) as sortlist'),
                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id')
                ->orderByDesc('vacancies.id')
                ->whereIn('vacancy_status_type_id', array(29))
                ->havingRaw('sortlist != 0')
                ->get();

            $vacancies2 = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                ->select(
                    'vancancy_non_academics.id',
                    'vancancy_non_academics.designation_id',
                    'categories.display_name',
                    'vancancy_non_academics.vacancy_status_type_id',
                    DB::raw('COALESCE(SUM(CASE WHEN application_forms.application_decision_id = 35 THEN 1 ELSE 0 END), 0) as booked'),
                    DB::raw('COALESCE(SUM(CASE WHEN application_forms.short_list_status = 1 THEN 1 ELSE 0 END), 0) as sortlist'),
                )
                ->groupBy('vancancy_non_academics.id', 'vancancy_non_academics.designation_id', 'categories.display_name', 'vancancy_non_academics.vacancy_status_type_id')
                ->orderByDesc('vancancy_non_academics.id')
                ->whereIn('vacancy_status_type_id', array(29,30,372))

                ->where('vancancy_non_academics.interview_status',1)
                ->havingRaw('sortlist != 0')
                ->get();


        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $panel = array();

                $panel1 = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                    ->join('vacancies', 'vacancies.id', '=', 'interview_panels.vacancy_id')
                    ->join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('interview_panels.id as pid', 'interview_panels.panel_id', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue', 'type.category_name as interType', 'vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.admin_officer_check_emp')
                    ->where('interview_panels.division_id', '=', 52)
                    ->where('interview_panels.interview_date', '>=', today())
                    ->get();

                $panel2 = array();


                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                    ->select(
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancies.vacancy_status_type_id',
                        'vacancies.admin_officer_check_emp',
                        DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 35 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN applications.head_check_status = 1 THEN 1 ELSE 0 END), 0) as sortlist'),
                    )
                    ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.admin_officer_check_emp')
                    ->orderByDesc('vacancies.id')
                    ->where('vacancies.admin_officer_check_emp', $empNo)
                    ->whereIn('vacancy_status_type_id', array(29))
                    ->whereIn('main_category_id', array(44, 45))
                    ->havingRaw('sortlist != 0')
                    ->get();

                $vacancies2 = array();

            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $panel = array();

                $panel1 = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                    ->join('vacancies', 'vacancies.id', '=', 'interview_panels.vacancy_id')
                    ->join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('vacancy_operators', 'vacancies.id', '=', 'vacancy_operators.vacancy_id')
                    ->select('interview_panels.id as pid', 'interview_panels.panel_id', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue', 'type.category_name as interType', 'vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.admin_officer_check_emp')
                    ->where('interview_panels.division_id', '=', 52)
                    ->where('interview_panels.user_id', '=', auth()->user()->employee_no)
                    ->where('interview_panels.interview_date', '>=', today())
                    ->get();

                $panel2 = array();

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('vacancy_operators', 'vacancies.id', '=', 'vacancy_operators.vacancy_id')
                    ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                    ->select(
                        'vacancies.id',
                        'vacancies.designation_id',
                        'vacancies.faculty_id',
                        'vacancies.department_id',
                        'vacancies.subject',
                        'categories.display_name',
                        'vacancies.main_category_id',
                        'vacancy_operators.employee_no',
                        'vacancies.vacancy_status_type_id',
                        'vacancies.dept_head_check_date',
                        DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 35 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN applications.head_check_status = 1 THEN 1 ELSE 0 END), 0) as sortlist'),
                    )
                    ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancy_operators.employee_no', 'vacancies.vacancy_status_type_id', 'vacancies.dept_head_check_date')
                    ->orderByDesc('vacancies.id')
                    ->where('vacancy_operators.employee_no', $empNo)
                    ->whereIn('vacancy_status_type_id', array(29))
                    ->whereIn('main_category_id', array(44, 45))
                    ->havingRaw('sortlist != 0')
                    ->get();

                $vacancies2 = array();
            }
        } elseif ($mainBranch == 53) {
            # code...
            if (Auth()->user()->hasRole(['est-head'])) {

                $panel = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                    ->join('designations', 'designations.id', '=', 'interview_panels.designation_id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('interview_panels.*', 'type.category_name as interType', 'designations.designation_name', 'categories.category_name')
                    ->where('interview_panels.interview_date', '>=', today())
                    ->where('interview_panels.division_id', '=', 53)
                    ->get();

                $panel1 = array();

                $panel2 = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                    ->join('vancancy_non_academics', 'vancancy_non_academics.id', '=', 'interview_panels.vacancy_id')
                    ->join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('interview_panels.id as pid', 'interview_panels.panel_id', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue', 'type.category_name as interType', 'vancancy_non_academics.id', 'vancancy_non_academics.designation_id','categories.display_name','vancancy_non_academics.vacancy_status_type_id')
                    ->where('interview_panels.interview_date', '>=', today())
                    ->get();

                $vacancies = array();

                $vacancies2 = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                ->select(
                    'vancancy_non_academics.id',
                    'vancancy_non_academics.designation_id',
                    'categories.display_name',
                    'vancancy_non_academics.vacancy_status_type_id',
                    DB::raw('COALESCE(SUM(CASE WHEN application_forms.application_decision_id = 35 THEN 1 ELSE 0 END), 0) as booked'),
                    DB::raw('COALESCE(SUM(CASE WHEN application_forms.short_list_status = 1 THEN 1 ELSE 0 END), 0) as sortlist'),
                )
                ->groupBy('vancancy_non_academics.id', 'vancancy_non_academics.designation_id', 'categories.display_name', 'vancancy_non_academics.vacancy_status_type_id')
                ->orderByDesc('vancancy_non_academics.id')
                ->whereIn('vacancy_status_type_id', array(29,30,372))
                ->where('vancancy_non_academics.interview_status',1)
                ->havingRaw('sortlist != 0')
                ->get();



            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $panel = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                    ->join('designations', 'designations.id', '=', 'interview_panels.designation_id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('interview_panels.*', 'type.category_name as interType', 'designations.designation_name', 'categories.category_name')
                    ->where('interview_panels.interview_date', '>=', today())
                    ->where('interview_panels.division_id', '=', 53)
                    ->where('interview_panels.user_id', '=', auth()->user()->employee_no)
                    ->get();

                $panel1 = array();

                $panel2 = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                    ->join('vancancy_non_academics', 'vancancy_non_academics.id', '=', 'interview_panels.vacancy_id')
                    ->join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('interview_panels.id as pid', 'interview_panels.panel_id', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue', 'type.category_name as interType', 'vancancy_non_academics.id', 'vancancy_non_academics.designation_id','categories.display_name','vancancy_non_academics.vacancy_status_type_id')
                    ->where('interview_panels.interview_date', '>=', today())
                    ->get();

                $vacancies = array();

                $vacancies2 = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('vancancy_operator_non_academics', 'vancancy_non_academics.id', '=', 'vancancy_operator_non_academics.vacancy_id')
                ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                ->select(
                    'vancancy_non_academics.id',
                    'vancancy_non_academics.designation_id',
                    'categories.display_name',
                    'vancancy_operator_non_academics.employee_no',
                    'vancancy_non_academics.vacancy_status_type_id',
                    DB::raw('COALESCE(SUM(CASE WHEN application_forms.application_decision_id = 35 THEN 1 ELSE 0 END), 0) as booked'),
                    DB::raw('COALESCE(SUM(CASE WHEN application_forms.short_list_status = 1 THEN 1 ELSE 0 END), 0) as sortlist'),
                )
                ->groupBy('vancancy_non_academics.id', 'vancancy_non_academics.designation_id', 'categories.display_name', 'vancancy_non_academics.vacancy_status_type_id','vancancy_operator_non_academics.employee_no')
                ->orderByDesc('vancancy_non_academics.id')
                ->where('vancancy_operator_non_academics.employee_no', $empNo)
                ->whereIn('vacancy_status_type_id', array(29,30,372))
                ->where('vancancy_non_academics.interview_status',1)
                ->havingRaw('sortlist != 0')
                ->get();


            }
        } else {

            $panel = array();
            $panel1 = array();
            $panel2 = array();
            $vacancies = array();
            $vacancies2 = array();

        }


        return view('admin.interview_panel.add_panel', compact('interType', 'desig', 'panel', 'panel1','panel2', 'vacancies','vacancies2'));
    }

    public function addInterPanelStore(Request $request)
    {

        $request->validate(
            [
                'type' => 'required',
                'intDate' => 'required|date|date_format:d-M-Y|after:yesterday',
                'venue' => 'required',
                'intTime' => 'required',
            ],
            [
                'type.required' => 'Please Select Interview Type',
                'intDate.required' => 'Please Select Interview Date',
                'intDate.after' => 'Interview Date Should Be a Future Date',
                'venue.required' => 'Please Enter Interview Location',
                'intTime.required' => 'Please Select Interview Time',
            ]
        );

        // Conditional Validation
        if ($request->input('type') == 209) {
            // If type equals 209, require 'desig' field
            $request->validate([
                'desig' => 'required',
            ], [
                'desig.required' => 'Please Select Interview Designation',
            ]);
        } elseif ($request->input('type') == 265) {
            // If type equals 265, require 'vacancy_id' field
            $request->validate([
                'vacancy_id' => 'required',
            ], [
                'vacancy_id.required' => 'Please Select Interview Vacancy',
            ]);
        }  elseif ($request->input('type') == 370) {
            // If type equals 265, require 'vacancy_id' field
            $request->validate([
                'vacancy_id1' => 'required',
            ], [
                'vacancy_id1.required' => 'Please Select Interview Vacancy',
            ]);
        }


        $year = date("Y");
        $divis = "OTH";

        if ($request->type == 209 || $request->type == 370) {
            $divis = "NAC";
            $divID = 53;
        } else if ($request->type == 265) {
            $divis = "AC";
            $divID = 52;
        }

        $ref = $year . "/" . $divis;

        $refCount = DB::table('interview_panels')
            ->where('year', '=', $year)
            ->where('division_id','=',$divID)
            ->count();
        //dd($refCount);

        $refValue = $refCount + 1;

        //dd($refValue);

        $refID = $ref . "/" . str_pad($refValue, 4, 0, STR_PAD_LEFT);

        $saveText = new InterviewPanel();
        $saveText->panel_id = $refID;
        $saveText->interview_type = $request->type;
        $saveText->year = $year;
        if ($request->type == 209 || $request->type == 370) {
            $saveText->division_id = 53;
        } else if ($request->type == 265) {
            $saveText->division_id = 52;
        }


        if ($request->desig != '') {

            $saveText->designation_id = $request->desig;
        }

        if ($request->vacancy_id != '') {

            $saveText->vacancy_id = $request->vacancy_id;
        }

        if ($request->vacancy_id1 != '') {

            $saveText->vacancy_id = $request->vacancy_id1;
        }

        $saveText->interview_date = date("Y-m-d", strtotime($request->intDate));
        $saveText->interview_time = $request->intTime;
        $saveText->venue = $request->venue;
        $saveText->user_id = auth()->user()->employee_no;
        $saveText->save();

        /************************************************************** */
        if ($request->vacancy_id != '') {

            $data = Vacancy::find($request->vacancy_id);
            $data->vacancy_status_type_id =  31;
            $data->interview_assign_emp = Auth()->user()->employee_no;
            $data->interview_assign_date = Carbon::today();
            $data->interview_board_no = $saveText->id;
            $data->interview_date = date("Y-m-d", strtotime($request->intDate));
            $data->save();

            $appCount = Application::where('vacancy_id', '=', $request->vacancy_id)
                ->where('head_check_status', 1)
                ->count();

            for ($i = 0; $i < $appCount; $i++) {

                $application = Application::where('vacancy_id', '=', $request->vacancy_id)
                    ->where('head_check_status', 1)
                    ->where('interview_status', '=', 0)
                    ->first(); // Retrieve the first application with interview_status = 0

                if ($application) {

                    $application->interview_status = 1;
                    $application->interview_board_no = $saveText->id;
                    $application->interview_date = Carbon::parse($request->intDate)->format('Y-m-d'); // Using Carbon to format the date
                    $application->save();
                }
            }

            $confText = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                ->join('vacancies', 'vacancies.id', '=', 'interview_panels.vacancy_id')
                ->join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('interview_panels.id as pid', 'interview_panels.panel_id', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue', 'interview_panels.interview_type', 'type.category_name as interType', 'vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id')
                ->where('interview_panels.panel_id', '=', $refID)
                ->get();

            if ($confText->count() > 0) {
                foreach ($confText as $data) {

                    //set employee number session
                    session()->get('panel_id');
                    session()->forget('panel_id');
                    Session::put('panel_id', $data->panel_id);

                    //set nic session
                    session()->get('interType');
                    session()->forget('interType');
                    Session::put('interType', $data->interType);

                    //set employee_type session
                    session()->get('vacancy_id');
                    session()->forget('vacancy_id');
                    Session::put('vacancy_id', $data->id);

                    //set employee_type session
                    session()->get('interview_date');
                    session()->forget('interview_date');
                    Session::put('interview_date', $data->interview_date);

                    //set employee_type session
                    session()->get('interview_time');
                    session()->forget('interview_time');
                    Session::put('interview_time', $data->interview_time);

                    //set employee_type session
                    session()->get('venue');
                    session()->forget('venue');
                    Session::put('venue', $data->venue);

                    //set employee_type session
                    session()->get('id');
                    session()->forget('id');
                    Session::put('id', $data->pid);

                    //set employee_type session
                    session()->get('interview_type');
                    session()->forget('interview_type');
                    Session::put('interview_type', $data->interview_type);
                }
            }
        }


        if ($request->vacancy_id1 != '') {

            $data = VancancyNonAcademic::find($request->vacancy_id1);

            $originalStatusTypeId = $data->vacancy_status_type_id;

            $data->vacancy_status_type_id =  31;
            $data->interview_assign_emp = Auth()->user()->employee_no;
            $data->interview_assign_date = Carbon::today();
            $data->interview_board_id = $saveText->id;
            $data->interview_date = date("Y-m-d", strtotime($request->intDate));
            $data->save();

            //dd($originalStatusTypeId);

            if($originalStatusTypeId == 29){

                $appCount = ApplicationForm::where('vacancy_id', '=', $request->vacancy_id1)
                ->where('short_list_status', 1)
                ->count();

            } else if($originalStatusTypeId == 30){

                $appCount = ApplicationForm::where('vacancy_id', '=', $request->vacancy_id1)
                ->where('written_exam_status', 1)
                ->count();

            }  else if($originalStatusTypeId == 372){

                 $appCount = ApplicationForm::where('vacancy_id', '=', $request->vacancy_id1)
                ->where('practical_exam_status', 1)
                ->count();

            }  else{

                $appCount = 0;
            }

            for ($i = 0; $i < $appCount; $i++) {

                if($originalStatusTypeId == 29){

                $application = ApplicationForm::where('vacancy_id', '=', $request->vacancy_id1)
                    ->where('short_list_status', 1)
                    ->where('interview_status', '=', 0)
                    ->first();

            } else if($originalStatusTypeId == 30){

               $application = ApplicationForm::where('vacancy_id', '=', $request->vacancy_id1)
                    ->where('written_exam_status', 1)
                    ->where('interview_status', '=', 0)
                    ->first();

            }  else if($originalStatusTypeId == 372){

                 $application = ApplicationForm::where('vacancy_id', '=', $request->vacancy_id1)
                    ->where('practical_exam_status', 1)
                    ->where('interview_status', '=', 0)
                    ->first();

            }

                if ($application) {

                    $application->interview_status = 1;
                    $application->interview_board_id = $saveText->id;
                    $application->interview_date = Carbon::parse($request->intDate)->format('Y-m-d'); // Using Carbon to format the date
                    $application->save();
                }
            }

            $confText = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                ->join('vancancy_non_academics', 'vancancy_non_academics.id', '=', 'interview_panels.vacancy_id')
                ->join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('interview_panels.id as pid', 'interview_panels.panel_id', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue', 'interview_panels.interview_type', 'type.category_name as interType', 'vancancy_non_academics.id', 'vancancy_non_academics.designation_id', 'categories.display_name', 'vancancy_non_academics.vacancy_status_type_id')
                ->where('interview_panels.panel_id', '=', $refID)
                ->get();

            if ($confText->count() > 0) {
                foreach ($confText as $data) {

                    //set employee number session
                    session()->get('panel_id');
                    session()->forget('panel_id');
                    Session::put('panel_id', $data->panel_id);

                    //set nic session
                    session()->get('interType');
                    session()->forget('interType');
                    Session::put('interType', $data->interType);

                    //set employee_type session
                    session()->get('vacancy_id');
                    session()->forget('vacancy_id');
                    Session::put('vacancy_id', $data->id);

                    //set employee_type session
                    session()->get('interview_date');
                    session()->forget('interview_date');
                    Session::put('interview_date', $data->interview_date);

                    //set employee_type session
                    session()->get('interview_time');
                    session()->forget('interview_time');
                    Session::put('interview_time', $data->interview_time);

                    //set employee_type session
                    session()->get('venue');
                    session()->forget('venue');
                    Session::put('venue', $data->venue);

                    //set employee_type session
                    session()->get('id');
                    session()->forget('id');
                    Session::put('id', $data->pid);

                    //set employee_type session
                    session()->get('interview_type');
                    session()->forget('interview_type');
                    Session::put('interview_type', $data->interview_type);
                }
            }
        }

        if ($request->desig != '') {

            $confText = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                ->join('designations', 'designations.id', '=', 'interview_panels.designation_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('interview_panels.*', 'type.category_name as interType', 'designations.designation_name', 'categories.category_name')
                ->where('interview_panels.panel_id', '=', $refID)
                ->get();

            if ($confText->count() > 0) {
                foreach ($confText as $data) {

                    //set employee number session
                    session()->get('panel_id');
                    session()->forget('panel_id');
                    Session::put('panel_id', $data->panel_id);

                    //set nic session
                    session()->get('interType');
                    session()->forget('interType');
                    Session::put('interType', $data->interType);

                    //set employee_type session
                    session()->get('designation');
                    session()->forget('designation');
                    Session::put('designation', $data->designation_name . " " . $data->category_name);

                    //set employee_type session
                    session()->get('interview_date');
                    session()->forget('interview_date');
                    Session::put('interview_date', $data->interview_date);

                    //set employee_type session
                    session()->get('interview_time');
                    session()->forget('interview_time');
                    Session::put('interview_time', $data->interview_time);

                    //set employee_type session
                    session()->get('venue');
                    session()->forget('venue');
                    Session::put('venue', $data->venue);

                    //set employee_type session
                    session()->get('id');
                    session()->forget('id');
                    Session::put('id', $data->id);

                    //set employee_type session
                    session()->get('interview_type');
                    session()->forget('interview_type');
                    Session::put('interview_type', $data->interview_type);
                }
            }
        }

        $notification = array(
            'message' => 'New Permanent Employee Added',
            'alert-type' => 'success'
        );
        return redirect()->route('interviewPanel.final')->with($notification);
    }

    public function interviewPanelFinal()
    {
        $id = session()->get('id');
        $interview_type = session()->get('interview_type');
        $panel_id = session()->get('panel_id');
        $interType = session()->get('interType');
        $designation = session()->get('designation');
        $vacancy_id = session()->get('vacancy_id');

        if( $interview_type == 265){

            $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($vacancy_id);

        }else if( $interview_type == 370){

            $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'categories.display_name')
            ->find($vacancy_id);
        }

        $interview_date = session()->get('interview_date');
        $interview_time = session()->get('interview_time');
        $venue = session()->get('venue');

        //dd($interview_type);
        return view('admin.interview_panel.add_panel_submit', compact('panel_id', 'interType', 'designation', 'interview_date', 'interview_time', 'venue', 'id', 'vacancy', 'vacancy_id', 'interview_type'));
    }

    public function interviewPanelEdit($id)
    {
        $panelId = decrypt($id);

        $editData = InterviewPanel::find($panelId);
        return view('admin.interview_panel.edit_panel', compact('editData'));
    }

    public function interviewPanelUpdate(Request $request, $id)
    {
        $request->validate(
            [
                'intDate' => 'required|date|date_format:d-M-Y|after:yesterday',
                'venue' => 'required',
                'intTime' => 'required',
            ],
            [
                'intDate.required' => 'Please Select Interview Date',
                'intDate.after' => 'Interview Date Should Be a Future Date',
                'venue.required' => 'Please Enter Interview Location',
                'intTime.required' => 'Please Select Interview Time',
            ]
        );

        $data = InterviewPanel::find($id);
        $data->interview_date = date("Y-m-d", strtotime($request->intDate));
        $data->interview_time = $request->intTime;
        $data->venue = $request->venue;
        $data->user_id = auth()->user()->employee_no;
        $data->save();

        $notification = array(
            'message' => 'Interview Panel updated',
            'alert-type' => 'info'
        );
        return redirect()->route('inetviewPanel.add.open')->with($notification);
    }

    public function interviewPanelDelete($id)
    {
        //Log::info('BondController -> bond data delete started');
        $panelId = decrypt($id);
        $interviewPanelMember = interviewPanelMember::where('panel_id', $panelId);
        $interviewPanelMember->delete();

        $data = InterviewPanel::where('panel_id', $panelId)->first(); // Use first() to retrieve a single record

        if ($data) {
            if ($data->vacancy_id != 0 && $data->interview_type == 265) {

                $vacancy = Vacancy::find($data->vacancy_id);
                if ($vacancy) {
                    $vacancy->vacancy_status_type_id = 29;
                    $vacancy->interview_assign_emp = null;
                    $vacancy->interview_assign_date = null;
                    $vacancy->interview_board_no = null;
                    $vacancy->interview_date = null;
                    $vacancy->save();
                }

                // Retrieve and update each application individually
                $applications = Application::where('vacancy_id', $data->vacancy_id)
                    ->where('interview_status', 1)
                    ->get();

                foreach ($applications as $application) {

                    $application->interview_status = 0;
                    $application->interview_board_no = null;
                    $application->interview_date = null;
                    $application->save();
                }
            }

            if ($data->vacancy_id != 0 && $data->interview_type == 370) {

                $vacancy = VancancyNonAcademic::find($data->vacancy_id);
                if ($vacancy) {
                    if($vacancy->practical_exam_status == 2 && $vacancy->interview_status == 1){
                       $vacancy->vacancy_status_type_id = 372;
                    }else if($vacancy->written_exam_status == 2 && $vacancy->practical_exam_status == 0 && $vacancy->interview_status == 1){
                        $vacancy->vacancy_status_type_id = 30;
                    }else if($vacancy->written_exam_status == 0 && $vacancy->practical_exam_status == 0 && $vacancy->interview_status == 1){
                        $vacancy->vacancy_status_type_id = 29;
                    }

                    $vacancy->interview_assign_emp = 0;
                    $vacancy->interview_assign_date = null;
                    $vacancy->interview_board_id = 0;
                    $vacancy->interview_date = null;
                    $vacancy->save();
                }

                // Retrieve and update each application individually
                $applications = ApplicationForm::where('vacancy_id', $data->vacancy_id)
                    ->where('interview_status', 1)
                    ->get();

                foreach ($applications as $application) {

                    $application->interview_status = 0;
                    $application->interview_board_id = null;
                    $application->interview_date = null;
                    $application->save();
                }
            }

            $data->delete();
        }

        $notification = array(
            'message' => 'Interview Panel Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('inetviewPanel.add.open')->with($notification);
    }

    public function interviewPanelShow($id)
    {
        $panelId = decrypt($id);
        $data = InterviewPanel::find($panelId);

        if( $data->interview_type == 265){

            $vacancy_id = $data->vacancy_id;

            $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find($vacancy_id);

        }else if( $data->interview_type == 370){

            $vacancy_id = $data->vacancy_id;

            $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'categories.display_name')
            ->find($vacancy_id);
        }

        $interviewPanelMembers = interviewPanelMember::where('panel_id', $data->panel_id)->get();

        $interviewPanelApplicants = PromotionApplicationsNonacademic::join('employees', 'promotion_applications_nonacademics.emp_no', '=', 'employees.employee_no')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->select('promotion_applications_nonacademics.*', 'employees.email', 'departments.department_name')
            ->where('interview_board_no', $panelId)->get();
        //dd($vacancy);

        return view('admin.interview_panel.detial_panel', compact('data', 'interviewPanelMembers', 'interviewPanelApplicants', 'vacancy'));
    }

    public function addPanelMemberOpen(Request $request)
    {
        $mainBranch = Auth()->user()->main_branch_id;

        $valueParts = explode('|', $request->panel . '|' . $request->panel);
        $panelID = $valueParts[0];
        $categoryType = $valueParts[1];

        if (isset($panelID)) {

            if ($panelID > 0) {

                if ($categoryType == 209) {

                    $confText = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                        ->join('designations', 'designations.id', '=', 'interview_panels.designation_id')
                        ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                        ->select('interview_panels.*', 'type.category_name as interType', 'interview_panels.interview_type','designations.designation_name', 'categories.category_name')
                        ->where('interview_panels.id', '=', $request->panel)
                        ->get();

                    if ($confText->count() > 0) {

                        foreach ($confText as $confTexts) {
                            $Id = $confTexts->id;
                            $pID = $confTexts->panel_id;
                            $pType = $confTexts->interType;
                            $pTypeID = $confTexts->interview_type;
                            $design = $confTexts->designation_name . " (" . $confTexts->category_name . ")";
                            $vacancy_id = "";
                            $pDate = $confTexts->interview_date;
                            $pTime = $confTexts->interview_time;
                            $pVenue = $confTexts->venue;
                        }

                        $pMember = interviewPanelMember::where('panel_id', $confTexts->panel_id)->get();

                    } else {
                        $Id = "";
                        $pID = "";
                        $pType = "";
                        $pTypeID = "";
                        $design = "";
                        $vacancy_id = "";
                        $pDate = "";
                        $pTime = "";
                        $pVenue = "";
                        $pMember = array();
                    }

                    $currentPanel = $panelID;

                } elseif ($categoryType == 265) {

                    $confText = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                        ->join('vacancies', 'vacancies.id', '=', 'interview_panels.vacancy_id')
                        ->join('designations', 'vacancies.designation_id', '=', 'designations.id')
                        ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                        ->select('interview_panels.id as pid', 'interview_panels.panel_id','interview_panels.interview_type', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue', 'type.category_name as interType', 'interview_panels.vacancy_id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id')
                        ->where('interview_panels.id', '=', $panelID)
                        ->get();

                    if ($confText->count() > 0) {

                        foreach ($confText as $confTexts) {
                            $Id = $confTexts->id;
                            $pID = $confTexts->panel_id;
                            $pType = $confTexts->interType;
                            $pTypeID = $confTexts->interview_type;
                            $design = "";
                            $vacancy_id = $confTexts->vacancy_id;
                            $pDate = $confTexts->interview_date;
                            $pTime = $confTexts->interview_time;
                            $pVenue = $confTexts->venue;
                        }

                        $pMember = interviewPanelMember::where('panel_id', $confTexts->panel_id)->get();

                    } else {
                        $Id = "";
                        $pID = "";
                        $pType = "";
                        $pTypeID = "";
                        $design = "";
                        $vacancy_id = "";
                        $pDate = "";
                        $pTime = "";
                        $pVenue = "";
                        $pMember = array();
                    }

                    $currentPanel = $panelID;

                }elseif ($categoryType == 370) {

                    $confText = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                        ->join('vancancy_non_academics', 'vancancy_non_academics.id', '=', 'interview_panels.vacancy_id')
                        ->join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                        ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                        ->select('interview_panels.id as pid', 'interview_panels.panel_id','interview_panels.interview_type', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue', 'type.category_name as interType', 'interview_panels.vacancy_id', 'vancancy_non_academics.designation_id', 'categories.display_name', 'vancancy_non_academics.vacancy_status_type_id')
                        ->where('interview_panels.id', '=', $panelID)
                        ->get();

                        //dd($confText);

                    if ($confText->count() > 0) {

                        foreach ($confText as $confTexts) {
                            $Id = $confTexts->id;
                            $pID = $confTexts->panel_id;
                            $pType = $confTexts->interType;
                            $pTypeID = $confTexts->interview_type;
                            $design = "";
                            $vacancy_id = $confTexts->vacancy_id;
                            $pDate = $confTexts->interview_date;
                            $pTime = $confTexts->interview_time;
                            $pVenue = $confTexts->venue;
                        }

                        $pMember = interviewPanelMember::where('panel_id', $confTexts->panel_id)->get();

                    } else {
                        $Id = "";
                        $pID = "";
                        $pType = "";
                        $pTypeID = "";
                        $design = "";
                        $vacancy_id = "";
                        $pDate = "";
                        $pTime = "";
                        $pVenue = "";
                        $pMember = array();
                    }

                    $currentPanel = $panelID;

                } else {
                    $confText = array();
                }
            } else {
                $Id = "";
                $pID = "";
                $pType = "";
                $pTypeID = "";
                $design = "";
                $vacancy_id = "";
                $pDate = "";
                $pTime = "";
                $pVenue = "";
                $currentPanel = "";
                $pMember = array();
            }
        } else {
            $Id = "";
            $pID = "";
            $pType = "";
            $pTypeID = "";
            $design = "";
            $vacancy_id = "";
            $pDate = "";
            $pTime = "";
            $pVenue = "";
            $currentPanel = "";
            $pMember = array();
        }

        if ($mainBranch == 51) {

            $panelText = DB::table('interview_panels')
                ->select('id', 'panel_id', 'interview_type')
                ->where('interview_panels.interview_date', '>=', today())
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $panelText = DB::table('interview_panels')
                    ->select('id', 'panel_id', 'interview_type')
                    ->where('interview_panels.interview_date', '>=', today())
                    ->where('interview_panels.division_id', '=', 52)
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $panelText = DB::table('interview_panels')
                    ->select('id', 'panel_id', 'interview_type')
                    ->where('interview_panels.interview_date', '>=', today())
                    ->where('interview_panels.division_id', '=', 52)
                    ->where('interview_panels.user_id', '=', auth()->user()->employee_no)
                    ->get();
            }
        } elseif ($mainBranch == 53) {
            # code...
            if (Auth()->user()->hasRole(['est-head'])) {

                $panelText = DB::table('interview_panels')
                    ->select('id', 'panel_id', 'interview_type')
                    ->where('interview_panels.interview_date', '>=', today())
                    ->where('interview_panels.division_id', '=', 53)
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $panelText = DB::table('interview_panels')
                    ->select('id', 'panel_id', 'interview_type')
                    ->where('interview_panels.interview_date', '>=', today())
                    ->where('interview_panels.division_id', '=', 53)
                    ->where('interview_panels.user_id', '=', auth()->user()->employee_no)
                    ->get();
            }
        } else {

            $panelText = array();
        }


        $categories = $this->getCategories([37, 44]);
        $memberText = $categories->where('category_type_id', '37');
        $memeberT = $categories->where('category_type_id', '44');

        $empText = DB::table('employees')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->select('employees.employee_no', 'categories.category_name', 'employees.initials', 'employees.last_name')
            ->where('employees.employee_status_id', '=', 110)
            ->where('employees.main_branch_id', '=', 52)
            ->get();

        return view('admin.interview_panel.add_panel_members', compact(
            'panelText',
            'pID',
            'Id',
            'pType',
            'pTypeID',
            'design',
            'vacancy_id',
            'pDate',
            'pTime',
            'pVenue',
            'memberText',
            'empText',
            'currentPanel',
            'pMember',
            'memeberT'
        ));
    }

    public function emp_search(Request $request)
    {
        $empNo = $request->emp_no;

        $empText = DB::table('employees')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as sg', 'designations.staff_grade', '=', 'sg.id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
            ->leftJoin('department_heads', 'department_heads.email', '=', 'employees.email')
            ->select(
                'employees.employee_no',
                'categories.category_name',
                'employees.initials',
                'employees.last_name',
                'employees.email',
                'designations.designation_name',
                'sg.category_name as sgn',
                'departments.department_name',
                'departments.name_status',
                'faculties.faculty_name',
                'faculties.id',
                DB::raw('CASE
            WHEN department_heads.email IS NOT NULL THEN
                (SELECT category_name FROM categories WHERE id = department_heads.head_position)
            ELSE
                designations.designation_name
            END AS position_name')
            )->where('employees.employee_no', '=', $empNo)->get();


        if ($empText->count() > 0) {
            foreach ($empText as $empTexts) {
                $empNum = $empTexts->employee_no;
                $emp_name = $empTexts->category_name . " " . $empTexts->initials . " " . $empTexts->last_name;
                $emp_email = $empTexts->email;

                if ($empTexts->name_status == 0) {

                    $dep_nam = $empTexts->department_name;
                    $deig = $empTexts->position_name;
                } else if ($empTexts->name_status == 1) {

                    $dep_nam =  "Department of" . " " . $empTexts->department_name;
                    $deig = $empTexts->position_name;
                }

                if ($empTexts->id == 50 || $empTexts->id == 51) {

                    $fac_name = "";
                } else {

                    $fac_name = $empTexts->faculty_name;
                }
            }
        } else {

            $empNum = "";
            $emp_name = "";
            $emp_email = "";
            $dep_nam = "";
            $fac_name = "";
            $deig = "";
        }

        return json_encode([
            "emp_num" => $empNum,
            "emp_name" => $emp_name,
            "emp_email" => $emp_email,
            "dep_name" => $dep_nam,
            "fac_name" => $fac_name,
            "desig" => $deig
        ]);
    }

    public function interviwMemberStore(Request $request)
    {

        if (isset($request->panelID)) {

            if ($request->membType != null) {

                for ($i = 0; $i < count($request->membType); $i++) {

                    $panelMemebers = new interviewPanelMember();
                    $panelMemebers->panel_id = $request->panelID;
                    $panelMemebers->member_type = $request->membType[$i];
                    $panelMemebers->m_type = $request->mType[$i];
                    $panelMemebers->member_id = $request->empNoM[$i];
                    $panelMemebers->name = $request->nameM[$i];
                    $panelMemebers->position = $request->positionM[$i];
                    $panelMemebers->division = $request->depM[$i];
                    $panelMemebers->faculty_name = $request->fac[$i];
                    $panelMemebers->emal = $request->emailM[$i];
                    $panelMemebers->status = 1;
                    $panelMemebers->enter_user = auth()->user()->employee_no;
                    $panelMemebers->enter_date = today();
                    $panelMemebers->save();
                }

                $panalid =  DB::table('interview_panels')
                    ->join('interview_panel_members', 'interview_panels.panel_id', '=', 'interview_panel_members.panel_id')
                    ->where('interview_panel_members.panel_id', '=', $request->panelID)
                    ->select('interview_panels.id')
                    ->get();

                $panalid = json_decode($panalid, true);
                $panalid = $panalid[0]["id"];
                //dd($request->Id);
                $interview_type =  DB::table('interview_panels')
                    ->join('interview_panel_members', 'interview_panels.panel_id', '=', 'interview_panel_members.panel_id')
                    ->where('interview_panel_members.panel_id', '=', $request->panelID)
                    ->select('interview_panels.interview_type')
                    ->get();

                $interview_type = json_decode($interview_type, true);
                $interview_type = $interview_type[0]["interview_type"];

                $notification = array(
                    'message' => 'Interview panel members details successfully submitted',
                    'alert-type' => 'success'
                );

                return redirect()->route('interviewPanel.add.members.open', ['panel' => $panalid . '|' . $interview_type])->with($notification);
            } else {

                $notification = array(
                    'message' => 'Interview panel members details not saved',
                    'alert-type' => 'error'
                );

                return redirect()->route('interviewPanel.add.members.open')->with($notification);
            }
        } else {

            $notification = array(
                'message' => 'Please Search penal Before Submit',
                'alert-type' => 'error'
            );

            return redirect()->route('interviewPanel.add.members.open')->with($notification);
        }
    }

    public function panelMemberDelete($id)
    {

        Log::info('interviewPanelMemberController -> interview panel member delete started');

        $panalid =  DB::table('interview_panels')
            ->join('interview_panel_members', 'interview_panels.panel_id', '=', 'interview_panel_members.panel_id')
            ->where('interview_panel_members.id', '=', $id)
            ->select('interview_panels.id')
            ->get();

        $panalid = json_decode($panalid, true);
        $panalid = $panalid[0]["id"];

        $interview_type =  DB::table('interview_panels')
            ->join('interview_panel_members', 'interview_panels.panel_id', '=', 'interview_panel_members.panel_id')
            ->where('interview_panel_members.id', '=', $id)
            ->select('interview_panels.interview_type')
            ->get();

        $interview_type = json_decode($interview_type, true);
        $interview_type = $interview_type[0]["interview_type"];

        $pmember = interviewPanelMember::find($id);
        $pmember->delete();

        Log::emergency('interviewPanelMemberController ->' . $pmember->panel_id . ' : ' . $pmember->name . ' deleted by ' . auth()->user()->employee_no);
        Log::info('interviewPanelMemberController -> interview panel member data delete ended');


        $notification = array(
            'message' => 'interview panel member data Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('interviewPanel.add.members.open', ['panel' => $panalid . '|' . $interview_type])->with($notification);
    }
}
