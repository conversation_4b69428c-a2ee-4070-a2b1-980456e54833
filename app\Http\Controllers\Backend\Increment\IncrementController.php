<?php

namespace App\Http\Controllers\Backend\Increment;

use App\Http\Controllers\Controller;
use App\Models\Increment;
use App\Models\Category;
use App\Models\Designation;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class IncrementController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function index(Request $request)
    {

        Log::info('IncrementController -> Increments data getAll started');

        if (isset($request->employee_no)) {

            $mainBranch = Auth()->user()->main_branch_id;

            $search_emp_no = $request->employee_no;

            //admin user data collection
            if ($mainBranch == 51) {

                $empfetchDatas = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                    ->select('categories.category_name', 'employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->where('employees.employee_no', $search_emp_no)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->get();

                $row_count = $empfetchDatas->count();

                /* $emp_increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                    ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                    //->join('category_types', 'category_types.id', '=', 'categories.category_type_id')
                    ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                    ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                    ->where('increments.emp_no', $search_emp_no)
                    //->where('category_types.id', '28')
                    ->orderBy('increments.effective_date', 'DESC')
                    ->get(); */
                $emp_increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                    ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                    //->join('category_types', 'category_types.id', '=', 'categories.category_type_id')
                    ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                    ->join('categories as inrtype', 'increments.increment_type', '=', 'inrtype.id')
                    ->select('increments.effective_date', 'designations.designation_name','inrtype.category_name as type', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                    ->where('increments.emp_no', $search_emp_no)
                    //->where('category_types.id', '28')
                    ->orderBy('increments.effective_date', 'DESC')
                    ->get();
            } //academic devision data collection
            elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head','cc'])) {

                    $empfetchDatas = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                        ->select('categories.category_name', 'employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                } elseif (Auth()->user()->hasRole(['sc'])) {


                    $empfetchDatas = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                        ->select('categories.category_name', 'employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();

                // $emp_increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                //     ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                //     //->join('category_types', 'category_types.id', '=', 'categories.category_type_id')
                //     ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                //     ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                //     ->where('increments.emp_no', $search_emp_no)
                //     //->where('category_types.id', '28')
                //     ->orderBy('increments.effective_date', 'DESC')
                //     ->get();
                $emp_increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                    ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                    //->join('category_types', 'category_types.id', '=', 'categories.category_type_id')
                    ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                    ->join('categories as inrtype', 'increments.increment_type', '=', 'inrtype.id')
                    ->select('increments.effective_date', 'designations.designation_name','inrtype.category_name as type', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                    ->where('increments.emp_no', $search_emp_no)
                    //->where('category_types.id', '28')
                    ->orderBy('increments.effective_date', 'DESC')
                    ->get();

            } //non academic division data collection
            elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head','cc'])) {

                    $empfetchDatas = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                        ->select('categories.category_name', 'employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                } elseif (Auth()->user()->hasRole(['sc'])) {


                    $empfetchDatas = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                        ->select('categories.category_name', 'employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();

                $emp_increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                    ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                    //->join('category_types', 'category_types.id', '=', 'categories.category_type_id')
                    ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                    ->join('categories as inrtype', 'increments.increment_type', '=', 'inrtype.id')
                    ->select('increments.effective_date', 'designations.designation_name','inrtype.category_name as type', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                    ->where('increments.emp_no', $search_emp_no)
                    //->where('category_types.id', '28')
                    ->orderBy('increments.effective_date', 'DESC')
                    ->get();
            }

            /********************************************************************* */

            if ($row_count > 0) {
                foreach ($empfetchDatas as $empData) {
                    $emp_no = $empData->employee_no;
                    $emp_name = $empData->category_name . " " . $empData->initials . " " . $empData->last_name;
                }
            } else {
                $emp_no = '';
                $emp_name = '';

                $notification = array(
                    'message' => 'employee number not found or employee profile lock',
                    'alert-type' => 'error'
                );

                return redirect()->back()->with($notification);
            }

            $pass_employee_no = $request->employee_no;
        } else {
            $pass_employee_no = '';
            $emp_no = '';
            $emp_name = '';
            $emp_increments = array();
        }

        $differ_list = Category::where('category_type_id', '28')->get();

        $desig = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('designations.id', 'designations.designation_name', 'categories.category_name', 'designations.salary_code')
            ->where('designations.deleted_at', '=', NULL)
            ->get();

        $increment_type = Category::where('category_type_id', 56)->get();


        Log::info('IncrementController -> Increments data getAll ended');
        return view('admin.increment.old_increments', compact('pass_employee_no', 'emp_no', 'emp_name', 'desig','increment_type', 'emp_increments', 'differ_list'));
    }

    public function store(Request $request)
    {
        Log::info('IncrementController -> old increment submit started');

        if (!isset($request->emp_no)) {
            $notification = array(
                'message' => 'Please Search Employee Before Submit',
                'alert-type' => 'error'
            );

            return redirect()->route('increments.old.index')->with($notification);
        }

        $request->validate(
            ['emp_no' => 'required'],
            ['emp_no.required' => 'Please search employee before the enter old increment data']
        );


        if ($request->eff_date != null) {

            for ($i = 0; $i < count($request->eff_date); $i++) {

                $increment = new Increment();
                $increment->emp_no = $request->emp_no;
                $increment->effective_date = date("Y-m-d", strtotime($request->eff_date[$i]));
                $increment->effective_date_arrears = date("Y-m-d", strtotime($request->eff_date_arrears[$i]));
                $increment->decision = $request->decision_id[$i];
                $increment->desgnation_id = $request->des[$i];
                $increment->increment_type =$request->type_id[$i];
                $increment->basic_sal = $request->bas_salary[$i];
                $increment->salary_step = $request->sal_step[$i];
                $increment->increment_value = $request->increment_value[$i];
                $increment->reason = $request->reson[$i];
                $increment->period = $request->period[$i];
                $increment->add_user_id = auth()->user()->employee_no;
                $increment->save();
            }

            Log::notice('IncrementController -> Created employee old increments data employee number - ' . $request->emp_no . ' created by ' . auth()->user()->employee_no);

            $notification = array(
                'message' => 'Old Increments data successfully added',
                'alert-type' => 'success'
            );

            Log::info('IncrementController -> old employee increments store ended');
            return redirect()->route('increments.old.index', ['employee_no' => $request->emp_no])->with($notification);
        } else {

            $notification = array(
                'message' => 'Old Increments Inserted Unsuccessfully',
                'alert-type' => 'error'
            );
            Log::info('IncrementController -> old employee increments store ended');
            return redirect()->route('increments.old.index', ['employee_no' => $request->emp_no])->with($notification);
        }
    }

    public function oldIncrementShow()
    {
        Log::info('IncrementController -> Increments data getAll started');

        $emp_increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
            ->join('categories', 'increments.decision', '=', 'categories.category_code')
            ->join('category_types', 'category_types.id', '=', 'categories.category_type_id')
            ->select('increments.effective_date', 'designations.designation_name', 'categories.category_name', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value')
            ->where('category_types.id', '28')
            ->get();

        Log::info('IncrementController -> Increments data getAll ended');

        return view('admin.increment.show_old_increments', compact('emp_increments'));
    }

    public function oldIncrementEdit($id)
    {

        Log::info('IncrementController -> old increment data edit started');

        $categories = $this->getCategories([28]);
        $incrementDefferOptions = $categories->where('category_type_id', '28');
        $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('designations.id', 'designations.designation_name', 'categories.category_name', 'designations.salary_code')
            ->where('designations.deleted_at', '=', NULL)
            ->get();
        $editData = Increment::find($id);

        $emp_increments = Category::where('category_type_id', 56)->get();

        Log::notice('IncrementController -> edit old increment data id - ' . $editData->id . ' edited by ' . auth()->user()->employee_no);
        Log::info('IncrementController -> old increment edit ended');

        return view('admin.increment.edit', compact('editData', 'incrementDefferOptions', 'designations','emp_increments'));
    }

    public function oldIncrementUpdate(Request $request, $id)
    {

        Log::info('IncrementController -> old increment data update started');

        $validatedData = $request->validate([
            'decision' => 'required',
            'desgnation_id' => 'required',
            'basic_sal' => 'required|numeric',
            'effective_date' => 'required|date',
            'effective_date_arrears' => 'nullable|date',
            'salary_step' => 'nullable|numeric',
            'increment_value' => 'required|numeric',
            'type_id' => 'required',
        ], [
            'basic_sal.numeric' => 'incremented basic salary value should be numeric',
            'basic_sal.required' => 'incremented basic salary required',
        ]);


        $increment = Increment::find($id);
        $increment->emp_no = $request->emp_no;
        $increment->decision = $request->decision;
        $increment->desgnation_id = $request->desgnation_id;
        $increment->effective_date = date("Y-m-d", strtotime($request->effective_date));
        $increment->effective_date_arrears = date("Y-m-d", strtotime($request->effective_date_arrears));
        $increment->basic_sal = $request->basic_sal;
        $increment->increment_type = $request->type_id;
        $increment->salary_step = $request->salary_step;
        $increment->increment_value = $request->increment_value;
        $increment->reason = $request->reason;
        $increment->period = $request->period;
        $increment->updated_user_id = auth()->user()->employee_no;
        $increment->save();

        Log::warning('IncrementController -> update old increment data id employee number - ' . $request->emp_no . ' updated by ' . auth()->user()->employee_no);
        Log::info('IncrementController -> old increment data update ended');

        $notification = array(
            'message' => 'Increment data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('increments.old.index', ['employee_no' => $request->emp_no])->with($notification);
    }

    public function oldIncrementDelete($id)
    {

        Log::info('IncrementController -> increment data delete started');

        $increment = Increment::find($id);
        $increment->delete();

        Log::emergency('IncrementController -> delete increment data employee number - ' . $increment->emp_no . ' deleted by ' . auth()->user()->employee_no);
        Log::info('IncrementController -> increment data delete ended');


        $notification = array(
            'message' => 'Increment data Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('increments.old.index', ['employee_no' => $increment->emp_no])->with($notification);
    }

    /************************************************************ */
    public function monthListIncrement()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $empNo = Auth()->user()->employee_no;

        if ($mainBranch == 51) {

            $employees =  Employee::join('designations','designations.id','=','employees.designation_id')
                         ->join('departments','departments.id','=','employees.department_id')
                         ->join('categories','designations.staff_grade','=','categories.id')
                         ->select('employees.*','designations.designation_name','departments.department_name','categories.category_name')
                         ->where('employees.increment_date', 'LIKE', date("m").'%')
                         ->where('employees.main_branch_id', 53)
                         ->where('employees.employee_status_id', 110)
                         ->orderBy('employees.increment_date','ASC')
                         ->get();

        }elseif ($mainBranch == 53) {

            if ( Auth()->user()->hasRole(['est-head','cc']) ) {

                $employees =  Employee::join('designations','designations.id','=','employees.designation_id')
                             ->join('departments','departments.id','=','employees.department_id')
                             ->join('categories','designations.staff_grade','=','categories.id')
                             ->select('employees.*','designations.designation_name','departments.department_name','categories.category_name')
                             ->where('employees.increment_date', 'LIKE', date("m").'%')
                             ->where('employees.main_branch_id', 53)
                             ->where('employees.employee_status_id', 110)
                             ->orderBy('employees.increment_date','ASC')
                             ->get();


            }elseif (Auth()->user()->hasRole(['sc'])) {

                $employees =  Employee::join('designations','designations.id','=','employees.designation_id')
                             ->join('departments','departments.id','=','employees.department_id')
                             ->join('categories','designations.staff_grade','=','categories.id')
                             ->select('employees.*','designations.designation_name','departments.department_name','categories.category_name')
                             ->where('employees.increment_date', 'LIKE', date("m").'%')
                             ->where('employees.main_branch_id', 53)
                             ->where('employees.employee_status_id', 110)
                             ->where('employees.assign_ma_user_id', $empNo)
                             ->orderBy('employees.increment_date','ASC')
                             ->get();

            }
        }
        $currentMonth = date('m');
        $currentYear = date('Y');

        return view('admin.increment.increment_month_list', compact('employees', 'currentMonth','currentYear'));
    }

    public function monthListIncrementSearch(Request $request){

        $mainBranch = Auth()->user()->main_branch_id;
        $empNo = Auth()->user()->employee_no;


        if ($request->year != NULL && $request->month != NULL) {

            if ($mainBranch == 51) {

                $employees =  Employee::join('designations','designations.id','=','employees.designation_id')
                             ->join('departments','departments.id','=','employees.department_id')
                             ->join('categories','designations.staff_grade','=','categories.id')
                             ->select('employees.*','designations.designation_name','departments.department_name','categories.category_name')
                             ->where('employees.increment_date', 'LIKE', str_pad($request->month, 2, '0', STR_PAD_LEFT).'%')
                             ->where('employees.main_branch_id', 53)
                             ->where('employees.employee_status_id', 110)
                             ->orderBy('employees.increment_date','ASC')
                             ->get();

            }elseif ($mainBranch == 53) {

                if ( Auth()->user()->hasRole(['est-head','cc']) ) {

                    $employees =  Employee::join('designations','designations.id','=','employees.designation_id')
                                 ->join('departments','departments.id','=','employees.department_id')
                                 ->join('categories','designations.staff_grade','=','categories.id')
                                 ->select('employees.*','designations.designation_name','departments.department_name','categories.category_name')
                                 ->where('employees.increment_date', 'LIKE', str_pad($request->month, 2, '0', STR_PAD_LEFT).'%')
                                 ->where('employees.main_branch_id', 53)
                                 ->where('employees.employee_status_id', 110)
                                 ->orderBy('employees.increment_date','ASC')
                                 ->get();


                }elseif (Auth()->user()->hasRole(['sc'])) {

                    $employees =  Employee::join('designations','designations.id','=','employees.designation_id')
                                 ->join('departments','departments.id','=','employees.department_id')
                                 ->join('categories','designations.staff_grade','=','categories.id')
                                 ->select('employees.*','designations.designation_name','departments.department_name','categories.category_name')
                                 ->where('employees.increment_date', 'LIKE', str_pad($request->month, 2, '0', STR_PAD_LEFT).'%')
                                 ->where('employees.main_branch_id', 53)
                                 ->where('employees.employee_status_id', 110)
                                 ->where('employees.assign_ma_user_id', $empNo)
                                 ->orderBy('employees.increment_date','ASC')
                                 ->get();

                }
            }

        }

         $currentMonth = $request->month;
         $currentYear = $request->year;

         return view('admin.increment.increment_month_list', compact('employees','currentMonth','currentYear'));

    }
}
