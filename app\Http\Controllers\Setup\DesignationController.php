<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\Designation;
use App\Models\DesignationMainGroup;
use App\Models\DesignationSubGroup;
use App\Models\SalaryScale;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class DesignationController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator');
    }

    public function designationIndex()
    {
        Log::info('DesignationController -> designation index started');
        $activeDesignations = Designation::where('active_status',1)->orderby('id')->get();
        $inactiveDesignations = Designation::where('active_status',0)->orderby('id')->get();
        $trashDesignations = Designation::onlyTrashed()->get();
        return view('admin.setups.designation.index', compact('activeDesignations','inactiveDesignations','trashDesignations'));

        Log::notice('DesignationController -> designation Count - ' . $activeDesignations->count());
        Log::info('DesignationController -> designation index ended');
    }

    public function designationAdd()
    {
        Log::info('DesignationController -> designation add started');
        $categories = $this->getCategories([13, 14, 17, 18, 19, 20]);
        $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        $grades = $categories->where('category_type_id', '14');
        $staffGrades = $categories->where('category_type_id', '17');
        $mainGroups = $categories->where('category_type_id', '18');
        $ugcMIS = $categories->where('category_type_id', '19');
        $ugcFinance = $categories->where('category_type_id', '20');
        $designationMainGroups = DesignationMainGroup::all();
        $salary_codes = Designation::select('salary_code')
                        ->whereNotIn('salary_code', ['ON CONTRACT', 'Allowance'])
                        ->where('active_status',1)
                        ->groupBy('salary_code')
                        ->orderBy('salary_code')
                        ->get();
        return view('admin.setups.designation.add', compact('designationMainGroups', 'mainBranches', 'grades', 'staffGrades', 'mainGroups', 'ugcMIS', 'ugcFinance','salary_codes'));
        Log::info('DesignationController -> designation add ended');
    }

    public function designationStore(Request $request)
    {
        Log::info('DesignationController -> designation store started');

        $validatedData = $request->validate([
            'designation_name' => 'required',
            'designation_main_id' => 'required',
            'designation_sub_id' => 'required',
            'salary_code' => 'required',
            'grade_id' => 'required',
            'staff_grade' => 'required',
            'main_group' => 'required',
            'ugc_mis' => 'required',
            'ugc_finance' => 'required',
        ]);

        // get salary scale data
        $salaryScale = SalaryScale::join('salary_scale_versions', 'salary_scales.salary_scale_version_id', '=', 'salary_scale_versions.id')
                                ->where('salary_scales.salary_code', $request->salary_code)
                                ->where('salary_scale_versions.status', 1)
                                ->select('salary_scales.id')
                                ->first();

        $data = new Designation();
        $data->designation_name = $request->designation_name;
        $data->designation_main_id = $request->designation_main_id;
        $data->designation_sub_id = $request->designation_sub_id;
        $data->designation_division = $request->designation_division;
        $data->salary_code = $request->salary_code;
        $data->grade_id = $request->grade_id;
        $data->staff_grade = $request->staff_grade;
        $data->main_group = $request->main_group;
        $data->ugc_mis = $request->ugc_mis;
        $data->ugc_finance = $request->ugc_finance;
        $data->salary_description = $request->salary_description;
        $data->active_status = 1;
        $data->salary_scale = $salaryScale?->id ?? 0;
        $data->created_at = Carbon::now();
        $data->save();

        Log::notice('DesignationController -> Created designation id - ' . $data->id . ' created by ' . auth()->user()->id);
        Log::info('DesignationController -> designation create ended');

        $notification = array(
            'message' => 'New designation Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('designation.index')->with($notification);
    }

    public function designationEdit($id)
    {
        Log::info('DesignationController -> designation edit started');
        $editData = Designation::find($id);
        $categories = $this->getCategories([13, 14, 17, 18, 19, 20]);
        $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
        $grades = $categories->where('category_type_id', '14');
        $staffGrades = $categories->where('category_type_id', '17');
        $mainGroups = $categories->where('category_type_id', '18');
        $ugcMIS = $categories->where('category_type_id', '19');
        $ugcFinance = $categories->where('category_type_id', '20');
        $designationMainGroups = DesignationMainGroup::all();
        $designationSubGroups = DesignationSubGroup::all();
        return view('admin.setups.designation.edit', compact('editData', 'designationMainGroups', 'designationSubGroups', 'mainBranches', 'grades', 'staffGrades', 'mainGroups', 'ugcMIS', 'ugcFinance'));
        Log::notice('DesignationController -> edit designation id - ' . $editData->id . ' edited by ' . auth()->user()->id);
        Log::info('DesignationController -> designation edit ended');
    }

    public function designationUpdate(Request $request, $id)
    {

        Log::info('DesignationController -> designation update started');

        $validatedData = $request->validate([
            'designation_name' => ['required'],
            'designation_main_id' => 'required',
            'designation_sub_id' => 'required',
            'salary_code' => 'required',
            'grade_id' => 'required',
            'staff_grade' => 'required',
            'main_group' => 'required',
            'ugc_mis' => 'required',
            'ugc_finance' => 'required',
        ]);


        $data = Designation::find($id);
        $data->designation_name = $request->designation_name;
        $data->designation_main_id = $request->designation_main_id;
        $data->designation_sub_id = $request->designation_sub_id;
        $data->designation_division = $request->designation_division;
        $data->salary_code = $request->salary_code;
        $data->grade_id = $request->grade_id;
        $data->staff_grade = $request->staff_grade;
        $data->main_group = $request->main_group;
        $data->ugc_mis = $request->ugc_mis;
        $data->ugc_finance = $request->ugc_finance;
        $data->salary_description = $request->salary_description;
        $data->updated_at = Carbon::now();
        $data->save();

        Log::notice('DesignationController -> update designation id - ' . $data->id . ' updated by ' . auth()->user()->id);
        Log::info('DesignationController -> designation update ended');

        $notification = array(
            'message' => 'designation data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('designation.index')->with($notification);
    }

    public function designationSoftdelete($id)
    {

        Log::info('DesignationController -> designation soft delete started');
        $designation = Designation::find($id);
        $designation->delete();

        $notification = array(
            'message' => 'Designation Deleted Successfully',
            'alert-type' => 'warning'
        );

        return redirect()->route('designation.index')->with($notification);

        Log::notice('DesignationController -> soft delete designation id - ' . $designation->id . ' deleted by ' . auth()->user()->id);
        Log::info('DesignationController -> Designation soft delete ended');
    }

    public function designationRestore($id)
    {

        Log::info('DesignationController -> designation restore started');

        $designation = Designation::withTrashed()->find($id);
        $designation->restore();

        $notification = array(
            'message' => 'Designation Restore Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('designation.index')->with($notification);

        Log::notice('DesignationController -> restore designation id - ' . $designation->id . ' deleted by ' . auth()->user()->id);
        Log::info('DesignationController -> Designation restore ended');
    }

    public function designationDelete($id)
    {

        Log::info('DesignationController -> designation delete started');

        $designation = Designation::onlyTrashed()->find($id);
        $designation->forceDelete();

        $notification = array(
            'message' => 'Designation Permenetly Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('designation.index')->with($notification);

        Log::emergency('DesignationController -> delete designation id - ' . $designation->id . ' deleted by ' . auth()->user()->id);
        Log::info('DesignationController -> Designation delete ended');
    }

    public function designationInactive($id)
    {

        $desigId = decrypt($id);
        $designation = Designation::find($desigId);
        $designation->active_status = 0;
        $designation->save();

        $notification = array(
            'message' => 'Designation Inactive successfully',
            'alert-type' => 'success'
        );

        return redirect()->back()->with($notification);
    } //end method


    public function designationActive($id)
    {

        $desigId = decrypt($id);
        $designation = Designation::find($desigId);
        $designation->active_status = 1;
        $designation->save();

        $notification = array(
            'message' => 'Designation Active successfully',
            'alert-type' => 'success'
        );

        return redirect()->back()->with($notification);
    } //end method
}
