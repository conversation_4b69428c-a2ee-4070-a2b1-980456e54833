<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class BondEmp extends Model
{
    use HasFactory,LogsActivity;

    protected $table = 'bond_emps';

    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('hrms_bond_emps')
        ->logOnlyDirty()
        ->dontLogIfAttributesChangedOnly(['updated_at'])
        ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    public function BondCategory()
    {
        return $this->belongsTo(Category::class,'bond_category_id');
    }

    public function BondType()
    {
        return $this->belongsTo(Category::class,'bond_type');
    }
}
