<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class MaNotifyMail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }


    public function envelope()
    {
        return new Envelope(
            subject: 'HRMS USJ - TEST MAIL',
            tags: ['notice'],
        );
    }

    public function content()
    {
        return new Content(
            markdown: 'emails.ma_mail',
            with: [
                'data' => $this->data
            ],
        );
    }


    public function attachments()
    {
        return [];
    }
}
