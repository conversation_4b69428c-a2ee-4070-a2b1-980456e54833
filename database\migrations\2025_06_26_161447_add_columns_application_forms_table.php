<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('application_forms', function (Blueprint $table) {
            $table->integer('duty_assume_status')->default(0)->after('total_marks');
            $table->integer('duty_assume_ma_emp')->default(0)->after('duty_assume_status');
            $table->date('duty_assume_ma_date')->nullable()->after('duty_assume_ma_emp');
            $table->date('duty_assume_date')->nullable()->after('duty_assume_ma_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('application_forms', function (Blueprint $table) {
            $table->dropColumn('duty_assume_status');
            $table->dropColumn('duty_assume_ma_emp');
            $table->dropColumn('duty_assume_ma_date');
            $table->dropColumn('duty_assume_date');
        });
    }
};
