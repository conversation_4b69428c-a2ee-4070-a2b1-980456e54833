<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('non_increments', function (Blueprint $table) {
            $table->integer('salary_scale_version_id')->default(1)->after('salary_step');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('non_increments', function (Blueprint $table) {
            $table->dropColumn('salary_scale_version_id');
        });
    }
};
