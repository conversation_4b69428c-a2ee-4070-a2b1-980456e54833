<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('salary_revision2025s', function (Blueprint $table) {
            $table->text('remark')->nullable()->after('sal_2027');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('salary_revision2025s', function (Blueprint $table) {
            //
            $table->dropColumn('remark');
        });
    }
};
