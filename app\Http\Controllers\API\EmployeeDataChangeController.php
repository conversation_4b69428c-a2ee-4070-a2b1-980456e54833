<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class EmployeeDataChangeController extends Controller
{

    public function mobileNumberUpdate(Request $request, $id)
    {
        $predefinedApiKey = '06f99d11-a02b-4322-ad44-37b09a610a02';

        // Checking for the API key in the request headers
        $apiKey = $request->header('Api-Key');

        // Validate the API key
        if ($apiKey !== $predefinedApiKey) {
            return response()->json(['error' => 'Unauthorized: Invalid API key'], 401);
        }

        try {
            $data = Employee::findOrFail($id);
        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Employee not found'], 404);
        }

        $data->mobile_no = $request->mobile_no;
        $data->updated_ma_user_id = 12395;
        $data->updated_ma_date = date('Y-m-d');
        $data->save();

        return response()->json(['message' => 'Mobile number updated successfully'], 200);
    }

    public function emailUpdate(Request $request, $id)
    {
        $predefinedApiKey = 'b8e950ca-fbef-4394-acef-feed4f739798';

        // Checking for the API key in the request headers
        $apiKey = $request->header('Api-Key');

        // Validate the API key
        if ($apiKey !== $predefinedApiKey) {
            return response()->json(['error' => 'Unauthorized: Invalid API key'], 401);
        }

        try {
            $data = Employee::findOrFail($id);
        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Employee not found'], 404);
        }

        $data->email = $request->email;
        $data->updated_ma_user_id = 12395;
        $data->updated_ma_date = date('Y-m-d');
        $data->save();

        return response()->json(['message' => 'Email updated successfully'], 200);
    }


}
