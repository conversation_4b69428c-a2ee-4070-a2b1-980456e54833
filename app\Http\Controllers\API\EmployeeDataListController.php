<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class EmployeeDataListController extends Controller
{
    public function AdminOfficerEligibilityList()
    {
        //  126 - Deputy Registrar
        // 18 - Assistant Registrar
        // 422 - Senior Assistant Registrar
        // 729 - Senior Assistant Registrar (contract)
        /**********************************/
        //9918 - Registrar
        //1932 - Vice Chaancellor
        //7858 - Center for IT Service Director

        $data = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as title', 'employees.title_id', '=', 'title.id')
            ->join('faculties', 'employees.faculty_id', '=', 'faculties.id')
            ->join('departments', 'employees.department_id', '=', 'departments.id')
            ->select(
                'employees.employee_no',
                'employees.email',
                'faculties.faculty_name',
                'departments.department_name',
                DB::raw("CONCAT(title.category_name, ' ', employees.initials, ' ', employees.last_name) as full_name"),
                'designations.designation_name'
            )
            ->whereNotIn('employees.faculty_id', [50, 51])
            ->whereIn('employees.designation_id', [18, 126, 422, 729])
            ->where('employee_status_id', 110)
            ->orWhereIn('employees.employee_no', [9918,1932,7858,12393])
            ->get();


        return $data;
    }
}
