<?php

namespace App\Http\Controllers\Backend\Election;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Models\nacVote;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class nacElectionController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|cc|sc|lc');
    }
    public function voteChecking()
    {

        $emp_text = Employee::join('categories as titelT', 'titelT.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->select(
                'employees.employee_no',
                'employees.initials',
                'employees.last_name',
                'titelT.category_name as title',
                'g.category_name as grade',
                'designations.designation_name',
                'departments.department_name',
                'employees.vote',
                'employees.nic'
            )
            ->where('employees.main_branch_id', 53)
            ->where('employees.employee_status_id', 110)
            ->whereIn('employees.employee_work_type', [138, 139])
            ->orderby('employees.employee_no')
            ->get();

        return view('admin.election.vote_check', compact('emp_text'));
    }

    public function voteConfirm($id1, $id2)
    {

        $empNo = decrypt($id1);
        $sn = $id2;

        $emp_text = Employee::join('categories as titelT', 'titelT.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->select(
                'employees.employee_no',
                'employees.initials',
                'employees.last_name',
                'titelT.category_name as title',
                'g.category_name as grade',
                'designations.designation_name',
                'departments.department_name',
                'departments.name_status',
                'faculties.id',
                'faculties.faculty_name',
                'employees.nic'
            )
            ->where('employees.employee_no', $empNo)
            ->get();

        if (count($emp_text) > 0) {
            foreach ($emp_text as $emp_texts) {
                $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                $desig = $emp_texts->designation_name . ' ' . $emp_texts->grade;
                if ($emp_texts->name_status == 1) {
                    $dep_name = 'Department of ' . $emp_texts->department_name;
                } else {
                    $dep_name = $emp_texts->department_name;
                }

                if ($emp_texts->id == 50) {
                    $fac_name = "";
                } elseif ($emp_texts->id == 51) {
                    $fac_name = "";
                } else {
                    $fac_name = $emp_texts->faculty_name;
                }
                $nic = $emp_texts->nic;
            }
        } else {
            $name = '';
            $desig = '';
            $dep_name = '';
            $fac_name = '';
            $nic = '';
        }



        return view('admin.election.vote_confirm', compact(
            'empNo',
            'name',
            'desig',
            'dep_name',
            'fac_name',
            'nic',
            'sn'
        ));
    }

    public function voteStore(Request $request)
    {
        if ($request->empNo != '') {
            $save_text = Employee::where('employee_no', $request->empNo)->first();

            if ($save_text) {
                $save_text->vote = 1;
                $save_text->vote_add_user = auth()->user()->employee_no;
                $save_text->vote_time = now();
                $save_text->save();

                $notification = array(
                    'message' => 'Employee Vote Successfully',
                    'alert-type' => 'success'
                );

                return redirect()->route('nac.election.vote.checking')->with($notification);
            }
        }
    }

    public function summaryDisplay()
    {

        $facultyVotes = Employee::join('faculties', 'employees.faculty_id', '=', 'faculties.id')
            ->select(
                'faculties.faculty_name',
                DB::raw('SUM(CASE WHEN employees.vote = 1 THEN 1 ELSE 0 END) as voted'),
                DB::raw('SUM(CASE WHEN employees.vote = 0 THEN 1 ELSE 0 END) as not_voted')
            )
            ->where('employees.main_branch_id', 53)
            ->where('employees.employee_status_id', 110)
            ->whereIn('employees.employee_work_type', [138, 139])
            ->groupBy('faculties.faculty_name')
            ->get();

        $totalVotes = Employee::select(
            DB::raw('SUM(CASE WHEN vote = 1 THEN 1 ELSE 0 END) as voted'),
            DB::raw('SUM(CASE WHEN vote = 0 THEN 1 ELSE 0 END) as not_voted')
        )
            ->where('employees.main_branch_id', 53)
            ->where('employees.employee_status_id', 110)
            ->whereIn('employees.employee_work_type', [138, 139])
            ->first();

        return view('admin.election.vote_summary_display', compact('facultyVotes', 'totalVotes'));
    }

    public function getVoteSummaryData()
    {
        $facultyVotes = Employee::join('faculties', 'employees.faculty_id', '=', 'faculties.id')
            ->select(
                'faculties.faculty_name',
                DB::raw('SUM(CASE WHEN employees.vote = 1 THEN 1 ELSE 0 END) as voted'),
                DB::raw('SUM(CASE WHEN employees.vote = 0 THEN 1 ELSE 0 END) as not_voted'),
                DB::raw('COUNT(*) as total_electors')
            )
            ->where('employees.main_branch_id', 53)
            ->where('employees.employee_status_id', 110)
            ->whereIn('employees.employee_work_type', [138, 139])
            ->groupBy('faculties.faculty_name')
            ->orderBy('faculties.faculty_name', 'asc')
            ->get();

        $totalVotes = Employee::select(
            DB::raw('SUM(CASE WHEN vote = 1 THEN 1 ELSE 0 END) as voted'),
            DB::raw('SUM(CASE WHEN vote = 0 THEN 1 ELSE 0 END) as not_voted'),
            DB::raw('COUNT(*) as total_electors')
        )
            ->where('employees.main_branch_id', 53)
            ->where('employees.employee_status_id', 110)
            ->whereIn('employees.employee_work_type', [138, 139])
            ->first();

        // Calculate total voted and not voted percentages
        $totalVotedPercentage = 0;
        $totalNotVotedPercentage = 0;

        if ($totalVotes->total_electors > 0) {
            $totalVotedPercentage = ($totalVotes->voted / $totalVotes->total_electors) * 100;
            $totalNotVotedPercentage = ($totalVotes->not_voted / $totalVotes->total_electors) * 100;
        }

        return response()->json([
            'facultyVotes' => $facultyVotes,
            'totalVotes' => $totalVotes,
            'totalVotedPercentage' => $totalVotedPercentage,
            'totalNotVotedPercentage' => $totalNotVotedPercentage
        ]);
    }

    public function summary_dep_open()
    {

        $emp_text = Employee::join('faculties', 'employees.faculty_id', '=', 'faculties.id')
            ->join('departments', 'department_id', '=', 'departments.id')
            ->select(
                'faculties.faculty_name',
                'departments.department_name',
                DB::raw('SUM(CASE WHEN employees.vote = 1 THEN 1 ELSE 0 END) as voted'),
                DB::raw('SUM(CASE WHEN employees.vote = 0 THEN 1 ELSE 0 END) as not_voted'),
                DB::raw('COUNT(*) as total_electors')
            )
            ->where('employees.main_branch_id', 53)
            ->where('employees.employee_status_id', 110)
            ->whereIn('employees.employee_work_type', [138, 139])
            ->groupBy('faculties.faculty_name', 'departments.department_name', 'departments.id', 'faculties.id')
            ->orderBy('faculties.faculty_name', 'asc')  // Ascending order for faculties
            ->orderBy('departments.id', 'asc')  // Ascending order for departments
            ->get();

        return view('admin.election.summary_dep', compact('emp_text'));
    }

    public function voteCountingOpen()
    {
        $vote_count = nacVote::get();

        if (count($vote_count) > 0) {
            foreach($vote_count as $vote_counts){
                if ($vote_counts->emp_no == 5038) {
                    $emp1vote = $vote_counts->voute_count;
                }
                if ($vote_counts->emp_no == 7888) {
                    $emp2vote = $vote_counts->voute_count;
                }
                if ($vote_counts->emp_no == 8915) {
                    $emp3vote = $vote_counts->voute_count;
                }
                if ($vote_counts->emp_no == 8232) {
                    $emp4vote = $vote_counts->voute_count;
                }
            }
        }

        $empNo1 = 5038;
        $empName1 = 'Mr. K.L.P. Dayarathna';      
        

        $empNo2 = 7888;
        $empName2 = 'Mr. K.D.C. Jagath';        
        // $emp2vote = 0;

        $empNo3 = 8915;
        $empName3 = 'Mr. K.K.V.V.P. Devinda';        
        // $emp3vote = 0;

        $empNo4 = 8232;
        $empName4 = 'Mr. S.B.G.R.A.B. Sampath';        
        // $emp4vote = 0;

        return view('admin.election.vote_counting', compact(
            'empNo1',
            'empName1',
            'emp1vote',
            'empNo2',
            'empName2',
            'emp2vote',
            'empNo3',
            'empName3',
            'emp3vote',
            'empNo4',
            'empName4',
            'emp4vote',
        ));
    }

    public function incrementVote(Request $request)
    {
        $empNo = $request->input('empNo');

        // Increment the vote count for the given employee
        $vote = nacVote::where('emp_no', $empNo)
            ->increment('voute_count');

        if ($vote) {
            return response()->json(['success' => true, 'message' => 'Vote added successfully!']);
        } else {
            return response()->json(['success' => false, 'message' => 'Failed to add vote!']);
        }
    }
}
