<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vancancy_non_academics', function (Blueprint $table) {
            //
            $table->integer('short_list_complete_status')->default(0)->after('vacancy_status_type_id');
            $table->integer('short_list_complete_user')->default(0)->after('short_list_complete_status');
            $table->integer('short_list_complete_user_position')->default(0)->after('short_list_complete_user');
            $table->date('short_list_complete_date')->nullable()->after('short_list_complete_user_position');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vancancy_non_academics', function (Blueprint $table) {
            //
            $table->dropColumn('short_list_complete_status');
            $table->dropColumn('short_list_complete_user');
            $table->dropColumn('short_list_complete_user_position');
            $table->dropColumn('short_list_complete_date');
        });
    }
};
