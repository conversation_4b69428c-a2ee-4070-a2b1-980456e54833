<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\ApplicationRequest;
use App\Mail\ApplicationCompleteMail;
use App\Models\Vacancy;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use App\Mail\UserVerificationMail;
use App\Models\AdvanceLevelResult;
use App\Models\AdvanceLevelResultSummary;
use App\Models\ApplicantVerification;
use App\Models\Application;
use App\Models\Bond;
use App\Models\City;
use App\Models\Degree;
use App\Models\DegreeCertificate;
use App\Models\DegreeSubject;
use App\Models\Department;
use App\Models\Diploma;
use App\Models\DiplomaCertificate;
use App\Models\EmploymentRecord;
use App\Models\EmploymentRecordCertificate;
use App\Models\Faculty;
use App\Models\Membership;
use App\Models\OrdinaryLevelResult;
use App\Models\OrdinaryLevelResultSummary;
use App\Models\ProfessionalQualification;
use App\Models\Referee;
use App\Models\ReleaseLetter;
use App\Models\Research;
use App\Models\SpecialQulification;
use Illuminate\Support\Facades\Mail as FacadesMail;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class IndexController extends Controller
{

    public function homePage()
    {

        Log::info('IndexController -> home page index started');

        $currentSession = session()->forget('vacancy_id', 'application_verification_id', 'application_verification_success', 'email', 'nic');
        return view('frontend.index');

        Log::info('IndexController -> home page index ended');
    }

    public function vacancyList(Request $request)
    {

        Log::info('IndexController-> vacancy list index started');

        $currentDate = date('Y-m-d');

        if (isset($request->faculty_id) && isset($request->department_id)) {

            if ($request->faculty_id == 0 && $request->department_id == 0) {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as employement', 'vacancies.main_category_id', '=', 'employement.id')
                    ->select('vacancies.*', 'categories.display_name', 'employement.category_name')
                    ->where('date_closed', '>=', $currentDate)
                    ->where('date_opened', '<=', $currentDate)
                    ->where('vacancy_visibility_status', '=', 200)
                    ->where('vacancy_status_id', '=', 1)
                    ->whereIn('vacancy_status_type_id', array(27, 259))
                    ->where('faculty_id', NULL)
                    ->where('department_id', NULL)
                    ->orderByDesc('vacancies.id')
                    ->get();
            } elseif ($request->faculty_id != 0 && $request->department_id == 0) {
                # code...
                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as employement', 'vacancies.main_category_id', '=', 'employement.id')
                    ->select('vacancies.*', 'categories.display_name', 'employement.category_name')
                    ->where('date_closed', '>=', $currentDate)
                    ->where('date_opened', '<=', $currentDate)
                    ->where('vacancy_visibility_status', '=', 200)
                    ->where('vacancy_status_id', '=', 1)
                    ->whereIn('vacancy_status_type_id', array(27, 259))
                    ->where('faculty_id', $request->faculty_id)
                    ->where('department_id', NULL)
                    ->orderByDesc('vacancies.id')
                    ->get();
            } elseif ($request->faculty_id == 0 && $request->department_id != 0) {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as employement', 'vacancies.main_category_id', '=', 'employement.id')
                    ->select('vacancies.*', 'categories.display_name', 'employement.category_name')
                    ->where('date_closed', '>=', $currentDate)
                    ->where('date_opened', '<=', $currentDate)
                    ->where('vacancy_visibility_status', '=', 200)
                    ->where('vacancy_status_id', '=', 1)
                    ->whereIn('vacancy_status_type_id', array(27, 259))
                    ->where('faculty_id', NULL)
                    ->where('department_id', $request->department_id)
                    ->orderByDesc('vacancies.id')
                    ->get();
            } else {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as employement', 'vacancies.main_category_id', '=', 'employement.id')
                    ->select('vacancies.*', 'categories.display_name', 'employement.category_name')
                    ->where('date_closed', '>=', $currentDate)
                    ->where('date_opened', '<=', $currentDate)
                    ->where('vacancy_visibility_status', '=', 200)
                    ->where('vacancy_status_id', '=', 1)
                    ->whereIn('vacancy_status_type_id', array(27, 259))
                    ->where('faculty_id', $request->faculty_id)
                    ->where('department_id', $request->department_id)
                    ->orderByDesc('vacancies.id')
                    ->get();
            }


            $currentDep = $request->department_id;
            $currentFac = $request->faculty_id;
        } elseif (isset($request->faculty_id) && !isset($request->department_id)) {

            if ($request->faculty_id == 0) {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as employement', 'vacancies.main_category_id', '=', 'employement.id')
                    ->select('vacancies.*', 'categories.display_name', 'employement.category_name')
                    ->where('date_closed', '>=', $currentDate)
                    ->where('date_opened', '<=', $currentDate)
                    ->where('vacancy_visibility_status', '=', 200)
                    ->where('vacancy_status_id', '=', 1)
                    ->whereIn('vacancy_status_type_id', array(27, 259))
                    ->where('faculty_id', NULL)
                    ->orderByDesc('vacancies.id')
                    ->get();
            } else {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as employement', 'vacancies.main_category_id', '=', 'employement.id')
                    ->select('vacancies.*', 'categories.display_name', 'employement.category_name')
                    ->where('date_closed', '>=', $currentDate)
                    ->where('date_opened', '<=', $currentDate)
                    ->where('vacancy_visibility_status', '=', 200)
                    ->where('vacancy_status_id', '=', 1)
                    ->whereIn('vacancy_status_type_id', array(27, 259))
                    ->where('faculty_id', $request->faculty_id)
                    ->orderByDesc('vacancies.id')
                    ->get();
            }

            $currentDep = "";
            $currentFac = $request->faculty_id;
        } elseif (!isset($request->faculty_id) && isset($request->department_id)) {

            if ($request->department_id == 0) {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as employement', 'vacancies.main_category_id', '=', 'employement.id')
                    ->select('vacancies.*', 'categories.display_name', 'employement.category_name')
                    ->where('date_closed', '>=', $currentDate)
                    ->where('date_opened', '<=', $currentDate)
                    ->where('vacancy_visibility_status', '=', 200)
                    ->where('vacancy_status_id', '=', 1)
                    ->whereIn('vacancy_status_type_id', array(27, 259))
                    ->where('department_id', NULL)
                    ->orderByDesc('vacancies.id')
                    ->get();
            } else {

                $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('categories as employement', 'vacancies.main_category_id', '=', 'employement.id')
                    ->select('vacancies.*', 'categories.display_name', 'employement.category_name')
                    ->where('date_closed', '>=', $currentDate)
                    ->where('date_opened', '<=', $currentDate)
                    ->where('vacancy_visibility_status', '=', 200)
                    ->where('vacancy_status_id', '=', 1)
                    ->whereIn('vacancy_status_type_id', array(27, 259))
                    ->where('department_id', $request->department_id)
                    ->orderByDesc('vacancies.id')
                    ->get();
            }



            $currentDep = $request->department_id;
            $currentFac = "";
        } else {

            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('categories as employement', 'vacancies.main_category_id', '=', 'employement.id')
                ->select('vacancies.*', 'categories.display_name', 'employement.category_name')
                ->where('date_closed', '>=', $currentDate)
                ->where('date_opened', '<=', $currentDate)
                ->where('vacancy_visibility_status', '=', 200)
                ->where('vacancy_status_id', '=', 1)
                ->whereIn('vacancy_status_type_id', array(27, 259))
                ->orderByDesc('vacancies.id')
                ->get();

            $currentDep = "";
            $currentFac = "";
        }

        $faculties = Vacancy::where('date_closed', '>=', $currentDate)
            ->where('date_opened', '<=', $currentDate)
            ->pluck('faculty_id')->unique();
        $NullFacultyCount = Vacancy::whereNull('faculty_id')->pluck('faculty_id')->count();
        $facultysWithInfo = Faculty::whereIn('id', $faculties)->get();

        $departments = Vacancy::where('date_closed', '>=', $currentDate)
            ->where('date_opened', '<=', $currentDate)
            ->pluck('department_id')->unique();

        $NullDepartmentCount = Vacancy::whereNull('department_id')->pluck('department_id')->count();

        $departmentsWithInfo = Department::whereIn('id', $departments);

        if ($currentFac) {
            $departmentsWithInfo->where('faculty_code', $currentFac);
        }

        $departmentsWithInfo = $departmentsWithInfo->get();

        return view('frontend.vacancy_list', compact('vacancies', 'facultysWithInfo', 'departmentsWithInfo', 'currentDep', 'currentFac', 'NullFacultyCount', 'NullDepartmentCount'));

        Log::info('IndexController-> active vancancies Count - ' . $vacancies->count());
        Log::info('IndexController-> vacancy list index ended');
    }

    public function jobSelect(Request $request)
    {

        Log::info('IndexController-> Applicate job select function started');

        session()->get('vacancy_id');
        session()->forget('vacancy_id');
        $session = Session::put('vacancy_id', $request->vacancy_id);

        return redirect()->route('applicant.login');

        Log::info('IndexController-> Applicate selected vacancy id - ' . $session);
        Log::info('IndexController-> Applicate job select function ended');
    }

    public function applicantLogin()
    {

        Log::info('IndexController-> Applicant Login get started');

        if (!session()->get('vacancy_id')) {

            $notification = array(
                'message' => 'Your application session has been expired',
                'alert-type' => 'error'
            );

            return redirect()->route('home')->with($notification);
        }

        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name')
            ->find(session()->get('vacancy_id'));

        return view('frontend.user_register', compact('vacancy'));

        Log::notice('IndexController-> Applicate login for vacancy id - ' . session()->get('vacancy_id'));

        Log::info('IndexController-> Applicamt Login ended');
    }

    public function mobileVerification(Request $request)
    {

        Log::info('IndexController-> Email and mobile verification get started');

        $validatedData = $request->validate([
            'nic' => ['required', 'regex:/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/m'],
            'mobile_no' => ['required', 'regex:/^(\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/m'],
            //'mobile_no' => ['required'],
            'email' => 'required|email:rfc,dns',
            'terms' => ['required'],
        ], [
            'nic.required' => 'please enter valid NIC number',
            'mobile_no.required' => 'please enter valid mobile number',
            'email.required' => 'please enter valid email',
            'terms.required' => 'please agree email information communication',
            'mobile_no.regex' => 'you entered phone number in invalid format',
        ]);

        if (!session()->get('vacancy_id')) {

            $notification = array(
                'message' => 'Your application session has been expired',
                'alert-type' => 'error'
            );

            return redirect()->route('home')->with($notification);
        }

        $vacancy = Vacancy::find(session()->get('vacancy_id'));
        $dateClosed = Carbon::parse($vacancy->date_closed);
        //dd($dateClosed);

        if (mb_strlen($request->nic) == 10) {

            $part01 = substr($request->nic, 0, 2); //Birth by year

            $part02 = substr($request->nic, 2, 3); //Birth day of the year

            $currentYear = Carbon::now()->year % 100;

            if ($part01 < $currentYear) {

                $fullYear = '20' . $part01;
            } else {

                $fullYear = '19' . $part01;
            }

            if ((($fullYear % 4 == 0) && ($fullYear % 100 != 0)) || ($fullYear % 400 == 0)) {

                //$isLeapYear = "Leap Year";
                if ($part02 >= 1 && $part02 <= 366) {

                    $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 1);
                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    //dd($dob);

                    $before = $dateClosed->subYears($vacancy->min_age)->format('Y-m-d');
                    $after = $dateClosed->subYears($vacancy->max_age - $vacancy->min_age)->format('Y-m-d');

                    $isWithinRange = Carbon::createFromFormat('Y-m-d', $dob)->between($before, $after);

                    if ($isWithinRange) {

                        /***************************************************************** */
                        $verifiedUser = DB::table('applications')
                            ->where('vacancy_id', session()->get('vacancy_id'))
                            ->where('nic', $request->nic)
                            //->orwhere('new_nic',$request->nic)
                            ->count();

                        if ($verifiedUser != 1) {

                            $data = new ApplicantVerification();
                            $data->vacancy_id = session()->get('vacancy_id');
                            $data->nic = strtoupper($request->nic);
                            $data->mobile_no = $request->mobile_no;
                            $data->email = $request->email;

                            $maxnumber = DB::table('applicant_verifications')
                                ->where('vacancy_id', session()->get('vacancy_id'))
                                ->where('nic', $request->nic)
                                ->select(DB::raw('MAX(attempt_count) as value'))
                                ->get();

                            $maxValue = json_decode($maxnumber, true);
                            $sortnumber = $maxValue[0]["value"] + 1;
                            $data->attempt_count = $sortnumber;
                            $data->created_at = Carbon::now();
                            $data->save();

                            $otp_code = random_int(10000, 99999);

                            Log::alert('IndexController-> Email and mobile verification otp code - ' . $otp_code . ' send to the ' . $request->mobile_no . ' and ' . $request->email . ' with NIC ' . $request->nic);

                            $otpCookie = Cookie::queue('otp_code', $otp_code, 10);
                            session()->get('application_verification_id');
                            session()->forget('application_verification_id');
                            Session::put('application_verification_id', $data->id);

                            $emailData = [
                                //'mobile' =>  $request->mobile_no,
                                'otp' => $otp_code,
                                'date' => Carbon::now()
                            ];

                            $mail = new UserVerificationMail($emailData);
                            //sending email
                            FacadesMail::to($request->email, 'USJ HRM SYSTEM')->send($mail);

                            Log::notice('IndexController->Email and mobile verification code for  - ' . $request->email . ' sending Successfully');

                            return redirect()->route('otp.screen');

                            Log::info('IndexController-> Email and mobile verification ended');
                        } else {

                            $notification = array(
                                'message' => 'You have already started the application process for this vacancy. Please use the “Edit & Submit” option for modifications.',
                                'alert-type' => 'error'
                            );

                            return redirect()->route('home')->with($notification);
                        }
                        /********************************************************** */
                    } else {

                        $notification = array(
                            'message' => 'Your age is not sufficient to apply for this vacancy',
                            'alert-type' => 'error'
                        );

                        return redirect()->route('home')->with($notification);
                    }
                } else if ($part02 >= 501 && $part02 <= 866) {

                    $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 501);
                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    $before = $dateClosed->subYears($vacancy->min_age)->format('Y-m-d');
                    $after = $dateClosed->subYears($vacancy->max_age - $vacancy->min_age)->format('Y-m-d');

                    $isWithinRange = Carbon::createFromFormat('Y-m-d', $dob)->between($before, $after);

                    if ($isWithinRange) {

                        /***************************************************************** */
                        $verifiedUser = DB::table('applications')
                            ->where('vacancy_id', session()->get('vacancy_id'))
                            ->where('nic', $request->nic)
                            //->orwhere('new_nic',$request->nic)
                            ->count();

                        if ($verifiedUser != 1) {

                            $data = new ApplicantVerification();
                            $data->vacancy_id = session()->get('vacancy_id');
                            $data->nic = strtoupper($request->nic);
                            $data->mobile_no = $request->mobile_no;
                            $data->email = $request->email;

                            $maxnumber = DB::table('applicant_verifications')
                                ->where('vacancy_id', session()->get('vacancy_id'))
                                ->where('nic', $request->nic)
                                ->select(DB::raw('MAX(attempt_count) as value'))
                                ->get();

                            $maxValue = json_decode($maxnumber, true);
                            $sortnumber = $maxValue[0]["value"] + 1;
                            $data->attempt_count = $sortnumber;
                            $data->created_at = Carbon::now();
                            $data->save();

                            $otp_code = random_int(10000, 99999);

                            Log::alert('IndexController-> Email and mobile verification otp code - ' . $otp_code . ' send to the ' . $request->mobile_no . ' and ' . $request->email . ' with NIC ' . $request->nic);

                            $otpCookie = Cookie::queue('otp_code', $otp_code, 10);
                            session()->get('application_verification_id');
                            session()->forget('application_verification_id');
                            Session::put('application_verification_id', $data->id);

                            $emailData = [
                                //'mobile' =>  $request->mobile_no,
                                'otp' => $otp_code,
                                'date' => Carbon::now()
                            ];

                            $mail = new UserVerificationMail($emailData);
                            //sending email
                            FacadesMail::to($request->email, 'USJ HRM SYSTEM')->send($mail);

                            Log::notice('IndexController->Email and mobile verification code for  - ' . $request->email . ' sending Successfully');

                            return redirect()->route('otp.screen');

                            Log::info('IndexController-> Email and mobile verification ended');
                        } else {

                            $notification = array(
                                'message' => 'You have already started the application process for this vacancy. Please use the “Edit & Submit” option for modifications.',
                                'alert-type' => 'error'
                            );

                            return redirect()->route('home')->with($notification);
                        }
                        /********************************************************** */
                    } else {

                        $notification = array(
                            'message' => 'Your age is not sufficient to apply for this vacancy',
                            'alert-type' => 'error'
                        );

                        return redirect()->route('home')->with($notification);
                    }
                } else {

                    $notification = array(
                        'message' => 'Invalid NIC Number',
                        'alert-type' => 'error'
                    );

                    return redirect()->back()->with($notification);
                }
            } else {

                //$isLeapYear = "Not a Leap Year";
                if ($part02 >= 1 && $part02 <= 366) {

                    if ($part02 >= 1 && $part02 <= 59) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 1);
                    } else if ($part02 == 60) {

                        $notification = array(
                            'message' => 'Invalid NIC Number',
                            'alert-type' => 'error'
                        );

                        return redirect()->back()->with($notification);
                    } else if ($part02 >= 61 && $part02 <= 366) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 2);
                    }


                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    //dd($dob);

                    $before = $dateClosed->subYears($vacancy->min_age)->format('Y-m-d');
                    $after = $dateClosed->subYears($vacancy->max_age - $vacancy->min_age)->format('Y-m-d');

                    $isWithinRange = Carbon::createFromFormat('Y-m-d', $dob)->between($before, $after);

                    if ($isWithinRange) {
                        /***************************************************************** */
                        $verifiedUser = DB::table('applications')
                            ->where('vacancy_id', session()->get('vacancy_id'))
                            ->where('nic', $request->nic)
                            //->orwhere('new_nic',$request->nic)
                            ->count();

                        if ($verifiedUser != 1) {

                            $data = new ApplicantVerification();
                            $data->vacancy_id = session()->get('vacancy_id');
                            $data->nic = strtoupper($request->nic);
                            $data->mobile_no = $request->mobile_no;
                            $data->email = $request->email;

                            $maxnumber = DB::table('applicant_verifications')
                                ->where('vacancy_id', session()->get('vacancy_id'))
                                ->where('nic', $request->nic)
                                ->select(DB::raw('MAX(attempt_count) as value'))
                                ->get();

                            $maxValue = json_decode($maxnumber, true);
                            $sortnumber = $maxValue[0]["value"] + 1;
                            $data->attempt_count = $sortnumber;
                            $data->created_at = Carbon::now();
                            $data->save();

                            $otp_code = random_int(10000, 99999);

                            Log::alert('IndexController-> Email and mobile verification otp code - ' . $otp_code . ' send to the ' . $request->mobile_no . ' and ' . $request->email . ' with NIC ' . $request->nic);

                            $otpCookie = Cookie::queue('otp_code', $otp_code, 10);
                            session()->get('application_verification_id');
                            session()->forget('application_verification_id');
                            Session::put('application_verification_id', $data->id);

                            $emailData = [
                                //'mobile' =>  $request->mobile_no,
                                'otp' => $otp_code,
                                'date' => Carbon::now()
                            ];

                            $mail = new UserVerificationMail($emailData);
                            //sending email
                            FacadesMail::to($request->email, 'USJ HRM SYSTEM')->send($mail);

                            Log::notice('IndexController->Email and mobile verification code for  - ' . $request->email . ' sending Successfully');

                            return redirect()->route('otp.screen');

                            Log::info('IndexController-> Email and mobile verification ended');
                        } else {

                            $notification = array(
                                'message' => 'You have already started the application process for this vacancy. Please use the “Edit & Submit” option for modifications.',
                                'alert-type' => 'error'
                            );

                            return redirect()->route('home')->with($notification);
                        }
                        /********************************************************** */
                    } else {

                        $notification = array(
                            'message' => 'Your age is not sufficient to apply for this vacancy',
                            'alert-type' => 'error'
                        );

                        return redirect()->route('home')->with($notification);
                    }
                } else if ($part02 >= 501 && $part02 <= 866) {

                    if ($part02 >= 501 && $part02 <= 559) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 501);
                    } else if ($part02 == 560) {

                        $notification = array(
                            'message' => 'Invalid NIC Number',
                            'alert-type' => 'error'
                        );

                        return redirect()->back()->with($notification);
                    } else if ($part02 >= 561 && $part02 <= 866) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 502);
                    }


                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    //dd($dob);

                    $before = $dateClosed->subYears($vacancy->min_age)->format('Y-m-d');
                    $after = $dateClosed->subYears($vacancy->max_age - $vacancy->min_age)->format('Y-m-d');

                    $isWithinRange = Carbon::createFromFormat('Y-m-d', $dob)->between($before, $after);

                    if ($isWithinRange) {

                        /***************************************************************** */
                        $verifiedUser = DB::table('applications')
                            ->where('vacancy_id', session()->get('vacancy_id'))
                            ->where('nic', $request->nic)
                            //->orwhere('new_nic',$request->nic)
                            ->count();

                        if ($verifiedUser != 1) {

                            $data = new ApplicantVerification();
                            $data->vacancy_id = session()->get('vacancy_id');
                            $data->nic = strtoupper($request->nic);
                            $data->mobile_no = $request->mobile_no;
                            $data->email = $request->email;

                            $maxnumber = DB::table('applicant_verifications')
                                ->where('vacancy_id', session()->get('vacancy_id'))
                                ->where('nic', $request->nic)
                                ->select(DB::raw('MAX(attempt_count) as value'))
                                ->get();

                            $maxValue = json_decode($maxnumber, true);
                            $sortnumber = $maxValue[0]["value"] + 1;
                            $data->attempt_count = $sortnumber;
                            $data->created_at = Carbon::now();
                            $data->save();

                            $otp_code = random_int(10000, 99999);

                            Log::alert('IndexController-> Email and mobile verification otp code - ' . $otp_code . ' send to the ' . $request->mobile_no . ' and ' . $request->email . ' with NIC ' . $request->nic);

                            $otpCookie = Cookie::queue('otp_code', $otp_code, 10);
                            session()->get('application_verification_id');
                            session()->forget('application_verification_id');
                            Session::put('application_verification_id', $data->id);

                            $emailData = [
                                //'mobile' =>  $request->mobile_no,
                                'otp' => $otp_code,
                                'date' => Carbon::now()
                            ];

                            $mail = new UserVerificationMail($emailData);
                            //sending email
                            FacadesMail::to($request->email, 'USJ HRM SYSTEM')->send($mail);

                            Log::notice('IndexController->Email and mobile verification code for  - ' . $request->email . ' sending Successfully');

                            return redirect()->route('otp.screen');

                            Log::info('IndexController-> Email and mobile verification ended');
                        } else {

                            $notification = array(
                                'message' => 'You have already started the application process for this vacancy. Please use the “Edit & Submit” option for modifications.',
                                'alert-type' => 'error'
                            );

                            return redirect()->route('home')->with($notification);
                        }
                        /********************************************************** */
                    } else {

                        $notification = array(
                            'message' => 'Your age is not sufficient to apply for this vacancy',
                            'alert-type' => 'error'
                        );

                        return redirect()->route('home')->with($notification);
                    }
                } else {

                    $notification = array(
                        'message' => 'Invalid NIC Number',
                        'alert-type' => 'error'
                    );

                    return redirect()->back()->with($notification);
                }
            }



            //$part03 = substr($request->nic, 5, 3); //Serial number

            //$part04 = substr($request->nic, 8, 1); //Check digit

            //$part05 = substr($request->nic, 9, 1); //Special letter

            /******************************************************************************************** */
        } elseif (mb_strlen($request->nic) == 12) {

            $fullYear = substr($request->nic, 0, 4); //Birth by year

            $part02 = substr($request->nic, 4, 3); //Birth day of the year

            //$part03 = substr($request->nic, 7, 4); //Serial number

            //$part04 = substr($request->nic, 11, 1); //Check digit

            if ((($fullYear % 4 == 0) && ($fullYear % 100 != 0)) || ($fullYear % 400 == 0)) {

                //$isLeapYear = "Leap Year";
                if ($part02 >= 1 && $part02 <= 366) {

                    $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 1);
                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    //dd($dob);

                    $before = $dateClosed->subYears($vacancy->min_age)->format('Y-m-d');
                    $after = $dateClosed->subYears($vacancy->max_age - $vacancy->min_age)->format('Y-m-d');

                    $isWithinRange = Carbon::createFromFormat('Y-m-d', $dob)->between($before, $after);

                    if ($isWithinRange) {
                        /***************************************************************** */
                        $verifiedUser = DB::table('applications')
                            ->where('vacancy_id', session()->get('vacancy_id'))
                            //->where('nic',$request->nic)
                            ->where('new_nic', $request->nic)
                            ->count();

                        if ($verifiedUser != 1) {

                            $data = new ApplicantVerification();
                            $data->vacancy_id = session()->get('vacancy_id');
                            $data->nic = $request->nic;
                            $data->mobile_no = $request->mobile_no;
                            $data->email = $request->email;

                            $maxnumber = DB::table('applicant_verifications')
                                ->where('vacancy_id', session()->get('vacancy_id'))
                                ->where('nic', $request->nic)
                                ->select(DB::raw('MAX(attempt_count) as value'))
                                ->get();

                            $maxValue = json_decode($maxnumber, true);
                            $sortnumber = $maxValue[0]["value"] + 1;
                            $data->attempt_count = $sortnumber;
                            $data->created_at = Carbon::now();
                            $data->save();

                            $otp_code = random_int(10000, 99999);

                            Log::alert('IndexController-> Email and mobile verification otp code - ' . $otp_code . ' send to the ' . $request->mobile_no . ' and ' . $request->email . ' with NIC ' . $request->nic);

                            $otpCookie = Cookie::queue('otp_code', $otp_code, 10);
                            session()->get('application_verification_id');
                            session()->forget('application_verification_id');
                            Session::put('application_verification_id', $data->id);

                            $emailData = [
                                //'mobile' =>  $request->mobile_no,
                                'otp' => $otp_code,
                                'date' => Carbon::now()
                            ];

                            $mail = new UserVerificationMail($emailData);
                            //sending email
                            FacadesMail::to($request->email, 'USJ HRM SYSTEM')->send($mail);

                            Log::notice('IndexController->Email and mobile verification code for  - ' . $request->email . ' sending Successfully');

                            return redirect()->route('otp.screen');

                            Log::info('IndexController-> Email and mobile verification ended');
                        } else {

                            $notification = array(
                                'message' => 'You have already started the application process for this vacancy. Please use the “Edit & Submit” option for modifications.',
                                'alert-type' => 'error'
                            );

                            return redirect()->route('home')->with($notification);
                        }
                        /********************************************************** */
                    } else {

                        $notification = array(
                            'message' => 'Your age is not sufficient to apply for this vacancy',
                            'alert-type' => 'error'
                        );

                        return redirect()->route('home')->with($notification);
                    }
                } else if ($part02 >= 501 && $part02 <= 866) {

                    $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 501);
                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    $before = $dateClosed->subYears($vacancy->min_age)->format('Y-m-d');
                    $after = $dateClosed->subYears($vacancy->max_age - $vacancy->min_age)->format('Y-m-d');

                    $isWithinRange = Carbon::createFromFormat('Y-m-d', $dob)->between($before, $after);

                    if ($isWithinRange) {
                        /***************************************************************** */
                        $verifiedUser = DB::table('applications')
                            ->where('vacancy_id', session()->get('vacancy_id'))
                            //->where('nic',$request->nic)
                            ->where('new_nic', $request->nic)
                            ->count();

                        if ($verifiedUser != 1) {

                            $data = new ApplicantVerification();
                            $data->vacancy_id = session()->get('vacancy_id');
                            $data->nic = $request->nic;
                            $data->mobile_no = $request->mobile_no;
                            $data->email = $request->email;

                            $maxnumber = DB::table('applicant_verifications')
                                ->where('vacancy_id', session()->get('vacancy_id'))
                                ->where('nic', $request->nic)
                                ->select(DB::raw('MAX(attempt_count) as value'))
                                ->get();

                            $maxValue = json_decode($maxnumber, true);
                            $sortnumber = $maxValue[0]["value"] + 1;
                            $data->attempt_count = $sortnumber;
                            $data->created_at = Carbon::now();
                            $data->save();

                            $otp_code = random_int(10000, 99999);

                            Log::alert('IndexController-> Email and mobile verification otp code - ' . $otp_code . ' send to the ' . $request->mobile_no . ' and ' . $request->email . ' with NIC ' . $request->nic);

                            $otpCookie = Cookie::queue('otp_code', $otp_code, 10);
                            session()->get('application_verification_id');
                            session()->forget('application_verification_id');
                            Session::put('application_verification_id', $data->id);

                            $emailData = [
                                //'mobile' =>  $request->mobile_no,
                                'otp' => $otp_code,
                                'date' => Carbon::now()
                            ];

                            $mail = new UserVerificationMail($emailData);
                            //sending email
                            FacadesMail::to($request->email, 'USJ HRM SYSTEM')->send($mail);

                            Log::notice('IndexController->Email and mobile verification code for  - ' . $request->email . ' sending Successfully');

                            return redirect()->route('otp.screen');

                            Log::info('IndexController-> Email and mobile verification ended');
                        } else {

                            $notification = array(
                                'message' => 'You have already started the application process for this vacancy. Please use the “Edit & Submit” option for modifications.',
                                'alert-type' => 'error'
                            );

                            return redirect()->route('home')->with($notification);
                        }
                        /********************************************************** */
                    } else {

                        $notification = array(
                            'message' => 'Your age is not sufficient to apply for this vacancy',
                            'alert-type' => 'error'
                        );

                        return redirect()->route('home')->with($notification);
                    }
                } else {

                    $notification = array(
                        'message' => 'Invalid NIC Number',
                        'alert-type' => 'error'
                    );

                    return redirect()->back()->with($notification);
                }
            } else {

                //$isLeapYear = "Not a Leap Year";
                if ($part02 >= 1 && $part02 <= 366) {

                    if ($part02 >= 1 && $part02 <= 59) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 1);
                    } else if ($part02 == 60) {

                        $notification = array(
                            'message' => 'Invalid NIC Number',
                            'alert-type' => 'error'
                        );

                        return redirect()->back()->with($notification);
                    } else if ($part02 >= 61 && $part02 <= 366) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 2);
                    }


                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    //dd($dob);

                    $before = $dateClosed->subYears($vacancy->min_age)->format('Y-m-d');
                    $after = $dateClosed->subYears($vacancy->max_age - $vacancy->min_age)->format('Y-m-d');

                    $isWithinRange = Carbon::createFromFormat('Y-m-d', $dob)->between($before, $after);

                    if ($isWithinRange) {
                        /***************************************************************** */
                        $verifiedUser = DB::table('applications')
                            ->where('vacancy_id', session()->get('vacancy_id'))
                            //->where('nic',$request->nic)
                            ->where('new_nic', $request->nic)
                            ->count();

                        if ($verifiedUser != 1) {

                            $data = new ApplicantVerification();
                            $data->vacancy_id = session()->get('vacancy_id');
                            $data->nic = $request->nic;
                            $data->mobile_no = $request->mobile_no;
                            $data->email = $request->email;

                            $maxnumber = DB::table('applicant_verifications')
                                ->where('vacancy_id', session()->get('vacancy_id'))
                                ->where('nic', $request->nic)
                                ->select(DB::raw('MAX(attempt_count) as value'))
                                ->get();

                            $maxValue = json_decode($maxnumber, true);
                            $sortnumber = $maxValue[0]["value"] + 1;
                            $data->attempt_count = $sortnumber;
                            $data->created_at = Carbon::now();
                            $data->save();

                            $otp_code = random_int(10000, 99999);

                            Log::alert('IndexController-> Email and mobile verification otp code - ' . $otp_code . ' send to the ' . $request->mobile_no . ' and ' . $request->email . ' with NIC ' . $request->nic);

                            $otpCookie = Cookie::queue('otp_code', $otp_code, 10);
                            session()->get('application_verification_id');
                            session()->forget('application_verification_id');
                            Session::put('application_verification_id', $data->id);

                            $emailData = [
                                //'mobile' =>  $request->mobile_no,
                                'otp' => $otp_code,
                                'date' => Carbon::now()
                            ];

                            $mail = new UserVerificationMail($emailData);
                            //sending email
                            FacadesMail::to($request->email, 'USJ HRM SYSTEM')->send($mail);

                            Log::notice('IndexController->Email and mobile verification code for  - ' . $request->email . ' sending Successfully');

                            return redirect()->route('otp.screen');

                            Log::info('IndexController-> Email and mobile verification ended');
                        } else {

                            $notification = array(
                                'message' => 'You have already started the application process for this vacancy. Please use the “Edit & Submit” option for modifications.',
                                'alert-type' => 'error'
                            );

                            return redirect()->route('home')->with($notification);
                        }
                        /********************************************************** */
                    } else {

                        $notification = array(
                            'message' => 'Your age is not sufficient to apply for this vacancy',
                            'alert-type' => 'error'
                        );

                        return redirect()->route('home')->with($notification);
                    }
                } else if ($part02 >= 501 && $part02 <= 866) {

                    if ($part02 >= 501 && $part02 <= 559) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 501);
                    } else if ($part02 == 560) {

                        $notification = array(
                            'message' => 'Invalid NIC Number',
                            'alert-type' => 'error'
                        );

                        return redirect()->back()->with($notification);
                    } else if ($part02 >= 561 && $part02 <= 866) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 502);
                    }


                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    //dd($dob);

                    $before = $dateClosed->subYears($vacancy->min_age)->format('Y-m-d');
                    $after = $dateClosed->subYears($vacancy->max_age - $vacancy->min_age)->format('Y-m-d');

                    $isWithinRange = Carbon::createFromFormat('Y-m-d', $dob)->between($before, $after);

                    if ($isWithinRange) {

                        /***************************************************************** */
                        $verifiedUser = DB::table('applications')
                            ->where('vacancy_id', session()->get('vacancy_id'))
                            //->where('nic',$request->nic)
                            ->where('new_nic', $request->nic)
                            ->count();

                        if ($verifiedUser != 1) {

                            $data = new ApplicantVerification();
                            $data->vacancy_id = session()->get('vacancy_id');
                            $data->nic = $request->nic;
                            $data->mobile_no = $request->mobile_no;
                            $data->email = $request->email;

                            $maxnumber = DB::table('applicant_verifications')
                                ->where('vacancy_id', session()->get('vacancy_id'))
                                ->where('nic', $request->nic)
                                ->select(DB::raw('MAX(attempt_count) as value'))
                                ->get();

                            $maxValue = json_decode($maxnumber, true);
                            $sortnumber = $maxValue[0]["value"] + 1;
                            $data->attempt_count = $sortnumber;
                            $data->created_at = Carbon::now();
                            $data->save();

                            $otp_code = random_int(10000, 99999);

                            Log::alert('IndexController-> Email and mobile verification otp code - ' . $otp_code . ' send to the ' . $request->mobile_no . ' and ' . $request->email . ' with NIC ' . $request->nic);

                            $otpCookie = Cookie::queue('otp_code', $otp_code, 10);
                            session()->get('application_verification_id');
                            session()->forget('application_verification_id');
                            Session::put('application_verification_id', $data->id);

                            $emailData = [
                                //'mobile' =>  $request->mobile_no,
                                'otp' => $otp_code,
                                'date' => Carbon::now()
                            ];

                            $mail = new UserVerificationMail($emailData);
                            //sending email
                            FacadesMail::to($request->email, 'USJ HRM SYSTEM')->send($mail);

                            Log::notice('IndexController->Email and mobile verification code for  - ' . $request->email . ' sending Successfully');

                            return redirect()->route('otp.screen');

                            Log::info('IndexController-> Email and mobile verification ended');
                        } else {

                            $notification = array(
                                'message' => 'You have already started the application process for this vacancy. Please use the “Edit & Submit” option for modifications.',
                                'alert-type' => 'error'
                            );

                            return redirect()->route('home')->with($notification);
                        }
                        /********************************************************** */
                    } else {

                        $notification = array(
                            'message' => 'Your age is not sufficient to apply for this vacancy',
                            'alert-type' => 'error'
                        );

                        return redirect()->route('home')->with($notification);
                    }
                } else {

                    $notification = array(
                        'message' => 'Invalid NIC Number',
                        'alert-type' => 'error'
                    );

                    return redirect()->back()->with($notification);
                }
            }
        } else {

            $notification = array(
                'message' => 'Invalid NIC Number',
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        }
    }

    public function otpScreen()
    {

        Log::info('IndexController-> Applicant OTP Screen get started');

        //$otp = request()->cookie('otp_code');
        $userDetial = ApplicantVerification::find(session()->get('application_verification_id'));
        return view('frontend.mobile_verification', compact('userDetial'));
        Log::notice('IndexController-> Applicate OTP Screen for user verification id - ' . session()->get('application_verification_id'));

        Log::info('IndexController-> Applicamt OTP Screen ended');
    }

    public function otpResend(Request $request)
    {

        Log::info('IndexController-> Applicant OTP resend get started');

        $data = new ApplicantVerification();
        $data->vacancy_id = session()->get('vacancy_id');
        $data->nic = strtoupper($request->nic);
        $data->mobile_no = $request->mobile_no;
        $data->email = $request->email;

        $maxnumber = DB::table('applicant_verifications')
            ->where('vacancy_id', session()->get('vacancy_id'))
            ->where('nic', $request->nic)
            ->select(DB::raw('MAX(attempt_count) as value'))
            ->get();

        $maxValue = json_decode($maxnumber, true);

        $sortnumber = $maxValue[0]["value"] + 1;
        $data->attempt_count = $sortnumber;
        $data->created_at = Carbon::now();
        $data->save();

        $otp_code = random_int(10000, 99999);

        Log::alert('IndexController-> Email and mobile verification otp code resend - ' . $otp_code . ' send to the ' . $request->mobile_no . ' and ' . $request->email . ' with NIC ' . $request->nic);

        //delete already exesit cookies
        Cookie::queue(Cookie::forget('otp_code'));

        //setup new cookies
        $otpCookie = Cookie::queue('otp_code', $otp_code, 10);
        session()->get('application_verification_id');
        session()->forget('application_verification_id');
        Session::put('application_verification_id', $data->id);

        //mail data
        $emailData = [
            //'mobile' =>  $request->mobile_no,
            'otp' => $otp_code,
            'date' => Carbon::now()
        ];

        $mail = new UserVerificationMail($emailData);
        //sending email
        FacadesMail::to($request->email, 'USJ HRM SYSTEM')->send($mail);

        Log::notice('IndexController->Email and mobile verification code for  - ' . $request->email . ' sending Successfully');

        return redirect()->route('otp.screen');

        Log::info('IndexController-> Applicant OTP resend ended');
    }

    public function otpConfirmation(Request $request)
    {

        Log::info('IndexController-> Applicant OTP confimation get started');

        $otp = request()->cookie('otp_code');
        $user_input = $request->first . $request->second . $request->third . $request->fourth . $request->fifth;

        if (!session()->get('application_verification_id')) {

            $notification = array(
                'message' => 'Your application session has been expired',
                'alert-type' => 'error'
            );

            return redirect()->route('home')->with($notification);
        }

        if ($otp == $user_input && $user_input != '') {

            $data = ApplicantVerification::find(session()->get('application_verification_id'));
            $data->status_id = 1;
            $data->save();

            session()->get('application_verification_id');
            session()->forget('application_verification_id');

            $verifiedUserEmail = $data->email;
            $verifiedUserMobile = $data->mobile_no;
            $verifiedUsernic = $data->nic;
            $verifiedUserVacancyId = $data->vacancy_id;



            if (mb_strlen($verifiedUsernic) == 10) {

                $part01 = substr($verifiedUsernic, 0, 2); //Birth by year

                $part02 = substr($verifiedUsernic, 2, 3); //Birth day of the year

                $part03 = substr($verifiedUsernic, 5, 3); //Serial number

                $part04 = substr($verifiedUsernic, 8, 1); //Check digit

                $part05 = substr($verifiedUsernic, 9, 1); //Special letter

                $currentYear = Carbon::now()->year % 100;

                if ($part01 < $currentYear) {

                    $fullYear = '20' . $part01;
                } else {

                    $fullYear = '19' . $part01;
                }

                if ((($fullYear % 4 == 0) && ($fullYear % 100 != 0)) || ($fullYear % 400 == 0)) {

                    //$isLeapYear = "Leap Year";
                    if ($part02 >= 1 && $part02 <= 366) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 1);
                        $month = $date->month;
                        $day = $date->day;

                        $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                        $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                        $formattedDate = "$fullYear-$month-$day";
                        $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');
                        $gender = 1;
                        $oldnic = $verifiedUsernic;
                        $newnic = $fullYear . $part02 . str_pad($part03, 4, '0', STR_PAD_LEFT) . $part04;
                        $activenic = 1;
                    } else if ($part02 >= 501 && $part02 <= 866) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 501);
                        $month = $date->month;
                        $day = $date->day;

                        $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                        $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                        $formattedDate = "$fullYear-$month-$day";
                        $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');
                        $gender = 2;
                        $oldnic = $verifiedUsernic;
                        $newnic = $fullYear . $part02 . str_pad($part03, 4, '0', STR_PAD_LEFT) . $part04;
                        $activenic = 1;
                    } else {

                        $notification = array(
                            'message' => 'Invalid NIC Number',
                            'alert-type' => 'error'
                        );

                        return redirect()->back()->with($notification);
                    }
                } else {

                    //$isLeapYear = "Not a Leap Year";
                    if ($part02 >= 1 && $part02 <= 366) {

                        if ($part02 >= 1 && $part02 <= 59) {

                            $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 1);
                        } else if ($part02 == 60) {

                            $notification = array(
                                'message' => 'Invalid NIC Number',
                                'alert-type' => 'error'
                            );

                            return redirect()->back()->with($notification);
                        } else if ($part02 >= 61 && $part02 <= 366) {

                            $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 2);
                        }


                        $month = $date->month;
                        $day = $date->day;

                        $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                        $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                        $formattedDate = "$fullYear-$month-$day";
                        $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');
                        $gender = 1;
                        $oldnic = $verifiedUsernic;
                        $newnic = $fullYear . $part02 . str_pad($part03, 4, '0', STR_PAD_LEFT) . $part04;
                        $activenic = 1;
                    } else if ($part02 >= 501 && $part02 <= 866) {

                        if ($part02 >= 501 && $part02 <= 559) {

                            $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 501);
                        } else if ($part02 == 560) {

                            $notification = array(
                                'message' => 'Invalid NIC Number',
                                'alert-type' => 'error'
                            );

                            return redirect()->back()->with($notification);
                        } else if ($part02 >= 561 && $part02 <= 866) {

                            $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 502);
                        }


                        $month = $date->month;
                        $day = $date->day;

                        $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                        $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                        $formattedDate = "$fullYear-$month-$day";
                        $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');
                        $gender = 2;
                        $oldnic = $verifiedUsernic;
                        $newnic = $fullYear . $part02 . str_pad($part03, 4, '0', STR_PAD_LEFT) . $part04;
                        $activenic = 1;
                    } else {

                        $notification = array(
                            'message' => 'Invalid NIC Number',
                            'alert-type' => 'error'
                        );

                        return redirect()->back()->with($notification);
                    }
                }


                /******************************************************************************************** */
            } elseif (mb_strlen($verifiedUsernic) == 12) {

                $fullYear = substr($verifiedUsernic, 0, 4); //Birth by year

                $part02 = substr($verifiedUsernic, 4, 3); //Birth day of the year

                $part03 = substr($verifiedUsernic, 7, 4); //Serial number

                $part04 = substr($verifiedUsernic, 11, 1); //Check digit


                if ((($fullYear % 4 == 0) && ($fullYear % 100 != 0)) || ($fullYear % 400 == 0)) {

                    //$isLeapYear = "Leap Year";
                    if ($part02 >= 1 && $part02 <= 366) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 1);
                        $month = $date->month;
                        $day = $date->day;

                        $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                        $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                        $formattedDate = "$fullYear-$month-$day";
                        $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');
                        $gender = 1;
                        $oldnic = substr($fullYear, 2, 2) . $part02 . substr($part03, 1, 3) . $part04 . 'V';
                        $newnic = $verifiedUsernic;
                        $activenic = 2;
                    } else if ($part02 >= 501 && $part02 <= 866) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 501);
                        $month = $date->month;
                        $day = $date->day;

                        $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                        $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                        $formattedDate = "$fullYear-$month-$day";
                        $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');
                        $gender = 2;
                        $oldnic = substr($fullYear, 2, 2) . $part02 . substr($part03, 1, 3) . $part04 . 'V';
                        $newnic = $verifiedUsernic;
                        $activenic = 2;
                    } else {

                        $notification = array(
                            'message' => 'Invalid NIC Number',
                            'alert-type' => 'error'
                        );

                        return redirect()->back()->with($notification);
                    }
                } else {

                    //$isLeapYear = "Not a Leap Year";
                    if ($part02 >= 1 && $part02 <= 366) {

                        if ($part02 >= 1 && $part02 <= 59) {

                            $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 1);
                        } else if ($part02 == 60) {

                            $notification = array(
                                'message' => 'Invalid NIC Number',
                                'alert-type' => 'error'
                            );

                            return redirect()->back()->with($notification);
                        } else if ($part02 >= 61 && $part02 <= 366) {

                            $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 2);
                        }


                        $month = $date->month;
                        $day = $date->day;

                        $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                        $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                        $formattedDate = "$fullYear-$month-$day";
                        $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');
                        $gender = 1;
                        $oldnic = substr($fullYear, 2, 2) . $part02 . substr($part03, 1, 3) . $part04 . 'V';
                        $newnic = $verifiedUsernic;
                        $activenic = 2;
                    } else if ($part02 >= 501 && $part02 <= 866) {

                        if ($part02 >= 501 && $part02 <= 559) {

                            $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 501);
                        } else if ($part02 == 560) {

                            $notification = array(
                                'message' => 'Invalid NIC Number',
                                'alert-type' => 'error'
                            );

                            return redirect()->back()->with($notification);
                        } else if ($part02 >= 561 && $part02 <= 866) {

                            $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 502);
                        }


                        $month = $date->month;
                        $day = $date->day;

                        $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                        $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                        $formattedDate = "$fullYear-$month-$day";
                        $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');
                        $gender = 2;
                        $oldnic = substr($fullYear, 2, 2) . $part02 . substr($part03, 1, 3) . $part04 . 'V';
                        $newnic = $verifiedUsernic;
                        $activenic = 2;
                    } else {

                        $notification = array(
                            'message' => 'Invalid NIC Number',
                            'alert-type' => 'error'
                        );

                        return redirect()->back()->with($notification);
                    }
                }
            } else {

                $notification = array(
                    'message' => 'Invalid NIC Number',
                    'alert-type' => 'error'
                );

                return redirect()->back()->with($notification);
            }

            /*****************************************/
            Log::info('IndexController-> Applicant original application creation get started');

            $data = new Application();

            $maxnumber = DB::table('applications')
                ->where('vacancy_id', session()->get('vacancy_id'))
                ->select(DB::raw('MAX(application_id) as value'))
                ->get();
            $maxValue = json_decode($maxnumber, true);

            $sortnumber = $maxValue[0]["value"] + 1;

            $data->application_id = $sortnumber;
            $data->vacancy_id = $verifiedUserVacancyId;
            $data->reference_no = str_pad($verifiedUserVacancyId, 3, '0', STR_PAD_LEFT) . '/' . str_pad($sortnumber, 4, '0', STR_PAD_LEFT);
            $data->nic = $oldnic;
            $data->new_nic = $newnic;
            $data->active_nic = $activenic;
            $data->mobile_no = $verifiedUserMobile;
            $data->email = $verifiedUserEmail;
            $data->gender_id = $gender;
            $data->date_of_birth = $dob;
            $data->application_decision_id = 33;
            $data->save();

            //create appliction referance number session
            session()->get('reference_no');
            session()->forget('reference_no');
            Session::put('reference_no',  str_pad($verifiedUserVacancyId, 3, '0', STR_PAD_LEFT) . '/' . str_pad($sortnumber, 4, '0', STR_PAD_LEFT));

            Log::notice('IndexController->User Get application data Referance number  - ' . str_pad($verifiedUserVacancyId, 3, '0', STR_PAD_LEFT) . '/' . str_pad($sortnumber, 4, '0', STR_PAD_LEFT) . ' Successfully');

            Log::info('IndexController-> Applicant original application creation get ended');

            return redirect()->route('vacancy.application');

            Log::info('IndexController-> Applicant OTP confimation successfully');
        } elseif (request()->cookie('otp_code') == null) {

            $notification = array(
                'message' => 'The entered OTP has timed out. Please try resending the OTP',
                'alert-type' => 'error'
            );

            return redirect()->route('otp.screen')->with($notification);
        } else {

            $notification = array(
                'message' => 'The entered OTP does not match',
                'alert-type' => 'error'
            );

            return redirect()->route('otp.screen')->with($notification);
        }

        Log::info('IndexController-> Applicant OTP confimation get ended');
    }


    /********************************Application Edit Function**************************************** */

    public function applicantEditLogin()
    {

        Log::info('IndexController-> Applicant Edit Login get started');

        return view('frontend.user_edit_login');

        Log::info('IndexController-> Applicamt Edit Login ended');
    }

    public function editMobileVerification(Request $request)
    {

        Log::info('IndexController-> Edit Email and mobile verification get started');

        $validatedData = $request->validate([
            'nic' => ['required', 'regex:/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/m'],
            //'mobile_no' => 'required|max:12',
            'email' => 'required|email:rfc,dns',
        ], [
            'nic.required' => 'please enter valid NIC number',
            //'mobile_no.required' => 'please enter valid mobile number',
            'email.required' => 'please enter valid email',
        ]);

        $verifiedUser = DB::table('applications')
            ->where(function ($query) use ($request) {
                $query->where('nic', strtoupper($request->nic))
                    ->orWhere('new_nic', $request->nic);
            })
            //->where('email', $request->email)
            ->count();

        if ($verifiedUser > 0) {

            $emailCkeckCount = DB::table('applications')
                ->where(function ($query) use ($request) {
                    $query->where('nic', strtoupper($request->nic))
                        ->orWhere('new_nic', $request->nic);
                })
                ->where('email', $request->email)->count();

            if ($emailCkeckCount == 0) {

                $notification = array(
                    'message' => 'Could not find any records associated with your email.please enter your vancancy registered email with your nic',
                    'alert-type' => 'error'
                );

                return redirect()->route('applicant.edit.login')->with($notification);
            } else {

                $otp_code = random_int(10000, 99999);

                Log::alert('IndexController-> Email and mobile verification otp code - ' . $otp_code . ' send to the ' . $request->email . ' with NIC ' . $request->nic);
                //create otp cookies
                $otpCookie = Cookie::queue('otp_code', $otp_code, 10);

                //set nic session
                session()->get('nic');
                session()->forget('nic');
                Session::put('nic', $request->nic);

                //set email session
                session()->get('email');
                session()->forget('email');
                Session::put('email', $request->email);

                $emailData = [
                    //'mobile' =>  $request->mobile_no,
                    'otp' => $otp_code,
                    'date' => Carbon::now()
                ];

                $mail = new UserVerificationMail($emailData);
                //sending email
                FacadesMail::to($request->email, 'USJ HRM SYSTEM')->send($mail);

                Log::notice('IndexController->Edit Email and mobile verification code for  - ' . $request->email . ' sending Successfully');

                return redirect()->route('edit.otp.screen');

                Log::info('IndexController->Edit Email and mobile verification ended');
            }
        } else {

            $notification = array(
                'message' => 'You have not applied for a vacancy previously. Please use the “Apply Now” option to start the application process.',
                'alert-type' => 'error'
            );

            return redirect()->route('home')->with($notification);
        }
    }

    public function editOtpScreen()
    {

        Log::info('IndexController-> Application Edit OTP Screen get started');

        return view('frontend.mobile_verification_edit');
        Log::notice('IndexController-> Applicate OTP Screen for user nic id - ' . session()->get('nic'));

        Log::info('IndexController-> Application Edit OTP Screen ended');
    }

    public function editOtpResend(Request $request)
    {

        Log::info('IndexController-> Edit Applicant OTP resend get started');

        $otp_code = random_int(10000, 99999);

        Log::alert('IndexController-> Email and mobile verification otp code resend - ' . $otp_code . ' send to the ' . $request->email . ' with NIC ' . $request->nic);

        //delete already exesit cookies
        Cookie::queue(Cookie::forget('otp_code'));

        //setup new cookies
        $otpCookie = Cookie::queue('otp_code', $otp_code, 10);

        //mail data
        $emailData = [
            //'mobile' =>  $request->mobile_no,
            'otp' => $otp_code,
            'date' => Carbon::now()
        ];

        $mail = new UserVerificationMail($emailData);
        //sending email
        FacadesMail::to(session('email'), 'USJ HRM SYSTEM')->send($mail);

        Log::notice('IndexController->Email and mobile verification code for  - ' . $request->email . ' sending Successfully');

        return redirect()->route('edit.otp.screen');

        Log::info('IndexController-> Applicant OTP resend ended');
    }

    public function editOtpConfirmation(Request $request)
    {

        Log::info('IndexController-> Edit Applicant OTP confimation get started');

        $otp = request()->cookie('otp_code');
        $user_input = $request->first . $request->second . $request->third . $request->fourth . $request->fifth;


        if ($otp == $user_input && $user_input != '') {

            return redirect()->route('enroll.list');
        } elseif (request()->cookie('otp_code') == null) {

            $notification = array(
                'message' => 'The entered OTP has timed out. Please try resending the OTP code',
                'alert-type' => 'error'
            );

            return redirect()->route('edit.otp.screen')->with($notification);
        } else {

            $notification = array(
                'message' => 'Your enter OTP does not match',
                'alert-type' => 'error'
            );

            return redirect()->route('edit.otp.screen')->with($notification);
        }

        Log::info('IndexController-> Applicant OTP confimation get ended');
    }

    public function enrollList()
    {
        $nic = session()->get('nic');
        $vacancyList = Application::join('vacancies', 'applications.vacancy_id', '=', 'vacancies.id')
            ->join('designations', 'vacancies.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vacancies.*', 'categories.display_name', 'applications.application_decision_id', 'applications.reference_no')
            ->where(function ($query) use ($nic) {
                $query->where('nic', strtoupper($nic))
                    ->orWhere('new_nic', $nic);
            })
            ->where('email', '=', session()->get('email'))
            ->where('vacancy_status_id', 1)
            ->orderByDesc('date_closed')
            ->get();

        return view('frontend.vacancy_edit_list', compact('vacancyList'));
    }


    public function applicationEdit(Request $request)
    {


        session()->get('vacancy_id');
        session()->forget('vacancy_id');
        $session = Session::put('vacancy_id', $request->vacancy_id);

        session()->get('reference_no');
        session()->forget('reference_no');
        $session = Session::put('reference_no', $request->reference_no);

        return redirect()->route('vacancy.application');
    }

    public function vacancyApplication()
    {

        Log::info('IndexController-> vacancy application index started');

        $vacancy = Vacancy::find(session()->get('vacancy_id'));

        if (!$vacancy) {

            $notification = array(
                'message' => 'Your application session has been expired',
                'alert-type' => 'error'
            );

            return redirect()->route('home')->with($notification);
        }

        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 11, 15, 16, 39, 40, 41, 42, 43]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $alStreams = $categories->where('category_type_id', '15');
        $degreeTypes = $categories->where('category_type_id', '39');
        $degreeClasses = $categories->where('category_type_id', '40');
        $diplomaTypes = $categories->where('category_type_id', '41');
        $specialQulifications = $categories->where('category_type_id', '42');
        $membershipTypes = $categories->where('category_type_id', '43');
        $membershipActiveStatus = $categories->where('category_type_id', '11');
        if ($vacancy->main_category_id == 44) {

            $educationLevels = $categories->where('category_type_id', '16')->whereIn('id', array(75, 76, 77, 78, 79, 80, 81, 82));
        } elseif ($vacancy->main_category_id == 45) {

            $educationLevels = $categories->where('category_type_id', '16')->whereIn('id', array(73, 74, 75, 76, 77, 78, 79, 80, 81, 82));
        } else {

            $educationLevels = $categories->where('category_type_id', '16');
        }

        $cities = City::all();

        //dd($specialQulifications);

        return view('frontend.application.index', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'alStreams', 'cities', 'degreeTypes', 'degreeClasses', 'educationLevels', 'diplomaTypes', 'specialQulifications', 'membershipTypes', 'membershipActiveStatus'));

        Log::info('IndexController-> Vacancies Application index ended');
    }


    public function vacancyApplicationSubmit(ApplicationRequest $request)
    {

        Log::info('IndexController-> vacancy application submit get started');


        if ($request->has('submit1')) {

            Log::info('IndexController -> submit application started');


            //data store
            $vacancy = Vacancy::find(session()->get('vacancy_id'));
            $data = Application::find(session()->get('reference_no'));
            $data->titel_id = $request->titel_id;
            $data->initials = strtoupper($request->initials);
            $data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
            $data->last_name = ucwords($request->last_name);
            $data->phone_no = $request->phone_no;
            //$data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
            //$data->gender_id = $request->gender_id;
            $data->civil_status_id = $request->civil_status_id;
            //$data->race_id = $request->race_id;
            //$data->religion_id = $request->religion_id;
            $data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
            $data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
            $data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
            $data->permanent_city_id = $request->permanent_city_id;
            $data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
            $data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
            $data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
            $data->postal_city_id = $request->postal_city_id;
            $data->citizenship = $request->citizenship;
            $data->state_of_citizenship_id = $request->state_of_citizenship_id;
            if ($request->state_of_citizenship_id == 25) {
                $data->citizen_registration_no = '';
            } elseif ($request->state_of_citizenship_id == 26) {
                $data->citizen_registration_no = $request->citizen_registration_no;
            }
            $data->emp_highest_edu_level = $request->emp_highest_edu_level;
            /****************************************************** */
            if ($request->hasFile('profile_photo')) {
                $image = $request->file('profile_photo');
                $name_gen = 'Profile Picture' . '.' . $image->getClientOriginalExtension();
                $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/profile';
                if (!File::exists($path)) {
                    File::makeDirectory(storage_path() . $path, 0777, true, true);
                }
                $request->file('profile_photo')->storeAs($path, $name_gen);
                $save_url = $path . '/' . $name_gen;

                $data->profile_photo = $save_url;
            }
            /********************************************************* */
            if ($request->has('terms')) {
                $data->user_agreement_status = $request->terms;
            }

            //$data->profile_photo = $save_url;
            if ($vacancy->main_category_id == 44 || $vacancy->main_category_id == 45) {

                if ($request->bondability != null) {

                    $data->bondability = $request->bondability;
                }

                if ($request->publicSector != null) {

                    $data->is_public_sector = $request->publicSector;
                }
            }

            $data->submition_date = date('Y-m-d-');
            $data->completion_status_id = 2;
            $data->application_decision_id = 34;
            $data->updated_at = Carbon::now();
            $data->save();
            /***************************************************************************************** */
            //non acdemic employee only
            if ($vacancy->main_category_id == 46) {

                $avalableolResultSummary = OrdinaryLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('attempt', '=', 1)->count();

                if ($avalableolResultSummary == 0) {

                    if ($request->hasFile('certificate1')) {

                        $certificate = $request->file('certificate1');
                        $name_gen = 'Ol Result Sheet 01' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/ol';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('certificate1')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    }

                    $olResultSummary = new OrdinaryLevelResultSummary();
                    $olResultSummary->reference_no = session()->get('reference_no');
                    $olResultSummary->index_no = $request->index_no;
                    $olResultSummary->year = $request->year;
                    $olResultSummary->attempt = $request->attempt;
                    if ($request->hasFile('certificate1')) {
                        $olResultSummary->file_path = $save_url;
                    }
                    $olResultSummary->save();


                    if ($request->subject_name != null) {

                        if (count($request->subject_name) > 0) {
                            OrdinaryLevelResult::where('result_slot_id', $olResultSummary->id)->delete();
                        }

                        for ($x = 0; $x < count($request->subject_name); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new OrdinaryLevelResult();
                            $olResult->result_slot_id = $olResultSummary->id;
                            $olResult->subject_name = $request->subject_name[$x];
                            $olResult->grade = strtoupper($request->grade[$x]);
                            $olResult->attempt = $request->attempt; // Save the attempt value
                            $olResult->save();

                            //}
                        }
                    }
                    //ol result first time save
                } else {

                    if ($request->hasFile('certificate1')) {

                        if ($request->old_certificate1 != '') {

                            $oldImage = $request->old_certificate1;
                            unlink(storage_path() . '/app/' . $oldImage);
                        }


                        $certificate = $request->file('certificate1');
                        $name_gen = 'Ol Result Sheet 01' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/ol';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('certificate1')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    } elseif ($request->old_certificate1 == '' && !$request->hasFile('certificate1')) {

                        $save_url = '';
                    } else {

                        $save_url = $request->old_certificate1;
                    }


                    $olResultSummary = OrdinaryLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('attempt', '=', 1)->update([
                        'reference_no' => session()->get('reference_no'),
                        'index_no' => $request->index_no,
                        'year' => $request->year,
                        'attempt' => $request->attempt,
                        'file_path' => $save_url
                    ]);

                    $id = OrdinaryLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('attempt', '=', 1)->get('id');
                    $id = json_decode($id, true);
                    $id = $id[0]["id"];

                    if ($request->subject_name != null) {

                        if (count($request->grade) > 0) {
                            OrdinaryLevelResult::where('result_slot_id', $id)->delete();
                        }

                        for ($x = 0; $x < count($request->subject_name); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new OrdinaryLevelResult();
                            $olResult->result_slot_id = $id;
                            $olResult->subject_name = $request->subject_name[$x];
                            $olResult->grade = strtoupper($request->grade[$x]);
                            $olResult->save();

                            //}
                        }
                    }
                }
                /****ol result save update */

                /****************************************************************************************** */

                $avalableolResultSummary = OrdinaryLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('attempt', '=', 2)->count();

                if ($avalableolResultSummary == 0) {

                    if ($request->hasFile('certificate2')) {

                        $certificate = $request->file('certificate2');
                        $name_gen = 'Ol Result Sheet 02' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/ol2';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('certificate2')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    }

                    $olResultSummary = new OrdinaryLevelResultSummary();
                    $olResultSummary->reference_no = session()->get('reference_no');
                    $olResultSummary->index_no = $request->index_no2;
                    $olResultSummary->year = $request->year2;
                    $olResultSummary->attempt = $request->attempt2;
                    if ($request->hasFile('certificate2')) {
                        $olResultSummary->file_path = $save_url;
                    }
                    $olResultSummary->save();


                    if ($request->subject_name2 != null) {

                        if (count($request->subject_name2) > 0) {
                            OrdinaryLevelResult::where('result_slot_id', $olResultSummary->id)->delete();
                        }

                        for ($x = 0; $x < count($request->subject_name2); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new OrdinaryLevelResult();
                            $olResult->result_slot_id = $olResultSummary->id;
                            $olResult->subject_name = $request->subject_name2[$x];
                            $olResult->grade = strtoupper($request->grade2[$x]);
                            $olResult->attempt = $request->attempt2; // Save the attempt value
                            $olResult->save();

                            //}
                        }
                    }
                } else {

                    if ($request->hasFile('certificate2')) {

                        if ($request->old_certificate2 != '') {

                            $oldImage = $request->old_certificate2;
                            unlink(storage_path() . '/app/' . $oldImage);
                        }


                        $certificate = $request->file('certificate2');
                        $name_gen = 'Ol Result Sheet 02' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/ol2';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('certificate2')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    } elseif ($request->old_certificate2 == '' && !$request->hasFile('certificate2')) {

                        $save_url = '';
                    } else {

                        $save_url = $request->old_certificate2;
                    }


                    $olResultSummary = OrdinaryLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('attempt', '=', 2)->update([
                        'reference_no' => session()->get('reference_no'),
                        'index_no' => $request->index_no2,
                        'year' => $request->year2,
                        'attempt' => $request->attempt2,
                        'file_path' => $save_url
                    ]);

                    $id = OrdinaryLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('attempt', '=', 2)->get('id');
                    $id = json_decode($id, true);
                    $id = $id[0]["id"];

                    if ($request->subject_name2 != null) {

                        if (count($request->grade2) > 0) {
                            OrdinaryLevelResult::where('result_slot_id', $id)->delete();
                        }

                        for ($x = 0; $x < count($request->subject_name2); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new OrdinaryLevelResult();
                            $olResult->result_slot_id = $id;
                            $olResult->subject_name = $request->subject_name2[$x];
                            $olResult->grade = strtoupper($request->grade2[$x]);
                            $olResult->save();

                            //}
                        }
                    }
                }
                /****ol result save update */
            } //ol result save section

            /*********************************************************************************************/
            //al result information save
            if ($vacancy->main_category_id == 46) {

                $avalableolResultSummary = AdvanceLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('level', '=', 1)->count();

                if ($avalableolResultSummary == 0) {

                    if ($request->hasFile('al_certificate1')) {

                        $certificate = $request->file('al_certificate1');
                        $name_gen = 'Al Result Sheet 01' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/al';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('al_certificate1')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    }

                    $alResultSummary = new AdvanceLevelResultSummary();
                    $alResultSummary->reference_no = session()->get('reference_no');
                    $alResultSummary->index_no = $request->al_index_no;
                    $alResultSummary->year = $request->al_year;
                    $alResultSummary->attempt = $request->al_attempt;
                    $alResultSummary->stream = $request->al_stream_id;
                    $alResultSummary->level = $request->al_level;

                    if ($request->hasFile('al_certificate1')) {
                        $alResultSummary->file_path = $save_url;
                    }
                    $alResultSummary->save();


                    if ($request->al_subject_name != null) {

                        if (count($request->al_subject_name) > 0) {
                            AdvanceLevelResult::where('result_slot_id', $alResultSummary->id)->delete();
                        }

                        for ($x = 0; $x < count($request->al_subject_name); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new AdvanceLevelResult();
                            $olResult->result_slot_id = $alResultSummary->id;
                            $olResult->subject_name = ucfirst($request->al_subject_name[$x]);
                            $olResult->grade = strtoupper($request->al_grade[$x]);
                            $olResult->attempt = $request->al_attempt; // Save the attempt value
                            $olResult->save();

                            //}
                        }
                    }
                } else {

                    if ($request->hasFile('al_certificate1')) {

                        if ($request->old_al_certificate1 != '') {

                            $oldImage = $request->old_al_certificate1;
                            unlink(storage_path() . '/app/' . $oldImage);
                        }


                        $certificate = $request->file('al_certificate1');
                        $name_gen = 'AL Result Sheet 01' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/al';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('al_certificate1')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    } elseif ($request->old_al_certificate1 == '' && !$request->hasFile('al_certificate1')) {

                        $save_url = '';
                    } else {

                        $save_url = $request->old_al_certificate1;
                    }


                    $olResultSummary = AdvanceLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('level', '=', 1)->update([
                        'reference_no' => session()->get('reference_no'),
                        'index_no' => $request->al_index_no,
                        'year' => $request->al_year,
                        'attempt' => $request->al_attempt,
                        'stream' => $request->al_stream_id,
                        'file_path' => $save_url
                    ]);

                    $id = AdvanceLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('level', '=', 1)->get('id');
                    $id = json_decode($id, true);
                    $id = $id[0]["id"];

                    if ($request->al_subject_name != null) {

                        if (count($request->al_grade) > 0) {
                            AdvanceLevelResult::where('result_slot_id', $id)->delete();
                        }

                        for ($x = 0; $x < count($request->al_subject_name); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new AdvanceLevelResult();
                            $olResult->result_slot_id = $id;
                            $olResult->subject_name = ucfirst($request->al_subject_name[$x]);
                            $olResult->grade = strtoupper($request->al_grade[$x]);
                            $olResult->attempt = 1; // Set attempt to 1 for first attempt
                            $olResult->save();

                            //}
                        }
                    }
                }
                /****al result save update */

                /********************************************************************************************** */
                $avalableolResultSummary = AdvanceLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('level', '=', 2)->count();

                if ($avalableolResultSummary == 0) {

                    if ($request->hasFile('al_certificate2')) {

                        $certificate = $request->file('al_certificate2');
                        $name_gen = 'Al Result Sheet 02' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/al2';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('al_certificate2')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    }

                    $alResultSummary = new AdvanceLevelResultSummary();
                    $alResultSummary->reference_no = session()->get('reference_no');
                    $alResultSummary->index_no = $request->al_index_no2;
                    $alResultSummary->year = $request->al_year2;
                    $alResultSummary->attempt = $request->al_attempt2;
                    $alResultSummary->stream = $request->al_stream_id2;
                    $alResultSummary->level = $request->al_level2;

                    if ($request->hasFile('al_certificate2')) {
                        $alResultSummary->file_path = $save_url;
                    }
                    $alResultSummary->save();


                    if ($request->al_subject_name2 != null) {

                        if (count($request->al_subject_name2) > 0) {
                            AdvanceLevelResult::where('result_slot_id', $alResultSummary->id)->delete();
                        }

                        for ($x = 0; $x < count($request->al_subject_name2); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new AdvanceLevelResult();
                            $olResult->result_slot_id = $alResultSummary->id;
                            $olResult->subject_name = ucfirst($request->al_subject_name2[$x]);
                            $olResult->grade = strtoupper($request->al_grade2[$x]);
                            $olResult->attempt = $request->al_attempt2; // Save the attempt value
                            $olResult->save();

                            //}
                        }
                    }
                } else {

                    if ($request->hasFile('al_certificate2')) {

                        if ($request->old_al_certificate2 != '') {

                            $oldImage = $request->old_al_certificate2;
                            unlink(storage_path() . '/app/' . $oldImage);
                        }


                        $certificate = $request->file('al_certificate2');
                        $name_gen = 'AL Result Sheet 02' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/al2';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('al_certificate2')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    } elseif ($request->old_al_certificate2 == '' && !$request->hasFile('al_certificate2')) {

                        $save_url = '';
                    } else {

                        $save_url = $request->old_al_certificate2;
                    }


                    $olResultSummary = AdvanceLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('level', '=', 2)->update([
                        'reference_no' => session()->get('reference_no'),
                        'index_no' => $request->al_index_no2,
                        'year' => $request->al_year2,
                        'attempt' => $request->al_attempt2,
                        'stream' => $request->al_stream_id2,
                        'file_path' => $save_url
                    ]);

                    $id = AdvanceLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('level', '=', 2)->get('id');
                    $id = json_decode($id, true);
                    $id = $id[0]["id"];

                    if ($request->al_subject_name2 != null) {

                        if (count($request->al_grade2) > 0) {
                            AdvanceLevelResult::where('result_slot_id', $id)->delete();
                        }

                        for ($x = 0; $x < count($request->al_subject_name2); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new AdvanceLevelResult();
                            $olResult->result_slot_id = $id;
                            $olResult->subject_name = ucfirst($request->al_subject_name2[$x]);
                            $olResult->grade = strtoupper($request->al_grade2[$x]);
                            $olResult->attempt = 2; // Set attempt to 2 for second attempt
                            $olResult->save();

                            //}
                        }
                    }
                }
                /****al result save update */
                /************************************************************************************** */
            } //al result section end

            if ($vacancy->main_category_id == 46) {

                if ($request->degree_type != null) {

                    for ($i = 0; $i < count($request->degree_type); $i++) {

                        $degreeData = new Degree();
                        $degreeData->reference_no = $request->reference_no;
                        $degreeData->degree_type = $request->degree_type_val[$i];
                        $degreeData->degree_titel = $request->degree_titel[$i];
                        $degreeData->degree_university = $request->degree_university[$i];
                        $degreeData->degree_class = $request->degree_class_val[$i];
                        $degreeData->eff_date = date("Y-m-d", strtotime($request->eff_date[$i]));
                        $degreeData->degree_index_number = $request->degree_index_number[$i];
                        $degreeData->degree_start = date("Y-m", strtotime($request->degree_start[$i]));
                        $degreeData->degree_end = date("Y-m", strtotime($request->degree_end[$i]));
                        $degreeData->save();
                    }
                }

                if ($request->hasFile('degree_certificate')) {

                    $certificates = $request->file('degree_certificate');

                    $availableDegreeFileCount = DegreeCertificate::where('reference_no', '=', session()->get('reference_no'))->count();

                    if ($availableDegreeFileCount > 0) {

                        $maxnumber = DB::table('degree_certificates')
                            ->where('reference_no', '=', session()->get('reference_no'))
                            ->select(DB::raw('MAX(id) as value'))
                            ->get();
                        $maxValue = json_decode($maxnumber, true);
                        $nextId = $maxValue[0]["value"] + 1;

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Degree Certificate' . ($nextId) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/degrees';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $degreeCertificateData = new DegreeCertificate();
                            $degreeCertificateData->reference_no = $request->reference_no;
                            $degreeCertificateData->save_path = $save_url;
                            $degreeCertificateData->save();

                            $nextId++;
                        }
                    } else {

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Degree Certificate' . ($key + 1) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/degrees';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $degreeCertificateData = new DegreeCertificate();
                            $degreeCertificateData->reference_no = $request->reference_no;
                            $degreeCertificateData->save_path = $save_url;
                            $degreeCertificateData->save();
                        }
                    }
                }

                if ($request->diploma_type != null) {

                    for ($i = 0; $i < count($request->diploma_type); $i++) {

                        $diplomaData = new Diploma();
                        $diplomaData->reference_no = $request->reference_no;
                        $diplomaData->type = $request->diploma_type_val[$i];
                        $diplomaData->course = $request->diploma_titel[$i];
                        $diplomaData->institute = $request->diploma_university[$i];
                        $diplomaData->duration = $request->diploma_duration[$i];
                        $diplomaData->year = $request->diploma_year[$i];
                        $diplomaData->save();
                    }
                }

                if ($request->hasFile('diploma_certificate')) {

                    $certificates = $request->file('diploma_certificate');

                    $availableDiplomaFileCount = DiplomaCertificate::where('reference_no', '=', session()->get('reference_no'))->count();

                    if ($availableDiplomaFileCount > 0) {

                        $maxnumber = DB::table('diploma_certificates')
                            ->where('reference_no', '=', session()->get('reference_no'))
                            ->select(DB::raw('MAX(id) as value'))
                            ->get();
                        $maxValue = json_decode($maxnumber, true);
                        $nextId = $maxValue[0]["value"] + 1;

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Diploma Certificate' . ($nextId) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/diplomas';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $diplomaCertificateData = new DiplomaCertificate();
                            $diplomaCertificateData->reference_no = $request->reference_no;
                            $diplomaCertificateData->save_path = $save_url;
                            $diplomaCertificateData->save();

                            $nextId++;
                        }
                    } else {

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Diploma Certificate' . ($key + 1) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/diplomas';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $diplomaCertificateData = new DiplomaCertificate();
                            $diplomaCertificateData->reference_no = $request->reference_no;
                            $diplomaCertificateData->save_path = $save_url;
                            $diplomaCertificateData->save();
                        }
                    }
                }
            }


            if ($vacancy->main_category_id == 44 || $vacancy->main_category_id == 45) {

                if ($request->degree_type != null) {

                    for ($i = 0; $i < count($request->degree_type); $i++) {

                        $degreeData = new Degree();
                        $degreeData->reference_no = $request->reference_no;
                        $degreeData->degree_type = $request->degree_type_val[$i];
                        $degreeData->degree_titel = $request->degree_titel[$i];
                        $degreeData->degree_university = $request->degree_university[$i];
                        $degreeData->degree_class = $request->degree_class_val[$i];
                        $degreeData->eff_date = date("Y-m-d", strtotime($request->eff_date[$i]));
                        $degreeData->degree_index_number = $request->degree_index_number[$i];
                        $degreeData->degree_start = date("Y-m", strtotime($request->degree_start[$i]));
                        $degreeData->degree_end = date("Y-m", strtotime($request->degree_end[$i]));
                        $degreeData->save();
                    }
                }

                // if ($request->degree_main_subject != null) {

                //     for ($i = 0; $i < count($request->degree_main_subject); $i++) {

                //         $data = new DegreeSubject();
                //         $data->reference_no = $request->reference_no;
                //         $data->degree_main_subject =$request->degree_main_subject[$i];
                //         $data->save();
                //     }
                // }


                $degreeSubject1Count = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', 1)->count();
                $degreeSubject2Count = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', 2)->count();
                $degreeSubject3Count = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', 3)->count();
                $degreeSubject4Count = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', 4)->count();
                $degreeSubject5Count = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', 5)->count();
                $degreeSubject6Count = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', 6)->count();

                if ($request->degree_main_subject1 != null) {

                    if ($degreeSubject1Count == 0) {

                        $degreeSubject1 = new DegreeSubject();
                        $degreeSubject1->reference_no = $request->reference_no;
                        $degreeSubject1->degree_main_subject = $request->degree_main_subject1;
                        $degreeSubject1->subject_number = 1;
                        $degreeSubject1->save();
                    } else {

                        $degreesubject = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', '=', 1)->update([
                            'degree_main_subject' => $request->degree_main_subject1,
                        ]);
                    }
                }

                if ($request->degree_main_subject2 != null) {

                    if ($degreeSubject2Count == 0) {

                        $degreeSubject2 = new DegreeSubject();
                        $degreeSubject2->reference_no = $request->reference_no;
                        $degreeSubject2->degree_main_subject = $request->degree_main_subject2;
                        $degreeSubject2->subject_number = 2;
                        $degreeSubject2->save();
                    } else {

                        $degreesubject = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', '=', 2)->update([
                            'degree_main_subject' => $request->degree_main_subject2,
                        ]);
                    }
                }

                if ($request->degree_main_subject3 != null) {

                    if ($degreeSubject3Count == 0) {

                        $degreeSubject3 = new DegreeSubject();
                        $degreeSubject3->reference_no = $request->reference_no;
                        $degreeSubject3->degree_main_subject = $request->degree_main_subject3;
                        $degreeSubject3->subject_number = 3;
                        $degreeSubject3->save();
                    } else {

                        $degreesubject = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', '=', 3)->update([
                            'degree_main_subject' => $request->degree_main_subject3,
                        ]);
                    }
                }

                if ($request->degree_main_subject4 != null) {

                    if ($degreeSubject4Count == 0) {

                        $degreeSubject4 = new DegreeSubject();
                        $degreeSubject4->reference_no = $request->reference_no;
                        $degreeSubject4->degree_main_subject = $request->degree_main_subject4;
                        $degreeSubject4->subject_number = 4;
                        $degreeSubject4->save();
                    } else {

                        $degreesubject = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', '=', 4)->update([
                            'degree_main_subject' => $request->degree_main_subject4,
                        ]);
                    }
                }

                if ($request->degree_main_subject5 != null) {

                    if ($degreeSubject5Count == 0) {

                        $degreeSubject5 = new DegreeSubject();
                        $degreeSubject5->reference_no = $request->reference_no;
                        $degreeSubject5->degree_main_subject = $request->degree_main_subject5;
                        $degreeSubject5->subject_number = 5;
                        $degreeSubject5->save();
                    } else {

                        $degreesubject = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', '=', 5)->update([
                            'degree_main_subject' => $request->degree_main_subject5,
                        ]);
                    }
                }

                if ($request->degree_main_subject6 != null) {

                    if ($degreeSubject6Count == 0) {

                        $degreeSubject6 = new DegreeSubject();
                        $degreeSubject6->reference_no = $request->reference_no;
                        $degreeSubject6->degree_main_subject = $request->degree_main_subject6;
                        $degreeSubject6->subject_number = 6;
                        $degreeSubject6->save();
                    } else {

                        $degreesubject = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', '=', 6)->update([
                            'degree_main_subject' => $request->degree_main_subject6,
                        ]);
                    }
                }

                /**************************************************************** */

                if ($request->hasFile('degree_certificate')) {

                    $certificates = $request->file('degree_certificate');

                    $availableDegreeFileCount = DegreeCertificate::where('reference_no', '=', session()->get('reference_no'))->count();

                    if ($availableDegreeFileCount > 0) {

                        $maxnumber = DB::table('degree_certificates')
                            ->where('reference_no', '=', session()->get('reference_no'))
                            ->select(DB::raw('MAX(id) as value'))
                            ->get();
                        $maxValue = json_decode($maxnumber, true);
                        $nextId = $maxValue[0]["value"] + 1;

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Degree Certificate' . ($nextId) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/degrees';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $degreeCertificateData = new DegreeCertificate();
                            $degreeCertificateData->reference_no = $request->reference_no;
                            $degreeCertificateData->save_path = $save_url;
                            $degreeCertificateData->save();

                            $nextId++;
                        }
                    } else {

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Degree Certificate' . ($key + 1) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/degrees';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $degreeCertificateData = new DegreeCertificate();
                            $degreeCertificateData->reference_no = $request->reference_no;
                            $degreeCertificateData->save_path = $save_url;
                            $degreeCertificateData->save();
                        }
                    }
                }

                if ($request->diploma_type != null) {

                    for ($i = 0; $i < count($request->diploma_type); $i++) {

                        $diplomaData = new Diploma();
                        $diplomaData->reference_no = $request->reference_no;
                        $diplomaData->type = $request->diploma_type_val[$i];
                        $diplomaData->course = $request->diploma_titel[$i];
                        $diplomaData->institute = $request->diploma_university[$i];
                        $diplomaData->duration = $request->diploma_duration[$i];
                        $diplomaData->year = $request->diploma_year[$i];
                        $diplomaData->save();
                    }
                }

                if ($request->hasFile('diploma_certificate')) {

                    $certificates = $request->file('diploma_certificate');

                    $availableDiplomaFileCount = DiplomaCertificate::where('reference_no', '=', session()->get('reference_no'))->count();

                    if ($availableDiplomaFileCount > 0) {

                        $maxnumber = DB::table('diploma_certificates')
                            ->where('reference_no', '=', session()->get('reference_no'))
                            ->select(DB::raw('MAX(id) as value'))
                            ->get();
                        $maxValue = json_decode($maxnumber, true);
                        $nextId = $maxValue[0]["value"] + 1;

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Diploma Certificate' . ($nextId) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/diplomas';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $diplomaCertificateData = new DiplomaCertificate();
                            $diplomaCertificateData->reference_no = $request->reference_no;
                            $diplomaCertificateData->save_path = $save_url;
                            $diplomaCertificateData->save();

                            $nextId++;
                        }
                    } else {

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Diploma Certificate' . ($key + 1) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/diplomas';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $diplomaCertificateData = new DiplomaCertificate();
                            $diplomaCertificateData->reference_no = $request->reference_no;
                            $diplomaCertificateData->save_path = $save_url;
                            $diplomaCertificateData->save();
                        }
                    }
                }

                if ($request->research_summary != null || $request->orcid != null) {

                    $researchCount = Research::where('reference_no', '=', session()->get('reference_no'))->count();
                    if ($researchCount > 0) {

                        $checkUpdate = Research::where('reference_no', '=', session()->get('reference_no'))
                            ->update([
                                'research_summary' => $request->research_summary,
                                'orcid' => $request->orcid
                            ]);
                    } else {

                        $researchData = new Research();
                        $researchData->reference_no = $request->reference_no;
                        $researchData->research_summary = $request->research_summary;
                        $researchData->orcid = $request->orcid;
                        $researchData->save();
                    }
                }

                if ($request->qulification_type != null) {

                    for ($i = 0; $i < count($request->qulification_type); $i++) {

                        $specialQulificationData = new SpecialQulification();
                        $specialQulificationData->reference_no = $request->reference_no;
                        $specialQulificationData->qulification_type = $request->qulification_type_val[$i];
                        $specialQulificationData->qulification_summary = $request->qulification_summary[$i];
                        $specialQulificationData->save();
                    }
                }

                if ($request->membership_type != null) {

                    for ($i = 0; $i < count($request->membership_type); $i++) {

                        $membershipData = new Membership();
                        $membershipData->reference_no = $request->reference_no;
                        $membershipData->membership_type = $request->membership_type_val[$i];
                        $membershipData->membership_university = $request->membership_university[$i];
                        $membershipData->membership_start_year = $request->membership_start_year[$i];
                        $membershipData->membership_end_year = $request->membership_end_year[$i];
                        $membershipData->membership_active_status = $request->membership_active_status_val[$i];
                        $membershipData->save();
                    }
                }

                if ($request->pqualification_name != null) {

                    for ($i = 0; $i < count($request->pqualification_name); $i++) {

                        $professionalQualificationData = new ProfessionalQualification();
                        $professionalQualificationData->reference_no = $request->reference_no;
                        $professionalQualificationData->name = $request->pqualification_name[$i];
                        $professionalQualificationData->university = $request->pqualification_university[$i];
                        $professionalQualificationData->start_year = $request->pqualification_start_year[$i];
                        $professionalQualificationData->end_year = $request->pqualification_end_year[$i];
                        $professionalQualificationData->save();
                    }
                }

                if ($request->experience_designation != null) {

                    for ($i = 0; $i < count($request->experience_designation); $i++) {

                        $employmentRecordData = new EmploymentRecord();
                        $employmentRecordData->reference_no = $request->reference_no;
                        $employmentRecordData->designation = $request->experience_designation[$i];
                        $employmentRecordData->institution = $request->experience_institution[$i];
                        $employmentRecordData->start_date = date("Y-m-d", strtotime($request->experience_start_date[$i]));
                        $employmentRecordData->end_date = date("Y-m-d", strtotime($request->experience_end_date[$i]));
                        $employmentRecordData->last_month_salary = number_format($request->experience_last_month_salary[$i], 2);
                        $employmentRecordData->save();
                    }
                }

                if ($request->hasFile('employment_record_certificate')) {

                    $certificates = $request->file('employment_record_certificate');

                    $availableEmploymentRecordFileCount = EmploymentRecordCertificate::where('reference_no', '=', session()->get('reference_no'))->count();

                    if ($availableEmploymentRecordFileCount > 0) {

                        $maxnumber = DB::table('employment_record_certificates')
                            ->where('reference_no', '=', session()->get('reference_no'))
                            ->select(DB::raw('MAX(id) as value'))
                            ->get();
                        $maxValue = json_decode($maxnumber, true);
                        $nextId = $maxValue[0]["value"] + 1;

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Employment Record Certificate' . ($nextId) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/employments';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $employmentRecordCertificateData = new EmploymentRecordCertificate();
                            $employmentRecordCertificateData->reference_no = $request->reference_no;
                            $employmentRecordCertificateData->save_path = $save_url;
                            $employmentRecordCertificateData->save();

                            $nextId++;
                        }
                    } else {

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Employment Record Certificate' . ($key + 1) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/employments';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $employmentRecordCertificateData = new EmploymentRecordCertificate();
                            $employmentRecordCertificateData->reference_no = $request->reference_no;
                            $employmentRecordCertificateData->save_path = $save_url;
                            $employmentRecordCertificateData->save();
                        }
                    }
                }

                if ($request->bond_institution != null) {

                    for ($i = 0; $i < count($request->bond_institution); $i++) {

                        $bondData = new Bond();
                        $bondData->reference_no = $request->reference_no;
                        $bondData->institute = $request->bond_institution[$i];
                        $bondData->from = date("Y-m-d", strtotime($request->bond_start_date[$i]));
                        $bondData->to = date("Y-m-d", strtotime($request->bond_end_date[$i]));
                        $bondData->bond_value = number_format($request->bond_total_value[$i], 2);
                        $bondData->save();
                    }
                }

                if ($request->referee_name != null) {

                    for ($i = 0; $i < count($request->referee_name); $i++) {

                        $refereeData = new Referee();
                        $refereeData->reference_no = $request->reference_no;
                        $refereeData->name = $request->referee_name[$i];
                        $refereeData->designation = $request->referee_designation[$i];
                        $refereeData->address = $request->referee_address[$i];
                        $refereeData->telephone = $request->referee_phome[$i];
                        $refereeData->email = $request->referee_email[$i];
                        $refereeData->save();
                    }
                }

                if ($request->hasFile('public_sector_letter')) {

                    $releaseLettersCount = ReleaseLetter::where('reference_no', '=', session()->get('reference_no'))->count();

                    $image = $request->file('public_sector_letter');
                    $name_gen = 'Release Letter' . '.' . $image->getClientOriginalExtension();
                    $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/release';
                    if (!File::exists($path)) {
                        File::makeDirectory(storage_path() . $path, 0777, true, true);
                    }
                    $request->file('public_sector_letter')->storeAs($path, $name_gen);
                    $save_url = $path . '/' . $name_gen;

                    if ($releaseLettersCount == 0) {

                        $releaseLetterData = new ReleaseLetter();
                        $releaseLetterData->reference_no = $request->reference_no;
                        $releaseLetterData->save_path = $save_url;
                        $releaseLetterData->save();
                    }
                }
            }




            $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('vacancies.*', 'categories.display_name')
                ->find(session()->get('vacancy_id'));

            if ($vacancy->subject == '' && $vacancy->faculty_id == '' && $vacancy->department_id == '') {

                //mail data
                $emailData = [
                    'name' => strtoupper($request->initials) . ' ' . ucwords($request->last_name),
                    'updated_at' => Carbon::now(),
                    'reference_no' => session()->get('reference_no'),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => '',
                    'department' => '',
                    'name_status' => '',
                    'subject' => '',
                ];
            } elseif ($vacancy->subject == '' && $vacancy->faculty_id != '' && $vacancy->department_id == '') {

                //mail data
                $emailData = [
                    'name' => strtoupper($request->initials) . ' ' . ucwords($request->last_name),
                    'updated_at' => Carbon::now(),
                    'reference_no' => session()->get('reference_no'),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => $vacancy->faculties->faculty_name,
                    'department' => '',
                    'name_status' => '',
                    'subject' => '',
                ];
                # code...
            } elseif ($vacancy->subject == '' && $vacancy->faculty_id != '' && $vacancy->department_id != '') {

                //mail data
                $emailData = [
                    'name' => strtoupper($request->initials) . ' ' . ucwords($request->last_name),
                    'updated_at' => Carbon::now(),
                    'reference_no' => session()->get('reference_no'),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => $vacancy->faculties->faculty_name,
                    'department' => $vacancy->departments->department_name,
                    'name_status' => $vacancy->departments->name_status,
                    'subject' => '',
                ];
            } elseif ($vacancy->subject == '' && $vacancy->faculty_id == '' && $vacancy->department_id != '') {

                $emailData = [
                    'name' => strtoupper($request->initials) . ' ' . ucwords($request->last_name),
                    'updated_at' => Carbon::now(),
                    'reference_no' => session()->get('reference_no'),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => '',
                    'department' => $vacancy->departments->department_name,
                    'name_status' => $vacancy->departments->name_status,
                    'subject' => '',
                ];
            } else {

                $emailData = [
                    'name' => strtoupper($request->initials) . ' ' . ucwords($request->last_name),
                    'updated_at' => Carbon::now(),
                    'reference_no' => session()->get('reference_no'),
                    'designation' => $vacancy->designations->designation_name,
                    'grade' => $vacancy->display_name,
                    'faculty' => $vacancy->faculties->faculty_name,
                    'department' => $vacancy->departments->department_name,
                    'name_status' => $vacancy->departments->name_status,
                    'subject' => $vacancy->subject,
                ];
            }


            $mail = new ApplicationCompleteMail($emailData);
            //sending email
            FacadesMail::to($request->email, 'USJ HRM SYSTEM')->send($mail);

            Log::notice('IndexController -> submit application data id - ' . session()->get('reference_no'));
            Log::info('IndexController -> submit application ended');

            $notification = array(
                'message' => 'Congratulations! Your application has been successfully submitted.',
                'alert-type' => 'success'
            );

            return redirect()->route('application.final')->with($notification);
        } //submit option

        elseif ($request->has('save') || $request->has('save1')) {

            Log::info('IndexController -> save application started');

            //data store
            $vacancy = Vacancy::find(session()->get('vacancy_id'));
            $data = Application::find(session()->get('reference_no'));
            //personal information save start
            $data->titel_id = $request->titel_id;
            $data->initials = strtoupper($request->initials);
            $data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
            $data->last_name = ucwords($request->last_name);
            $data->phone_no = $request->phone_no;
            //$data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
            //$data->gender_id = $request->gender_id;
            $data->civil_status_id = $request->civil_status_id;
            //$data->race_id = $request->race_id;
            //$data->religion_id = $request->religion_id;
            $data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
            $data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
            $data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
            $data->permanent_city_id = $request->permanent_city_id;
            $data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
            $data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
            $data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
            $data->postal_city_id = $request->postal_city_id;
            $data->citizenship = $request->citizenship;
            $data->state_of_citizenship_id = $request->state_of_citizenship_id;
            if ($request->state_of_citizenship_id == 25) {
                $data->citizen_registration_no = '';
            } elseif ($request->state_of_citizenship_id == 26) {
                $data->citizen_registration_no = $request->citizen_registration_no;
            }
            $data->emp_highest_edu_level = $request->emp_highest_edu_level;

            /****************************************************** */
            if ($request->hasFile('profile_photo')) {
                $image = $request->file('profile_photo');
                $name_gen = 'Profile Picture' . '.' . $image->getClientOriginalExtension();
                $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/profile';
                if (!File::exists($path)) {
                    File::makeDirectory(storage_path() . $path, 0777, true, true);
                }
                $request->file('profile_photo')->storeAs($path, $name_gen);
                $save_url = $path . '/' . $name_gen;

                $data->profile_photo = $save_url;
            }
            /***************************************************** */
            if ($request->has('terms')) {
                $data->user_agreement_status = $request->terms;
            }

            if ($vacancy->main_category_id == 44 || $vacancy->main_category_id == 45) {

                if ($request->bondability != null) {

                    $data->bondability = $request->bondability;
                }

                if ($request->publicSector != null) {

                    $data->is_public_sector = $request->publicSector;
                }
            }

            $data->updated_at = Carbon::now();
            $data->save();
            //personal information save end
            /***************************************************************************** */
            //ol result information save
            if ($vacancy->main_category_id == 46) {

                $avalableolResultSummary = OrdinaryLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('attempt', '=', 1)->count();

                if ($avalableolResultSummary == 0) {

                    if ($request->hasFile('certificate1')) {

                        $certificate = $request->file('certificate1');
                        $name_gen = 'Ol Result Sheet 01' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/ol';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('certificate1')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    }

                    $olResultSummary = new OrdinaryLevelResultSummary();
                    $olResultSummary->reference_no = session()->get('reference_no');
                    $olResultSummary->index_no = $request->index_no;
                    $olResultSummary->year = $request->year;
                    $olResultSummary->attempt = $request->attempt;
                    if ($request->hasFile('certificate1')) {
                        $olResultSummary->file_path = $save_url;
                    }
                    $olResultSummary->save();


                    if ($request->subject_name != null) {

                        if (count($request->subject_name) > 0) {
                            OrdinaryLevelResult::where('result_slot_id', $olResultSummary->id)->delete();
                        }

                        for ($x = 0; $x < count($request->subject_name); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new OrdinaryLevelResult();
                            $olResult->result_slot_id = $olResultSummary->id;
                            $olResult->subject_name = $request->subject_name[$x];
                            $olResult->grade = strtoupper($request->grade[$x]);
                            $olResult->save();

                            //}
                        }
                    }
                    //ol result first time save
                } else {

                    if ($request->hasFile('certificate1')) {

                        if ($request->old_certificate1 != '') {

                            $oldImage = $request->old_certificate1;
                            unlink(storage_path() . '/app/' . $oldImage);
                        }


                        $certificate = $request->file('certificate1');
                        $name_gen = 'Ol Result Sheet 01' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/ol';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('certificate1')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    } elseif ($request->old_certificate1 == '' && !$request->hasFile('certificate1')) {

                        $save_url = '';
                    } else {

                        $save_url = $request->old_certificate1;
                    }


                    $olResultSummary = OrdinaryLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('attempt', '=', 1)->update([
                        'reference_no' => session()->get('reference_no'),
                        'index_no' => $request->index_no,
                        'year' => $request->year,
                        'attempt' => $request->attempt,
                        'file_path' => $save_url
                    ]);

                    $id = OrdinaryLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('attempt', '=', 1)->get('id');
                    $id = json_decode($id, true);
                    $id = $id[0]["id"];

                    if ($request->subject_name != null) {

                        if (count($request->grade) > 0) {
                            OrdinaryLevelResult::where('result_slot_id', $id)->delete();
                        }

                        for ($x = 0; $x < count($request->subject_name); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new OrdinaryLevelResult();
                            $olResult->result_slot_id = $id;
                            $olResult->subject_name = $request->subject_name[$x];
                            $olResult->grade = strtoupper($request->grade[$x]);
                            $olResult->save();

                            //}
                        }
                    }
                }
                /****ol result save update */

                /********************************************************************************* */

                $avalableolResultSummary = OrdinaryLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('attempt', '=', 2)->count();

                if ($avalableolResultSummary == 0) {

                    if ($request->hasFile('certificate2')) {

                        $certificate = $request->file('certificate2');
                        $name_gen = 'Ol Result Sheet 02' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/ol2';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('certificate2')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    }

                    $olResultSummary = new OrdinaryLevelResultSummary();
                    $olResultSummary->reference_no = session()->get('reference_no');
                    $olResultSummary->index_no = $request->index_no2;
                    $olResultSummary->year = $request->year2;
                    $olResultSummary->attempt = $request->attempt2;
                    if ($request->hasFile('certificate2')) {
                        $olResultSummary->file_path = $save_url;
                    }
                    $olResultSummary->save();


                    if ($request->subject_name2 != null) {

                        if (count($request->subject_name2) > 0) {
                            OrdinaryLevelResult::where('result_slot_id', $olResultSummary->id)->delete();
                        }

                        for ($x = 0; $x < count($request->subject_name2); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new OrdinaryLevelResult();
                            $olResult->result_slot_id = $olResultSummary->id;
                            $olResult->subject_name = $request->subject_name2[$x];
                            $olResult->grade = strtoupper($request->grade2[$x]);
                            $olResult->save();

                            //}
                        }
                    }
                    //ol result first time save
                } else {

                    if ($request->hasFile('certificate2')) {

                        if ($request->old_certificate2 != '') {

                            $oldImage = $request->old_certificate2;
                            unlink(storage_path() . '/app/' . $oldImage);
                        }


                        $certificate = $request->file('certificate2');
                        $name_gen = 'Ol Result Sheet 02' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/ol2';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('certificate2')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    } elseif ($request->old_certificate2 == '' && !$request->hasFile('certificate2')) {

                        $save_url = '';
                    } else {

                        $save_url = $request->old_certificate2;
                    }


                    $olResultSummary = OrdinaryLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('attempt', '=', 2)->update([
                        'reference_no' => session()->get('reference_no'),
                        'index_no' => $request->index_no2,
                        'year' => $request->year2,
                        'attempt' => $request->attempt2,
                        'file_path' => $save_url
                    ]);

                    $id = OrdinaryLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('attempt', '=', 2)->get('id');
                    $id = json_decode($id, true);
                    $id = $id[0]["id"];

                    if ($request->subject_name2 != null) {

                        if (count($request->grade2) > 0) {
                            OrdinaryLevelResult::where('result_slot_id', $id)->delete();
                        }

                        for ($x = 0; $x < count($request->subject_name2); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new OrdinaryLevelResult();
                            $olResult->result_slot_id = $id;
                            $olResult->subject_name = $request->subject_name2[$x];
                            $olResult->grade = strtoupper($request->grade2[$x]);
                            $olResult->save();

                            //}
                        }
                    }
                }
                /****ol result save update */
            } //ol result save section

            /************************************************************************************* */

            //al result information save
            if ($vacancy->main_category_id == 46) {

                $avalableolResultSummary = AdvanceLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('level', '=', 1)->count();

                if ($avalableolResultSummary == 0) {

                    if ($request->hasFile('al_certificate1')) {

                        $certificate = $request->file('al_certificate1');
                        $name_gen = 'Al Result Sheet 01' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/al';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('al_certificate1')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    }

                    $alResultSummary = new AdvanceLevelResultSummary();
                    $alResultSummary->reference_no = session()->get('reference_no');
                    $alResultSummary->index_no = $request->al_index_no;
                    $alResultSummary->year = $request->al_year;
                    $alResultSummary->attempt = $request->al_attempt;
                    $alResultSummary->stream = $request->al_stream_id;
                    $alResultSummary->level = $request->al_level;

                    if ($request->hasFile('al_certificate1')) {
                        $alResultSummary->file_path = $save_url;
                    }
                    $alResultSummary->save();


                    if ($request->al_subject_name != null) {

                        if (count($request->al_subject_name) > 0) {
                            AdvanceLevelResult::where('result_slot_id', $alResultSummary->id)->delete();
                        }

                        for ($x = 0; $x < count($request->al_subject_name); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new AdvanceLevelResult();
                            $olResult->result_slot_id = $alResultSummary->id;
                            $olResult->subject_name = ucfirst($request->al_subject_name[$x]);
                            $olResult->grade = strtoupper($request->al_grade[$x]);
                            $olResult->save();

                            //}
                        }
                    }
                } else {

                    if ($request->hasFile('al_certificate1')) {

                        if ($request->old_al_certificate1 != '') {

                            $oldImage = $request->old_al_certificate1;
                            unlink(storage_path() . '/app/' . $oldImage);
                        }


                        $certificate = $request->file('al_certificate1');
                        $name_gen = 'AL Result Sheet 01' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/al';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('al_certificate1')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    } elseif ($request->old_al_certificate1 == '' && !$request->hasFile('al_certificate1')) {

                        $save_url = '';
                    } else {

                        $save_url = $request->old_al_certificate1;
                    }


                    $olResultSummary = AdvanceLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('level', '=', 1)->update([
                        'reference_no' => session()->get('reference_no'),
                        'index_no' => $request->al_index_no,
                        'year' => $request->al_year,
                        'attempt' => $request->al_attempt,
                        'stream' => $request->al_stream_id,
                        'file_path' => $save_url
                    ]);

                    $id = AdvanceLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('level', '=', 1)->get('id');
                    $id = json_decode($id, true);
                    $id = $id[0]["id"];

                    if ($request->al_subject_name != null) {

                        if (count($request->al_grade) > 0) {
                            AdvanceLevelResult::where('result_slot_id', $id)->delete();
                        }

                        for ($x = 0; $x < count($request->al_subject_name); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new AdvanceLevelResult();
                            $olResult->result_slot_id = $id;
                            $olResult->subject_name = ucfirst($request->al_subject_name[$x]);
                            $olResult->grade = strtoupper($request->al_grade[$x]);
                            $olResult->save();

                            //}
                        }
                    }
                }
                /****al result save update */
                /******************************************************************************************** */

                $avalableolResultSummary = AdvanceLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('level', '=', 2)->count();

                if ($avalableolResultSummary == 0) {

                    if ($request->hasFile('al_certificate2')) {

                        $certificate = $request->file('al_certificate2');
                        $name_gen = 'Al Result Sheet 02' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/al2';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('al_certificate2')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    }

                    $alResultSummary = new AdvanceLevelResultSummary();
                    $alResultSummary->reference_no = session()->get('reference_no');
                    $alResultSummary->index_no = $request->al_index_no2;
                    $alResultSummary->year = $request->al_year2;
                    $alResultSummary->attempt = $request->al_attempt2;
                    $alResultSummary->stream = $request->al_stream_id2;
                    $alResultSummary->level = $request->al_level2;

                    if ($request->hasFile('al_certificate2')) {
                        $alResultSummary->file_path = $save_url;
                    }
                    $alResultSummary->save();


                    if ($request->al_subject_name2 != null) {

                        if (count($request->al_subject_name2) > 0) {
                            AdvanceLevelResult::where('result_slot_id', $alResultSummary->id)->delete();
                        }

                        for ($x = 0; $x < count($request->al_subject_name2); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new AdvanceLevelResult();
                            $olResult->result_slot_id = $alResultSummary->id;
                            $olResult->subject_name = ucfirst($request->al_subject_name2[$x]);
                            $olResult->grade = strtoupper($request->al_grade2[$x]);
                            $olResult->save();

                            //}
                        }
                    }
                } else {

                    if ($request->hasFile('al_certificate2')) {

                        if ($request->old_al_certificate2 != '') {

                            $oldImage = $request->old_al_certificate2;
                            unlink(storage_path() . '/app/' . $oldImage);
                        }


                        $certificate = $request->file('al_certificate2');
                        $name_gen = 'AL Result Sheet 02' . '.' . $certificate->getClientOriginalExtension();
                        $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/al2';

                        if (!File::exists($path)) {
                            File::makeDirectory(storage_path() . $path, 0777, true, true);
                        }
                        $request->file('al_certificate2')->storeAs($path, $name_gen);
                        $save_url = $path . '/' . $name_gen;
                    } elseif ($request->old_al_certificate2 == '' && !$request->hasFile('al_certificate2')) {

                        $save_url = '';
                    } else {

                        $save_url = $request->old_al_certificate2;
                    }


                    $olResultSummary = AdvanceLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('level', '=', 2)->update([
                        'reference_no' => session()->get('reference_no'),
                        'index_no' => $request->al_index_no2,
                        'year' => $request->al_year2,
                        'attempt' => $request->al_attempt2,
                        'stream' => $request->al_stream_id2,
                        'file_path' => $save_url
                    ]);

                    $id = AdvanceLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('level', '=', 2)->get('id');
                    $id = json_decode($id, true);
                    $id = $id[0]["id"];

                    if ($request->al_subject_name2 != null) {

                        if (count($request->al_grade2) > 0) {
                            AdvanceLevelResult::where('result_slot_id', $id)->delete();
                        }

                        for ($x = 0; $x < count($request->al_subject_name2); $x++) {
                            //if ($request->grade[$x] != "") {

                            $olResult = new AdvanceLevelResult();
                            $olResult->result_slot_id = $id;
                            $olResult->subject_name = ucfirst($request->al_subject_name2[$x]);
                            $olResult->grade = strtoupper($request->al_grade2[$x]);
                            $olResult->save();

                            //}
                        }
                    }
                }
                /****al result save update */

                /************************************************************************************* */
            } //al result section end


            if ($vacancy->main_category_id == 46) {

                if ($request->degree_type != null) {

                    for ($i = 0; $i < count($request->degree_type); $i++) {

                        $degreeData = new Degree();
                        $degreeData->reference_no = $request->reference_no;
                        $degreeData->degree_type = $request->degree_type_val[$i];
                        $degreeData->degree_titel = $request->degree_titel[$i];
                        $degreeData->degree_university = $request->degree_university[$i];
                        $degreeData->degree_class = $request->degree_class_val[$i];
                        $degreeData->eff_date = date("Y-m-d", strtotime($request->eff_date[$i]));
                        $degreeData->degree_index_number = $request->degree_index_number[$i];
                        $degreeData->degree_start = date("Y-m", strtotime($request->degree_start[$i]));
                        $degreeData->degree_end = date("Y-m", strtotime($request->degree_end[$i]));
                        $degreeData->save();
                    }
                }

                if ($request->hasFile('degree_certificate')) {

                    $certificates = $request->file('degree_certificate');

                    $availableDegreeFileCount = DegreeCertificate::where('reference_no', '=', session()->get('reference_no'))->count();

                    if ($availableDegreeFileCount > 0) {

                        $maxnumber = DB::table('degree_certificates')
                            ->where('reference_no', '=', session()->get('reference_no'))
                            ->select(DB::raw('MAX(id) as value'))
                            ->get();
                        $maxValue = json_decode($maxnumber, true);
                        $nextId = $maxValue[0]["value"] + 1;

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Degree Certificate' . ($nextId) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/degrees';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $degreeCertificateData = new DegreeCertificate();
                            $degreeCertificateData->reference_no = $request->reference_no;
                            $degreeCertificateData->save_path = $save_url;
                            $degreeCertificateData->save();

                            $nextId++;
                        }
                    } else {

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Degree Certificate' . ($key + 1) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/degrees';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $degreeCertificateData = new DegreeCertificate();
                            $degreeCertificateData->reference_no = $request->reference_no;
                            $degreeCertificateData->save_path = $save_url;
                            $degreeCertificateData->save();
                        }
                    }
                }

                if ($request->diploma_type != null) {

                    for ($i = 0; $i < count($request->diploma_type); $i++) {

                        $diplomaData = new Diploma();
                        $diplomaData->reference_no = $request->reference_no;
                        $diplomaData->type = $request->diploma_type_val[$i];
                        $diplomaData->course = $request->diploma_titel[$i];
                        $diplomaData->institute = $request->diploma_university[$i];
                        $diplomaData->duration = $request->diploma_duration[$i];
                        $diplomaData->year = $request->diploma_year[$i];
                        $diplomaData->save();
                    }
                }

                if ($request->hasFile('diploma_certificate')) {

                    $certificates = $request->file('diploma_certificate');

                    $availableDiplomaFileCount = DiplomaCertificate::where('reference_no', '=', session()->get('reference_no'))->count();

                    if ($availableDiplomaFileCount > 0) {

                        $maxnumber = DB::table('diploma_certificates')
                            ->where('reference_no', '=', session()->get('reference_no'))
                            ->select(DB::raw('MAX(id) as value'))
                            ->get();
                        $maxValue = json_decode($maxnumber, true);
                        $nextId = $maxValue[0]["value"] + 1;

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Diploma Certificate' . ($nextId) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/diplomas';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $diplomaCertificateData = new DiplomaCertificate();
                            $diplomaCertificateData->reference_no = $request->reference_no;
                            $diplomaCertificateData->save_path = $save_url;
                            $diplomaCertificateData->save();

                            $nextId++;
                        }
                    } else {

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Diploma Certificate' . ($key + 1) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/diplomas';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $diplomaCertificateData = new DiplomaCertificate();
                            $diplomaCertificateData->reference_no = $request->reference_no;
                            $diplomaCertificateData->save_path = $save_url;
                            $diplomaCertificateData->save();
                        }
                    }
                }
            }


            if ($vacancy->main_category_id == 44 || $vacancy->main_category_id == 45) {

                if ($request->degree_type != null) {

                    for ($i = 0; $i < count($request->degree_type); $i++) {

                        $degreeData = new Degree();
                        $degreeData->reference_no = $request->reference_no;
                        $degreeData->degree_type = $request->degree_type_val[$i];
                        $degreeData->degree_titel = $request->degree_titel[$i];
                        $degreeData->degree_university = $request->degree_university[$i];
                        $degreeData->degree_class = $request->degree_class_val[$i];
                        $degreeData->eff_date = date("Y-m-d", strtotime($request->eff_date[$i]));
                        $degreeData->degree_index_number = $request->degree_index_number[$i];
                        $degreeData->degree_start = date("Y-m", strtotime($request->degree_start[$i]));
                        $degreeData->degree_end = date("Y-m", strtotime($request->degree_end[$i]));
                        $degreeData->save();
                    }
                }

                // if ($request->degree_main_subject != null) {

                //     for ($i = 0; $i < count($request->degree_main_subject); $i++) {

                //         $data = new DegreeSubject();
                //         $data->reference_no = $request->reference_no;
                //         $data->degree_main_subject =$request->degree_main_subject[$i];
                //         $data->save();
                //     }
                // }


                $degreeSubject1Count = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', 1)->count();
                $degreeSubject2Count = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', 2)->count();
                $degreeSubject3Count = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', 3)->count();
                $degreeSubject4Count = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', 4)->count();
                $degreeSubject5Count = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', 5)->count();
                $degreeSubject6Count = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', 6)->count();

                if ($request->degree_main_subject1 != null) {

                    if ($degreeSubject1Count == 0) {

                        $degreeSubject1 = new DegreeSubject();
                        $degreeSubject1->reference_no = $request->reference_no;
                        $degreeSubject1->degree_main_subject = $request->degree_main_subject1;
                        $degreeSubject1->subject_number = 1;
                        $degreeSubject1->save();
                    } else {

                        $degreesubject = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', '=', 1)->update([
                            'degree_main_subject' => $request->degree_main_subject1,
                        ]);
                    }
                }

                if ($request->degree_main_subject2 != null) {

                    if ($degreeSubject2Count == 0) {

                        $degreeSubject2 = new DegreeSubject();
                        $degreeSubject2->reference_no = $request->reference_no;
                        $degreeSubject2->degree_main_subject = $request->degree_main_subject2;
                        $degreeSubject2->subject_number = 2;
                        $degreeSubject2->save();
                    } else {

                        $degreesubject = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', '=', 2)->update([
                            'degree_main_subject' => $request->degree_main_subject2,
                        ]);
                    }
                }

                if ($request->degree_main_subject3 != null) {

                    if ($degreeSubject3Count == 0) {

                        $degreeSubject3 = new DegreeSubject();
                        $degreeSubject3->reference_no = $request->reference_no;
                        $degreeSubject3->degree_main_subject = $request->degree_main_subject3;
                        $degreeSubject3->subject_number = 3;
                        $degreeSubject3->save();
                    } else {

                        $degreesubject = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', '=', 3)->update([
                            'degree_main_subject' => $request->degree_main_subject3,
                        ]);
                    }
                }

                if ($request->degree_main_subject4 != null) {

                    if ($degreeSubject4Count == 0) {

                        $degreeSubject4 = new DegreeSubject();
                        $degreeSubject4->reference_no = $request->reference_no;
                        $degreeSubject4->degree_main_subject = $request->degree_main_subject4;
                        $degreeSubject4->subject_number = 4;
                        $degreeSubject4->save();
                    } else {

                        $degreesubject = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', '=', 4)->update([
                            'degree_main_subject' => $request->degree_main_subject4,
                        ]);
                    }
                }

                if ($request->degree_main_subject5 != null) {

                    if ($degreeSubject5Count == 0) {

                        $degreeSubject5 = new DegreeSubject();
                        $degreeSubject5->reference_no = $request->reference_no;
                        $degreeSubject5->degree_main_subject = $request->degree_main_subject5;
                        $degreeSubject5->subject_number = 5;
                        $degreeSubject5->save();
                    } else {

                        $degreesubject = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', '=', 5)->update([
                            'degree_main_subject' => $request->degree_main_subject5,
                        ]);
                    }
                }

                if ($request->degree_main_subject6 != null) {

                    if ($degreeSubject6Count == 0) {

                        $degreeSubject6 = new DegreeSubject();
                        $degreeSubject6->reference_no = $request->reference_no;
                        $degreeSubject6->degree_main_subject = $request->degree_main_subject6;
                        $degreeSubject6->subject_number = 6;
                        $degreeSubject6->save();
                    } else {

                        $degreesubject = DegreeSubject::where('reference_no', '=', session()->get('reference_no'))->where('subject_number', '=', 6)->update([
                            'degree_main_subject' => $request->degree_main_subject6,
                        ]);
                    }
                }

                /**************************************************************** */

                if ($request->hasFile('degree_certificate')) {

                    $certificates = $request->file('degree_certificate');

                    $availableDegreeFileCount = DegreeCertificate::where('reference_no', '=', session()->get('reference_no'))->count();

                    if ($availableDegreeFileCount > 0) {

                        $maxnumber = DB::table('degree_certificates')
                            ->where('reference_no', '=', session()->get('reference_no'))
                            ->select(DB::raw('MAX(id) as value'))
                            ->get();
                        $maxValue = json_decode($maxnumber, true);
                        $nextId = $maxValue[0]["value"] + 1;

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Degree Certificate' . ($nextId) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/degrees';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $degreeCertificateData = new DegreeCertificate();
                            $degreeCertificateData->reference_no = $request->reference_no;
                            $degreeCertificateData->save_path = $save_url;
                            $degreeCertificateData->save();

                            $nextId++;
                        }
                    } else {

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Degree Certificate' . ($key + 1) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/degrees';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $degreeCertificateData = new DegreeCertificate();
                            $degreeCertificateData->reference_no = $request->reference_no;
                            $degreeCertificateData->save_path = $save_url;
                            $degreeCertificateData->save();
                        }
                    }
                }

                if ($request->diploma_type != null) {

                    for ($i = 0; $i < count($request->diploma_type); $i++) {

                        $diplomaData = new Diploma();
                        $diplomaData->reference_no = $request->reference_no;
                        $diplomaData->type = $request->diploma_type_val[$i];
                        $diplomaData->course = $request->diploma_titel[$i];
                        $diplomaData->institute = $request->diploma_university[$i];
                        $diplomaData->duration = $request->diploma_duration[$i];
                        $diplomaData->year = $request->diploma_year[$i];
                        $diplomaData->save();
                    }
                }

                if ($request->hasFile('diploma_certificate')) {

                    $certificates = $request->file('diploma_certificate');

                    $availableDiplomaFileCount = DiplomaCertificate::where('reference_no', '=', session()->get('reference_no'))->count();

                    if ($availableDiplomaFileCount > 0) {

                        $maxnumber = DB::table('diploma_certificates')
                            ->where('reference_no', '=', session()->get('reference_no'))
                            ->select(DB::raw('MAX(id) as value'))
                            ->get();
                        $maxValue = json_decode($maxnumber, true);
                        $nextId = $maxValue[0]["value"] + 1;

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Diploma Certificate' . ($nextId) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/diplomas';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $diplomaCertificateData = new DiplomaCertificate();
                            $diplomaCertificateData->reference_no = $request->reference_no;
                            $diplomaCertificateData->save_path = $save_url;
                            $diplomaCertificateData->save();

                            $nextId++;
                        }
                    } else {

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Diploma Certificate' . ($key + 1) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/diplomas';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $diplomaCertificateData = new DiplomaCertificate();
                            $diplomaCertificateData->reference_no = $request->reference_no;
                            $diplomaCertificateData->save_path = $save_url;
                            $diplomaCertificateData->save();
                        }
                    }
                }

                if ($request->research_summary != null || $request->orcid != null) {

                    $researchCount = Research::where('reference_no', '=', session()->get('reference_no'))->count();
                    if ($researchCount > 0) {

                        $checkUpdate = Research::where('reference_no', '=', session()->get('reference_no'))
                            ->update([
                                'research_summary' => $request->research_summary,
                                'orcid' => $request->orcid
                            ]);
                    } else {

                        $researchData = new Research();
                        $researchData->reference_no = $request->reference_no;
                        $researchData->research_summary = $request->research_summary;
                        $researchData->orcid = $request->orcid;
                        $researchData->save();
                    }
                }

                if ($request->qulification_type != null) {

                    for ($i = 0; $i < count($request->qulification_type); $i++) {

                        $specialQulificationData = new SpecialQulification();
                        $specialQulificationData->reference_no = $request->reference_no;
                        $specialQulificationData->qulification_type = $request->qulification_type_val[$i];
                        $specialQulificationData->qulification_summary = $request->qulification_summary[$i];
                        $specialQulificationData->save();
                    }
                }

                if ($request->membership_type != null) {

                    for ($i = 0; $i < count($request->membership_type); $i++) {

                        $membershipData = new Membership();
                        $membershipData->reference_no = $request->reference_no;
                        $membershipData->membership_type = $request->membership_type_val[$i];
                        $membershipData->membership_university = $request->membership_university[$i];
                        $membershipData->membership_start_year = $request->membership_start_year[$i];
                        $membershipData->membership_end_year = $request->membership_end_year[$i];
                        $membershipData->membership_active_status = $request->membership_active_status_val[$i];
                        $membershipData->save();
                    }
                }

                if ($request->pqualification_name != null) {

                    for ($i = 0; $i < count($request->pqualification_name); $i++) {

                        $professionalQualificationData = new ProfessionalQualification();
                        $professionalQualificationData->reference_no = $request->reference_no;
                        $professionalQualificationData->name = $request->pqualification_name[$i];
                        $professionalQualificationData->university = $request->pqualification_university[$i];
                        $professionalQualificationData->start_year = $request->pqualification_start_year[$i];
                        $professionalQualificationData->end_year = $request->pqualification_end_year[$i];
                        $professionalQualificationData->save();
                    }
                }

                if ($request->experience_designation != null) {

                    for ($i = 0; $i < count($request->experience_designation); $i++) {

                        $employmentRecordData = new EmploymentRecord();
                        $employmentRecordData->reference_no = $request->reference_no;
                        $employmentRecordData->designation = $request->experience_designation[$i];
                        $employmentRecordData->institution = $request->experience_institution[$i];
                        $employmentRecordData->start_date = date("Y-m-d", strtotime($request->experience_start_date[$i]));
                        $employmentRecordData->end_date = date("Y-m-d", strtotime($request->experience_end_date[$i]));
                        $employmentRecordData->last_month_salary = number_format($request->experience_last_month_salary[$i], 2);
                        $employmentRecordData->save();
                    }
                }

                if ($request->hasFile('employment_record_certificate')) {

                    $certificates = $request->file('employment_record_certificate');

                    $availableEmploymentRecordFileCount = EmploymentRecordCertificate::where('reference_no', '=', session()->get('reference_no'))->count();

                    if ($availableEmploymentRecordFileCount > 0) {

                        $maxnumber = DB::table('employment_record_certificates')
                            ->where('reference_no', '=', session()->get('reference_no'))
                            ->select(DB::raw('MAX(id) as value'))
                            ->get();
                        $maxValue = json_decode($maxnumber, true);
                        $nextId = $maxValue[0]["value"] + 1;

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Employment Record Certificate' . ($nextId) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/employments';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $employmentRecordCertificateData = new EmploymentRecordCertificate();
                            $employmentRecordCertificateData->reference_no = $request->reference_no;
                            $employmentRecordCertificateData->save_path = $save_url;
                            $employmentRecordCertificateData->save();

                            $nextId++;
                        }
                    } else {

                        foreach ($certificates as $key => $file) {
                            // Customize the file storage path and naming convention as per your needs
                            $name_gen = 'Employment Record Certificate' . ($key + 1) . '' . '.' . $file->getClientOriginalExtension();
                            $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/employments';

                            if (!File::exists($path)) {
                                File::makeDirectory(storage_path() . $path, 0777, true, true);
                            }
                            $file->storeAs($path, $name_gen);
                            $save_url = $path . '/' . $name_gen;

                            $employmentRecordCertificateData = new EmploymentRecordCertificate();
                            $employmentRecordCertificateData->reference_no = $request->reference_no;
                            $employmentRecordCertificateData->save_path = $save_url;
                            $employmentRecordCertificateData->save();
                        }
                    }
                }

                if ($request->bond_institution != null) {

                    for ($i = 0; $i < count($request->bond_institution); $i++) {

                        $bondData = new Bond();
                        $bondData->reference_no = $request->reference_no;
                        $bondData->institute = $request->bond_institution[$i];
                        $bondData->from = date("Y-m-d", strtotime($request->bond_start_date[$i]));
                        $bondData->to = date("Y-m-d", strtotime($request->bond_end_date[$i]));
                        $bondData->bond_value = number_format($request->bond_total_value[$i], 2);
                        $bondData->save();
                    }
                }

                if ($request->referee_name != null) {

                    for ($i = 0; $i < count($request->referee_name); $i++) {

                        $refereeData = new Referee();
                        $refereeData->reference_no = $request->reference_no;
                        $refereeData->name = $request->referee_name[$i];
                        $refereeData->designation = $request->referee_designation[$i];
                        $refereeData->address = $request->referee_address[$i];
                        $refereeData->telephone = $request->referee_phome[$i];
                        $refereeData->email = $request->referee_email[$i];
                        $refereeData->save();
                    }
                }

                if ($request->hasFile('public_sector_letter')) {

                    $releaseLettersCount = ReleaseLetter::where('reference_no', '=', session()->get('reference_no'))->count();

                    $image = $request->file('public_sector_letter');
                    $name_gen = 'Release Letter' . '.' . $image->getClientOriginalExtension();
                    $path = 'public/uploads/vacancy/' . session()->get('reference_no') . '/release';
                    if (!File::exists($path)) {
                        File::makeDirectory(storage_path() . $path, 0777, true, true);
                    }
                    $request->file('public_sector_letter')->storeAs($path, $name_gen);
                    $save_url = $path . '/' . $name_gen;

                    if ($releaseLettersCount == 0) {

                        $releaseLetterData = new ReleaseLetter();
                        $releaseLetterData->reference_no = $request->reference_no;
                        $releaseLetterData->save_path = $save_url;
                        $releaseLetterData->save();
                    }
                }
            }





            Log::notice('IndexController -> save application data id - ' . session()->get('reference_no'));
            Log::info('IndexController -> save application ended');

            if ($request->has('save')) {

                $notification = array(
                    'message' => 'Data furnished up to now have been saved as a draft.',
                    'alert-type' => 'info'
                );

                return redirect()->route('vacancy.application')->with($notification);
            } elseif ($request->has('save1')) {

                $notification = array(
                    'message' => 'Your application data has been successfully saved.',
                    'alert-type' => 'success'
                );

                return redirect()->route('home')->with($notification);
            }
        } //save

    }

    public function applicationFinal()
    {

        Log::info('IndexController-> Application Final Screen get started');

        return view('frontend.final');

        Log::info('IndexController-> Application Final Screen ended');
    } //application registration end

    public function applicationDataDownloadOl1()
    {

        Log::info('IndexController-> Application Data Download OL Attempt 01 get started');

        $olSummarypath = OrdinaryLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('attempt', '=', 1)->get('file_path');
        $olSummarypath = json_decode($olSummarypath, true);
        $olSummarypath = $olSummarypath[0]["file_path"];
        //dd($olSummarypath);

        return Storage::download($olSummarypath);

        Log::info('IndexController-> Application Data Download OL Attempt 01 ended');
    } //ol attempt 01 data download

    public function applicationDataDownloadOl2()
    {

        Log::info('IndexController-> Application Data Download OL Attempt 02 get started');

        $olSummarypath = OrdinaryLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('attempt', '=', 2)->get('file_path');
        $olSummarypath = json_decode($olSummarypath, true);
        $olSummarypath = $olSummarypath[0]["file_path"];
        //dd($olSummarypath);


        return Storage::download($olSummarypath);

        Log::info('IndexController-> Application Data Download OL Attempt 02 ended');
    } //ol attempt 02 data download

    public function applicationDataDownloadAl1()
    {

        Log::info('IndexController-> Application Data Download AL Attempt 01 get started');

        $alSummarypath = AdvanceLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('level', '=', 1)->get('file_path');
        $alSummarypath = json_decode($alSummarypath, true);
        $alSummarypath = $alSummarypath[0]["file_path"];
        //dd($olSummarypath);

        return Storage::download($alSummarypath);

        Log::info('IndexController-> Application Data Download AL Attempt 01 ended');
    } //al attempt 01 data download

    public function applicationDataDownloadAl2()
    {

        Log::info('IndexController-> Application Data Download AL Attempt 02 get started');

        $alSummarypath = AdvanceLevelResultSummary::where('reference_no', '=', session()->get('reference_no'))->where('level', '=', 2)->get('file_path');
        $alSummarypath = json_decode($alSummarypath, true);
        $alSummarypath = $alSummarypath[0]["file_path"];
        //dd($olSummarypath);

        return Storage::download($alSummarypath);

        Log::info('IndexController-> Application Data Download AL Attempt 02 ended');
    } //al attempt 02 data download

    public function applicationDataDownloadReleaseLetter()
    {

        //Log::info('IndexController-> Application Data Download AL Attempt 02 get started');

        $path = ReleaseLetter::where('reference_no', '=', session()->get('reference_no'))->get('save_path');
        $path = json_decode($path, true);
        $path = $path[0]["save_path"];
        //dd($olSummarypath);

        return Storage::download($path);

        //Log::info('IndexController-> Application Data Download AL Attempt 02 ended');

    } //al attempt 02 data download

    public function applicationDataDownloadDegree($id)
    {

        Log::info('IndexController-> Application Data Download Degree get started');

        $degreepath = DegreeCertificate::find($id);
        return Storage::download($degreepath->save_path);

        Log::info('IndexController-> Application Data Download Degree ended');
    } //degree data download

    public function applicationDataDownloadDiploma($id)
    {

        Log::info('IndexController-> Application Data Download Diploma get started');

        $diplomapath = DiplomaCertificate::find($id);
        return Storage::download($diplomapath->save_path);

        Log::info('IndexController-> Application Data Download Diploma ended');
    } //diploma data download

    public function applicationDataDownloadEmploymentRecord($id)
    {

        Log::info('IndexController-> Application Data Download Diploma get started');

        $employmentpath = EmploymentRecordCertificate::find($id);
        return Storage::download($employmentpath->save_path);

        Log::info('IndexController-> Application Data Download Diploma ended');
    } //diploma data download


    public function degreeDelete($id)
    {

        $record = Degree::findOrFail($id);
        $record->delete();

        return response()->json(['message' => 'Record deleted successfully']);
    }

    public function degreeOldDelete(Request $request, $id)
    {

        $request->session()->forget('degree_type' . $id);
        $request->session()->forget('degree_type_val' . $id);
        $request->session()->forget('degree_titel' . $id);
        $request->session()->forget('degree_university' . $id);
        $request->session()->forget('degree_class' . $id);
        $request->session()->forget('degree_class_val' . $id);
        $request->session()->forget('eff_date' . $id);
        $request->session()->forget('degree_index_number' . $id);
        $request->session()->forget('degree_start' . $id);
        $request->session()->forget('degree_end' . $id);

        return response()->json(['message' => 'Old value deleted']);
    }

    public function degreeSubjectDelete($id)
    {

        $record = DegreeSubject::findOrFail($id);
        $record->delete();

        return response()->json(['message' => 'Record deleted successfully']);
    }

    public function degreeSubjectOldDelete(Request $request, $id)
    {

        $request->session()->forget('degree_main_subject' . $id);

        return response()->json(['message' => 'Old value deleted']);
    }

    public function applicationDataDeleteDegree($id)
    {

        $record = DegreeCertificate::findOrFail($id);
        $record->delete();

        unlink(storage_path() . '/app/' . $record->save_path);

        return response()->json(['message' => 'Record deleted successfully']);
    }


    public function diplomaDelete($id)
    {

        $record = Diploma::findOrFail($id);
        $record->delete();

        return response()->json(['message' => 'Record deleted successfully']);
    }

    public function diplomaOldDelete(Request $request, $id)
    {

        $request->session()->forget('diploma_type' . $id);
        $request->session()->forget('diploma_type_val' . $id);
        $request->session()->forget('diploma_titel' . $id);
        $request->session()->forget('diploma_university' . $id);
        $request->session()->forget('diploma_duration' . $id);
        $request->session()->forget('diploma_year' . $id);

        return response()->json(['message' => 'Old value deleted']);
    }

    public function applicationDataDeleteDiploma($id)
    {

        $record = DiplomaCertificate::findOrFail($id);
        $record->delete();

        unlink(storage_path() . '/app/' . $record->save_path);

        return response()->json(['message' => 'Record deleted successfully']);
    }


    public function squlificationDelete($id)
    {

        $record = SpecialQulification::findOrFail($id);
        $record->delete();

        return response()->json(['message' => 'Record deleted successfully']);
    }

    public function squlificationOldDelete(Request $request, $id)
    {

        $request->session()->forget('qulification_type' . $id);
        $request->session()->forget('qulification_type_val' . $id);
        $request->session()->forget('qulification_summary' . $id);

        return response()->json(['message' => 'Old value deleted']);
    }


    public function membershipDelete($id)
    {

        $record = Membership::findOrFail($id);
        $record->delete();

        return response()->json(['message' => 'Record deleted successfully']);
    }

    public function membershipOldDelete(Request $request, $id)
    {

        $request->session()->forget('membership_type' . $id);
        $request->session()->forget('membership_type_val' . $id);
        $request->session()->forget('membership_university' . $id);
        $request->session()->forget('membership_start_year' . $id);
        $request->session()->forget('membership_end_year' . $id);

        return response()->json(['message' => 'Old value deleted']);
    }

    public function pqulificationDelete($id)
    {

        $record = ProfessionalQualification::findOrFail($id);
        $record->delete();

        return response()->json(['message' => 'Record deleted successfully']);
    }

    public function pqulificationOldDelete(Request $request, $id)
    {

        $request->session()->forget('pqualification_name' . $id);
        $request->session()->forget('pqualification_university' . $id);
        $request->session()->forget('pqualification_start_year' . $id);
        $request->session()->forget('pqualification_end_year' . $id);

        return response()->json(['message' => 'Old value deleted']);
    }

    public function employmentRecordDelete($id)
    {

        $record = EmploymentRecord::findOrFail($id);
        $record->delete();

        return response()->json(['message' => 'Record deleted successfully']);
    }

    public function employmentRecordOldDelete(Request $request, $id)
    {

        $request->session()->forget('experience_designation' . $id);
        $request->session()->forget('experience_institution' . $id);
        $request->session()->forget('experience_start_date' . $id);
        $request->session()->forget('experience_end_date' . $id);
        $request->session()->forget('experience_last_month_salary' . $id);

        return response()->json(['message' => 'Old value deleted']);
    }

    public function applicationDataDeleteEmploymentRecord($id)
    {

        $record = EmploymentRecordCertificate::findOrFail($id);
        $record->delete();

        unlink(storage_path() . '/app/' . $record->save_path);

        return response()->json(['message' => 'Record deleted successfully']);
    }

    public function bondRecordDelete($id)
    {

        $record = Bond::findOrFail($id);
        $record->delete();

        return response()->json(['message' => 'Record deleted successfully']);
    }

    public function bondRecordOldDelete(Request $request, $id)
    {

        $request->session()->forget('bond_institution' . $id);
        $request->session()->forget('bond_start_date' . $id);
        $request->session()->forget('bond_end_date' . $id);
        $request->session()->forget('bond_total_value' . $id);

        return response()->json(['message' => 'Old value deleted']);
    }

    public function refereeRecordDelete($id)
    {

        $record = Referee::findOrFail($id);
        $record->delete();

        return response()->json(['message' => 'Record deleted successfully']);
    }

    public function refereeRecordOldDelete(Request $request, $id)
    {

        $request->session()->forget('referee_name' . $id);
        $request->session()->forget('referee_designation' . $id);
        $request->session()->forget('referee_address' . $id);
        $request->session()->forget('referee_phome' . $id);
        $request->session()->forget('referee_email' . $id);

        return response()->json(['message' => 'Old value deleted']);
    }

    public function releaseLetterDownload()
    {

        $path = public_path('/pdf/Consent to release letter.pdf');

        return response()->file($path, [
            'Content-Type' => 'application/pdf'
        ]);
    }

    public function noticeDownload($id)
    {

        $path = public_path('/pdf/Notice/' . $id . '.pdf');

        return response()->file($path, [
            'Content-Type' => 'application/pdf'
        ]);
    } //vacancy notice download

    public function sorDownload1()
    {

        $path = public_path('/pdf/SOR/2025.06.09 SOR - Medical-Dental.pdf');

        return response()->file($path, [
            'Content-Type' => 'application/pdf'
        ]);
    } //vacancy notice download

    public function sorDownload2()
    {

        $path = public_path('/pdf/SOR/2025.06.09 SOR - Non Medical-Dental.pdf');

        return response()->file($path, [
            'Content-Type' => 'application/pdf'
        ]);
    } //vacancy notice download

    public function detailInstruction()
    {

        return view('frontend.details_instruction');
    }

    public function photographInstruction()
    {

        $path = public_path('/pdf/Photograph/Specifications-of-the-Photograph.pdf');

        return response()->file($path, [
            'Content-Type' => 'application/pdf'
        ]);
    }
}
