<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('non_increments', function (Blueprint $table) {
            $table->integer('designation_id')->after('employee_no')->default(0);
            $table->integer('department_id')->after('designation_id')->default(0);
            $table->integer('flag')->after('ar_finalize_date')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('non_increments', function (Blueprint $table) {
            $table->dropColumn('designation_id');
            $table->dropColumn('department_id');
            $table->dropColumn('flag');
        });
    }
};
