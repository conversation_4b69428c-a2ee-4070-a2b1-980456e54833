<?php

namespace App\Http\Controllers\Backend\Vacancy;

use App\Http\Controllers\Controller;
use App\Http\Requests\DutyAssumeRequest;
use App\Mail\UJSWelcomeMail;
use App\Mail\WritingExamNotificationMail;
use App\Models\AdvanceLevelResult;
use App\Models\AdvanceLevelResultSummary;
use App\Models\ApplicationForm;
use App\Models\City;
use App\Models\ContractEmployee;
use App\Models\Department;
use App\Models\DepartmentSub;
use App\Models\Employee;
use App\Models\EmployeeSalaryStatus;
use App\Models\Faculty;
use App\Models\nav_experience;
use App\Models\nav_higher_eduacation_qulification;
use App\Models\nav_pdf_upload_path;
use App\Models\nav_professional_qulifications;
use App\Models\OrdinaryLevelResult;
use App\Models\OrdinaryLevelResultSummary;
use App\Models\PermanentEmployee;
use App\Models\TemporyEmployee;
use App\Models\User;
use App\Models\VancancyNonAcademic;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator as FacadesValidator;

class nacRecruitmentProcessController extends Controller
{

    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function shortList_page1_open()
    {
        Log::info('VacancyController -> vacancy index started');

        $mainBranch = Auth()->user()->main_branch_id;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {

            if (Auth()->user()->hasRole(['super-admin', 'administrator'])) {

                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 AND application_forms.short_list_status = 0 THEN 1 ELSE 0 END), 0) as pending'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 AND application_forms.short_list_status = 1 THEN 1 ELSE 0 END), 0) as selected'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 AND application_forms.short_list_status = 2 THEN 1 ELSE 0 END), 0) as rejected')
                    )
                    ->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name'
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->whereIn('vacancy_status_type_id', array(28, 29, 30, 31, 372))
                    ->get();


                //dd($vacancies);

                Log::info('VacancyController -> vacancies Count - ' . $vacancies->count());
                Log::info('VacancyController -> vacancy index ended');
            } else {
                $vacancies = array();
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['cc', 'sc'])) {


                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('vancancy_operator_non_academics', 'vancancy_operator_non_academics.vacancy_id', '=', 'vancancy_non_academics.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 AND application_forms.short_list_status = 0 THEN 1 ELSE 0 END), 0) as pending'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 AND application_forms.short_list_status = 1 THEN 1 ELSE 0 END), 0) as selected'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 AND application_forms.short_list_status = 2 THEN 1 ELSE 0 END), 0) as rejected')
                    )
                    ->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name'
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->where('vancancy_operator_non_academics.employee_no', auth()->user()->employee_no)
                    ->whereIn('vacancy_status_type_id', array(28, 29, 30, 31, 372))
                    ->get();
            } else {
                $vacancies = array();
            }
        }

        return view('admin.nonacademic_vacancy.recruitment_process.shortList_page1', compact('vacancies'));
    }

    public function shortList_page2_open($id)
    {

        $vacancyid = decrypt($id);

        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select(
                'designations.designation_name',
                'vancancy_non_academics.short_list_complete_status',
                'vancancy_non_academics.vacancy_status_type_id',
                'vancancy_non_academics.designation_category',
                'categories.display_name',
            )
            ->where('vancancy_non_academics.id', $vacancyid)->first();
        if ($vacancy) {
            if ($vacancy->display_name != '' && $vacancy->designation_category == 138) {
                $vacancy_name = $vacancy->designation_name . ' (' . $vacancy->display_name . ')';
            } else {

                $vacancy_name = $vacancy->designation_name;
            }

            $short_list_complete_status = $vacancy->short_list_complete_status;
            $vacancy_status = $vacancy->vacancy_status_type_id;
            $designation_category = $vacancy->designation_category;
        } else {
            $vacancy_name = '';
            $short_list_complete_status = 0;
            $vacancy_status = 0;
            $designation_category = 0;
        }

        $counts = ApplicationForm::select('short_list_status', DB::raw('count(*) as total'))
            ->where('vacancy_id', $vacancyid)
            ->where('protect_application', 1)
            ->groupBy('short_list_status')
            ->pluck('total', 'short_list_status');


        $countForPending = $counts[0] ?? 0;
        $countForAccept = $counts[1] ?? 0;
        $countForReject = $counts[2] ?? 0;


        $data_text = ApplicationForm::select(
            'id',
            'name_with_initials',
            'last_name',
            'nic',
            'created_at',
            'short_list_status',
            'short_list_remark',
            'telephone_mobile',
            'email_address'
        )
            ->where('vacancy_id', $vacancyid)
            ->where('protect_application', 1)
            ->orderBy('id')
            ->get();

        return view('admin.nonacademic_vacancy.recruitment_process.shortList_page2', compact(
            'data_text',
            'vacancy_name',
            'vacancy_status',
            'short_list_complete_status',
            'countForPending',
            'countForAccept',
            'countForReject',
            'designation_category',
            'vacancyid'
        ));
    }

    public function shortList_page3_open($id)
    {
        $appID = decrypt($id);
        // Find the application
        $applicationForm = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->select(
                'application_forms.*',
                't.category_name as title_name',
                'cs.category_name as civil_status_name'
            )
            ->where('application_forms.id', $appID)
            ->first();

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        // Get vacancy information with designation name
        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'designations.designation_name', 'categories.display_name')
            ->find($applicationForm->vacancy_id);

        // Get O/L results
        $olResults = OrdinaryLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 1)->get();

        $olResults2 = OrdinaryLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 2)->get();

        // Get O/L summaries
        $olSummary = OrdinaryLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 1)->first();

        $olSummary2 = OrdinaryLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 2)->first();

        // Get A/L results
        $alResults = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 1)->get();

        $alResults2 = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 2)->get();

        $alResults3 = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 3)->get();

        // Get A/L summaries
        $alSummary = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 1)->first();

        $alSummary2 = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 2)->first();

        $alSummary3 = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 3)->first();

        // Get higher education qualifications
        $higherEducation = nav_higher_eduacation_qulification::where('result_slot_id', $applicationForm->id)->get();

        // Get professional qualifications
        $proQualifications = nav_professional_qulifications::where('user_id', $applicationForm->id)->get();

        // Get experience information
        $experiences = nav_experience::where('user_id', $applicationForm->id)->get();

        // Get extra curricular data
        $extraCurricular = DB::table('nav_extra_curricular')->where('user_id', $applicationForm->id)->first();

        // Get all uploaded documents grouped by category
        $documents = nav_pdf_upload_path::where('user_id', $applicationForm->id)->get();

        $documentsByCategory = [
            'higher_education' => $documents->where('nav_cat_type', 1),
            'professional_qualifications' => $documents->where('nav_cat_type', 2),
            'experience' => $documents->where('nav_cat_type', 3),
            'extra_curricular' => $documents->where('nav_cat_type', 4),
            'public_sector' => $documents->where('nav_cat_type', 5),
            'driver_licenses' => $documents->where('nav_cat_type', 6),
        ];

        // Load reference data
        $titles = DB::table('categories')->where('category_type_id', '=', 5)->get();
        $civilStatuses = DB::table('categories')->where('category_type_id', '4')->get();
        $cities = City::all();
        $degreeTypes = DB::table('categories')->where('category_type_id', '16')->get();
        $alStreams = DB::table('categories')->where('category_type_id', '15')->get();

        return view('admin.nonacademic_vacancy.recruitment_process.shortList_page3', compact(
            'appID',
            'applicationForm',
            'vacancy',
            'olResults',
            'olResults2',
            'olSummary',
            'olSummary2',
            'alResults',
            'alResults2',
            'alResults3',
            'alSummary',
            'alSummary2',
            'alSummary3',
            'higherEducation',
            'proQualifications',
            'experiences',
            'extraCurricular',
            'documents',
            'documentsByCategory',
            'titles',
            'civilStatuses',
            'cities',
            'degreeTypes',
            'alStreams'
        ));
    }

    public function shortList_next_open($id)
    {

        try {
            $appID = decrypt($id);
        } catch (\Exception $e) {

            $notification = array(
                'message' => 'Invalid application ID.',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
            // return redirect()->route('nac.recruitment.process.shortList.page1.open');
        }

        $select_text = ApplicationForm::where('id', $appID)->first();

        if ($select_text) {
            $next_id = ApplicationForm::select('id', 'protect_application')
                ->where('id', '>', $appID)
                ->where('protect_application', 1)
                ->where('vacancy_id', $select_text->vacancy_id)
                ->orderBy('id')
                ->first();

            if ($next_id) {

                return redirect()->route('nac.recruitment.process.shortList.page3.open', encrypt($next_id->id));
            } else {
                $notification = array(
                    'message' => 'You have reached the last application. There are no more.',
                    'alert-type' => 'error'
                );
                return redirect()->back()->with($notification);
                // return redirect()->route('nac.recruitment.process.shortList.page1.open');
            }
        } else {
            $notification = array(
                'message' => 'Something wrong',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
        }
    }

    public function shortList_pervious_open($id)
    {

        try {
            $appID = decrypt($id);
        } catch (\Exception $e) {

            $notification = array(
                'message' => 'Invalid application ID.',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
            // return redirect()->route('nac.recruitment.process.shortList.page1.open');
        }

        $select_text = ApplicationForm::where('id', $appID)->first();

        if ($select_text) {

            $next_id = ApplicationForm::select('id', 'protect_application')
                ->where('id', '<', $appID)
                ->where('protect_application', 1)
                ->where('vacancy_id', $select_text->vacancy_id)
                ->orderBy('id', 'desc')
                ->first();

            if ($next_id) {

                // $notification = array(
                //     'message' => 'Successfully added',
                //     'alert-type' => 'success'
                // );

                return redirect()->route('nac.recruitment.process.shortList.page3.open', encrypt($next_id->id));
            } else {
                $notification = array(
                    'message' => 'You have reached the last application. There are no more.',
                    'alert-type' => 'error'
                );
                return redirect()->back()->with($notification);
                // return redirect()->route('nac.recruitment.process.shortList.page1.open');
            }
        } else {
            $notification = array(
                'message' => 'Something wrong',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
        }
    }

    public function shortList_store(Request $request)
    {

        $appID = $request->input('app_id');

        // Check which button was clicked
        $action = $request->input('action');

        if ($action == 'reject') {
            // Validate that remark is required for reject
            $validator = FacadesValidator::make($request->all(), [
                'remark' => 'required|string|max:1000',
            ]);

            if ($validator->fails()) {
                $notification = [
                    'message' => 'Please enter the remark',
                    'alert-type' => 'error'
                ];
                return redirect()->back()->withErrors($validator)->withInput()->with($notification);
            }
        }

        $application = ApplicationForm::find($appID);

        if (!$application) {
            return redirect()->back()->with([
                'message' => 'Something went wrong. Application not found.',
                'alert-type' => 'error'
            ]);
        }

        if ($action == 'accept') {
            $application->short_list_status = 1;
        } elseif ($action == 'reject') {
            $application->short_list_status = 2;
        }


        $application->short_list_remark = $request->input('remark');
        $application->short_list_user = auth()->user()->employee_no;
        $application->application_decision_id = 35;
        // $application->short_list_user_position = auth()->user()->position ?? null;
        $application->short_list_date = today();

        $application->save();

        return redirect()->route('nac.recruitment.process.shortList.nextPage.open', encrypt($appID))
            ->with([
                'message' => 'Application updated successfully.',
                'alert-type' => 'success'
            ]);
    }

    public function shortList_backToList($id)
    {
        try {
            $appID = decrypt($id);
        } catch (\Exception $e) {

            $notification = array(
                'message' => 'Invalid application ID.',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
            // return redirect()->route('nac.recruitment.process.shortList.page1.open');
        }

        $data_text = ApplicationForm::where('id', $appID)->first();

        if ($data_text) {
            return redirect()->route('nac.recruitment.process.shortList.page2.open', encrypt($data_text->vacancy_id));
        } else {
            return redirect()->back()->with([
                'message' => 'Something went wrong. Application not found.',
                'alert-type' => 'error'
            ]);
        }
    }

    public function shortListComplete(Request $request)
    {

        $request->validate([
            'proType' => 'required|in:1,2,3',  // must be one of these values, not 0 or empty
            'vacancyid' => 'required|integer|exists:vancancy_non_academics,id', // assuming your table is vancancy_non_academics
            'contributionMark' => 'required|numeric|min:0|max:100',
        ]);

        $update_text = VancancyNonAcademic::where('id', $request->vacancyid)->first();

        if ($update_text) {

            $update_text->vacancy_status_type_id = 29;
            $update_text->short_list_complete_status = 1;
            $update_text->short_list_complete_user = auth()->user()->employee_no;
            $update_text->short_list_complete_user_position = 0;
            $update_text->short_list_complete_date = today();

            if ($request->proType == 1) {

                $update_text->written_exam_status = 1;
                $update_text->written_exam_select_user = auth()->user()->employee_no;
                $update_text->written_exam_select_date = today();
                $update_text->written_exam_contribution_percentage = $request->contributionMark;
            }
            if ($request->proType == 2) {

                $update_text->practical_exam_status = 1;
                $update_text->practical_exam_select_user = auth()->user()->employee_no;
                $update_text->practical_exam_select_date = today();
                $update_text->practical_exam_contribution_percentage = $request->contributionMark;
            }
            if ($request->proType == 3) {

                $update_text->interview_status = 1;
                $update_text->interview_select_user = auth()->user()->employee_no;
                $update_text->interview_select_date = today();
                $update_text->interview_contribution_percentage = 100;
            }
            $update_text->save();

            $notification = array(
                'message' => 'Completed',
                'alert-type' => 'success'
            );
            return redirect()->route('nac.recruitment.process.shortList.page1.open')->with($notification);
        } else {
            $notification = array(
                'message' => 'Vacancy not found',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
        }
    }

    public function VacancyInterviewPendingList()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {

            if (Auth()->user()->hasRole(['super-admin', 'administrator'])) {

                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 AND application_forms.interview_status = 1 THEN 1 ELSE 0 END), 0) as interview'),
                    )
                    ->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status'
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->whereIn('vacancy_status_type_id', array(29, 30, 372))
                    ->get();
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 AND application_forms.interview_status = 1 THEN 1 ELSE 0 END), 0) as interview'),
                    )
                    ->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status'
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->whereIn('vacancy_status_type_id', array(29, 30, 372))
                    ->get();
            } else if (Auth()->user()->hasRole(['cc', 'sc'])) {


                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('vancancy_operator_non_academics', 'vancancy_operator_non_academics.vacancy_id', '=', 'vancancy_non_academics.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 AND application_forms.interview_status = 1 THEN 1 ELSE 0 END), 0) as interview'),
                    )
                    ->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status'
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->where('vancancy_operator_non_academics.employee_no', auth()->user()->employee_no)
                    ->whereIn('vacancy_status_type_id', array(29, 30, 372))
                    ->get();
            }
        }

        return view('admin.nonacademic_vacancy.shortlisted.index', compact('vacancies'));
    }

    public function VacancyInterviewPendingApplicationList($id)
    {

        $vacancyid = decrypt($id);

        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select(
                'designations.designation_name',
                'vancancy_non_academics.short_list_complete_status',
                'vancancy_non_academics.vacancy_status_type_id',
                'vancancy_non_academics.designation_category',
                'categories.display_name',
            )
            ->where('vancancy_non_academics.id', $vacancyid)->first();
        if ($vacancy) {
            if ($vacancy->display_name != '' && $vacancy->designation_category == 138) {
                $vacancy_name = $vacancy->designation_name . ' (' . $vacancy->display_name . ')';
            } else {

                $vacancy_name = $vacancy->designation_name;
            }

            $short_list_complete_status = $vacancy->short_list_complete_status;
            $vacancy_status = $vacancy->vacancy_status_type_id;
            $designation_category = $vacancy->designation_category;
        } else {
            $vacancy_name = '';
            $short_list_complete_status = 0;
            $vacancy_status = 0;
            $designation_category = 0;
        }


        $data_text = ApplicationForm::select(
            'id',
            'vacancy_id',
            'name_with_initials',
            'last_name',
            'nic',
            'created_at',
            'short_list_status',
            'short_list_remark'
        )
            ->where('vacancy_id', $vacancyid)
            ->where('protect_application', 1)
            ->where('application_decision_id', 35)
            // ->where('interview_status', 1)
            ->orderBy('id')
            ->get();

        return view('admin.nonacademic_vacancy.shortlisted.list', compact(
            'data_text',
            'vacancy_name',
            'vacancy_status',
            'short_list_complete_status',
            'designation_category',
            'vacancyid'
        ));
    }

    public function VacancyInterviewAssignList()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {

            if (Auth()->user()->hasRole(['super-admin', 'administrator'])) {

                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('interview_panels', 'vancancy_non_academics.interview_board_id', '=', 'interview_panels.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'interview_panels.panel_id',
                        'interview_panels.interview_date',
                        'interview_panels.interview_time',
                        'interview_panels.venue',
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 AND application_forms.interview_status = 1 THEN 1 ELSE 0 END), 0) as interview'),
                    )
                    ->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'interview_panels.panel_id',
                        'interview_panels.interview_date',
                        'interview_panels.interview_time',
                        'interview_panels.venue'
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->whereIn('vacancy_status_type_id', array(31))
                    //->whereRaw("DATE_ADD(interview_panels.interview_date, INTERVAL 20 WEEK) >= ?", [date('Y-m-d')])
                    ->get();


                //dd($vacancies);

                Log::info('VacancyController -> vacancies Count - ' . $vacancies->count());
                Log::info('VacancyController -> vacancy index ended');
            } else {
                $vacancies = array();
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['cc', 'sc'])) {


                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('interview_panels', 'vancancy_non_academics.interview_board_id', '=', 'interview_panels.id')
                    ->join('vancancy_operator_non_academics', 'vancancy_operator_non_academics.vacancy_id', '=', 'vancancy_non_academics.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'interview_panels.panel_id',
                        'interview_panels.interview_date',
                        'interview_panels.interview_time',
                        'interview_panels.venue',
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 AND application_forms.interview_status = 1 THEN 1 ELSE 0 END), 0) as interview'),
                    )
                    ->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'interview_panels.panel_id',
                        'interview_panels.interview_date',
                        'interview_panels.interview_time',
                        'interview_panels.venue'
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->where('vancancy_operator_non_academics.employee_no', auth()->user()->employee_no)
                    ->whereIn('vacancy_status_type_id', array(31))
                    ->whereRaw("DATE_ADD(interview_panels.interview_date, INTERVAL 2 WEEK) >= ?", [date('Y-m-d')])
                    ->get();
            } else {
                $vacancies = array();
            }
        }

        return view('admin.nonacademic_vacancy.interview.index', compact('vacancies'));
    }

    public function VacancyInterviewAssignApplicationList($id)
    {

        $vacancyid = decrypt($id);

        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select(
                'designations.designation_name',
                'vancancy_non_academics.short_list_complete_status',
                'vancancy_non_academics.vacancy_status_type_id',
                'vancancy_non_academics.designation_category',
                'categories.display_name',
            )
            ->where('vancancy_non_academics.id', $vacancyid)->first();
        if ($vacancy) {
            if ($vacancy->display_name != '' && $vacancy->designation_category == 138) {
                $vacancy_name = $vacancy->designation_name . ' (' . $vacancy->display_name . ')';
            } else {

                $vacancy_name = $vacancy->designation_name;
            }

            $short_list_complete_status = $vacancy->short_list_complete_status;
            $vacancy_status = $vacancy->vacancy_status_type_id;
            $designation_category = $vacancy->designation_category;
        } else {
            $vacancy_name = '';
            $short_list_complete_status = 0;
            $vacancy_status = 0;
            $designation_category = 0;
        }


        $data_text = ApplicationForm::select(
            'id',
            'vacancy_id',
            'name_with_initials',
            'last_name',
            'nic',
            'telephone_mobile',
            'created_at',
            'short_list_status',
            'short_list_remark'
        )
            ->where('vacancy_id', $vacancyid)
            ->where('protect_application', 1)
            ->where('application_decision_id', 35)
            ->where('interview_status', 1)
            ->orderBy('id')
            ->get();

        return view('admin.nonacademic_vacancy.interview.list', compact(
            'data_text',
            'vacancy_name',
            'vacancy_status',
            'short_list_complete_status',
            'designation_category',
            'vacancyid'
        ));
    }

    public function VacancyInterviewScheduleDownloadIndividualWebview(Request $request)
    {
        $appID = $request->input('app_id');
        $sn = $request->sn;

        $applicationForm = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->select(
                'application_forms.*',
                't.category_name as title_name',
                'cs.category_name as civil_status_name'
            )
            ->where('application_forms.id', $appID)
            ->first();

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        // Get vacancy information with designation name
        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('interview_panels', 'vancancy_non_academics.interview_board_id', '=', 'interview_panels.id')
            ->select('vancancy_non_academics.*', 'designations.designation_name', 'categories.display_name', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue')
            ->find($applicationForm->vacancy_id);

        // Get O/L results
        $olResults = OrdinaryLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 1)->get();

        $olResults2 = OrdinaryLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 2)->get();

        // Get O/L summaries
        $olSummary = OrdinaryLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 1)->first();

        $olSummary2 = OrdinaryLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 2)->first();

        // Get A/L results
        $alResults = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 1)->get();

        $alResults2 = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 2)->get();

        $alResults3 = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 3)->get();

        // Get A/L summaries
        $alSummary = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 1)->first();

        $alSummary2 = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 2)->first();

        $alSummary3 = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 3)->first();

        // Get higher education qualifications
        $higherEducation = nav_higher_eduacation_qulification::where('result_slot_id', $applicationForm->id)->get();

        // Get professional qualifications
        $proQualifications = nav_professional_qulifications::where('user_id', $applicationForm->id)->get();

        // Get experience information
        $experiences = nav_experience::where('user_id', $applicationForm->id)->get();

        // Get extra curricular data
        $extraCurricular = DB::table('nav_extra_curricular')->where('user_id', $applicationForm->id)->first();

        // Get all uploaded documents grouped by category
        $documents = nav_pdf_upload_path::where('user_id', $applicationForm->id)->get();

        $documentsByCategory = [
            'higher_education' => $documents->where('nav_cat_type', 1),
            'professional_qualifications' => $documents->where('nav_cat_type', 2),
            'experience' => $documents->where('nav_cat_type', 3),
            'extra_curricular' => $documents->where('nav_cat_type', 4),
            'public_sector' => $documents->where('nav_cat_type', 5),
            'driver_licenses' => $documents->where('nav_cat_type', 6),
        ];

        // Load reference data
        $titles = DB::table('categories')->where('category_type_id', '=', 5)->get();
        $civilStatuses = DB::table('categories')->where('category_type_id', '4')->get();
        $cities = City::all();
        $degreeTypes = DB::table('categories')->where('category_type_id', '16')->get();
        $alStreams = DB::table('categories')->where('category_type_id', '15')->get();

        return view('admin.nonacademic_vacancy.interview.induvidule_web', compact(
            'appID',
            'sn',
            'applicationForm',
            'vacancy',
            'olResults',
            'olResults2',
            'olSummary',
            'olSummary2',
            'alResults',
            'alResults2',
            'alResults3',
            'alSummary',
            'alSummary2',
            'alSummary3',
            'higherEducation',
            'proQualifications',
            'experiences',
            'extraCurricular',
            'documents',
            'documentsByCategory',
            'titles',
            'civilStatuses',
            'cities',
            'degreeTypes',
            'alStreams'
        ));
    }

    public function VacancyInterviewScheduleIndividualWebview(Request $request)
    {

        $appID = $request->input('app_id');
        $sn = $request->sn;

        $applicationForm = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->select(
                'application_forms.*',
                't.category_name as title_name',
                'cs.category_name as civil_status_name'
            )
            ->where('application_forms.id', $appID)
            ->first();

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        // Get vacancy information with designation name
        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('interview_panels', 'vancancy_non_academics.interview_board_id', '=', 'interview_panels.id')
            ->select('vancancy_non_academics.*', 'designations.designation_name', 'categories.display_name', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue')
            ->find($applicationForm->vacancy_id);

        // Get O/L results
        $olResults = OrdinaryLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 1)->get();

        $olResults2 = OrdinaryLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 2)->get();

        // Get O/L summaries
        $olSummary = OrdinaryLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 1)->first();

        $olSummary2 = OrdinaryLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 2)->first();

        // Get A/L results
        $alResults = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 1)->get();

        $alResults2 = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 2)->get();

        $alResults3 = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->where('attempt', 3)->get();

        // Get A/L summaries
        $alSummary = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 1)->first();

        $alSummary2 = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 2)->first();

        $alSummary3 = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->where('attempt', 3)->first();

        // Get higher education qualifications
        $higherEducation = nav_higher_eduacation_qulification::where('result_slot_id', $applicationForm->id)->get();

        // Get professional qualifications
        $proQualifications = nav_professional_qulifications::where('user_id', $applicationForm->id)->get();

        // Get experience information
        $experiences = nav_experience::where('user_id', $applicationForm->id)->get();

        // Get extra curricular data
        $extraCurricular = DB::table('nav_extra_curricular')->where('user_id', $applicationForm->id)->first();

        // Get all uploaded documents grouped by category
        $documents = nav_pdf_upload_path::where('user_id', $applicationForm->id)->get();

        $documentsByCategory = [
            'higher_education' => $documents->where('nav_cat_type', 1),
            'professional_qualifications' => $documents->where('nav_cat_type', 2),
            'experience' => $documents->where('nav_cat_type', 3),
            'extra_curricular' => $documents->where('nav_cat_type', 4),
            'public_sector' => $documents->where('nav_cat_type', 5),
            'driver_licenses' => $documents->where('nav_cat_type', 6),
        ];

        // Load reference data
        $titles = DB::table('categories')->where('category_type_id', '=', 5)->get();
        $civilStatuses = DB::table('categories')->where('category_type_id', '4')->get();
        $cities = City::all();
        $degreeTypes = DB::table('categories')->where('category_type_id', '16')->get();
        $alStreams = DB::table('categories')->where('category_type_id', '15')->get();

        return view('admin.nonacademic_vacancy.interview.induvidule_web_view', compact(
            'appID',
            'sn',
            'applicationForm',
            'vacancy',
            'olResults',
            'olResults2',
            'olSummary',
            'olSummary2',
            'alResults',
            'alResults2',
            'alResults3',
            'alSummary',
            'alSummary2',
            'alSummary3',
            'higherEducation',
            'proQualifications',
            'experiences',
            'extraCurricular',
            'documents',
            'documentsByCategory',
            'titles',
            'civilStatuses',
            'cities',
            'degreeTypes',
            'alStreams'
        ));
    }

    public function VacancyInterviewScheduleBulkWebview(Request $request)
    {
        $vacancyid = $request->input('vacancy_id');

        $applications = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->select(
                'application_forms.*',
                't.category_name as title_name',
                'cs.category_name as civil_status_name'
            )
            ->where('application_forms.vacancy_id', $vacancyid)
            ->where('protect_application', 1)
            ->where('application_decision_id', 35)
            ->where('interview_status', 1)
            ->orderBy('id')
            ->get();

        if ($applications->isEmpty()) {
            return redirect()->back()->with('error', 'No applications found for bulk processing.');
        }

        // Get vacancy information with designation name for the first application
        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('interview_panels', 'vancancy_non_academics.interview_board_id', '=', 'interview_panels.id')
            ->select('vancancy_non_academics.*', 'designations.designation_name', 'categories.display_name', 'interview_panels.interview_date', 'interview_panels.interview_time', 'interview_panels.venue', 'interview_panels.panel_id')
            ->find($vacancyid);

        // Load reference data
        $titles = DB::table('categories')->where('category_type_id', '=', 5)->get();
        $civilStatuses = DB::table('categories')->where('category_type_id', '4')->get();
        $cities = City::all();
        $degreeTypes = DB::table('categories')->where('category_type_id', '16')->get();
        $alStreams = DB::table('categories')->where('category_type_id', '15')->get();

        // Prepare bulk data for each application
        $bulkData = [];
        foreach ($applications as $application) {
            $appData = [
                'application' => $application,
                'olResults' => OrdinaryLevelResult::where('result_slot_id', $application->id)->get()->groupBy('attempt'),
                'olSummaries' => OrdinaryLevelResultSummary::where('reference_no', $application->id)->get()->keyBy('attempt'),
                'alResults' => AdvanceLevelResult::where('result_slot_id', $application->id)->get()->groupBy('attempt'),
                'alSummaries' => AdvanceLevelResultSummary::where('reference_no', $application->id)->get()->keyBy('attempt'),
                'higherEducation' => nav_higher_eduacation_qulification::where('result_slot_id', $application->id)->get(),
                'proQualifications' => nav_professional_qulifications::where('user_id', $application->id)->get(),
                'experiences' => nav_experience::where('user_id', $application->id)->get(),
                'extraCurricular' => DB::table('nav_extra_curricular')->where('user_id', $application->id)->first(),
                'documents' => nav_pdf_upload_path::where('user_id', $application->id)->get()->groupBy('nav_cat_type')
            ];
            $bulkData[] = $appData;
        }

        return view('admin.nonacademic_vacancy.interview.bulk_web_view', compact(
            'bulkData',
            'vacancy',
            'titles',
            'civilStatuses',
            'cities',
            'degreeTypes',
            'alStreams'
        ));
    }

    public function WritingExamResultPendingList()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {

            if (Auth()->user()->hasRole(['super-admin', 'administrator'])) {

                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        'vancancy_non_academics.written_exam_contribution_percentage'
                    )->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        'vancancy_non_academics.written_exam_contribution_percentage',
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->whereIn('vacancy_status_type_id', array(29))
                    ->where('vancancy_non_academics.written_exam_status', 1)
                    ->get();
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        'vancancy_non_academics.written_exam_contribution_percentage'
                    )->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        'vancancy_non_academics.written_exam_contribution_percentage',
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->whereIn('vacancy_status_type_id', array(29))
                    ->where('vancancy_non_academics.written_exam_status', 1)
                    ->get();
            } else if (Auth()->user()->hasRole(['cc', 'sc'])) {


                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('vancancy_operator_non_academics', 'vancancy_operator_non_academics.vacancy_id', '=', 'vancancy_non_academics.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        'vancancy_non_academics.written_exam_contribution_percentage'
                    )->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        'vancancy_non_academics.written_exam_contribution_percentage',
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->where('vancancy_operator_non_academics.employee_no', auth()->user()->employee_no)
                    ->whereIn('vacancy_status_type_id', array(29))
                    ->where('vancancy_non_academics.written_exam_status', 1)
                    ->get();
            }
        }

        return view('admin.nonacademic_vacancy.written_exam.index', compact('vacancies'));
    }

    public function WritingExamResultEntryList($id)
    {

        $vacancyid = decrypt($id);

        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select(
                'designations.designation_name',
                'vancancy_non_academics.short_list_complete_status',
                'vancancy_non_academics.vacancy_status_type_id',
                'vancancy_non_academics.designation_category',
                'categories.display_name',
                'vancancy_non_academics.written_exam_status'
            )
            ->where('vancancy_non_academics.id', $vacancyid)->first();
        if ($vacancy) {
            if ($vacancy->display_name != '' && $vacancy->designation_category == 138) {
                $vacancy_name = $vacancy->designation_name . ' (' . $vacancy->display_name . ')';
            } else {

                $vacancy_name = $vacancy->designation_name;
            }

            $short_list_complete_status = $vacancy->short_list_complete_status;
            $vacancy_status = $vacancy->vacancy_status_type_id;
            $written_exam_status = $vacancy->written_exam_status;
            $designation_category = $vacancy->designation_category;
        } else {
            $vacancy_name = '';
            $short_list_complete_status = 0;
            $vacancy_status = 0;
            $designation_category = 0;
            $written_exam_status = 0;
        }


        $data_text = ApplicationForm::select(
            'id',
            'vacancy_id',
            'name_with_initials',
            'last_name',
            'nic',
            'written_exam_status',
            'written_marks',
            'short_list_status',
            'short_list_remark'
        )
            ->where('vacancy_id', $vacancyid)
            ->where('protect_application', 1)
            ->where('application_decision_id', 35)
            ->where('short_list_status', 1)
            ->orderBy('id')
            ->get();

        $counts = ApplicationForm::select('written_exam_status', DB::raw('count(*) as total'))
            ->where('vacancy_id', $vacancyid)
            ->where('protect_application', 1)
            ->where('application_decision_id', 35)
            ->where('short_list_status', 1)
            ->groupBy('written_exam_status')
            ->pluck('total', 'written_exam_status');


        $countForPending = $counts[0] ?? 0;
        //dd($countForPending);

        return view('admin.nonacademic_vacancy.written_exam.list', compact(
            'data_text',
            'vacancy_name',
            'vacancy_status',
            'countForPending',
            'short_list_complete_status',
            'designation_category',
            'written_exam_status',
            'vacancyid',

        ));
    }

    public function WritingExamResultCheckViewShow($id)
    {
        $appID = decrypt($id);
        // Find the application
        $applicationForm = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->select(
                'application_forms.*',
                't.category_name as title_name',
                'cs.category_name as civil_status_name'
            )
            ->where('application_forms.id', $appID)
            ->first();

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        // Get vacancy information with designation name
        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'designations.designation_name', 'categories.display_name')
            ->find($applicationForm->vacancy_id);


        // Load reference data
        $titles = DB::table('categories')->where('category_type_id', '=', 5)->get();
        $civilStatuses = DB::table('categories')->where('category_type_id', '4')->get();
        $cities = City::all();

        return view('admin.nonacademic_vacancy.written_exam.show', compact(
            'appID',
            'applicationForm',
            'vacancy',
            'titles',
            'civilStatuses',
            'cities'
        ));
    }

    public function WritingExamResultStore(Request $request)
    {
        $request->validate([
            'written_marks' => 'nullable|max:' . $request->written_exam_contribution_percentage,
            'recom' => 'required',
        ]);

        $applicationForm = ApplicationForm::find($request->app_id);
        $applicationForm->written_exam_status = $request->recom;
        $applicationForm->written_marks = $request->written_marks;
        $applicationForm->save();

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        $notification = array(
            'message' => 'Exam result added successfully',
            'alert-type' => 'success'
        );
        return redirect()->route('nac.vacancy.writing.exam.result.entry.list', encrypt($applicationForm->vacancy_id))->with($notification);
    }

    public function WritingExamResultFinalize(Request $request)
    {

        $request->validate([
            'proType' => 'required|in:1,2,3',  // must be one of these values, not 0 or empty
            'vacancyid' => 'required|integer|exists:vancancy_non_academics,id', // assuming your table is vancancy_non_academics
            'contributionMark' => 'required|numeric|min:0|max:100',
        ]);

        $update_text = VancancyNonAcademic::where('id', $request->vacancyid)->first();

        //dd($request->proType);

        if ($update_text) {

            $update_text->vacancy_status_type_id = 30;
            $update_text->written_exam_status = 2;
            $update_text->written_exam_complete_user = auth()->user()->employee_no;
            $update_text->written_exam_complete_date = today();

            if ($request->proType == 2) {

                $update_text->practical_exam_status = 1;
                $update_text->practical_exam_select_user = auth()->user()->employee_no;
                $update_text->practical_exam_select_date = today();
                $update_text->practical_exam_contribution_percentage = $request->contributionMark;
            }
            if ($request->proType == 3) {

                $update_text->interview_status = 1;
                $update_text->interview_select_user = auth()->user()->employee_no;
                $update_text->interview_select_date = today();
                $update_text->interview_contribution_percentage = 100 - $update_text->written_exam_contribution_percentage;
            }
            $update_text->save();

            $notification = array(
                'message' => 'Written exam result finalize successfully',
                'alert-type' => 'success'
            );
            return redirect()->route('nac.vacancy.writing.exam.result.pending.list')->with($notification);
        } else {
            $notification = array(
                'message' => 'Vacancy not found',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
        }
    }

    public function WritingExamNotificationSend($id)
    {
        $vacancyId = decrypt($id);

        $update_text = VancancyNonAcademic::where('id', $vacancyId)->first();

        $applicationForm = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->select(
                'application_forms.*',
                't.category_name as title_name',
                'cs.category_name as civil_status_name'
            )
            ->where('application_forms.vacancy_id', $vacancyId)
            ->where('application_forms.application_decision_id', 35)
            ->get();

        foreach ($applicationForm as $value) {


            $emailData = [
                'reference_no' => str_pad($value->id, 3, '0', STR_PAD_LEFT),
                'name' => $value->title_name . ' ' . $value->initials . ' ' . $value->last_name,
                'vacancy' => $update_text->designations->designation_name,
                'date' => date('d.m.Y', strtotime($update_text->written_exam_date)),
                'time' => date('h:i A', strtotime($update_text->written_exam_time)),
                'venue' => 'Science Building - USJ',
                'index_no' => 123525,
            ];


            $message = "Dear {$emailData['name']},\n"
                . "Your written exam for the post of {$emailData['vacancy']} is scheduled on:\n"
                . "Date: {$emailData['date']}\n"
                . "Time: {$emailData['time']}\n"
                . "Venue: {$emailData['venue']}\n\n"
                . "Application No: {$emailData['reference_no']}\n"
                . "Index No: {$emailData['index_no']}\n\n"
                . "Please make sure to arrive at the venue at least 15 minutes before the scheduled time.\n"
                . "Bring your NIC or valid identification and this index number.\n\n"
                . "Thank you.";


            $mobile = $value->telephone_mobile;


            $response = Http::withHeaders([
                'Cookie' => 'incap_ses_1220_2724997=...; nlbi_2724997=...; visid_incap_2724997=...'
            ])->get('https://cpsolutions.dialog.lk/api/sms/inline/send.php', [
                'q' => '',
                'destination' => $mobile,
                'message' => $message,
            ]);

            if ($response->successful()) {
                Log::info("SMS sent to {$mobile}: {$message}");
                $value->written_exam_notification_status = 1;
                $value->save();
            } else {
                Log::error("Failed to send SMS to {$mobile}: " . $response->body());
            }
        }

        $notification = array(
            'message' => 'Exam notification sent successfully',
            'alert-type' => 'success'
        );
        return redirect()->route('nac.vacancy.writing.exam.result.pending.list', encrypt($vacancyId))->with($notification);
    }


    public function WritingExamEmailNotificationSend($id)
    {
        $vacancyId = decrypt($id);

        $update_text = VancancyNonAcademic::where('id', $vacancyId)->first();

        $applicationForm = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->select(
                'application_forms.*',
                't.category_name as title_name',
                'cs.category_name as civil_status_name'
            )
            ->where('application_forms.vacancy_id', $vacancyId)
            ->where('application_forms.application_decision_id', 35)
            ->where('application_forms.written_exam_email_notification_status', 0)
            ->get();

        foreach ($applicationForm as $value) {

            $emailData = [
                'reference_no' => str_pad($value->id, 3, '0', STR_PAD_LEFT),
                'name' => $value->title_name . ' ' . $value->initials . ' ' . $value->last_name,
                'vacancy' => $update_text->designations->designation_name,
                'date' => date('d.m.Y', strtotime($update_text->written_exam_date)),
                'time' => date('h:i A', strtotime($update_text->written_exam_time)),
                'venue' => 'Science Building - USJ',
                'index_no' => 123525,
            ];

            $email = $value->email_address;

            $mail = new WritingExamNotificationMail($emailData);
            //sending email
            Mail::to($email)->send($mail);

            $value->written_exam_email_notification_status = 1;
            $value->save();
        }

        $notification = array(
            'message' => 'Exam notification sent successfully',
            'alert-type' => 'success'
        );
        return redirect()->route('nac.vacancy.writing.exam.result.pending.list', encrypt($vacancyId))->with($notification);
    }

    public function PracticalExamResultPendingList()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {

            if (Auth()->user()->hasRole(['super-admin', 'administrator'])) {

                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        'vancancy_non_academics.practical_exam_contribution_percentage'
                    )->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        'vancancy_non_academics.practical_exam_contribution_percentage',
                    )->orderByDesc('vancancy_non_academics.id')
                    ->whereIn('vacancy_status_type_id', array(29, 30))
                    ->where('vancancy_non_academics.practical_exam_status', 1)
                    ->get();
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        'vancancy_non_academics.practical_exam_contribution_percentage'
                    )->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        'vancancy_non_academics.practical_exam_contribution_percentage',
                    )->orderByDesc('vancancy_non_academics.id')
                    ->whereIn('vacancy_status_type_id', array(29, 30))
                    ->where('vancancy_non_academics.practical_exam_status', 1)
                    ->get();

            } else if (Auth()->user()->hasRole(['cc', 'sc'])) {


                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('vancancy_operator_non_academics', 'vancancy_operator_non_academics.vacancy_id', '=', 'vancancy_non_academics.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        'vancancy_non_academics.practical_exam_contribution_percentage'
                    )->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'vancancy_non_academics.written_exam_status',
                        'vancancy_non_academics.practical_exam_status',
                        'vancancy_non_academics.interview_status',
                        'vancancy_non_academics.practical_exam_contribution_percentage',
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->where('vancancy_operator_non_academics.employee_no', auth()->user()->employee_no)
                    ->whereIn('vacancy_status_type_id', array(29, 30))
                    ->where('vancancy_non_academics.practical_exam_status', 1)
                    ->get();
            }
        }

        return view('admin.nonacademic_vacancy.practical_exam.index', compact('vacancies'));
    }

    public function PracticalExamResultEntryList($id)
    {

        $vacancyid = decrypt($id);

        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select(
                'designations.designation_name',
                'vancancy_non_academics.short_list_complete_status',
                'vancancy_non_academics.vacancy_status_type_id',
                'vancancy_non_academics.designation_category',
                'categories.display_name',
                'vancancy_non_academics.practical_exam_status',
                'vancancy_non_academics.written_exam_status',
            )
            ->where('vancancy_non_academics.id', $vacancyid)->first();

        if ($vacancy->display_name != '' && $vacancy->designation_category == 138) {
            $vacancy_name = $vacancy->designation_name . ' (' . $vacancy->display_name . ')';
        } else {

            $vacancy_name = $vacancy->designation_name;
        }

        $short_list_complete_status = $vacancy->short_list_complete_status;
        $vacancy_status = $vacancy->vacancy_status_type_id;
        $practical_exam_status = $vacancy->practical_exam_status;
        $designation_category = $vacancy->designation_category;
        $written_exam_status = $vacancy->written_exam_status;

        if ($practical_exam_status == 1 && $written_exam_status == 2) {
            $data_text = ApplicationForm::select(
                'id',
                'vacancy_id',
                'name_with_initials',
                'last_name',
                'nic',
                'practical_exam_status',
                'practical_marks',
            )
                ->where('vacancy_id', $vacancyid)
                ->where('protect_application', 1)
                ->where('application_decision_id', 35)
                ->where('written_exam_status', 1)
                ->orderBy('id')
                ->get();

            $counts = ApplicationForm::select('practical_exam_status', DB::raw('count(*) as total'))
                ->where('vacancy_id', $vacancyid)
                ->where('protect_application', 1)
                ->where('application_decision_id', 35)
                ->where('written_exam_status', 1)
                ->groupBy('practical_exam_status')
                ->pluck('total', 'practical_exam_status');


            $countForPending = $counts[0] ?? 0;
        } else {
            $data_text = ApplicationForm::select(
                'id',
                'vacancy_id',
                'name_with_initials',
                'last_name',
                'nic',
                'practical_exam_status',
                'practical_marks',
            )
                ->where('vacancy_id', $vacancyid)
                ->where('protect_application', 1)
                ->where('application_decision_id', 35)
                ->where('short_list_status', 1)
                ->orderBy('id')
                ->get();

            $counts = ApplicationForm::select('practical_exam_status', DB::raw('count(*) as total'))
                ->where('vacancy_id', $vacancyid)
                ->where('protect_application', 1)
                ->where('application_decision_id', 35)
                ->where('short_list_status', 1)
                ->groupBy('practical_exam_status')
                ->pluck('total', 'practical_exam_status');


            $countForPending = $counts[0] ?? 0;
        }

        return view('admin.nonacademic_vacancy.practical_exam.list', compact(
            'data_text',
            'vacancy_name',
            'vacancy_status',
            'countForPending',
            'short_list_complete_status',
            'designation_category',
            'practical_exam_status',
            'vacancyid',

        ));
    }

    public function PracticalExamResultCheckViewShow($id)
    {
        $appID = decrypt($id);
        // Find the application
        $applicationForm = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->select(
                'application_forms.*',
                't.category_name as title_name',
                'cs.category_name as civil_status_name'
            )
            ->where('application_forms.id', $appID)
            ->first();

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        // Get vacancy information with designation name
        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'designations.designation_name', 'categories.display_name')
            ->find($applicationForm->vacancy_id);


        // Load reference data
        $titles = DB::table('categories')->where('category_type_id', '=', 5)->get();
        $civilStatuses = DB::table('categories')->where('category_type_id', '4')->get();
        $cities = City::all();

        return view('admin.nonacademic_vacancy.practical_exam.show', compact(
            'appID',
            'applicationForm',
            'vacancy',
            'titles',
            'civilStatuses',
            'cities'
        ));
    }

    public function PracticalExamResultStore(Request $request)
    {
        $request->validate([
            'practical_marks' => 'nullable|max:' . $request->practical_exam_contribution_percentage,
            'recom' => 'required',
        ]);

        $applicationForm = ApplicationForm::find($request->app_id);
        $applicationForm->practical_exam_status = $request->recom;
        $applicationForm->practical_marks = $request->practical_marks;
        $applicationForm->save();

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        $notification = array(
            'message' => 'Exam result added successfully',
            'alert-type' => 'success'
        );
        return redirect()->route('nac.vacancy.practical.exam.result.entry.list', encrypt($applicationForm->vacancy_id))->with($notification);
    }

    public function PracticalExamResultFinalize(Request $request)
    {

        $request->validate([
            'proType' => 'required|in:1,2,3',  // must be one of these values, not 0 or empty
            'vacancyid' => 'required|integer|exists:vancancy_non_academics,id', // assuming your table is vancancy_non_academics
            'contributionMark' => 'required|numeric|min:0|max:100',
        ]);

        $update_text = VancancyNonAcademic::where('id', $request->vacancyid)->first();

        if ($update_text) {

            $update_text->vacancy_status_type_id = 372;
            $update_text->practical_exam_status = 2;
            $update_text->practical_exam_complete_user = auth()->user()->employee_no;
            $update_text->practical_exam_complete_date = today();

            if ($request->proType == 3) {

                $update_text->interview_status = 1;
                $update_text->interview_select_user = auth()->user()->employee_no;
                $update_text->interview_select_date = today();
                if ($update_text->written_exam_status == 2) {
                    $update_text->interview_contribution_percentage = 100 - $update_text->written_exam_contribution_percentage - $update_text->practical_exam_contribution_percentage;
                } else {
                    $update_text->interview_contribution_percentage = 100 - $update_text->practical_exam_contribution_percentage;
                }
            }
            $update_text->save();

            $notification = array(
                'message' => 'Practical exam result finalize successfully',
                'alert-type' => 'success'
            );
            return redirect()->route('nac.vacancy.practical.exam.result.pending.list')->with($notification);
        } else {
            $notification = array(
                'message' => 'Vacancy not found',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
        }
    }

    public function InterviewResultPendingList()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {

            if (Auth()->user()->hasRole(['super-admin', 'administrator'])) {

                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('interview_panels', 'vancancy_non_academics.interview_board_id', '=', 'interview_panels.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'interview_panels.panel_id',
                        'interview_panels.interview_date',
                        'interview_panels.interview_time',
                        'interview_panels.venue',
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 AND application_forms.interview_status IN (1,2,3,4) THEN 1 ELSE 0 END), 0) as interview')
                    )
                    ->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'interview_panels.panel_id',
                        'interview_panels.interview_date',
                        'interview_panels.interview_time',
                        'interview_panels.venue'
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->whereIn('vacancy_status_type_id', array(31))
                    ->where('interview_panels.interview_date', '<=', date("Y-m-d"))
                    ->get();
            } else {
                $vacancies = array();
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['cc', 'sc'])) {


                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('interview_panels', 'vancancy_non_academics.interview_board_id', '=', 'interview_panels.id')
                    ->join('vancancy_operator_non_academics', 'vancancy_operator_non_academics.vacancy_id', '=', 'vancancy_non_academics.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'interview_panels.panel_id',
                        'interview_panels.interview_date',
                        'interview_panels.interview_time',
                        'interview_panels.venue',
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.protect_application = 1 AND application_forms.interview_status IN (1,2,3,4) THEN 1 ELSE 0 END), 0) as interview'),
                    )
                    ->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        'interview_panels.panel_id',
                        'interview_panels.interview_date',
                        'interview_panels.interview_time',
                        'interview_panels.venue'
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->where('vancancy_operator_non_academics.employee_no', auth()->user()->employee_no)
                    ->whereIn('vacancy_status_type_id', array(31))
                    ->where('interview_panels.interview_date', '<=', date("Y-m-d"))
                    ->get();
            } else {
                $vacancies = array();
            }
        }

        return view('admin.nonacademic_vacancy.interview_result.index', compact('vacancies'));
    }

    public function InterviewResultEntryList($id)
    {

        $vacancyid = decrypt($id);

        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select(
                'designations.designation_name',
                'vancancy_non_academics.short_list_complete_status',
                'vancancy_non_academics.vacancy_status_type_id',
                'vancancy_non_academics.designation_category',
                'categories.display_name',
                'vancancy_non_academics.practical_exam_status',
                'vancancy_non_academics.written_exam_status',
                'vancancy_non_academics.interview_status',
            )
            ->where('vancancy_non_academics.id', $vacancyid)->first();

        if ($vacancy->display_name != '' && $vacancy->designation_category == 138) {
            $vacancy_name = $vacancy->designation_name . ' (' . $vacancy->display_name . ')';
        } else {

            $vacancy_name = $vacancy->designation_name;
        }

        $short_list_complete_status = $vacancy->short_list_complete_status;
        $vacancy_status = $vacancy->vacancy_status_type_id;
        $practical_exam_status = $vacancy->practical_exam_status;
        $designation_category = $vacancy->designation_category;
        $written_exam_status = $vacancy->written_exam_status;
        $interview_status = $vacancy->interview_status;

        $data_text = ApplicationForm::select(
            'id',
            'vacancy_id',
            'name_with_initials',
            'last_name',
            'nic',
            'interview_status',
            'interview_marks',
            'total_marks'
        )
            ->where('vacancy_id', $vacancyid)
            ->where('protect_application', 1)
            ->whereIn('application_decision_id', array(35, 37))
            ->whereIn('interview_status', array(1, 2, 3, 4))
            ->orderBy('id')
            ->get();

        $counts = ApplicationForm::select('interview_status', DB::raw('count(*) as total'))
            ->where('vacancy_id', $vacancyid)
            ->where('protect_application', 1)
            ->where('application_decision_id', 35)
            ->where('interview_status', 1)
            ->groupBy('interview_status')
            ->pluck('total', 'interview_status');


        $countForPending = $counts[1] ?? 0;

        return view('admin.nonacademic_vacancy.interview_result.list', compact(
            'data_text',
            'vacancy_name',
            'vacancy_status',
            'countForPending',
            'short_list_complete_status',
            'designation_category',
            'practical_exam_status',
            'written_exam_status',
            'interview_status',
            'vacancyid',

        ));
    }

    public function InterviewResultCheckViewShow($id)
    {
        $appID = decrypt($id);
        // Find the application
        $applicationForm = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->select(
                'application_forms.*',
                't.category_name as title_name',
                'cs.category_name as civil_status_name'
            )
            ->where('application_forms.id', $appID)
            ->first();

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        // Get vacancy information with designation name
        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'designations.designation_name', 'categories.display_name')
            ->find($applicationForm->vacancy_id);


        // Load reference data
        $titles = DB::table('categories')->where('category_type_id', '=', 5)->get();
        $civilStatuses = DB::table('categories')->where('category_type_id', '4')->get();
        $cities = City::all();

        return view('admin.nonacademic_vacancy.interview_result.show', compact(
            'appID',
            'applicationForm',
            'vacancy',
            'titles',
            'civilStatuses',
            'cities'
        ));
    }

    public function InterviewResultStore(Request $request)
    {
        $request->validate([
            'interview_marks' => 'nullable|max:' . $request->interview_contribution_percentage,
            'recom' => 'required',
        ]);

        $applicationForm = ApplicationForm::find($request->app_id);
        $applicationForm->application_decision_id = 37;
        $applicationForm->interview_status = $request->recom;
        $applicationForm->interview_marks = $request->interview_marks;
        $applicationForm->total_marks = $request->interview_marks + $applicationForm->written_marks + $applicationForm->practical_marks;
        $applicationForm->save();

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        $notification = array(
            'message' => 'Interview result added successfully',
            'alert-type' => 'success'
        );
        return redirect()->route('nac.vacancy.interview.result.entry.list', encrypt($applicationForm->vacancy_id))->with($notification);
    }

    public function InterviewResultFinalize(Request $request)
    {

        $request->validate([
            'vacancyid' => 'required|integer|exists:vancancy_non_academics,id',
        ]);

        $update_text = VancancyNonAcademic::where('id', $request->vacancyid)->first();

        if ($update_text) {

            $update_text->vacancy_status_type_id = 32;
            $update_text->interview_status = 2;
            $update_text->interview_complete_user = auth()->user()->employee_no;
            $update_text->interview_complete_date = today();
            $update_text->save();

            $notification = array(
                'message' => 'Interview result finalize successfully',
                'alert-type' => 'success'
            );
            return redirect()->route('nac.vacancy.interview.result.pending.list')->with($notification);
        } else {
            $notification = array(
                'message' => 'Vacancy not found',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
        }
    }

    public function VacancyInterviewCompletedList()
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {

            if (Auth()->user()->hasRole(['super-admin', 'administrator'])) {

                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.interview_status = 2 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.duty_assume_status = 1 THEN 1 ELSE 0 END), 0) as noduty'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.duty_assume_status = 2 THEN 1 ELSE 0 END), 0) as complete'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.duty_assume_status = 3 THEN 1 ELSE 0 END), 0) as empConvert'),
                    )
                    ->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name'
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->whereIn('vacancy_status_type_id', array(32))
                    ->get();
            }
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.interview_status = 2 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.duty_assume_status = 1 THEN 1 ELSE 0 END), 0) as noduty'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.duty_assume_status = 2 THEN 1 ELSE 0 END), 0) as complete'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.duty_assume_status = 3 THEN 1 ELSE 0 END), 0) as empConvert'),
                    )
                    ->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name'
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->whereIn('vacancy_status_type_id', array(32))
                    ->get();

            } else if (Auth()->user()->hasRole(['cc', 'sc'])) {

                $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('vancancy_operator_non_academics', 'vancancy_operator_non_academics.vacancy_id', '=', 'vancancy_non_academics.id')
                    ->leftJoin('application_forms', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
                    ->select(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name',
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.interview_status = 2 THEN 1 ELSE 0 END), 0) as booked'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.duty_assume_status = 1 THEN 1 ELSE 0 END), 0) as noduty'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.duty_assume_status = 2 THEN 1 ELSE 0 END), 0) as complete'),
                        DB::raw('COALESCE(SUM(CASE WHEN application_forms.duty_assume_status = 3 THEN 1 ELSE 0 END), 0) as empConvert'),
                    )
                    ->groupBy(
                        'vancancy_non_academics.id',
                        'vancancy_non_academics.designation_id',
                        'vancancy_non_academics.designation_category',
                        'vancancy_non_academics.vacancy_status_type_id',
                        'categories.display_name'
                    )
                    ->orderByDesc('vancancy_non_academics.id')
                    ->where('vancancy_operator_non_academics.employee_no', auth()->user()->employee_no)
                    ->whereIn('vacancy_status_type_id', array(32))
                    ->get();
            }
        }

        return view('admin.nonacademic_vacancy.finalization.index', compact('vacancies'));
    }

    public function VacancyInterviewCompletedApplicationList($id)
    {

        $vacancyid = decrypt($id);

        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select(
                'designations.designation_name',
                'vancancy_non_academics.short_list_complete_status',
                'vancancy_non_academics.vacancy_status_type_id',
                'vancancy_non_academics.designation_category',
                'categories.display_name',
                'vancancy_non_academics.practical_exam_status',
                'vancancy_non_academics.written_exam_status',
                'vancancy_non_academics.interview_status',
            )
            ->where('vancancy_non_academics.id', $vacancyid)->first();

        if ($vacancy->display_name != '' && $vacancy->designation_category == 138) {
            $vacancy_name = $vacancy->designation_name . ' (' . $vacancy->display_name . ')';
        } else {

            $vacancy_name = $vacancy->designation_name;
        }

        $short_list_complete_status = $vacancy->short_list_complete_status;
        $vacancy_status = $vacancy->vacancy_status_type_id;
        $practical_exam_status = $vacancy->practical_exam_status;
        $designation_category = $vacancy->designation_category;
        $written_exam_status = $vacancy->written_exam_status;
        $interview_status = $vacancy->interview_status;

        $data_text = ApplicationForm::select(
            'id',
            'vacancy_id',
            'name_with_initials',
            'last_name',
            'nic',
            'total_marks',
            'duty_assume_status',
            'employee_no'
        )
            ->where('vacancy_id', $vacancyid)
            ->where('protect_application', 1)
            ->whereIn('application_decision_id', array(37, 38, 39))
            ->where('interview_status', 2)
            ->orderBy('id')
            ->get();

        $booked = ApplicationForm::where('vacancy_id', $vacancyid)->where('protect_application', 1)->where('interview_status', 2)->count();

        $noduty = ApplicationForm::where('vacancy_id', $vacancyid)->where('protect_application', 1)->where('interview_status', 2)->where('duty_assume_status', 1)->count();

        $complete = ApplicationForm::where('vacancy_id', $vacancyid)->where('protect_application', 1)->where('interview_status', 2)->where('duty_assume_status', 2)->count();

        $empConvert = ApplicationForm::where('vacancy_id', $vacancyid)->where('protect_application', 1)->where('interview_status', 2)->where('duty_assume_status', 3)->count();


        return view('admin.nonacademic_vacancy.finalization.list', compact(
            'data_text',
            'vacancy_name',
            'vacancy_status',
            'short_list_complete_status',
            'designation_category',
            'practical_exam_status',
            'written_exam_status',
            'interview_status',
            'vacancyid',
            'booked',
            'noduty',
            'complete',
            'empConvert'
        ));
    }

    public function ApplicantDutyAssumeViewShow($id)
    {
        $appID = decrypt($id);
        // Find the application
        $applicationForm = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->select(
                'application_forms.*',
                't.category_name as title_name',
                'cs.category_name as civil_status_name'
            )
            ->where('application_forms.id', $appID)
            ->first();

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        // Get vacancy information with designation name
        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'designations.designation_name', 'categories.display_name')
            ->find($applicationForm->vacancy_id);


        // Load reference data
        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 48]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $educationLevels = $categories->where('category_type_id', '16');

        $salaryPaymentTypes = $categories->where('category_type_id', '48');

        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
        $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

        $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

        $designations =  DB::table('designations')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
            ->where('designations.deleted_at', '=', NULL)
            ->where('designation_division', 53)
            ->where('active_status', 1)
            ->get();

        return view('admin.nonacademic_vacancy.finalization.duty_assume', compact(
            'appID',
            'applicationForm',
            'vacancy',
            'titles',
            'civilStatuses',
            'cities',
            'genders',
            'races',
            'religions',
            'citizenships',
            'educationLevels',
            'faculties',
            'departments',
            'subDepartments',
            'CarderDepartment',
            'CarderSubDepartment',
            'designations',
            'salaryPaymentTypes',
        ));
    }


    public function ApplicantDutyAssumeStore(DutyAssumeRequest $request)
    {

        $applicationForm = ApplicationForm::find($request->app_id);
        $applicationForm->nic = $request->nic;
        $applicationForm->title = $request->title;
        $applicationForm->name_with_initials = $request->name_with_initials;
        $applicationForm->name_denoted_by_initials = $request->name_denoted_by_initials;
        $applicationForm->last_name = $request->last_name;
        $applicationForm->telephone_mobile = $request->telephone_mobile;
        $applicationForm->telephone_residence = $request->telephone_residence;
        $applicationForm->email_address = $request->email_address;
        $applicationForm->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
        $applicationForm->gender_id = $request->gender_id;
        $applicationForm->civil_status = $request->civil_status;
        $applicationForm->permanent_address_line1 = $request->permanent_address_line1;
        $applicationForm->permanent_address_line2 = $request->permanent_address_line2;
        $applicationForm->permanent_address_line3 = $request->permanent_address_line3;
        $applicationForm->permanent_address_city = $request->permanent_address_city;
        $applicationForm->postal_address_line1 = $request->postal_address_line1;
        $applicationForm->postal_address_line2 = $request->postal_address_line2;
        $applicationForm->postal_address_line3 = $request->postal_address_line3;
        $applicationForm->postal_address_city = $request->postal_address_city;
        $applicationForm->citizen_sri_lanka_obtained = $request->citizen_sri_lanka_obtained;
        $applicationForm->citizen_registration_no = $request->citizen_registration_no;
        $applicationForm->file_reference_number = $request->file_reference_number;
        $applicationForm->faculty_id = $request->faculty_id;
        $applicationForm->department_id = $request->department_id;
        $applicationForm->sub_department_id = $request->sub_department_id;
        $applicationForm->carder_faculty_id = $request->carder_faculty_id;
        $applicationForm->carder_department_id = $request->carder_department_id;
        $applicationForm->carder_sub_department_id = $request->carder_sub_department_id;
        $applicationForm->current_basic_salary = $request->current_basic_salary;
        $applicationForm->duty_assume_date = $request->duty_assume_date;

        if ($request->designation_category != 138) {
            $applicationForm->salary_termination_date = $request->salary_termination_date;
            $applicationForm->salary_payment_type = $request->salary_payment_type;
        }
        $applicationForm->academic_qualification = $request->academic_qualification;
        $applicationForm->duty_assume_status = $request->recom;
        if ($request->recom == 2) {
            $applicationForm->application_decision_id = 38;
        } else {
            $applicationForm->application_decision_id = 39;
        }
        $applicationForm->added_ma_user_id = Auth()->user()->employee_no;
        $applicationForm->added_ma_date = Carbon::today();

        $applicationForm->save();

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        $notification = array(
            'message' => 'Duty assume details added successfully',
            'alert-type' => 'success'
        );
        return redirect()->route('nac.vacancy.interview.completed.application.list', encrypt($applicationForm->vacancy_id))->with($notification);
    }

    public function ApplicantConvertEmployeeViewShow($id)
    {
        $appID = decrypt($id);
        // Find the application
        $appData = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->select(
                'application_forms.*',
                't.category_name as title_name',
                'cs.category_name as civil_status_name'
            )
            ->where('application_forms.id', $appID)
            ->first();

        if (!$appData) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        // Get vacancy information with designation name
        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'designations.designation_name', 'categories.display_name')
            ->find($appData->vacancy_id);


        // Load reference data
        $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 48]);
        $genders = $categories->where('category_type_id', '1');
        $races = $categories->where('category_type_id', '2');
        $religions = $categories->where('category_type_id', '3');
        $civilStatuses = $categories->where('category_type_id', '4');
        $titles = $categories->where('category_type_id', '5');
        $citizenships = $categories->where('category_type_id', '6');
        $educationLevels = $categories->where('category_type_id', '16');

        $salaryPaymentTypes = $categories->where('category_type_id', '48');

        $cities = City::all();
        $faculties = Faculty::all();
        $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
        $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

        $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
        $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

        $designations =  DB::table('designations')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
            ->where('designations.deleted_at', '=', NULL)
            ->where('designation_division', 53)
            ->where('active_status', 1)
            ->get();

        $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
            ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
            ->where('users.main_branch_id', 53)
            ->whereIn('roles.id', [5, 6])
            ->distinct()
            ->get();

        $empDataCount = Employee::where(function ($query) use ($appData) {
            $query->where('nic_old', $appData->nic)
                ->orWhere('nic_new', $appData->nic);
        })
            ->where('employee_status_id', 110)
            ->count();

        if ($empDataCount > 0) {

            $empSimilarData = Employee::where(function ($query) use ($appData) {
                $query->where('nic_old', $appData->nic)
                    ->orWhere('nic_new', $appData->nic);
            })
                ->where('employee_status_id', 110)
                ->get();

            return view('admin.nonacademic_vacancy.finalization.similar_nic_find', compact('vacancy', 'appData', 'empSimilarData', 'operators'));
        } else {

            return view('admin.nonacademic_vacancy.finalization.applicant_to_employee', compact(
                'appID',
                'appData',
                'vacancy',
                'titles',
                'civilStatuses',
                'cities',
                'genders',
                'races',
                'religions',
                'citizenships',
                'educationLevels',
                'faculties',
                'departments',
                'subDepartments',
                'CarderDepartment',
                'CarderSubDepartment',
                'designations',
                'salaryPaymentTypes',
                'operators',
            ));
        }
    }

    public function ApplicantConvertEmployeeStore(Request $request)
    {

        if ($request->app_id != '') {

            //Application table update

            $data = ApplicationForm::find($request->app_id);
            $data->duty_assume_status =  3;
            $data->file_assign_ma_user = $request->operator_user;
            $data->duty_assume_confirm_emp = Auth()->user()->employee_no;
            $data->duty_assume_confirm_date = Carbon::today();
            $data->save();

            /************************************************************* */

            $appData = ApplicationForm::find($request->app_id);

            $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('vancancy_non_academics.*', 'designations.designation_name', 'categories.display_name')
                ->find($appData->vacancy_id);

            if ($vacancy->designation_category == 138) {

                // create employee number
                $data = new PermanentEmployee();
                $data->nic = strtoupper($appData->nic);
                $data->main_branch = Auth()->user()->main_branch_id;
                $data->assign_ma_user_id = $request->operator_user;
                $data->assign_ma_date = date("Y-m-d");
                $data->employee_status_id = 110;
                $data->employee_status_type_id = 112;
                $data->employee_work_type = 138;
                $data->added_user_id = auth()->user()->employee_no;
                $data->added_date = date("Y-m-d");
                $data->save();

            } else if ($vacancy->designation_category == 140) {

                $data = new TemporyEmployee();
                $data->nic = strtoupper($appData->nic);
                $data->main_branch = Auth()->user()->main_branch_id;
                $data->assign_ma_user_id = $request->operator_user;
                $data->assign_ma_date = date("Y-m-d");
                $data->employee_status_id = 110;
                $data->employee_status_type_id = 112;
                $data->employee_work_type = 140;
                $data->added_user_id = auth()->user()->employee_no;
                $data->added_date = date("Y-m-d");
                $data->save();

            } else if ($vacancy->designation_category == 141) {

                $data = new ContractEmployee();
                $data->nic = strtoupper($appData->nic);
                $data->main_branch = Auth()->user()->main_branch_id;
                $data->assign_ma_user_id = $request->operator_user;
                $data->assign_ma_date = date("Y-m-d");
                $data->employee_status_id = 110;
                $data->employee_status_type_id = 112;
                $data->employee_work_type = 141;
                $data->added_user_id = auth()->user()->employee_no;
                $data->added_date = date("Y-m-d");
                $data->save();

            } else if ($vacancy->designation_category == 142) {

                $data = new TemporyEmployee();
                $data->nic = strtoupper($appData->nic);
                $data->main_branch = Auth()->user()->main_branch_id;
                $data->assign_ma_user_id = $request->operator_user;
                $data->assign_ma_date = date("Y-m-d");
                $data->employee_status_id = 110;
                $data->employee_status_type_id = 112;
                $data->employee_work_type = 142;
                $data->added_user_id = auth()->user()->employee_no;
                $data->added_date = date("Y-m-d");
                $data->save();
            }


            // create employee data in employee data
            $maxnumber = Employee::select(DB::raw('MAX(id) as value'))->get();
            $maxValue = json_decode($maxnumber, true);
            $nextId = $maxValue[0]["value"] + 1;

            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($data->nic)]);

            $nicData = json_decode($empDetails->body(), true);

            $employee_data = new Employee();
            $employee_data->id = $nextId;
            $employee_data->employee_no = $data->id;
            $employee_data->file_reference_number = strtoupper($request->file_reference_number);
            $employee_data->vacancy_id = $vacancy->id;
            $employee_data->application_referance_no = $appData->reference_number;
            $employee_data->main_branch_id = 53;
            $employee_data->designation_id = $vacancy->designation_id;

            $employee_data->faculty_id = $appData->faculty_id;
            $employee_data->department_id = $appData->department_id;
            $employee_data->sub_department_id = $appData->sub_department_id;

            $employee_data->carder_faculty_id = $appData->carder_faculty_id;
            $employee_data->carder_department_id = $appData->carder_department_id;
            $employee_data->carder_sub_department_id = $appData->carder_sub_department_id;

            $employee_data->initials = strtoupper($request->initials);
            $employee_data->name_denoted_by_initials = ucwords(strtolower($request->name_denoted_by_initials));
            $employee_data->last_name = ucwords(strtolower($request->last_name));
            $employee_data->civil_status_id =  $request->civil_status_id;
            $employee_data->gender_id =  $request->gender_id;

            $employee_data->permanent_add1 = ucwords(strtolower(preg_replace("/(\,|\.)/", " ", $request->permanent_add1)));
            $employee_data->permanent_add2 = ucwords(strtolower(preg_replace("/(\,|\.)/", " ", $request->permanent_add2)));
            $employee_data->permanent_add3 = ucwords(strtolower(preg_replace("/(\,|\.)/", " ", $request->permanent_add3)));
            $employee_data->permanent_city_id = $request->permanent_city_id;

            $employee_data->postal_add1 = ucwords(strtolower(preg_replace("/(\,|\.)/", " ", $request->postal_add1)));
            $employee_data->postal_add2 = ucwords(strtolower(preg_replace("/(\,|\.)/", " ", $request->postal_add2)));
            $employee_data->postal_add3 = ucwords(strtolower(preg_replace("/(\,|\.)/", " ", $request->postal_add3)));
            $employee_data->postal_city_id = $request->postal_city_id;

            $employee_data->personal_email = $request->email;

            $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
            $employee_data->citizen_registration_no = $request->citizen_registration_no;

            $employee_data->initial_appointment_date = date("Y-m-d", strtotime($appData->duty_assume_date));
            $employee_data->current_appointment_date = date("Y-m-d", strtotime($appData->duty_assume_date));
            $employee_data->gratuity_cal_date = date("Y-m-d", strtotime($appData->duty_assume_date));

            if ($vacancy->designation_category == 140 || $vacancy->designation_category == 141 || $vacancy->designation_category == 142) {

                $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($appData->salary_termination_date_1));
            }

            $employee_data->retirement_date = date("Y-m-d", strtotime($appData->date_of_birth . " +60 years"));
            $employee_data->current_basic_salary = $appData->current_basic_salary;
            $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;

            $employee_data->added_ma_user_id = Auth()->user()->employee_no;
            $employee_data->added_ma_date = date('Y-m-d');

            $employee_data->assign_ma_user_id = $request->operator_user;
            $employee_data->assign_ma_date = date('Y-m-d');
            $employee_data->status_id = 1;
            $employee_data->employee_status_id = 110;
            $employee_data->employee_status_type_id = 112;
            $employee_data->employee_work_type = $vacancy->designation_category;

            if ($vacancy->designation_category == 138) {
                $employee_data->increment_date = date("m-d", strtotime($appData->duty_assume_date));
            }

            $employee_data->emp_decision_id = 41;
            $employee_data->mobile_no = $request->mobile_no;
            $employee_data->telephone_no = $request->phone_no;
            $employee_data->nic = strtoupper($data->nic);

            $employee_data->nic_old = $nicData['oldnic'] ?? null;
            $employee_data->nic_new = $nicData['newnic'] ?? null;
            $employee_data->active_nic = $nicData['activenic'] ?? null;
            $employee_data->dob_gen = $nicData['dob'] ?? null;

            $employee_data->date_of_birth = date("Y-m-d", strtotime($appData->date_of_birth));
            $employee_data->title_id = $request->titel_id;

            if ($vacancy->designation_category == 140 || $vacancy->designation_category == 141 || $vacancy->designation_category == 142) {
                $employee_data->salary_payment_type = $request->salary_payment_type;
            } else {
                $employee_data->salary_payment_type = 266;
            }

            $employee_data->save();

            if ($vacancy->designation_category == 138) {

                //completion status data update
                $permanent_data = PermanentEmployee::find($data->id);
                $permanent_data->completion_status = 1;
                $permanent_data->updated_at = Carbon::now();
                $permanent_data->save();
            } else if ($vacancy->designation_category == 140) {

                $data = TemporyEmployee::find($data->id);
                $data->completion_status = 1;
                $data->updated_at = Carbon::now();
                $data->save();
            } else if ($vacancy->designation_category == 141) {

                $data = ContractEmployee::find($data->id);
                $data->completion_status = 1;
                $data->updated_at = Carbon::now();
                $data->save();
            } else if ($vacancy->designation_category == 142) {

                $data = TemporyEmployee::find($data->id);
                $data->completion_status = 1;
                $data->updated_at = Carbon::now();
                $data->save();
            }


            // create new salary record
            $statusRecord = new EmployeeSalaryStatus();
            $statusRecord->employee_no = $employee_data->id;
            $statusRecord->status = 1;
            $statusRecord->created_date = Carbon::now();
            $statusRecord->save();

            // employee number update in application table
            $Empdata = ApplicationForm::find($request->app_id);
            $Empdata->employee_no = $data->id;
            $Empdata->save();

            /****************************************** */

            $mailData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('last_name', 'initials', 'category_name', 'employee_no', 'personal_email')
                ->where('employee_no', $data->id)
                ->first();

            $data = [
                'name' => $mailData->category_name . ' ' . $mailData->initials . ' ' . $mailData->last_name,
                'empNo' => $mailData->employee_no
            ];

            $mail = new UJSWelcomeMail($data);

            Mail::to($mailData->personal_email)->send($mail);

            $employee = Employee::find($mailData->employee_no);
            $employee->welcome_mail = 1;
            $employee->save();

            $notification = array(
                'message' => 'Applicant Transfer To Employee Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('nac.vacancy.interview.completed.application.list', encrypt($vacancy->id))->with($notification);
        } else {

            $notification = array(
                'message' => 'somthing is wrong please try again',
                'alert-type' => 'error'
            );

            return redirect()->route('nac.vacancy.interview.completed.list')->with($notification);
        }
    }

    public function VacancyFinalize(Request $request)
    {

        $request->validate([
            'vacancyid' => 'required|integer|exists:vancancy_non_academics,id',
        ]);

        $update_text = VancancyNonAcademic::where('id', $request->vacancyid)->first();

        if ($update_text) {
            if ($request->empConvert == 0) {
                $update_text->vacancy_status_type_id = 287;
            } else {
                $update_text->vacancy_status_type_id = 288;
            }
            $update_text->finalized_user = auth()->user()->employee_no;
            $update_text->finalized_date = today();
            $update_text->save();

            $notification = array(
                'message' => 'Vacancy finalize successfully',
                'alert-type' => 'success'
            );
            return redirect()->route('nac.vacancy.interview.completed.list')->with($notification);
        } else {

            $notification = array(
                'message' => 'Vacancy not found',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
        }
    }



}
