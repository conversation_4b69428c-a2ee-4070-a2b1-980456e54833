<?php

namespace App\Http\Controllers\Backend\Increment;

use App\Http\Controllers\Controller;
use App\Mail\acIncreForwardToDeanMail;
use App\Mail\acIncreForwardToEmpMail;
use App\Mail\acIncreForwardVCMail;
use App\Mail\acIncreForwardToHodMail;
use App\Models\Department;
use App\Models\DepartmentHead;
use App\Models\Employee;
use App\Models\Faculty;
use App\Models\FacultyDean;
use App\Models\financeListAcIncrement;
use App\Models\increAcSerMain;
use App\Models\increAcSerOther;
use App\Models\Increment;
use App\Models\incrementsProcessAc;
use App\Models\Leave;
use App\Models\leaveAcademicSummery;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Barryvdh\DomPDF\Facade\Pdf as FacadePdf;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\Calculation\TextData\Format;

class AcInrementController extends Controller
{
    public function __construct()
    {

        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc|vc|dean|head');
    }

    public function checkingListOpen(Request $request)
    {



        $months = [];
        for ($i = -1; $i < 2; $i++) {
            $currentMonth = Carbon::now()->addMonths($i)->format('m');
            $months[] = [
                'value' => $currentMonth,
                'name' => Carbon::createFromDate(null, $currentMonth)->format('F'),
            ];
        }

        if (isset($request->month)) {
            if ($request->month == 0) {
                $notification = array(
                    'message' => 'Please select the month',
                    'alert-type' => 'error'
                );

                return redirect()->route('ac.incre.checking.open')->with($notification);
            } else {
                $mainBranch = Auth()->user()->main_branch_id;
                $empNo = Auth()->user()->employee_no;

                $pass_month = Carbon::createFromDate(null, $request->month)->format('F');


                $currentYear = Carbon::now()->year;
                $currentMonth = Carbon::now()->month;


                if ($currentMonth == 12 && $pass_month == 1) {
                    $currentYear = $currentYear + 1;
                }

                if ($currentMonth == 1 && $pass_month == 12) {
                    $currentYear = $currentYear - 1;
                }

                $increText = incrementsProcessAc::select('emp_no')
                    ->where('year', '=', $currentYear)
                    ->get();


                if (Auth()->user()->hasRole(['administrator'])) {

                    $data_text =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        //->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                        ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                        ->join('categories as t', 't.id', '=', 'employees.title_id')
                        ->select(
                            'employees.employee_no',
                            'employees.initials',
                            'employees.last_name',
                            'employees.increment_date',
                            'designations.designation_name',
                            'departments.department_name',
                            //'faculties.faculty_name',
                            'categories.category_name',
                            't.category_name as title',
                            'designations.ugc_mis'
                        )
                        ->where('employees.increment_date', 'LIKE', str_pad($request->month, 2, '0', STR_PAD_LEFT) . '%')
                        ->where('employees.main_branch_id', 52)
                        ->where('employees.employee_status_id', 110)
                        ->whereNotIn('employees.employee_no', $increText)
                        ->orderBy('employees.increment_date', 'ASC')
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                    $data_text =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        //->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                        ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                        ->select(
                            'employees.employee_no',
                            'employees.initials',
                            'employees.last_name',
                            'employees.increment_date',
                            'designations.designation_name',
                            'departments.department_name',
                            //'faculties.faculty_name',
                            'categories.category_name',
                            'designations.ugc_mis'
                        )
                        ->where('employees.increment_date', 'LIKE', str_pad($request->month, 2, '0', STR_PAD_LEFT) . '%')
                        ->where('employees.main_branch_id', 52)
                        ->where('employees.employee_status_id', 110)
                        ->where('employees.assign_ma_user_id', $empNo)
                        ->whereNotIn('employees.employee_no', $increText)
                        ->orderBy('employees.increment_date', 'ASC')
                        ->orderBy('employees.employee_no', 'ASC')
                        ->get();
                } else {
                    $data_text = array();
                }
            }
        } else {
            $data_text = array();
            $pass_month = "";
        }


        return view('admin.increment.Academic.1stChecking', compact('data_text', 'months', 'pass_month'));
    }

    public function CheckingDetail($id)
    {

        $empNo = decrypt($id);

        $emp_text = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            //->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
            ->select(
                'employees.employee_no',
                'employees.initials',
                'employees.last_name',
                'employees.increment_date',
                'designations.designation_name',
                'designations.ugc_mis',
                'departments.department_name',
                'departments.id',
                'categories.category_name',
                't.category_name as title',
                'employees.increment_date',
                'employees.current_appointment_date',
                'employees.initial_appointment_date',
                'salary_scales.salary_scale_txt',
                'employees.current_basic_salary',
                'salary_scales.increment_value1',
                'employees.lock',
                'employees.designation_id',
                'salary_scales.id as sal_scal_id',
                'salary_scales.initial_basic_salary',
                'salary_scales.step_number1',
                'salary_scales.increment_value2',
                'salary_scales.eb_exam'

            )
            ->where('employees.employee_no', $empNo)
            ->get();

        if (count($emp_text) > 0) {
            foreach ($emp_text as $emp_texts) {
                $emp_no = $emp_texts->employee_no;
                $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                $appDate = $emp_texts->current_appointment_date;
                $depName = $emp_texts->department_name;
                $fAppDate = $emp_texts->initial_appointment_date;
                $desig = $emp_texts->designation_name . ' ' . $emp_texts->category_name;
                $salScale = $emp_texts->salary_scale_txt;
                $increDate = $emp_texts->increment_date;
                $bSal = sprintf("%.2f", $emp_texts->current_basic_salary);

                $lock = $emp_texts->lock;
                $depId = $emp_texts->id;
                $desigID = $emp_texts->designation_id;

                $sal_scal_id = $emp_texts->sal_scal_id;
                $initial_bSal = $emp_texts->initial_basic_salary;
                $step_num1 = $emp_texts->step_number1;
                $increAmount2 = $emp_texts->increment_value2;
                $increAmount1 =  $emp_texts->increment_value1;

                $eb_id = $emp_texts->eb_exam;
                $ugc_mis = $emp_texts->ugc_mis;

                $bSal_after_step = ($step_num1 * $increAmount1) + $initial_bSal;

                if ($sal_scal_id == 10) {

                    if ($bSal >= $bSal_after_step) {
                        $increAmount = sprintf("%.2f", $increAmount2);
                    } else {
                        $increAmount = sprintf("%.2f", $increAmount1);
                    }
                } else {
                    $increAmount = sprintf("%.2f", $increAmount1);
                }

                $newBSal = sprintf("%.2f", ($bSal + $increAmount));

                if ($eb_id == 1) {
                    if ($bSal == $bSal_after_step) {
                        $eb_exam = 'Yes';
                        $eb_exam_status = 1;
                    } else {
                        $eb_exam = 'No';
                        $eb_exam_status = 2;
                    }
                } else {
                    $eb_exam = 'Not applicable';
                    $eb_exam_status = 3;
                }
            }

            $currentYear = Carbon::now()->year;

            list($incre_month, $incre_day) = explode('-', $increDate);

            $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                ->where('increments.emp_no', $empNo)
                ->orderBy('increments.effective_date', 'DESC')
                ->take(6)
                ->get();

            if (count($increments) > 0) {
                $maxSalaryRecord = Increment::where('emp_no', $empNo)->orderBy('basic_sal', 'desc')->first();
                if ($maxSalaryRecord) {
                    $last_BasicSalary = sprintf("%.2f", $maxSalaryRecord->basic_sal);
                    $incre_last_date = $maxSalaryRecord->effective_date;
                    $lat_incre_year = Carbon::parse($incre_last_date)->year;
                }
                // $incre_last_date = Increment::where('emp_no', $empNo)->max('effective_date');
                $first_incre = 0;
                $increYear = Carbon::now()->year;
                $currentMonth = Carbon::now()->month;
                if ($currentMonth == 12 && $incre_month == 1) {
                    $increYear = $increYear + 1;
                }

                if ($currentMonth == 1 && $incre_month == 12) {
                    $increYear = $increYear - 1;
                }

                if ($increYear == ($lat_incre_year + 1)) {
                    $incre_ok = 1;
                } else {
                    $incre_ok = 0;
                }

                if ($last_BasicSalary == $bSal) {
                    $bSal_ok = 1;
                } else {
                    $bSal_ok = 0;
                }

                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $empNo)
                    ->where('leave_academic_summeries.status', 0)
                    ->where('leave_academic_summeries.starting_date', '>=', $incre_last_date)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            } else {
                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $empNo)
                    ->where('leave_academic_summeries.status', 0)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();

                $incre_last_date = '';
                $first_incre = 1;
                $incre_ok = 0;
                $bSal_ok = 0;
            }
            $leave_text_as = Leave::where('emp_no', $empNo)
                ->whereIn('year', [$currentYear, $currentYear - 1])
                ->get();
        } else {
            $emp_no = "";
            $name = "";
            $appDate = "";
            $depName = "";
            $fAppDate = "";
            $desig = "";
            $salScale = "";
            $increDate = "";
            $bSal = "";
            $increAmount = "";
            $newBSal = "";
            $increments = array();
            $lock = 0;
            $depId = 0;
            $desigID = 0;
            $leave_text = array();
            $incre_last_date = '';
            $first_incre = 0;
            $incre_ok = 0;
            $bSal_ok = 0;
            $eb_exam = '';
            $eb_exam_status = 3;
            $ugc_mis = 0;
            $leave_text_as = array();
        }



        return view('admin.increment.Academic.1stCheckingDetails', compact(
            'emp_no',
            'name',
            'appDate',
            'depName',
            'fAppDate',
            'desig',
            'salScale',
            'increDate',
            'bSal',
            'increAmount',
            'newBSal',
            'increments',
            'lock',
            'depId',
            'desigID',
            'leave_text',
            'incre_last_date',
            'first_incre',
            'incre_ok',
            'bSal_ok',
            'eb_exam',
            'ugc_mis',
            'leave_text_as',
            'eb_exam_status'

        ));
    }

    public function checkingSave(Request $request)
    {
        if ($request->empNo != '') {
            if ($request->dep > 0) {
                if ($request->increDate != '') {
                    if ($request->increAmount != '') {
                        if ($request->presentSal != '') {
                            if ($request->newSal != '') {
                                if ($request->desigID > 0) {
                                    $currentYear = Carbon::now()->year;
                                    $currentMonth = Carbon::now()->month;

                                    $parts = explode('-', $request->increDate);
                                    $increMonth = intval($parts[0]);

                                    if ($currentMonth == 12 && $increMonth == 1) {
                                        $currentYear = $currentYear + 1;
                                    }

                                    if ($currentMonth == 1 && $increMonth == 12) {
                                        $currentYear = $currentYear - 1;
                                    }

                                    $refText = incrementsProcessAc::where('year', '=', $currentYear)
                                        ->where('emp_category', '=', 44)
                                        ->get();

                                    $refText_rowcount = $refText->count() + 1;

                                    $refNo = $currentYear . "/" . substr("000{$refText_rowcount}", -3);

                                    if ($request->ugc_mis == 99) {
                                        $emp_category = 45;
                                    } else {
                                        $emp_category = 44;
                                    }


                                    $acIncre = new incrementsProcessAc();
                                    $acIncre->emp_category = $emp_category;
                                    $acIncre->emp_no = $request->empNo;
                                    $acIncre->year = $currentYear;
                                    $acIncre->ref_no = $refNo;
                                    $acIncre->desig_id = $request->desigID;
                                    $acIncre->eb_status = $request->eb_status;
                                    $acIncre->dep_no = $request->dep;
                                    $acIncre->incre_date = $request->increDate;
                                    $acIncre->incre_amount = $request->increAmount;
                                    $acIncre->present_sal_step = $request->presentSal;
                                    $acIncre->new_sal_step = $request->newSal;
                                    $acIncre->check_user_id = auth()->user()->employee_no;
                                    $acIncre->check_date = today();
                                    $acIncre->ex_forward_status = 1;
                                    $acIncre->save();

                                    $notification = array(
                                        'message' => 'Successfully done checking stage. This increment application sent to the head of the establishment.',
                                        'alert-type' => 'success'
                                    );

                                    return redirect()->route('ac.incre.checking.open')->with($notification);
                                } else {
                                    $notification = array(
                                        'message' => 'There is an issue with the designation. Please try again.',
                                        'alert-type' => 'error'
                                    );

                                    return redirect()->route('ac.incre.checking.details', [encrypt($request->empNo)])->with($notification);
                                }
                            } else {
                                $notification = array(
                                    'message' => 'There is an issue with the new salary step. Please try again.',
                                    'alert-type' => 'error'
                                );

                                return redirect()->route('ac.incre.checking.details', [encrypt($request->empNo)])->with($notification);
                            }
                        } else {
                            $notification = array(
                                'message' => 'There is an issue with the present salary step. Please try again.',
                                'alert-type' => 'error'
                            );

                            return redirect()->route('ac.incre.checking.details', [encrypt($request->empNo)])->with($notification);
                        }
                    } else {
                        $notification = array(
                            'message' => 'There is an issue with the increment amount. Please try again.',
                            'alert-type' => 'error'
                        );

                        return redirect()->route('ac.incre.checking.details', [encrypt($request->empNo)])->with($notification);
                    }
                } else {
                    $notification = array(
                        'message' => 'There is an issue with the increment date. Please try again.',
                        'alert-type' => 'error'
                    );

                    return redirect()->route('ac.incre.checking.details', [encrypt($request->empNo)])->with($notification);
                }
            } else {
                $notification = array(
                    'message' => 'There is an issue with the department. Please try again.',
                    'alert-type' => 'error'
                );

                return redirect()->route('ac.incre.checking.details', [encrypt($request->empNo)])->with($notification);
            }
        } else {
            $notification = array(
                'message' => 'There is an issue with the employee number. Please try again.',
                'alert-type' => 'error'
            );

            return redirect()->route('ac.incre.checking.open')->with($notification);
        }
    }

    public function diviHeadListOpen()
    {
        $headList = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->select(
                'increments_process_acs.emp_no',
                'increments_process_acs.incre_date',
                'increments_process_acs.ref_no',
                'increments_process_acs.id',
                'employees.initials',
                'employees.last_name',
                'categories.category_name as title',
                'designations.designation_name',
                'g.category_name as grade',
                'departments.department_name'
            )
            ->where('increments_process_acs.ex_forward_status', '=', 1)
            ->get();

        // dd($headList);

        return view('admin.increment.Academic.divisionHead', compact('headList'));
    }

    public function diviHeadForwardDetail($id)
    {

        $tbID = decrypt($id);

        $emp_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
            ->join('designations', 'designations.id', '=', 'increments_process_acs.desig_id')
            ->join('departments', 'departments.id', '=', 'increments_process_acs.dep_no')
            ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
            ->select(
                'employees.initials',
                'employees.last_name',

                'designations.designation_name',
                'departments.id as depID',
                'departments.department_name',
                'faculties.faculty_name',
                'categories.category_name',
                't.category_name as title',

                'employees.current_appointment_date',
                'employees.initial_appointment_date',
                'salary_scales.salary_scale_txt',

                'salary_scales.increment_value1',
                'increments_process_acs.emp_no',
                'increments_process_acs.incre_date',
                'increments_process_acs.ref_no',
                'increments_process_acs.id',
                'increments_process_acs.year',
                'increments_process_acs.incre_amount',
                'increments_process_acs.present_sal_step',
                'increments_process_acs.new_sal_step',
                'increments_process_acs.check_date',
                'increments_process_acs.emp_category',
                'increments_process_acs.eb_status'

            )
            ->where('increments_process_acs.id', $tbID)
            ->get();

        if (count($emp_text) > 0) {
            foreach ($emp_text as $emp_texts) {
                $incre_id = $emp_texts->id;
                $emp_no = $emp_texts->emp_no;
                $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                $appDate = $emp_texts->current_appointment_date;
                $depName = $emp_texts->department_name;
                $fac = $emp_texts->faculty_name;
                $fAppDate = $emp_texts->initial_appointment_date;
                $desig = $emp_texts->designation_name . ' ' . $emp_texts->category_name;
                $salScale = $emp_texts->salary_scale_txt;
                $increDate = $emp_texts->incre_date;
                $bSal = sprintf("%.2f", $emp_texts->present_sal_step);
                $increAmount = sprintf("%.2f", $emp_texts->incre_amount);
                $newBSal = sprintf("%.2f", $emp_texts->new_sal_step);
                $lock = $emp_texts->lock;
                $depId = $emp_texts->depID;
                $refNo = $emp_texts->ref_no;
                $increYear = $emp_texts->year;
                $emp_type = $emp_texts->emp_category;

                if ($emp_texts->eb_status == 1) {
                    $eb_status = 'Yes';
                } else if ($emp_texts->eb_status == 2) {
                    $eb_status = 'No';
                } else {
                    $eb_status = 'Not applicable';
                }
            }

            $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                ->where('increments.emp_no', $emp_no)
                ->orderBy('increments.effective_date', 'DESC')
                ->take(6)
                ->get();

            $ma_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.check_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.check_date'
                )
                ->where('increments_process_acs.id', $tbID)
                ->get();

            if (count($ma_text) > 0) {
                foreach ($ma_text as $ma_texts) {
                    $ma_name = $ma_texts->title . ' ' . $ma_texts->initials . ' ' . $ma_texts->last_name;
                    $ma_date = $ma_texts->check_date;
                }
            } else {
                $ma_name = "";
                $ma_date = "";
            }

            $maxSalaryRecord = Increment::where('emp_no', $emp_no)->orderBy('basic_sal', 'desc')->first();
            if ($maxSalaryRecord) {
                $incre_last_date = $maxSalaryRecord->effective_date;

                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->where('leave_academic_summeries.starting_date', '>=', $incre_last_date)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            } else {
                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            }

            $currentYear = Carbon::now()->year;

            $leave_text_as = Leave::where('emp_no', $emp_no)
                ->whereIn('year', [$currentYear, $currentYear - 1])
                ->get();
        } else {
            $incre_id = 0;
            $emp_no = "";
            $name = "";
            $appDate = "";
            $depName = "";
            $fac = "";
            $fAppDate = "";
            $desig = "";
            $salScale = "";
            $increDate = "";
            $bSal = "";
            $increAmount = "";
            $newBSal = "";
            $increments = array();
            $lock = 0;
            $depId = 0;
            $refNo = '';
            $increYear = "";
            $ma_name = "";
            $ma_date = "";
            $leave_text = array();
            $eb_status = '';
            $emp_type = 0;
            $leave_text_as = array();
        }



        return view('admin.increment.Academic.divisionHeadDetails', compact(
            'incre_id',
            'emp_no',
            'name',
            'appDate',
            'depName',
            'fac',
            'fAppDate',
            'desig',
            'salScale',
            'increDate',
            'bSal',
            'increAmount',
            'newBSal',
            'increments',
            'lock',
            'depId',
            'refNo',
            'increYear',
            'ma_name',
            'ma_date',
            'leave_text',
            'eb_status',
            'emp_type',
            'leave_text_as'

        ));
    }

    public function diviHeadForwardStore(Request $request)
    {
        // $hp_text = DepartmentHead::join('categories', 'categories.id', '=', 'department_heads.head_position')
        //     ->select('department_heads.head_position', 'department_heads.appointmemt_type', 'categories.category_name')
        //     ->where('department_heads.emp_no', '=', auth()->user()->employee_no)
        //     ->where('department_heads.department_id', 5003)
        //     ->get();

        $hp_text = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
            ->select(
                'designations.designation_name',
                'employees.designation_id'
            )
            ->where('employees.employee_no', auth()->user()->employee_no)
            ->get();

        if (count($hp_text)) {
            foreach ($hp_text as $hp_texts) {
                $position = $hp_texts->designation_id;
                $position_type = 0;
                $ex_position = $hp_texts->designation_name;
            }
        } else {
            $position = 0;
            $position_type = 0;
            $ex_position = "";
        }

        if ($request->increID > 0) {
            // $dh_test = incrementsProcessAc::where('id', '=', $request->increID)
            //     ->update([
            //         'ex_forward_status' => 2,
            //         'ex_forward_user_id' => auth()->user()->employee_no,
            //         'ex_position' => $position,
            //         'ex_position_type' => $position_type,
            //         'ex_forward_date' => today(),
            //         'self_eval_status' => 1
            //     ]);

            $dh_test = incrementsProcessAc::where('id', $request->increID)->first();
            if ($dh_test) {
                $dh_test->ex_forward_status = 2;
                $dh_test->ex_forward_user_id = auth()->user()->employee_no;
                $dh_test->ex_position = $position;
                $dh_test->ex_position_type = $position_type;
                $dh_test->ex_forward_date = today();
                $dh_test->self_eval_status = 1;
                $dh_test->save();
            }

            $emp_text = Employee::join('departments', 'departments.id', '=', 'employees.department_id')

                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->select(
                    'employees.employee_no',
                    'employees.initials',
                    'employees.last_name',
                    'employees.increment_date',
                    'departments.department_name',
                    't.category_name as title',
                    'employees.department_id',
                    'employees.gender_id',
                    'departments.name_status',
                    'employees.email'

                )
                ->where('employees.employee_no', $request->empNo)
                ->get();

            if (count($emp_text) > 0) {
                foreach ($emp_text as $emp_texts) {
                    $emp_name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                    if ($emp_texts->name_status == 1) {
                        $dep_name = "Department of " . $emp_texts->department_name;
                    } else {
                        $dep_name = $emp_texts->department_name;
                    }


                    $incre_date = $emp_texts->increment_date;

                    list($incre_month, $incre_day) = explode('-', $incre_date);

                    $currentMonth = now()->month;
                    $currentYear = now()->year;

                    if ($currentMonth == 12 && $incre_month == 1) {
                        $currentYear = $currentYear + 1;
                    }

                    if ($currentMonth == 1 && $incre_month == 12) {
                        $currentYear = $currentYear - 1;
                    }

                    $incredate = Carbon::create($currentYear, $incre_month, $incre_day);
                    $formattedDate = $incredate->format('d-m-Y');

                    if ($emp_texts->gender_id == 2) {
                        $gender = "Madam";
                    } else {
                        $gender = "Sir";
                    }

                    $emp_email = $emp_texts->email;

                    $dep_id = $emp_texts->department_id;

                    $hod_text = DepartmentHead::join('categories', 'categories.id', '=', 'department_heads.head_position')
                        ->select('department_heads.email', 'categories.category_name')
                        ->where('department_heads.department_id', $dep_id)
                        ->get();

                    if (count($hod_text) > 0) {
                        foreach ($hod_text as $hod_texts) {
                            $hod_position = $hod_texts->category_name;
                            $hod_email = $hod_texts->email;
                        }
                    } else {
                        $hod_position = "";
                    }
                }
            } else {
                $emp_name = "";
                $dep_name = "";
                $formattedDate = "";
                $gender = "";
                $emp_email = "";
                $hod_position = "";
                $hod_email = "";
            }


            //send email
            $emailData = [
                'empName' =>  $emp_name,
                'depName' => $dep_name,
                'increDate' => $formattedDate,
                'gender' => $gender,
                'exPosition' => $ex_position,
                'hodPosition' => $hod_position,

            ];

            $ccEmails = $hod_email;

            $mail = new acIncreForwardToEmpMail($emailData, $ccEmails);
            //sending email
            Mail::to($emp_email)->send($mail);
            // Mail::to('<EMAIL>')->send($mail);

            $notification = array(
                'message' => 'Successfully forwarded',
                'alert-type' => 'success'
            );

            return redirect()->route('ac.incre.eshtab.head.list.open')->with($notification);
        } else {
            $notification = array(
                'message' => 'Unsuccessfully forwarded, please try again.',
                'alert-type' => 'error'
            );

            return redirect()->route('ac.incre.eshtab.head.list.open')->with($notification);
        }
    }

    // public function hodListOpen()
    // {
    //     $mainBranch = Auth()->user()->main_branch_id;
    //     if ($mainBranch == 51) {
    //         $forwarding_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
    //             ->join('categories as t', 't.id', '=', 'employees.title_id')
    //             ->select(
    //                 'increments_process_acs.id',
    //                 't.category_name as title',
    //                 'employees.initials',
    //                 'employees.last_name',
    //                 'increments_process_acs.ex_forward_date',
    //             )
    //             ->where('increments_process_acs.self_eval_status', '=', 1)
    //             ->get();

    //         $rec_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
    //             ->join('categories as t', 't.id', '=', 'employees.title_id')
    //             ->select(
    //                 'increments_process_acs.id',
    //                 't.category_name as title',
    //                 'employees.initials',
    //                 'employees.last_name',
    //                 'increments_process_acs.self_eval_date',
    //             )
    //             ->where('increments_process_acs.head_status', '=', 1)
    //             ->get();
    //     } else {
    //         $hod_dep_text = Employee::select('department_id')
    //             ->where('employee_no', '=', auth()->user()->employee_no)
    //             ->get();

    //         if (count($hod_dep_text) > 0) {
    //             foreach ($hod_dep_text as  $hod_dep_texts) {
    //                 $hod_dep_id =  $hod_dep_texts->department_id;
    //             }
    //         } else {
    //             $hod_dep_id = 0;
    //         }

    //         $forwarding_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
    //             ->join('categories as t', 't.id', '=', 'employees.title_id')
    //             ->select(
    //                 'increments_process_acs.id',
    //                 't.category_name as title',
    //                 'employees.initials',
    //                 'employees.last_name',
    //                 'increments_process_acs.ex_forward_date',
    //             )
    //             ->where('increments_process_acs.dep_no', '=', $hod_dep_id)
    //             ->where('increments_process_acs.self_eval_status', '=', 1)
    //             ->get();

    //         $rec_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
    //             ->join('categories as t', 't.id', '=', 'employees.title_id')
    //             ->select(
    //                 'increments_process_acs.id',
    //                 't.category_name as title',
    //                 'employees.initials',
    //                 'employees.last_name',
    //                 'increments_process_acs.self_eval_date',
    //             )
    //             ->where('increments_process_acs.dep_no', '=', $hod_dep_id)
    //             ->where('increments_process_acs.head_status', '=', 1)
    //             ->get();
    //     }

    //     return view('admin.increment.Academic.hodList', compact('forwarding_text', 'rec_text'));
    // }

    public function hodForward($id)
    {
        $tbID = decrypt($id);

        $emp_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
            ->join('designations', 'designations.id', '=', 'increments_process_acs.desig_id')
            ->join('departments', 'departments.id', '=', 'increments_process_acs.dep_no')
            ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
            ->select(
                'employees.initials',
                'employees.last_name',

                'designations.designation_name',
                'departments.id as depID',
                'departments.department_name',
                'faculties.faculty_name',
                'categories.category_name',
                't.category_name as title',

                'employees.current_appointment_date',
                'employees.initial_appointment_date',
                'salary_scales.salary_scale_txt',

                'salary_scales.increment_value1',
                'increments_process_acs.emp_no',
                'increments_process_acs.incre_date',
                'increments_process_acs.ref_no',
                'increments_process_acs.id',
                'increments_process_acs.year',
                'increments_process_acs.incre_amount',
                'increments_process_acs.present_sal_step',
                'increments_process_acs.new_sal_step',
                'increments_process_acs.check_date'

            )
            ->where('increments_process_acs.id', $tbID)
            ->get();

        if (count($emp_text) > 0) {
            foreach ($emp_text as $emp_texts) {
                $incre_id = $emp_texts->id;
                $emp_no = $emp_texts->emp_no;
                $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                $appDate = $emp_texts->current_appointment_date;
                $depName = $emp_texts->department_name;
                $fac = $emp_texts->faculty_name;
                $fAppDate = $emp_texts->initial_appointment_date;
                $desig = $emp_texts->designation_name . ' ' . $emp_texts->category_name;
                $salScale = $emp_texts->salary_scale_txt;
                $increDate = $emp_texts->incre_date;
                $bSal = sprintf("%.2f", $emp_texts->present_sal_step);
                $increAmount = sprintf("%.2f", $emp_texts->incre_amount);
                $newBSal = sprintf("%.2f", $emp_texts->new_sal_step);
                $lock = $emp_texts->lock;
                $depId = $emp_texts->depID;
                $refNo = $emp_texts->ref_no;
                $increYear = $emp_texts->year;
            }

            $ma_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.check_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.check_date'
                )
                ->where('increments_process_acs.id', $tbID)
                ->get();

            if (count($ma_text) > 0) {
                foreach ($ma_text as $ma_texts) {
                    $ma_name = $ma_texts->title . ' ' . $ma_texts->initials . ' ' . $ma_texts->last_name;
                    $ma_date = $ma_texts->check_date;
                }
            } else {
                $ma_name = "";
                $ma_date = "";
            }

            $ex_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.ex_forward_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('designations', 'designations.id', '=', 'increments_process_acs.ex_position')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.ex_forward_date',
                    'designations.designation_name',
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();

            if (count($ex_text) > 0) {
                foreach ($ex_text as $ex_texts) {
                    $ex_name = $ex_texts->title . ' ' . $ex_texts->initials . ' ' . $ex_texts->last_name;
                    $ex_position = $ex_texts->designation_name;
                    $ex_date = $ex_texts->ex_forward_date;
                }
            } else {
                $ex_name = "";
                $ex_position = "";
                $ex_date = "";
            }

            $maxSalaryRecord = Increment::where('emp_no', $emp_no)->orderBy('basic_sal', 'desc')->first();
            if ($maxSalaryRecord) {
                $incre_last_date = $maxSalaryRecord->effective_date;

                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->where('leave_academic_summeries.starting_date', '>=', $incre_last_date)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            } else {
                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            }
        } else {
            $incre_id = 0;
            $emp_no = "";
            $name = "";
            $appDate = "";
            $depName = "";
            $fac = "";
            $fAppDate = "";
            $desig = "";
            $salScale = "";
            $increDate = "";
            $bSal = "";
            $increAmount = "";
            $newBSal = "";
            $leave_text = array();
            $lock = 0;
            $depId = 0;
            $refNo = '';
            $increYear = "";
            $ma_name = "";
            $ma_date = "";

            $ex_name = "";
            $ex_position = "";
            $ex_date = "";
        }



        return view('admin.increment.Academic.hodForward', compact(
            'incre_id',
            'emp_no',
            'name',
            'appDate',
            'depName',
            'fac',
            'fAppDate',
            'desig',
            'salScale',
            'increDate',
            'bSal',
            'increAmount',
            'newBSal',
            'leave_text',
            'lock',
            'depId',
            'refNo',
            'increYear',
            'ma_name',
            'ma_date',
            'ex_name',
            'ex_position',
            'ex_date'
        ));
    }

    public function hodForwardStore(Request $request)
    {
        $hp_text = DepartmentHead::select('head_position')
            ->where('emp_no', '=', auth()->user()->employee_no)
            ->get();
        if (count($hp_text)) {
            foreach ($hp_text as $hp_texts) {
                $position = $hp_texts->head_position;
            }
        } else {
            $position = 0;
        }

        if ($request->increID > 0) {
            $dh_test = incrementsProcessAc::where('id', '=', $request->increID)
                ->update([
                    'head_forward_status' => 2,
                    'head_forward_user_id' => auth()->user()->employee_no,
                    'head_forward_position' => $position,
                    'head_forward_date' => today(),
                    'self_eval_status' => 1
                ]);

            $notification = array(
                'message' => 'Successfully forwarded',
                'alert-type' => 'success'
            );

            return redirect()->route('ac.incre.hod.list.open')->with($notification);
        } else {
            $notification = array(
                'message' => 'Unsuccessfully forwarded, please try again.',
                'alert-type' => 'error'
            );

            return redirect()->route('ac.incre.hod.list.open')->with($notification);
        }
    }

    // public function acUsj1stPageOpen()
    // {

    //     $empNo = auth()->user()->employee_no;

    //     $desig_group_text = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
    //         ->select('designations.ugc_mis')
    //         ->where('employees.employee_no', $empNo)
    //         ->get();

    //     if (count($desig_group_text) > 0) {
    //         foreach ($desig_group_text as $desig_group_texts) {
    //             $desig_group_id = $desig_group_texts->ugc_mis;
    //         }
    //     } else {
    //         $desig_group_id = 0;
    //     }

    //     if ($desig_group_id == 135) {
    //         //ac
    //         $incre_text = incrementsProcessAc::where('emp_no', '=', $empNo)
    //             ->orderBy('year', 'desc')
    //             ->first();

    //         if ($incre_text !== null) {

    //             $has_record = 1;
    //             $incre_year = $incre_text->year;
    //             $incre_date = $incre_text->incre_date;
    //             $incre_amount = $incre_text->incre_amount;
    //             $sal_step = $incre_text->present_sal_step;
    //             $new_sal_step = $incre_text->new_sal_step;

    //             $check_date = $incre_text->check_date;
    //             $ex_status = $incre_text->ex_forward_status;
    //             $ex_date = $incre_text->ex_forward_date;
    //             $head_forward_status = $incre_text->head_forward_status;
    //             $head_forward_date = $incre_text->head_forward_date;
    //             $self_eval_status = $incre_text->self_eval_status;
    //             $self_eval_date = $incre_text->self_eval_date;
    //             $head_status = $incre_text->head_status;
    //             $head_date = $incre_text->head_date;
    //             $dean_status = $incre_text->dean_status;
    //             $dean_date = $incre_text->dean_date;
    //             $vc_status = $incre_text->vc_status;
    //             $vc_date = $incre_text->vc_date;

    //             $appID = $incre_text->id;
    //             $refNo = $incre_text->ref_no;
    //         } else {
    //             $has_record = 0;
    //             $incre_year = '';
    //             $incre_date = '';
    //             $incre_amount = '';
    //             $sal_step = '';
    //             $new_sal_step = '';
    //             $check_date = '';
    //             $ex_status = 0;
    //             $ex_date = '';
    //             $head_forward_status = '';
    //             $head_forward_date = '';
    //             $self_eval_status = 0;
    //             $self_eval_date = '';
    //             $head_status = 0;
    //             $head_date = '';
    //             $dean_status = 0;
    //             $dean_date = '';
    //             $vc_status = 0;
    //             $vc_date = '';

    //             $appID = 0;
    //             $refNo = 0;
    //         }


    //         return view('admin.increment.Academic.self_status', compact(

    //             'has_record',
    //             'incre_year',
    //             'incre_date',
    //             'incre_amount',
    //             'sal_step',
    //             'new_sal_step',
    //             'check_date',
    //             'ex_status',
    //             'ex_date',
    //             'head_forward_status',
    //             'head_forward_date',
    //             'self_eval_status',
    //             'self_eval_date',
    //             'head_status',
    //             'head_date',
    //             'dean_status',
    //             'dean_date',
    //             'vc_status',
    //             'vc_date',
    //             'appID',
    //             'refNo'
    //         ));
    //     } elseif ($desig_group_id == 99) {
    //         //AS
    //         return view('admin.commonPages.coming_soon');
    //     } elseif ($desig_group_id == 100 || $desig_group_id == 103 || $desig_group_id == 102) {
    //         //EX
    //         return view('admin.commonPages.coming_soon');
    //     } else {
    //         //Other
    //         return view('admin.commonPages.coming_soon');
    //     }
    // }

    // public function selfEvalOpen(Request $request)
    // {
    //     $appID = $request->appID;
    //     $refID = $request->RefNo;
    //     return view('admin.increment.Academic.self_eval_report', compact('appID', 'refID'));
    // }

    public function allIncrement(Request $request)
    {

        if (isset($request->year) && $request->year != '') {
            $pass_year = $request->year;
            $empfetchDatas = array();

            if (Auth()->user()->hasRole(['cc']) || Auth()->user()->hasRole(['sc'])) {
                $incre_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
                    ->join('categories', 'categories.id', '=', 'employees.title_id')
                    ->select(
                        'increments_process_acs.*',
                        'employees.initials',
                        'employees.last_name',
                        'categories.category_name'
                    )
                    ->where('increments_process_acs.year', '=', $pass_year)
                    ->where('employees.assign_ma_user_id', '=', auth()->user()->employee_no)
                    ->get();
            } else {
                $incre_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
                    ->join('categories', 'categories.id', '=', 'employees.title_id')
                    ->select(
                        'increments_process_acs.*',
                        'employees.initials',
                        'employees.last_name',
                        'categories.category_name'
                    )
                    ->where('increments_process_acs.year', '=', $pass_year)
                    ->get();
            }
        } else {
            $pass_year = '';
            $empfetchDatas = array();
            $incre_text = array();
        }

        return view('admin.increment.Academic.allIncrementStatus', compact('pass_year', 'empfetchDatas', 'incre_text'));
    }

    public function allStatusOpen($id)
    {
        $tbID = decrypt($id);

        $emp_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
            ->join('designations', 'designations.id', '=', 'increments_process_acs.desig_id')
            ->join('departments', 'departments.id', '=', 'increments_process_acs.dep_no')
            ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
            ->select(
                'employees.initials',
                'employees.last_name',

                'designations.designation_name',
                'departments.id as depID',
                'departments.department_name',
                'faculties.id as facID',
                'faculties.faculty_name',
                'categories.category_name',
                't.category_name as title',

                'employees.current_appointment_date',
                'employees.initial_appointment_date',
                'salary_scales.salary_scale_txt',

                'salary_scales.increment_value1',
                'increments_process_acs.emp_no',
                'increments_process_acs.incre_date',
                'increments_process_acs.ref_no',
                'increments_process_acs.id',
                'increments_process_acs.year',
                'increments_process_acs.incre_amount',
                'increments_process_acs.present_sal_step',
                'increments_process_acs.new_sal_step',
                'increments_process_acs.check_date',
                'increments_process_acs.self_eval_date'

            )
            ->where('increments_process_acs.id', $tbID)
            ->get();

        if (count($emp_text) > 0) {
            foreach ($emp_text as $emp_texts) {
                $incre_id = $emp_texts->id;
                $emp_no = $emp_texts->emp_no;
                $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                $appDate = $emp_texts->current_appointment_date;
                $depID = $emp_texts->depID;
                $depName = $emp_texts->department_name;
                $fac = $emp_texts->faculty_name;
                $fAppDate = $emp_texts->initial_appointment_date;
                $desig = $emp_texts->designation_name . ' ' . $emp_texts->category_name;
                $salScale = $emp_texts->salary_scale_txt;
                $increDate = $emp_texts->incre_date;
                $bSal = sprintf("%.2f", $emp_texts->present_sal_step);
                $increAmount = sprintf("%.2f", $emp_texts->incre_amount);
                $newBSal = sprintf("%.2f", $emp_texts->new_sal_step);
                $lock = $emp_texts->lock;
                $depId = $emp_texts->depID;
                $refNo = $emp_texts->ref_no;
                $increYear = $emp_texts->year;
                $serDate = $emp_texts->self_eval_date;
                $facID = $emp_texts->facID;
            }


            $ma_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.check_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.check_date'
                )
                ->where('increments_process_acs.id', $tbID)
                ->get();

            if (count($ma_text) > 0) {
                foreach ($ma_text as $ma_texts) {
                    $ma_name = $ma_texts->title . ' ' . $ma_texts->initials . ' ' . $ma_texts->last_name;
                    $ma_date = $ma_texts->check_date;
                }
            } else {
                $ma_name = "";
                $ma_date = "";
            }

            $ex_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.ex_forward_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('designations', 'designations.id', '=', 'increments_process_acs.ex_position')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.ex_forward_date',
                    'designations.designation_name',
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();

            if (count($ex_text) > 0) {
                foreach ($ex_text as $ex_texts) {
                    $ex_name = $ex_texts->title . ' ' . $ex_texts->initials . ' ' . $ex_texts->last_name;
                    $ex_position = $ex_texts->designation_name;
                    $ex_date = $ex_texts->ex_forward_date;
                }
            } else {
                $ex_name = "";
                $ex_position = "";
                $ex_date = "";
            }

            $ser_part1 = increAcSerMain::where('incre_id', '=', $tbID)
                ->get();
            if (count($ser_part1) > 0) {
                foreach ($ser_part1 as $ser_part1s) {
                    $under_lect_week = $ser_part1s->under_lect_conduct_week;
                    $under_lect_year = $ser_part1s->under_lect_conduct_year;
                    $post_lect_week = $ser_part1s->post_lect_conduct_week;
                    $post_lect_year = $ser_part1s->post_lect_conduct_year;
                    $under_tutor_week = $ser_part1s->under_tutor_conduct_week;
                    $under_tutor_year = $ser_part1s->under_tutor_conduct_year;
                    $post_tutor_weeek = $ser_part1s->post_tutor_conduct_week;
                    $post_tutor_year = $ser_part1s->post_tutor_conduct_year;
                    $under_pract_week = $ser_part1s->under_practical_conduct_week;
                    $under_pract_year = $ser_part1s->under_practical_conduct_year;
                    $post_pract_week = $ser_part1s->post_practical_conduct_week;
                    $post_pract_year = $ser_part1s->post_practical_conduct_year;
                    $under_project_sup = $ser_part1s->under_project_supervised;
                    $post_project_sup = $ser_part1s->post_project_supervised;
                }
            } else {
                $under_lect_week = "";
                $under_lect_year = "";
                $post_lect_week = "";
                $post_lect_year = "";
                $under_tutor_week = "";
                $under_tutor_year = "";
                $post_tutor_weeek = "";
                $post_tutor_year = "";
                $under_pract_week = "";
                $under_pract_year = "";
                $post_pract_week = "";
                $post_pract_year = "";
                $under_project_sup = "";
                $post_project_sup = "";
            }

            $maxSalaryRecord = Increment::where('emp_no', $emp_no)->orderBy('basic_sal', 'desc')->first();
            if ($maxSalaryRecord) {
                $incre_last_date = $maxSalaryRecord->effective_date;

                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->where('leave_academic_summeries.starting_date', '>=', $incre_last_date)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            } else {
                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            }

            $ser_part2 = increAcSerOther::where('incre_id', '=', $tbID)->get();

            $hodR_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.head_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as posi', 'posi.id', '=', 'increments_process_acs.head_position')
                ->join('categories as pt', 'pt.id', '=', 'increments_process_acs.head_position_type')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'posi.category_name as position',
                    'pt.category_name as appType',
                    'increments_process_acs.head_status',
                    'increments_process_acs.head_position_type',
                    'increments_process_acs.head_date',
                    'increments_process_acs.performance',
                    'increments_process_acs.conduct',
                    'increments_process_acs.other_comments',
                    'increments_process_acs.head_reason'
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();
            if (count($hodR_text) > 0) {
                foreach ($hodR_text as $hodR_texts) {
                    $hodr_name = $hodR_texts->title . ' ' . $hodR_texts->initials . ' ' . $hodR_texts->last_name;
                    if ($hodR_texts->head_position_type == 199) {
                        $hodr_position = $hodR_texts->position;
                    } else {
                        $hodr_position = $hodR_texts->position . '( ' . $hodR_texts->appType . ' )';
                    }

                    $hodr_date = $hodR_texts->head_date;
                    $perfomance = $hodR_texts->performance;
                    $conduct = $hodR_texts->conduct;
                    $comment = $hodR_texts->other_comments;
                    $hod_reason = $hodR_texts->head_reason;

                    if ($hodR_texts->head_status == 2) {
                        $hod_rec = "Recommended";
                    } else if ($hodR_texts->head_status == 3) {
                        $hod_rec = "Not Recommended";
                    } else {
                        $hod_rec = "";
                    }
                }
            } else {
                $hodr_name = "";
                $hodr_position = "";
                $hodr_date = "";
                $conduct = "";
                $comment = "";
                $hod_rec = "";
                $hod_reason = "";
            }

            $dean_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.dean_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as posi', 'posi.id', '=', 'increments_process_acs.dean_position')
                ->join('categories as pt', 'pt.id', '=', 'increments_process_acs.dean_position_type')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'posi.category_name as position',
                    'pt.category_name as appType',
                    'increments_process_acs.dean_status',
                    'increments_process_acs.dean_position_type',
                    'increments_process_acs.dean_date',
                    'increments_process_acs.observation',
                    'increments_process_acs.dean_reason'
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();

            if (count($dean_text) > 0) {
                foreach ($dean_text as $dean_texts) {
                    $dean_name = $dean_texts->title . ' ' . $dean_texts->initials . ' ' . $dean_texts->last_name;
                    if ($dean_texts->dean_position_type == 199) {
                        $dean_position = $dean_texts->position;
                    } else {
                        $dean_position = $dean_texts->position . '( ' . $dean_texts->appType . ' )';
                    }

                    $dean_date = $dean_texts->dean_date;
                    $observation = $dean_texts->observation;
                    $dean_reason = $dean_texts->dean_reason;

                    if ($dean_texts->dean_status == 2) {
                        $dean_rec = "Recommended";
                    } else if ($dean_texts->dean_status == 3) {
                        $dean_rec = "Not Recommended";
                    } else {
                        $dean_rec = "";
                    }
                }
            } else {
                $dean_name = "";
                $dean_position = "";
                $dean_date = "";
                $observation = "";
                $dean_reason = "";
                $dean_rec = "";
            }
        } else {
            $incre_id = 0;
            $emp_no = "";
            $name = "";
            $appDate = "";
            $depID = 0;
            $depName = "";
            $fac = "";
            $fAppDate = "";
            $desig = "";
            $salScale = "";
            $increDate = "";
            $bSal = "";
            $increAmount = "";
            $newBSal = "";

            $lock = 0;
            $depId = 0;
            $refNo = '';
            $increYear = "";
            $ma_name = "";
            $ma_date = "";

            $ex_name = "";
            $ex_position = "";
            $ex_date = "";

            $serDate = "";

            $under_lect_week = "";
            $under_lect_year = "";
            $post_lect_week = "";
            $post_lect_year = "";
            $under_tutor_week = "";
            $under_tutor_year = "";
            $post_tutor_weeek = "";
            $post_tutor_year = "";
            $under_pract_week = "";
            $under_pract_year = "";
            $post_pract_week = "";
            $post_pract_year = "";
            $under_project_sup = "";
            $post_project_sup = "";

            $ser_part2 = array();

            $hodr_name = "";
            $hodr_position = "";
            $hodr_date = "";
            $perfomance = "";
            $conduct = "";
            $comment = "";
            $hod_rec = "";
            $hod_reason = "";

            $facID = 0;

            $leave_text = array();

            $dean_name = "";
            $dean_position = "";
            $dean_date = "";
            $observation = "";
            $dean_reason = "";
            $dean_rec = "";
        }

        return view('admin.increment.Academic.allStatusDetails', compact(
            'incre_id',
            'emp_no',
            'name',
            'appDate',
            'depID',
            'depName',
            'fac',
            'fAppDate',
            'desig',
            'salScale',
            'increDate',
            'bSal',
            'increAmount',
            'newBSal',

            'lock',
            'depId',
            'refNo',
            'increYear',
            'ma_name',
            'ma_date',
            'ex_name',
            'ex_position',
            'ex_date',

            'serDate',

            'under_lect_week',
            'under_lect_year',
            'post_lect_week',
            'post_lect_year',
            'under_tutor_week',
            'under_tutor_year',
            'post_tutor_weeek',
            'post_tutor_year',
            'under_pract_week',
            'under_pract_year',
            'post_pract_week',
            'post_pract_year',
            'under_project_sup',
            'post_project_sup',

            'ser_part2',

            'hodr_name',
            'hodr_position',
            'hodr_date',
            'perfomance',
            'conduct',
            'comment',
            'hod_rec',
            'hod_reason',
            'facID',

            'leave_text',

            'dean_name',
            'dean_position',
            'dean_date',
            'observation',
            'dean_reason',
            'dean_rec'
        ));
    }
    // public function serSubmit(Request $request)
    // {

    //     $part1_text = new increAcSerMain();
    //     $part1_text->incre_id = $request->appID;
    //     $part1_text->incre_ref_no = $request->RefNo;
    //     $part1_text->under_lect_conduct_week = $request->lec_under_week;
    //     $part1_text->under_lect_conduct_year = $request->lect_under_year;
    //     $part1_text->post_lect_conduct_week = $request->lect_post_week;
    //     $part1_text->post_lect_conduct_year = $request->lect_post_year;
    //     $part1_text->under_tutor_conduct_week = $request->tuto_under_week;
    //     $part1_text->under_tutor_conduct_year = $request->tuto_under_year;
    //     $part1_text->post_tutor_conduct_week = $request->tuto_post_week;
    //     $part1_text->post_tutor_conduct_year = $request->tuto_post_year;
    //     $part1_text->under_practical_conduct_week = $request->pract_under_week;
    //     $part1_text->under_practical_conduct_year = $request->pract_under_year;
    //     $part1_text->post_practical_conduct_week = $request->pract_post_week;
    //     $part1_text->post_practical_conduct_year = $request->pract_post_year;
    //     $part1_text->under_project_supervised = $request->under_proj_sup;
    //     $part1_text->post_project_supervised = $request->post_proj_sup;
    //     $part1_text->user_id = auth()->user()->employee_no;
    //     $part1_text->save();

    //     if ($request->section5 != null) {
    //         for ($i = 0; $i < count($request->section5); $i++) {
    //             $sect5_text = new increAcSerOther();
    //             $sect5_text->incre_id = $request->appID;
    //             $sect5_text->incre_ref_no = $request->RefNo;
    //             $sect5_text->type_no = 304;
    //             $sect5_text->description = $request->section5[$i];
    //             $sect5_text->user_id = auth()->user()->employee_no;
    //             $sect5_text->save();
    //         }
    //     }

    //     if ($request->section6 != null) {
    //         for ($i = 0; $i < count($request->section6); $i++) {
    //             $sect6_text = new increAcSerOther();
    //             $sect6_text->incre_id = $request->appID;
    //             $sect6_text->incre_ref_no = $request->RefNo;
    //             $sect6_text->type_no = 305;
    //             $sect6_text->description = $request->section6[$i];
    //             $sect6_text->user_id = auth()->user()->employee_no;
    //             $sect6_text->save();
    //         }
    //     }

    //     if ($request->section7 != null) {
    //         for ($i = 0; $i < count($request->section7); $i++) {
    //             $sect7_text = new increAcSerOther();
    //             $sect7_text->incre_id = $request->appID;
    //             $sect7_text->incre_ref_no = $request->RefNo;
    //             $sect7_text->type_no = 306;
    //             $sect7_text->description = $request->section7[$i];
    //             $sect7_text->user_id = auth()->user()->employee_no;
    //             $sect7_text->save();
    //         }
    //     }

    //     if ($request->section8 != null) {
    //         for ($i = 0; $i < count($request->section8); $i++) {
    //             $sect8_text = new increAcSerOther();
    //             $sect8_text->incre_id = $request->appID;
    //             $sect8_text->incre_ref_no = $request->RefNo;
    //             $sect8_text->type_no = 307;
    //             $sect8_text->description = $request->section8[$i];
    //             $sect8_text->user_id = auth()->user()->employee_no;
    //             $sect8_text->save();
    //         }
    //     }

    //     if ($request->section9 != null) {
    //         for ($i = 0; $i < count($request->section9); $i++) {
    //             $sect9_text = new increAcSerOther();
    //             $sect9_text->incre_id = $request->appID;
    //             $sect9_text->incre_ref_no = $request->RefNo;
    //             $sect9_text->type_no = 308;
    //             $sect9_text->description = $request->section9[$i];
    //             $sect9_text->user_id = auth()->user()->employee_no;
    //             $sect9_text->save();
    //         }
    //     }

    //     if ($request->section10 != null) {
    //         for ($i = 0; $i < count($request->section10); $i++) {
    //             $sect10_text = new increAcSerOther();
    //             $sect10_text->incre_id = $request->appID;
    //             $sect10_text->incre_ref_no = $request->RefNo;
    //             $sect10_text->type_no = 309;
    //             $sect10_text->description = $request->section10[$i];
    //             $sect10_text->user_id = auth()->user()->employee_no;
    //             $sect10_text->save();
    //         }
    //     }

    //     $s_test = incrementsProcessAc::where('id', '=', $request->appID)
    //         ->update([
    //             'self_eval_status' => 2,
    //             'self_eval_date' => today(),
    //             'head_status' => 1
    //         ]);
    //     //comment

    //     $emp_text = Employee::join('categories as t', 't.id', '=', 'employees.title_id')
    //         ->join('designations', 'designations.id', '=', 'employees.designation_id')
    //         ->join('categories', 'designations.staff_grade', '=', 'categories.id')
    //         ->join('departments', 'departments.id', '=', 'employees.department_id')
    //         ->select(
    //             'employees.initials',
    //             'employees.last_name',
    //             'designations.designation_name',
    //             'departments.id as depID',
    //             'departments.department_name',
    //             'categories.category_name',
    //             't.category_name as title',
    //         )
    //         ->where('employees.employee_no', auth()->user()->employee_no)
    //         ->get();

    //     if (count($emp_text) > 0) {
    //         foreach ($emp_text as $emp_texts) {
    //             $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
    //             $depID = $emp_texts->depID;
    //             $depName = $emp_texts->department_name;
    //             $desig = $emp_texts->designation_name . ' ' . $emp_texts->category_name;
    //         }
    //     } else {
    //         $name = '';
    //         $depName = '';
    //         $desig = '';
    //         $depID = 0;
    //     }

    //     $hod_text = DepartmentHead::join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
    //         ->join('categories as t', 't.id', '=', 'employees.title_id')
    //         ->select(
    //             't.category_name as title',
    //             'employees.initials',
    //             'employees.last_name',
    //             'department_heads.email'
    //         )
    //         ->where('department_heads.department_id', '=', $depID)
    //         ->get();

    //     if (count($hod_text) > 0) {
    //         foreach ($hod_text as $hod_texts) {
    //             $hod_name = $hod_texts->title . ' ' . $hod_texts->initials . ' ' . $hod_texts->last_name;
    //             $hod_email = $hod_texts->email;
    //         }
    //     } else {
    //         $hod_name = '';
    //         $hod_email = '';
    //     }


    //     //send email
    //     $emailData = [
    //         'name' =>  $name,
    //         'headName' => $hod_name,
    //         'depName' => $depName,
    //         'desig' => $desig,

    //     ];

    //     $mail = new acIncreForwardToHodMail($emailData);
    //     //sending email
    //     Mail::to($hod_email)->send($mail);
    //     // Mail::to('<EMAIL>')->send($mail);

    //     $notification = array(
    //         'message' => 'Self evaluation report submitted successfully.',
    //         'alert-type' => 'success'
    //     );

    //     return redirect()->route('ac.incre.selfEval.page1.open')->with($notification);
    // }

    public function hodRecommendation($id)
    {
        $tbID = decrypt($id);

        $emp_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
            ->join('designations', 'designations.id', '=', 'increments_process_acs.desig_id')
            ->join('departments', 'departments.id', '=', 'increments_process_acs.dep_no')
            ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
            ->select(
                'employees.initials',
                'employees.last_name',

                'designations.designation_name',
                'departments.id as depID',
                'departments.department_name',
                'faculties.id as facID',
                'faculties.faculty_name',
                'categories.category_name',
                't.category_name as title',

                'employees.current_appointment_date',
                'employees.initial_appointment_date',
                'salary_scales.salary_scale_txt',

                'salary_scales.increment_value1',
                'increments_process_acs.emp_no',
                'increments_process_acs.incre_date',
                'increments_process_acs.ref_no',
                'increments_process_acs.id',
                'increments_process_acs.year',
                'increments_process_acs.incre_amount',
                'increments_process_acs.present_sal_step',
                'increments_process_acs.new_sal_step',
                'increments_process_acs.check_date',
                'increments_process_acs.self_eval_date'

            )
            ->where('increments_process_acs.id', $tbID)
            ->get();

        if (count($emp_text) > 0) {
            foreach ($emp_text as $emp_texts) {
                $incre_id = $emp_texts->id;
                $emp_no = $emp_texts->emp_no;
                $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                $appDate = $emp_texts->current_appointment_date;
                $depID = $emp_texts->depID;
                $depName = $emp_texts->department_name;
                $fac = $emp_texts->faculty_name;
                $fAppDate = $emp_texts->initial_appointment_date;
                $desig = $emp_texts->designation_name . ' ' . $emp_texts->category_name;
                $salScale = $emp_texts->salary_scale_txt;
                $increDate = $emp_texts->incre_date;
                $bSal = sprintf("%.2f", $emp_texts->present_sal_step);
                $increAmount = sprintf("%.2f", $emp_texts->incre_amount);
                $newBSal = sprintf("%.2f", $emp_texts->new_sal_step);
                $lock = $emp_texts->lock;
                $depId = $emp_texts->depID;
                $refNo = $emp_texts->ref_no;
                $increYear = $emp_texts->year;
                $serDate = $emp_texts->self_eval_date;
                $facID = $emp_texts->facID;
            }


            $ma_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.check_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.check_date'
                )
                ->where('increments_process_acs.id', $tbID)
                ->get();

            if (count($ma_text) > 0) {
                foreach ($ma_text as $ma_texts) {
                    $ma_name = $ma_texts->title . ' ' . $ma_texts->initials . ' ' . $ma_texts->last_name;
                    $ma_date = $ma_texts->check_date;
                }
            } else {
                $ma_name = "";
                $ma_date = "";
            }

            $ex_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.ex_forward_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('designations', 'designations.id', '=', 'increments_process_acs.ex_position')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.ex_forward_date',
                    'designations.designation_name',
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();

            if (count($ex_text) > 0) {
                foreach ($ex_text as $ex_texts) {
                    $ex_name = $ex_texts->title . ' ' . $ex_texts->initials . ' ' . $ex_texts->last_name;
                    $ex_position = $ex_texts->designation_name;
                    $ex_date = $ex_texts->ex_forward_date;
                }
            } else {
                $ex_name = "";
                $ex_position = "";
                $ex_date = "";
            }

            $ser_part1 = increAcSerMain::where('incre_id', '=', $tbID)
                ->get();
            if (count($ser_part1) > 0) {
                foreach ($ser_part1 as $ser_part1s) {
                    $under_lect_week = $ser_part1s->under_lect_conduct_week;
                    $under_lect_year = $ser_part1s->under_lect_conduct_year;
                    $post_lect_week = $ser_part1s->post_lect_conduct_week;
                    $post_lect_year = $ser_part1s->post_lect_conduct_year;
                    $under_tutor_week = $ser_part1s->under_tutor_conduct_week;
                    $under_tutor_year = $ser_part1s->under_tutor_conduct_year;
                    $post_tutor_weeek = $ser_part1s->post_tutor_conduct_week;
                    $post_tutor_year = $ser_part1s->post_tutor_conduct_year;
                    $under_pract_week = $ser_part1s->under_practical_conduct_week;
                    $under_pract_year = $ser_part1s->under_practical_conduct_year;
                    $post_pract_week = $ser_part1s->post_practical_conduct_week;
                    $post_pract_year = $ser_part1s->post_practical_conduct_year;
                    $under_project_sup = $ser_part1s->under_project_supervised;
                    $post_project_sup = $ser_part1s->post_project_supervised;
                }
            } else {
                $under_lect_week = "";
                $under_lect_year = "";
                $post_lect_week = "";
                $post_lect_year = "";
                $under_tutor_week = "";
                $under_tutor_year = "";
                $post_tutor_weeek = "";
                $post_tutor_year = "";
                $under_pract_week = "";
                $under_pract_year = "";
                $post_pract_week = "";
                $post_pract_year = "";
                $under_project_sup = "";
                $post_project_sup = "";
            }

            $ser_part2 = increAcSerOther::where('incre_id', '=', $tbID)->get();

            $maxSalaryRecord = Increment::where('emp_no', $emp_no)->orderBy('basic_sal', 'desc')->first();
            if ($maxSalaryRecord) {
                $incre_last_date = $maxSalaryRecord->effective_date;

                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->where('leave_academic_summeries.starting_date', '>=', $incre_last_date)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            } else {
                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            }

            $dn_text = FacultyDean::join('employees', 'employees.employee_no', '=', 'faculty_deans.emp_no')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as appType', 'appType.id', '=', 'faculty_deans.appointmemt_type')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'faculty_deans.appointmemt_type',
                    'faculty_deans.active_status',
                    'appType.category_name as appName',
                    'faculty_deans.email'
                )
                ->where('faculty_deans.faculty_id', '=', $facID)
                ->where('faculty_deans.active_status', '=', 1)
                ->get();
            if (count($dn_text) > 0) {
                foreach ($dn_text as $dn_texts) {
                    $dean_name = $dn_texts->title . ' ' . $dn_texts->initials . ' ' . $dn_texts->last_name;
                    if ($dn_texts->appointmemt_type == 199) {
                        $dn_posi = 'Dean';
                    } else {
                        $dn_posi = 'Dean ( ' . $dn_texts->appName . ' )';
                    }
                    $dean_email = $dn_texts->email;
                }
            } else {
                $dean_name = "";
                $dn_posi = "";
                $dean_email = "";
            }
        } else {
            $incre_id = 0;
            $emp_no = "";
            $name = "";
            $appDate = "";
            $depID = 0;
            $depName = "";
            $fac = "";
            $fAppDate = "";
            $desig = "";
            $salScale = "";
            $increDate = "";
            $bSal = "";
            $increAmount = "";
            $newBSal = "";

            $lock = 0;
            $depId = 0;
            $refNo = '';
            $increYear = "";
            $ma_name = "";
            $ma_date = "";

            $ex_name = "";
            $ex_position = "";
            $ex_date = "";

            $serDate = "";

            $under_lect_week = "";
            $under_lect_year = "";
            $post_lect_week = "";
            $post_lect_year = "";
            $under_tutor_week = "";
            $under_tutor_year = "";
            $post_tutor_weeek = "";
            $post_tutor_year = "";
            $under_pract_week = "";
            $under_pract_year = "";
            $post_pract_week = "";
            $post_pract_year = "";
            $under_project_sup = "";
            $post_project_sup = "";

            $ser_part2 = array();


            $dean_name = "";
            $dn_posi = "";
            $dean_email = "";

            $leave_text = array();
        }



        return view('admin.increment.Academic.hodRecommendation', compact(
            'incre_id',
            'emp_no',
            'name',
            'appDate',
            'depID',
            'depName',
            'fac',
            'fAppDate',
            'desig',
            'salScale',
            'increDate',
            'bSal',
            'increAmount',
            'newBSal',

            'lock',
            'depId',
            'refNo',
            'increYear',
            'ma_name',
            'ma_date',
            'ex_name',
            'ex_position',
            'ex_date',

            'serDate',

            'under_lect_week',
            'under_lect_year',
            'post_lect_week',
            'post_lect_year',
            'under_tutor_week',
            'under_tutor_year',
            'post_tutor_weeek',
            'post_tutor_year',
            'under_pract_week',
            'under_pract_year',
            'post_pract_week',
            'post_pract_year',
            'under_project_sup',
            'post_project_sup',

            'ser_part2',
            'dean_name',
            'dn_posi',
            'dean_email',
            'leave_text'
        ));
    }

    public function hodRecomStore(Request $request)
    {

        $hod_text = DepartmentHead::join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('categories as posi', 'posi.id', '=', 'department_heads.head_position')
            ->join('categories as posiType', 'posiType.id', '=', 'department_heads.appointmemt_type')
            ->select(
                'department_heads.head_position',
                'department_heads.appointmemt_type',
                't.category_name as title',
                'employees.initials',
                'employees.last_name',
                'posi.category_name as posiName',
                'posiType.category_name as posiTypeName'
            )
            ->where('department_heads.department_id', '=', $request->depID)
            ->where('department_heads.emp_no', '=', auth()->user()->employee_no)
            ->get();
        if (count($hod_text) > 0) {
            foreach ($hod_text as $hod_texts) {
                $hod_name = $hod_texts->title . ' ' . $hod_texts->initials . ' ' . $hod_texts->last_name;
                $hod_position = $hod_texts->head_position;
                $hos_posi_type = $hod_texts->appointmemt_type;
                if ($hos_posi_type == 199) {
                    $hod_posi_name = $hod_texts->posiname;
                } else {
                    $hod_posi_name = $hod_texts->posiname . '( ' . $hod_texts->posiTypeName . ' )';
                }
            }

            $fac_text = Department::where('id', $request->depID)->get();
            if (count($fac_text) > 0) {
                foreach ($fac_text as $fac_texts) {
                    $fac_id = $fac_texts->faculty_code;
                }
            } else {
                $fac_id = 0;
            }

            if ($fac_id == 50) {
                $fac_head_text = Faculty::where('id', $fac_id)->get();
                if (count($fac_head_text) > 0) {
                    foreach ($fac_head_text as $fac_head_texts) {
                        $vc_email = $fac_head_texts->dean_email;
                    }

                    $s_test = incrementsProcessAc::where('id', '=', $request->increID)
                        ->update([
                            'head_status' => $request->recom,
                            'head_user_id' => auth()->user()->employee_no,
                            'head_position' => $hod_position,
                            'head_position_type' => $hos_posi_type,
                            'head_date' => today(),
                            'performance' => $request->preformance,
                            'conduct' => $request->conduct,
                            'other_comments' => $request->comment,
                            'head_reason' => $request->notRecomCmm,
                            'vc_status' => 1
                        ]);

                    //send email
                    $emailData = [
                        'name' =>  $request->name,
                        'deanName' => $hod_name,
                        'deanPosi' => $hod_posi_name,
                        'facName' => $request->depName,
                        'vcEmail' => $vc_email,
                    ];

                    $mail = new acIncreForwardVCMail($emailData);
                    //sending email
                    Mail::to($vc_email)->send($mail);
                    // Mail::to('<EMAIL>')->send($mail);
                } else {
                    $notification = array(
                        'message' => 'This form was not forwarded to the vice chancellor.',
                        'alert-type' => 'error'
                    );
                }
            } else {
                $s_test = incrementsProcessAc::where('id', '=', $request->increID)
                    ->update([
                        'head_status' => $request->recom,
                        'head_user_id' => auth()->user()->employee_no,
                        'head_position' => $hod_position,
                        'head_position_type' => $hos_posi_type,
                        'head_date' => today(),
                        'performance' => $request->preformance,
                        'conduct' => $request->conduct,
                        'other_comments' => $request->comment,
                        'head_reason' => $request->notRecomCmm,
                        'dean_status' => 1
                    ]);

                //send email
                $emailData = [
                    'name' =>  $request->name,
                    'headName' => $hod_name,
                    'headPosi' => $hod_posi_name,
                    'depName' => $request->depName,
                    'deanName' => $request->deanName,
                    'deanEmail' => $request->deanEmail,
                ];

                $mail = new acIncreForwardToDeanMail($emailData);
                //sending email
                Mail::to($request->deanEmail)->send($mail);
                // Mail::to('<EMAIL>')->send($mail);

                $notification = array(
                    'message' => 'Successfully forwarded.',
                    'alert-type' => 'success'
                );
            }
        } else {
            $notification = array(
                'message' => 'Unsuccessfully forwarded.',
                'alert-type' => 'error'
            );
        }

        return redirect()->route('ac.incre.hod.list.open')->with($notification);
    }

    // public function deanListOpen()
    // {
    //     $mainBranch = Auth()->user()->main_branch_id;

    //     if ($mainBranch == 51) {
    //         $dean_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
    //             ->join('categories as t', 't.id', '=', 'employees.title_id')
    //             ->join('departments', 'departments.id', '=', 'employees.department_id')
    //             ->join('designations', 'designations.id', '=', 'employees.designation_id')
    //             ->join('categories as dg', 'dg.id', '=', 'designations.staff_grade')
    //             ->select(
    //                 'increments_process_acs.id',
    //                 'increments_process_acs.ref_no',
    //                 'increments_process_acs.emp_no',
    //                 'increments_process_acs.incre_date',
    //                 't.category_name as title',
    //                 'employees.initials',
    //                 'employees.last_name',
    //                 'departments.department_name',
    //                 'designations.designation_name',
    //                 'dg.category_name as grade',
    //             )
    //             ->where('increments_process_acs.dean_status', '=', 1)
    //             ->get();
    //     } else {
    //         $dean_dep_text = FacultyDean::select('faculty_id')
    //             ->where('emp_no', '=', auth()->user()->employee_no)
    //             ->where('faculty_id', '!=', 51)
    //             ->get();

    //         if (count($dean_dep_text) > 0) {
    //             foreach ($dean_dep_text as  $dean_dep_texts) {
    //                 $dean_fuc_id =  $dean_dep_texts->faculty_id;
    //             }
    //         } else {
    //             $dean_fuc_id = 0;
    //         }

    //         $dean_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
    //             ->join('categories as t', 't.id', '=', 'employees.title_id')
    //             ->join('departments', 'departments.id', '=', 'employees.department_id')
    //             ->join('designations', 'designations.id', '=', 'employees.designation_id')
    //             ->join('categories as dg', 'dg.id', '=', 'designations.staff_grade')
    //             ->select(
    //                 'increments_process_acs.id',
    //                 'increments_process_acs.ref_no',
    //                 'increments_process_acs.emp_no',
    //                 'increments_process_acs.incre_date',
    //                 't.category_name as title',
    //                 'employees.initials',
    //                 'employees.last_name',
    //                 'departments.department_name',
    //                 'designations.designation_name',
    //                 'dg.category_name as grade',
    //             )
    //             ->where('employees.faculty_id', '=', $dean_fuc_id)
    //             ->where('increments_process_acs.dean_status', '=', 1)
    //             ->get();
    //     }

    //     return view('admin.increment.Academic.deanList', compact('dean_text'));
    // }

    public function deanRecommendation($id)
    {
        $tbID = decrypt($id);

        $emp_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
            ->join('designations', 'designations.id', '=', 'increments_process_acs.desig_id')
            ->join('departments', 'departments.id', '=', 'increments_process_acs.dep_no')
            ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
            ->select(
                'employees.initials',
                'employees.last_name',

                'designations.designation_name',
                'departments.id as depID',
                'departments.department_name',
                'faculties.id as facID',
                'faculties.faculty_name',
                'categories.category_name',
                't.category_name as title',

                'employees.current_appointment_date',
                'employees.initial_appointment_date',
                'salary_scales.salary_scale_txt',

                'salary_scales.increment_value1',
                'increments_process_acs.emp_no',
                'increments_process_acs.incre_date',
                'increments_process_acs.ref_no',
                'increments_process_acs.id',
                'increments_process_acs.year',
                'increments_process_acs.incre_amount',
                'increments_process_acs.present_sal_step',
                'increments_process_acs.new_sal_step',
                'increments_process_acs.check_date',
                'increments_process_acs.self_eval_date'

            )
            ->where('increments_process_acs.id', $tbID)
            ->get();

        if (count($emp_text) > 0) {
            foreach ($emp_text as $emp_texts) {
                $incre_id = $emp_texts->id;
                $emp_no = $emp_texts->emp_no;
                $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                $appDate = $emp_texts->current_appointment_date;
                $depID = $emp_texts->depID;
                $depName = $emp_texts->department_name;
                $fac = $emp_texts->faculty_name;
                $fAppDate = $emp_texts->initial_appointment_date;
                $desig = $emp_texts->designation_name . ' ' . $emp_texts->category_name;
                $salScale = $emp_texts->salary_scale_txt;
                $increDate = $emp_texts->incre_date;
                $bSal = sprintf("%.2f", $emp_texts->present_sal_step);
                $increAmount = sprintf("%.2f", $emp_texts->incre_amount);
                $newBSal = sprintf("%.2f", $emp_texts->new_sal_step);
                $lock = $emp_texts->lock;
                $depId = $emp_texts->depID;
                $refNo = $emp_texts->ref_no;
                $increYear = $emp_texts->year;
                $serDate = $emp_texts->self_eval_date;
                $facID = $emp_texts->facID;
            }


            $ma_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.check_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.check_date'
                )
                ->where('increments_process_acs.id', $tbID)
                ->get();

            if (count($ma_text) > 0) {
                foreach ($ma_text as $ma_texts) {
                    $ma_name = $ma_texts->title . ' ' . $ma_texts->initials . ' ' . $ma_texts->last_name;
                    $ma_date = $ma_texts->check_date;
                }
            } else {
                $ma_name = "";
                $ma_date = "";
            }

            $ex_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.ex_forward_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('designations', 'designations.id', '=', 'increments_process_acs.ex_position')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.ex_forward_date',
                    'designations.designation_name',
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();

            if (count($ex_text) > 0) {
                foreach ($ex_text as $ex_texts) {
                    $ex_name = $ex_texts->title . ' ' . $ex_texts->initials . ' ' . $ex_texts->last_name;
                    $ex_position = $ex_texts->designation_name;
                    $ex_date = $ex_texts->ex_forward_date;
                }
            } else {
                $ex_name = "";
                $ex_position = "";
                $ex_date = "";
            }

            $ser_part1 = increAcSerMain::where('incre_id', '=', $tbID)
                ->get();
            if (count($ser_part1) > 0) {
                foreach ($ser_part1 as $ser_part1s) {
                    $under_lect_week = $ser_part1s->under_lect_conduct_week;
                    $under_lect_year = $ser_part1s->under_lect_conduct_year;
                    $post_lect_week = $ser_part1s->post_lect_conduct_week;
                    $post_lect_year = $ser_part1s->post_lect_conduct_year;
                    $under_tutor_week = $ser_part1s->under_tutor_conduct_week;
                    $under_tutor_year = $ser_part1s->under_tutor_conduct_year;
                    $post_tutor_weeek = $ser_part1s->post_tutor_conduct_week;
                    $post_tutor_year = $ser_part1s->post_tutor_conduct_year;
                    $under_pract_week = $ser_part1s->under_practical_conduct_week;
                    $under_pract_year = $ser_part1s->under_practical_conduct_year;
                    $post_pract_week = $ser_part1s->post_practical_conduct_week;
                    $post_pract_year = $ser_part1s->post_practical_conduct_year;
                    $under_project_sup = $ser_part1s->under_project_supervised;
                    $post_project_sup = $ser_part1s->post_project_supervised;
                }
            } else {
                $under_lect_week = "";
                $under_lect_year = "";
                $post_lect_week = "";
                $post_lect_year = "";
                $under_tutor_week = "";
                $under_tutor_year = "";
                $post_tutor_weeek = "";
                $post_tutor_year = "";
                $under_pract_week = "";
                $under_pract_year = "";
                $post_pract_week = "";
                $post_pract_year = "";
                $under_project_sup = "";
                $post_project_sup = "";
            }

            $maxSalaryRecord = Increment::where('emp_no', $emp_no)->orderBy('basic_sal', 'desc')->first();
            if ($maxSalaryRecord) {
                $incre_last_date = $maxSalaryRecord->effective_date;

                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->where('leave_academic_summeries.starting_date', '>=', $incre_last_date)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            } else {
                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            }

            $ser_part2 = increAcSerOther::where('incre_id', '=', $tbID)->get();

            $hodR_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.head_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as posi', 'posi.id', '=', 'increments_process_acs.head_position')
                ->join('categories as pt', 'pt.id', '=', 'increments_process_acs.head_position_type')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'posi.category_name as position',
                    'pt.category_name as appType',
                    'increments_process_acs.head_status',
                    'increments_process_acs.head_position_type',
                    'increments_process_acs.head_date',
                    'increments_process_acs.performance',
                    'increments_process_acs.conduct',
                    'increments_process_acs.other_comments',
                    'increments_process_acs.head_reason'
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();
            if (count($hodR_text) > 0) {
                foreach ($hodR_text as $hodR_texts) {
                    $hodr_name = $hodR_texts->title . ' ' . $hodR_texts->initials . ' ' . $hodR_texts->last_name;
                    if ($hodR_texts->head_position_type == 199) {
                        $hodr_position = $hodR_texts->position;
                    } else {
                        $hodr_position = $hodR_texts->position . '( ' . $hodR_texts->appType . ' )';
                    }

                    $hodr_date = $hodR_texts->head_date;
                    $perfomance = $hodR_texts->performance;
                    $conduct = $hodR_texts->conduct;
                    $comment = $hodR_texts->other_comments;
                    $hod_reason = $hodR_texts->head_reason;

                    if ($hodR_texts->head_status == 2) {
                        $hod_rec = "Recommended";
                    } else if ($hodR_texts->head_status == 3) {
                        $hod_rec = "Not Recommended";
                    } else {
                        $hod_rec = "";
                    }
                }
            } else {
                $hodr_name = "";
                $hodr_position = "";
                $hodr_date = "";
                $conduct = "";
                $comment = "";
                $hod_rec = "";
                $hod_reason = "";
            }

            $vc_text = FacultyDean::join('employees', 'employees.employee_no', '=', 'faculty_deans.emp_no')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as appType', 'appType.id', '=', 'faculty_deans.appointmemt_type')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'faculty_deans.appointmemt_type',
                    'faculty_deans.active_status',
                    'appType.category_name as appName',
                    'faculty_deans.email'
                )
                ->where('faculty_deans.faculty_id', '=', 51)
                ->where('faculty_deans.active_status', '=', 1)
                ->get();
            if (count($vc_text) > 0) {
                foreach ($vc_text as $dn_texts) {
                    $vc_name = $dn_texts->title . ' ' . $dn_texts->initials . ' ' . $dn_texts->last_name;
                    if ($dn_texts->appointmemt_type == 199) {
                        $vc_posi = 'Vice-Chancellor';
                    } else {
                        $vc_posi = 'Vice-Chancellor ( ' . $dn_texts->appName . ' )';
                    }
                    $vc_email = $dn_texts->email;
                }
            } else {
                $vc_name = "";
                $vc_posi = "";
                $vc_email = "";
            }
        } else {
            $incre_id = 0;
            $emp_no = "";
            $name = "";
            $appDate = "";
            $depID = 0;
            $depName = "";
            $fac = "";
            $fAppDate = "";
            $desig = "";
            $salScale = "";
            $increDate = "";
            $bSal = "";
            $increAmount = "";
            $newBSal = "";

            $lock = 0;
            $depId = 0;
            $refNo = '';
            $increYear = "";
            $ma_name = "";
            $ma_date = "";

            $ex_name = "";
            $ex_position = "";
            $ex_date = "";

            $serDate = "";

            $under_lect_week = "";
            $under_lect_year = "";
            $post_lect_week = "";
            $post_lect_year = "";
            $under_tutor_week = "";
            $under_tutor_year = "";
            $post_tutor_weeek = "";
            $post_tutor_year = "";
            $under_pract_week = "";
            $under_pract_year = "";
            $post_pract_week = "";
            $post_pract_year = "";
            $under_project_sup = "";
            $post_project_sup = "";

            $ser_part2 = array();

            $hodr_name = "";
            $hodr_position = "";
            $hodr_date = "";
            $perfomance = "";
            $conduct = "";
            $comment = "";
            $hod_rec = "";
            $hod_reason = "";

            $vc_name = "";
            $vc_posi = "";
            $vc_email = "";
            $facID = 0;

            $leave_text = array();
        }



        return view('admin.increment.Academic.deanRecommendation', compact(
            'incre_id',
            'emp_no',
            'name',
            'appDate',
            'depID',
            'depName',
            'fac',
            'fAppDate',
            'desig',
            'salScale',
            'increDate',
            'bSal',
            'increAmount',
            'newBSal',

            'lock',
            'depId',
            'refNo',
            'increYear',
            'ma_name',
            'ma_date',
            'ex_name',
            'ex_position',
            'ex_date',

            'serDate',

            'under_lect_week',
            'under_lect_year',
            'post_lect_week',
            'post_lect_year',
            'under_tutor_week',
            'under_tutor_year',
            'post_tutor_weeek',
            'post_tutor_year',
            'under_pract_week',
            'under_pract_year',
            'post_pract_week',
            'post_pract_year',
            'under_project_sup',
            'post_project_sup',

            'ser_part2',

            'hodr_name',
            'hodr_position',
            'hodr_date',
            'perfomance',
            'conduct',
            'comment',
            'hod_rec',
            'hod_reason',

            'vc_name',
            'vc_posi',
            'facID',
            'vc_email',

            'leave_text'
        ));
    }

    public function deanRecomStore(Request $request)
    {

        $dean_text = FacultyDean::join('employees', 'employees.employee_no', '=', 'faculty_deans.emp_no')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('categories as posiType', 'posiType.id', '=', 'faculty_deans.appointmemt_type')
            ->select(
                'faculty_deans.appointmemt_type',
                't.category_name as title',
                'employees.initials',
                'employees.last_name',
                'posiType.category_name as posiTypeName'
            )
            ->where('faculty_deans.faculty_id', '=', $request->facID)
            ->where('faculty_deans.emp_no', '=', auth()->user()->employee_no)
            ->get();

        if (count($dean_text) > 0) {
            foreach ($dean_text as $dean_texts) {
                $dean_name = $dean_texts->title . ' ' . $dean_texts->initials . ' ' . $dean_texts->last_name;

                $dean_posi_type = $dean_texts->appointmemt_type;
                if ($dean_posi_type == 199) {
                    $dean_posi_name = 'Dean';
                } else {
                    $dean_posi_name = 'Dean ( ' . $dean_texts->posiTypeName . ' )';
                }
            }

            $s_test = incrementsProcessAc::where('id', '=', $request->increID)
                ->update([
                    'dean_status' => $request->recom,
                    'dean_user_id' => auth()->user()->employee_no,
                    'dean_position' => 238,
                    'dean_position_type' => $dean_posi_type,
                    'dean_date' => today(),
                    'observation' => $request->observation,
                    'dean_reason' => $request->notRecomCmm,
                    'vc_status' => 1
                ]);

            //send email
            $emailData = [
                'name' =>  $request->name,
                'deanName' => $dean_name,
                'deanPosi' => $dean_posi_name,
                'facName' => $request->facName,
                'vcName' => $request->vcName,
                'vcEmail' => $request->vcEmail,
            ];

            $mail = new acIncreForwardVCMail($emailData);
            //sending email
            Mail::to($request->vcEmail)->send($mail);
            // Mail::to('<EMAIL>')->send($mail);

            $notification = array(
                'message' => 'Successfully submitted.',
                'alert-type' => 'success'
            );
        } else {
            $notification = array(
                'message' => 'Successfully submitted.',
                'alert-type' => 'error'
            );
        }
        //redirect to list
        return redirect()->route('ac.incre.dean.list.open')->with($notification);
    }

    public function deuListOpen()
    {

        $year = now()->year;
        $currentProcessing = incrementsProcessAc::select('emp_no')->where('year', $year)->get();
        $currentYearDueIncrements = Employee::leftJoin('increments as i', function ($join) use ($year) {
            $join->on('employees.employee_no', '=', 'i.emp_no')
                ->whereYear('i.effective_date', $year);
        })
            ->whereNull('i.id') // No increment for current year
            ->whereRaw("STR_TO_DATE(CONCAT('$year-', employees.increment_date), '%Y-%m-%d') <= CURDATE()") // increment_date passed in current year
            ->whereNotIn('employees.employee_no', $currentProcessing)
            ->select('employees.employee_no')
            ->get();

        $lastYear = now()->subYear()->year;
        $lastYearDueIncrements = Employee::leftJoin('increments as i', function ($join) use ($lastYear) {
            $join->on('employees.employee_no', '=', 'i.emp_no')
                ->whereYear('i.effective_date', $lastYear);
        })
            ->whereNull('i.id') // No increment for last year
            ->select('employees.employee_no')
            ->get();

        $currentYearEmpNos = $currentYearDueIncrements->pluck('employee_no');
        $lastYearEmpNos = $lastYearDueIncrements->pluck('employee_no');

        $combinedEmpNos = $currentYearEmpNos->merge($lastYearEmpNos)->unique();

        if (Auth()->user()->hasRole(['administrator'])) {

            $incrementDue = Employee::join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select(
                    'employees.employee_no',
                    'employees.initials',
                    'employees.last_name',
                    'employees.increment_date',
                    't.category_name as title',
                    'designations.designation_name',
                    'departments.department_name',
                    'categories.category_name',
                )
                ->whereIn('employees.employee_no', $combinedEmpNos)
                ->where('employees.main_branch_id', 52)
                ->where('employees.employee_status_id', 110)
                ->whereIn('employees.employee_work_type', [138, 139])
                ->orderBy('employees.increment_date', 'ASC')
                ->orderBy('employees.employee_no', 'ASC')
                ->get();
        } else {
            $incrementDue = Employee::join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select(
                    'employees.employee_no',
                    'employees.initials',
                    'employees.last_name',
                    'employees.increment_date',
                    't.category_name as title',
                    'designations.designation_name',
                    'departments.department_name',
                    'categories.category_name',
                )
                ->whereIn('employees.employee_no', $combinedEmpNos)
                ->where('employees.main_branch_id', 52)
                ->where('employees.employee_status_id', 110)
                ->whereIn('employees.employee_work_type', [138, 139])
                ->where('employees.assign_ma_user_id', Auth()->user()->employee_no)
                ->orderBy('employees.increment_date', 'ASC')
                ->orderBy('employees.employee_no', 'ASC')
                ->get();
        }

        return view('admin.increment.Academic.dueList', compact('incrementDue'));
    }

    public function dueDetails($id)
    {

        $empNo = decrypt($id);

        $emp_text = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            //->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
            ->select(
                'employees.employee_no',
                'employees.initials',
                'employees.last_name',
                'employees.increment_date',
                'designations.designation_name',
                'departments.department_name',
                'departments.id',
                'categories.category_name',
                't.category_name as title',
                'employees.increment_date',
                'employees.current_appointment_date',
                'employees.initial_appointment_date',
                'salary_scales.salary_scale_txt',
                'employees.current_basic_salary',
                'salary_scales.increment_value1',
                'employees.lock',
                'employees.designation_id'

            )
            ->where('employees.employee_no', $empNo)
            ->get();

        if (count($emp_text) > 0) {
            foreach ($emp_text as $emp_texts) {
                $emp_no = $emp_texts->employee_no;
                $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                $appDate = $emp_texts->current_appointment_date;
                $depName = $emp_texts->department_name;
                $fAppDate = $emp_texts->initial_appointment_date;
                $desig = $emp_texts->designation_name . ' ' . $emp_texts->category_name;
                $salScale = $emp_texts->salary_scale_txt;
                $increDate = $emp_texts->increment_date;
                $bSal = sprintf("%.2f", $emp_texts->current_basic_salary);
                $increAmount = sprintf("%.2f", $emp_texts->increment_value1);
                $newBSal = sprintf("%.2f", ($bSal + $increAmount));
                $lock = $emp_texts->lock;
                $depId = $emp_texts->id;
                $desigID = $emp_texts->designation_id;
            }


            $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                ->where('increments.emp_no', $empNo)
                ->orderBy('increments.effective_date', 'DESC')
                ->get();
        } else {
            $emp_no = "";
            $name = "";
            $appDate = "";
            $depName = "";
            $fAppDate = "";
            $desig = "";
            $salScale = "";
            $increDate = "";

            $increments = array();
        }



        return view('admin.increment.Academic.dueListDetails', compact(
            'emp_no',
            'name',
            'appDate',
            'depName',
            'fAppDate',
            'desig',
            'salScale',
            'increDate',

            'increments',


        ));
    }

    public function select_hod_dean()
    {

        $empNo = auth()->user()->employee_no;

        $hod_text = DepartmentHead::where('emp_no', $empNo)
            ->where('active_status', 1)
            ->get();

        if (count($hod_text) > 0) {
            $hod = 1;
            foreach ($hod_text as $hod_texts) {
                $dep_id = $hod_texts->department_id;
            }
        } else {
            $hod = 0;
            $dep_id = 0;
        }

        $dean_text = FacultyDean::where('emp_no', $empNo)
            ->where('active_status', 1)
            ->get();

        if (count($dean_text) > 0) {
            $dean = 1;
            foreach ($dean_text as $dean_texts) {
                $fac_id = $dean_texts->faculty_id;
            }
        } else {
            $dean = 0;
            $fac_id = 0;
        }

        $dp_emp_text = incrementsProcessAc::where('dep_no', $dep_id)
            ->where('ex_forward_status', '>', 1)
            ->where('head_status', '<=', 1)
            ->get();

        if (count($dp_emp_text) > 0) {
            $dep_emp = 1;
        } else {
            $dep_emp = 0;
        }

        $fac_emp_text = incrementsProcessAc::join('departments', 'departments.id', 'increments_process_acs.dep_no')
            ->where('departments.faculty_code', $fac_id)
            ->where('increments_process_acs.dean_status', 1)
            ->get();

        if (count($fac_emp_text) > 0) {
            $fac_emp = 1;
        } else {
            $fac_emp = 0;
        }



        if ($hod == 0 && $dean == 0) {
            return view('admin.commonPages.coming_soon');
        } elseif ($hod == 1 && $dean == 0) {
            return redirect()->route('ac.incre.hod.list.open');
        } elseif ($hod == 0 && $dean == 1) {
            return redirect()->route('ac.incre.dean.list.open');
        } elseif ($hod == 1 && $dean == 1) {
            if ($dep_emp == 0 && $fac_emp == 0) {
                return redirect()->route('ac.incre.dean.list.open');
            } elseif ($dep_emp == 1 && $fac_emp == 0) {
                return redirect()->route('ac.incre.hod.list.open');
            } elseif ($dep_emp == 0 && $fac_emp == 1) {
                return redirect()->route('ac.incre.dean.list.open');
            } elseif ($dep_emp == 1 && $fac_emp == 1) {
                # code...
            } else {
                return redirect()->route('ac.incre.hod.list.open');
            }
        } else {
            return view('admin.commonPages.coming_soon');
        }
    }

    // public function vcListOpen()
    // {


    //     $vc_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
    //         ->join('categories as t', 't.id', '=', 'employees.title_id')
    //         ->join('departments', 'departments.id', '=', 'employees.department_id')
    //         ->join('designations', 'designations.id', '=', 'employees.designation_id')
    //         ->join('categories as dg', 'dg.id', '=', 'designations.staff_grade')
    //         ->select(
    //             'increments_process_acs.id',
    //             'increments_process_acs.ref_no',
    //             'increments_process_acs.emp_no',
    //             'increments_process_acs.incre_date',
    //             't.category_name as title',
    //             'employees.initials',
    //             'employees.last_name',
    //             'departments.department_name',
    //             'designations.designation_name',
    //             'dg.category_name as grade',
    //         )
    //         ->where('increments_process_acs.vc_status', '=', 1)
    //         ->get();


    //     return view('admin.increment.Academic.vcList', compact('vc_text'));
    // }

    public function vcApproval($id)
    {
        $tbID = decrypt($id);

        $emp_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
            ->join('designations', 'designations.id', '=', 'increments_process_acs.desig_id')
            ->join('departments', 'departments.id', '=', 'increments_process_acs.dep_no')
            ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
            ->select(
                'employees.initials',
                'employees.last_name',

                'designations.designation_name',
                'departments.id as depID',
                'departments.department_name',
                'faculties.id as facID',
                'faculties.faculty_name',
                'categories.category_name',
                't.category_name as title',

                'employees.current_appointment_date',
                'employees.initial_appointment_date',
                'salary_scales.salary_scale_txt',

                'salary_scales.increment_value1',
                'increments_process_acs.emp_no',
                'increments_process_acs.incre_date',
                'increments_process_acs.ref_no',
                'increments_process_acs.id',
                'increments_process_acs.year',
                'increments_process_acs.incre_amount',
                'increments_process_acs.present_sal_step',
                'increments_process_acs.new_sal_step',
                'increments_process_acs.check_date',
                'increments_process_acs.self_eval_date'

            )
            ->where('increments_process_acs.id', $tbID)
            ->get();

        if (count($emp_text) > 0) {
            foreach ($emp_text as $emp_texts) {
                $incre_id = $emp_texts->id;
                $emp_no = $emp_texts->emp_no;
                $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                $appDate = $emp_texts->current_appointment_date;
                $depID = $emp_texts->depID;
                $depName = $emp_texts->department_name;
                $fac = $emp_texts->faculty_name;
                $fAppDate = $emp_texts->initial_appointment_date;
                $desig = $emp_texts->designation_name . ' ' . $emp_texts->category_name;
                $salScale = $emp_texts->salary_scale_txt;
                $increDate = $emp_texts->incre_date;
                $bSal = sprintf("%.2f", $emp_texts->present_sal_step);
                $increAmount = sprintf("%.2f", $emp_texts->incre_amount);
                $newBSal = sprintf("%.2f", $emp_texts->new_sal_step);
                $lock = $emp_texts->lock;
                $depId = $emp_texts->depID;
                $refNo = $emp_texts->ref_no;
                $increYear = $emp_texts->year;
                $serDate = $emp_texts->self_eval_date;
                $facID = $emp_texts->facID;
            }


            $ma_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.check_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.check_date'
                )
                ->where('increments_process_acs.id', $tbID)
                ->get();

            if (count($ma_text) > 0) {
                foreach ($ma_text as $ma_texts) {
                    $ma_name = $ma_texts->title . ' ' . $ma_texts->initials . ' ' . $ma_texts->last_name;
                    $ma_date = $ma_texts->check_date;
                }
            } else {
                $ma_name = "";
                $ma_date = "";
            }

            $ex_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.ex_forward_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('designations', 'designations.id', '=', 'increments_process_acs.ex_position')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.ex_forward_date',
                    'designations.designation_name',
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();

            if (count($ex_text) > 0) {
                foreach ($ex_text as $ex_texts) {
                    $ex_name = $ex_texts->title . ' ' . $ex_texts->initials . ' ' . $ex_texts->last_name;
                    $ex_position = $ex_texts->designation_name;
                    $ex_date = $ex_texts->ex_forward_date;
                }
            } else {
                $ex_name = "";
                $ex_position = "";
                $ex_date = "";
            }

            $ser_part1 = increAcSerMain::where('incre_id', '=', $tbID)
                ->get();
            if (count($ser_part1) > 0) {
                foreach ($ser_part1 as $ser_part1s) {
                    $under_lect_week = $ser_part1s->under_lect_conduct_week;
                    $under_lect_year = $ser_part1s->under_lect_conduct_year;
                    $post_lect_week = $ser_part1s->post_lect_conduct_week;
                    $post_lect_year = $ser_part1s->post_lect_conduct_year;
                    $under_tutor_week = $ser_part1s->under_tutor_conduct_week;
                    $under_tutor_year = $ser_part1s->under_tutor_conduct_year;
                    $post_tutor_weeek = $ser_part1s->post_tutor_conduct_week;
                    $post_tutor_year = $ser_part1s->post_tutor_conduct_year;
                    $under_pract_week = $ser_part1s->under_practical_conduct_week;
                    $under_pract_year = $ser_part1s->under_practical_conduct_year;
                    $post_pract_week = $ser_part1s->post_practical_conduct_week;
                    $post_pract_year = $ser_part1s->post_practical_conduct_year;
                    $under_project_sup = $ser_part1s->under_project_supervised;
                    $post_project_sup = $ser_part1s->post_project_supervised;
                }
            } else {
                $under_lect_week = "";
                $under_lect_year = "";
                $post_lect_week = "";
                $post_lect_year = "";
                $under_tutor_week = "";
                $under_tutor_year = "";
                $post_tutor_weeek = "";
                $post_tutor_year = "";
                $under_pract_week = "";
                $under_pract_year = "";
                $post_pract_week = "";
                $post_pract_year = "";
                $under_project_sup = "";
                $post_project_sup = "";
            }

            $maxSalaryRecord = Increment::where('emp_no', $emp_no)->orderBy('basic_sal', 'desc')->first();
            if ($maxSalaryRecord) {
                $incre_last_date = $maxSalaryRecord->effective_date;

                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->where('leave_academic_summeries.starting_date', '>=', $incre_last_date)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            } else {
                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            }

            $ser_part2 = increAcSerOther::where('incre_id', '=', $tbID)->get();

            $hodR_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.head_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as posi', 'posi.id', '=', 'increments_process_acs.head_position')
                ->join('categories as pt', 'pt.id', '=', 'increments_process_acs.head_position_type')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'posi.category_name as position',
                    'pt.category_name as appType',
                    'increments_process_acs.head_status',
                    'increments_process_acs.head_position_type',
                    'increments_process_acs.head_date',
                    'increments_process_acs.performance',
                    'increments_process_acs.conduct',
                    'increments_process_acs.other_comments',
                    'increments_process_acs.head_reason'
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();
            if (count($hodR_text) > 0) {
                foreach ($hodR_text as $hodR_texts) {
                    $hodr_name = $hodR_texts->title . ' ' . $hodR_texts->initials . ' ' . $hodR_texts->last_name;
                    if ($hodR_texts->head_position_type == 199) {
                        $hodr_position = $hodR_texts->position;
                    } else {
                        $hodr_position = $hodR_texts->position . '( ' . $hodR_texts->appType . ' )';
                    }

                    $hodr_date = $hodR_texts->head_date;
                    $perfomance = $hodR_texts->performance;
                    $conduct = $hodR_texts->conduct;
                    $comment = $hodR_texts->other_comments;
                    $hod_reason = $hodR_texts->head_reason;

                    if ($hodR_texts->head_status == 2) {
                        $hod_rec = "Recommended";
                    } else if ($hodR_texts->head_status == 3) {
                        $hod_rec = "Not Recommended";
                    } else {
                        $hod_rec = "";
                    }
                }
            } else {
                $hodr_name = "";
                $hodr_position = "";
                $hodr_date = "";
                $conduct = "";
                $comment = "";
                $hod_rec = "";
                $hod_reason = "";
            }

            $dean_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.dean_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as posi', 'posi.id', '=', 'increments_process_acs.dean_position')
                ->join('categories as pt', 'pt.id', '=', 'increments_process_acs.dean_position_type')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'posi.category_name as position',
                    'pt.category_name as appType',
                    'increments_process_acs.dean_status',
                    'increments_process_acs.dean_position_type',
                    'increments_process_acs.dean_date',
                    'increments_process_acs.observation',
                    'increments_process_acs.dean_reason'
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();

            if (count($dean_text) > 0) {
                foreach ($dean_text as $dean_texts) {
                    $dean_name = $dean_texts->title . ' ' . $dean_texts->initials . ' ' . $dean_texts->last_name;
                    if ($dean_texts->dean_position_type == 199) {
                        $dean_position = $dean_texts->position;
                    } else {
                        $dean_position = $dean_texts->position . '( ' . $dean_texts->appType . ' )';
                    }

                    $dean_date = $dean_texts->dean_date;
                    $observation = $dean_texts->observation;
                    $dean_reason = $dean_texts->dean_reason;

                    if ($dean_texts->dean_status == 2) {
                        $dean_rec = "Recommended";
                    } else if ($dean_texts->dean_status == 3) {
                        $dean_rec = "Not Recommended";
                    } else {
                        $dean_rec = "";
                    }
                }
            } else {
                $dean_name = "";
                $dean_position = "";
                $dean_date = "";
                $observation = "";
                $dean_reason = "";
                $dean_rec = "";
            }
        } else {
            $incre_id = 0;
            $emp_no = "";
            $name = "";
            $appDate = "";
            $depID = 0;
            $depName = "";
            $fac = "";
            $fAppDate = "";
            $desig = "";
            $salScale = "";
            $increDate = "";
            $bSal = "";
            $increAmount = "";
            $newBSal = "";

            $lock = 0;
            $depId = 0;
            $refNo = '';
            $increYear = "";
            $ma_name = "";
            $ma_date = "";

            $ex_name = "";
            $ex_position = "";
            $ex_date = "";

            $serDate = "";

            $under_lect_week = "";
            $under_lect_year = "";
            $post_lect_week = "";
            $post_lect_year = "";
            $under_tutor_week = "";
            $under_tutor_year = "";
            $post_tutor_weeek = "";
            $post_tutor_year = "";
            $under_pract_week = "";
            $under_pract_year = "";
            $post_pract_week = "";
            $post_pract_year = "";
            $under_project_sup = "";
            $post_project_sup = "";

            $ser_part2 = array();

            $hodr_name = "";
            $hodr_position = "";
            $hodr_date = "";
            $perfomance = "";
            $conduct = "";
            $comment = "";
            $hod_rec = "";
            $hod_reason = "";

            $facID = 0;

            $leave_text = array();

            $dean_name = "";
            $dean_position = "";
            $dean_date = "";
            $observation = "";
            $dean_reason = "";
            $dean_rec = "";
        }



        return view('admin.increment.Academic.vcApproval', compact(
            'incre_id',
            'emp_no',
            'name',
            'appDate',
            'depID',
            'depName',
            'fac',
            'fAppDate',
            'desig',
            'salScale',
            'increDate',
            'bSal',
            'increAmount',
            'newBSal',

            'lock',
            'depId',
            'refNo',
            'increYear',
            'ma_name',
            'ma_date',
            'ex_name',
            'ex_position',
            'ex_date',

            'serDate',

            'under_lect_week',
            'under_lect_year',
            'post_lect_week',
            'post_lect_year',
            'under_tutor_week',
            'under_tutor_year',
            'post_tutor_weeek',
            'post_tutor_year',
            'under_pract_week',
            'under_pract_year',
            'post_pract_week',
            'post_pract_year',
            'under_project_sup',
            'post_project_sup',

            'ser_part2',

            'hodr_name',
            'hodr_position',
            'hodr_date',
            'perfomance',
            'conduct',
            'comment',
            'hod_rec',
            'hod_reason',
            'facID',

            'leave_text',

            'dean_name',
            'dean_position',
            'dean_date',
            'observation',
            'dean_reason',
            'dean_rec'
        ));
    }

    public function vcApprovalStore(Request $request)
    {

        $cv_text = FacultyDean::where('faculty_id', 51)
            ->where('emp_no', auth()->user()->employee_no)
            ->where('active_status', 1)
            ->get();
        if (count($cv_text) > 0) {
            foreach ($cv_text as $cv_texts) {
                $vc_posi_type = $cv_texts->appointmemt_type;
            }

            $save_text = incrementsProcessAc::where('id', $request->increID)->first();

            if ($save_text) {
                $save_text->vc_status = $request->recom;
                $save_text->vc_user_id = auth()->user()->employee_no;
                $save_text->vc_position = 236;
                $save_text->vc_position_type = $vc_posi_type;
                $save_text->vc_date = today();
                $save_text->vc_reason = $request->notRecomCmm;
                $save_text->ma_status = 1;
                $save_text->save();

                $notification = array(
                    'message' => 'Successfully submitted.',
                    'alert-type' => 'success'
                );
            } else {
                $notification = array(
                    'message' => 'Something wrong .Please try again.',
                    'alert-type' => 'error'
                );
            }
        } else {
            $notification = array(
                'message' => "Sorry You haven't permission to do this.",
                'alert-type' => 'error'
            );
        }

        //redirect to list
        return redirect()->route('ac.incre.vc.list.open')->with($notification);
    }

    public function maListOpen()
    {
        $mainBranch = Auth()->user()->main_branch_id;

        if (Auth()->user()->main_branch_id == 51) {
            $ma_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('categories as dg', 'dg.id', '=', 'designations.staff_grade')
                ->select(
                    'increments_process_acs.id',
                    'increments_process_acs.ref_no',
                    'increments_process_acs.emp_no',
                    'increments_process_acs.incre_date',
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'departments.department_name',
                    'designations.designation_name',
                    'dg.category_name as grade',
                )
                ->where('increments_process_acs.ma_status', '=', 1)
                ->get();
        } else {
            $ma_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('categories as dg', 'dg.id', '=', 'designations.staff_grade')
                ->select(
                    'increments_process_acs.id',
                    'increments_process_acs.ref_no',
                    'increments_process_acs.emp_no',
                    'increments_process_acs.incre_date',
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'departments.department_name',
                    'designations.designation_name',
                    'dg.category_name as grade',
                )
                ->where('increments_process_acs.ma_status', '=', 1)
                ->where('employees.assign_ma_user_id', Auth()->user()->employee_no)
                ->get();
        }

        return view('admin.increment.Academic.maList', compact('ma_text'));
    }

    public function maDetails($id)
    {
        $tbID = decrypt($id);

        $emp_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
            ->join('designations', 'designations.id', '=', 'increments_process_acs.desig_id')
            ->join('departments', 'departments.id', '=', 'increments_process_acs.dep_no')
            ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
            ->select(
                'employees.initials',
                'employees.last_name',

                'designations.designation_name',
                'departments.id as depID',
                'departments.department_name',
                'faculties.id as facID',
                'faculties.faculty_name',
                'categories.category_name',
                't.category_name as title',

                'employees.current_appointment_date',
                'employees.initial_appointment_date',
                'salary_scales.salary_scale_txt',

                'salary_scales.increment_value1',
                'increments_process_acs.emp_no',
                'increments_process_acs.incre_date',
                'increments_process_acs.ref_no',
                'increments_process_acs.id',
                'increments_process_acs.year',
                'increments_process_acs.incre_amount',
                'increments_process_acs.present_sal_step',
                'increments_process_acs.new_sal_step',
                'increments_process_acs.check_date',
                'increments_process_acs.self_eval_date'

            )
            ->where('increments_process_acs.id', $tbID)
            ->get();

        if (count($emp_text) > 0) {
            foreach ($emp_text as $emp_texts) {
                $incre_id = $emp_texts->id;
                $emp_no = $emp_texts->emp_no;
                $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                $appDate = $emp_texts->current_appointment_date;
                $depID = $emp_texts->depID;
                $depName = $emp_texts->department_name;
                $fac = $emp_texts->faculty_name;
                $fAppDate = $emp_texts->initial_appointment_date;
                $desig = $emp_texts->designation_name . ' ' . $emp_texts->category_name;
                $salScale = $emp_texts->salary_scale_txt;
                $increDate = $emp_texts->incre_date;
                $bSal = sprintf("%.2f", $emp_texts->present_sal_step);
                $increAmount = sprintf("%.2f", $emp_texts->incre_amount);
                $newBSal = sprintf("%.2f", $emp_texts->new_sal_step);
                $lock = $emp_texts->lock;
                $depId = $emp_texts->depID;
                $refNo = $emp_texts->ref_no;
                $increYear = $emp_texts->year;
                $serDate = $emp_texts->self_eval_date;
                $facID = $emp_texts->facID;
            }

            list($incre_month, $incre_day) = explode('-', $increDate);

            $effective_date = Carbon::create($increYear, $incre_month, $incre_day)->format('Y-m-d');

            $ma_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.check_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.check_date'
                )
                ->where('increments_process_acs.id', $tbID)
                ->get();

            if (count($ma_text) > 0) {
                foreach ($ma_text as $ma_texts) {
                    $ma_name = $ma_texts->title . ' ' . $ma_texts->initials . ' ' . $ma_texts->last_name;
                    $ma_date = $ma_texts->check_date;
                }
            } else {
                $ma_name = "";
                $ma_date = "";
            }

            $ex_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.ex_forward_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as posi', 'posi.id', '=', 'increments_process_acs.ex_position')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'posi.category_name as position',
                    'increments_process_acs.ex_forward_date'
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();

            if (count($ex_text) > 0) {
                foreach ($ex_text as $ex_texts) {
                    $ex_name = $ex_texts->title . ' ' . $ex_texts->initials . ' ' . $ex_texts->last_name;
                    $ex_position = $ex_texts->position;
                    $ex_date = $ex_texts->ex_forward_date;
                }
            } else {
                $ex_name = "";
                $ex_position = "";
                $ex_date = "";
            }

            $ser_part1 = increAcSerMain::where('incre_id', '=', $tbID)
                ->get();
            if (count($ser_part1) > 0) {
                foreach ($ser_part1 as $ser_part1s) {
                    $under_lect_week = $ser_part1s->under_lect_conduct_week;
                    $under_lect_year = $ser_part1s->under_lect_conduct_year;
                    $post_lect_week = $ser_part1s->post_lect_conduct_week;
                    $post_lect_year = $ser_part1s->post_lect_conduct_year;
                    $under_tutor_week = $ser_part1s->under_tutor_conduct_week;
                    $under_tutor_year = $ser_part1s->under_tutor_conduct_year;
                    $post_tutor_weeek = $ser_part1s->post_tutor_conduct_week;
                    $post_tutor_year = $ser_part1s->post_tutor_conduct_year;
                    $under_pract_week = $ser_part1s->under_practical_conduct_week;
                    $under_pract_year = $ser_part1s->under_practical_conduct_year;
                    $post_pract_week = $ser_part1s->post_practical_conduct_week;
                    $post_pract_year = $ser_part1s->post_practical_conduct_year;
                    $under_project_sup = $ser_part1s->under_project_supervised;
                    $post_project_sup = $ser_part1s->post_project_supervised;
                }
            } else {
                $under_lect_week = "";
                $under_lect_year = "";
                $post_lect_week = "";
                $post_lect_year = "";
                $under_tutor_week = "";
                $under_tutor_year = "";
                $post_tutor_weeek = "";
                $post_tutor_year = "";
                $under_pract_week = "";
                $under_pract_year = "";
                $post_pract_week = "";
                $post_pract_year = "";
                $under_project_sup = "";
                $post_project_sup = "";
            }

            $maxSalaryRecord = Increment::where('emp_no', $emp_no)->orderBy('basic_sal', 'desc')->first();
            if ($maxSalaryRecord) {
                $incre_last_date = $maxSalaryRecord->effective_date;

                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->where('leave_academic_summeries.starting_date', '>=', $incre_last_date)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            } else {
                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            }

            $ser_part2 = increAcSerOther::where('incre_id', '=', $tbID)->get();

            $hodR_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.head_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as posi', 'posi.id', '=', 'increments_process_acs.head_position')
                ->join('categories as pt', 'pt.id', '=', 'increments_process_acs.head_position_type')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'posi.category_name as position',
                    'pt.category_name as appType',
                    'increments_process_acs.head_status',
                    'increments_process_acs.head_position_type',
                    'increments_process_acs.head_date',
                    'increments_process_acs.performance',
                    'increments_process_acs.conduct',
                    'increments_process_acs.other_comments',
                    'increments_process_acs.head_reason'
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();
            if (count($hodR_text) > 0) {
                foreach ($hodR_text as $hodR_texts) {
                    $hodr_name = $hodR_texts->title . ' ' . $hodR_texts->initials . ' ' . $hodR_texts->last_name;
                    if ($hodR_texts->head_position_type == 199) {
                        $hodr_position = $hodR_texts->position;
                    } else {
                        $hodr_position = $hodR_texts->position . '( ' . $hodR_texts->appType . ' )';
                    }

                    $hodr_date = $hodR_texts->head_date;
                    $perfomance = $hodR_texts->performance;
                    $conduct = $hodR_texts->conduct;
                    $comment = $hodR_texts->other_comments;
                    $hod_reason = $hodR_texts->head_reason;

                    if ($hodR_texts->head_status == 2) {
                        $hod_rec = "Recommended";
                    } else if ($hodR_texts->head_status == 3) {
                        $hod_rec = "Not Recommended";
                    } else {
                        $hod_rec = "";
                    }
                }
            } else {
                $hodr_name = "";
                $hodr_position = "";
                $hodr_date = "";
                $conduct = "";
                $comment = "";
                $hod_rec = "";
                $hod_reason = "";
            }

            $dean_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.dean_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as posi', 'posi.id', '=', 'increments_process_acs.dean_position')
                ->join('categories as pt', 'pt.id', '=', 'increments_process_acs.dean_position_type')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'posi.category_name as position',
                    'pt.category_name as appType',
                    'increments_process_acs.dean_status',
                    'increments_process_acs.dean_position_type',
                    'increments_process_acs.dean_date',
                    'increments_process_acs.observation',
                    'increments_process_acs.dean_reason'
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();

            if (count($dean_text) > 0) {
                foreach ($dean_text as $dean_texts) {
                    $dean_name = $dean_texts->title . ' ' . $dean_texts->initials . ' ' . $dean_texts->last_name;
                    if ($dean_texts->dean_position_type == 199) {
                        $dean_position = $dean_texts->position;
                    } else {
                        $dean_position = $dean_texts->position . '( ' . $dean_texts->appType . ' )';
                    }

                    $dean_date = $dean_texts->dean_date;
                    $observation = $dean_texts->observation;
                    $dean_reason = $dean_texts->dean_reason;

                    if ($dean_texts->dean_status == 2) {
                        $dean_rec = "Recommended";
                    } else if ($dean_texts->dean_status == 3) {
                        $dean_rec = "Not Recommended";
                    } else {
                        $dean_rec = "";
                    }
                }
            } else {
                $dean_name = "";
                $dean_position = "";
                $dean_date = "";
                $observation = "";
                $dean_reason = "";
                $dean_rec = "";
            }

            $vc_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.vc_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as posi', 'posi.id', '=', 'increments_process_acs.vc_position')
                ->join('categories as pt', 'pt.id', '=', 'increments_process_acs.vc_position_type')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'posi.category_name as position',
                    'pt.category_name as appType',
                    'increments_process_acs.vc_status',
                    'increments_process_acs.vc_position_type',
                    'increments_process_acs.vc_date',
                    'increments_process_acs.vc_reason',
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();

            if (count($vc_text) > 0) {
                foreach ($vc_text as $vc_texts) {
                    $vc_name = $vc_texts->title . ' ' . $vc_texts->initials . ' ' . $vc_texts->last_name;
                    if ($vc_texts->vc_position_type == 199) {
                        $vc_position = $vc_texts->position;
                    } else {
                        $vc_position = $vc_texts->position . '( ' . $vc_texts->appType . ' )';
                    }

                    $vc_date = $vc_texts->vc_date;
                    $vc_reason = $vc_texts->vc_reason;
                    $vc_status = $vc_texts->vc_status;
                }
            } else {
                $vc_name = "";
                $vc_position = "";
                $vc_date = "";
                $vc_reason = "";
                $vc_status = 0;
            }
        } else {
            $incre_id = 0;
            $emp_no = "";
            $name = "";
            $appDate = "";
            $depID = 0;
            $depName = "";
            $fac = "";
            $fAppDate = "";
            $desig = "";
            $salScale = "";
            $increDate = "";
            $bSal = "";
            $increAmount = "";
            $newBSal = "";

            $lock = 0;
            $depId = 0;
            $refNo = '';
            $increYear = "";
            $ma_name = "";
            $ma_date = "";

            $ex_name = "";
            $ex_position = "";
            $ex_date = "";

            $serDate = "";

            $under_lect_week = "";
            $under_lect_year = "";
            $post_lect_week = "";
            $post_lect_year = "";
            $under_tutor_week = "";
            $under_tutor_year = "";
            $post_tutor_weeek = "";
            $post_tutor_year = "";
            $under_pract_week = "";
            $under_pract_year = "";
            $post_pract_week = "";
            $post_pract_year = "";
            $under_project_sup = "";
            $post_project_sup = "";

            $ser_part2 = array();

            $hodr_name = "";
            $hodr_position = "";
            $hodr_date = "";
            $perfomance = "";
            $conduct = "";
            $comment = "";
            $hod_rec = "";
            $hod_reason = "";

            $facID = 0;

            $leave_text = array();

            $dean_name = "";
            $dean_position = "";
            $dean_date = "";
            $observation = "";
            $dean_reason = "";
            $dean_rec = "";

            $vc_name = "";
            $vc_position = "";
            $vc_date = "";
            $vc_reason = "";
            $vc_status = 0;

            $effective_date = "";
        }



        return view('admin.increment.Academic.maDetails', compact(
            'incre_id',
            'emp_no',
            'name',
            'appDate',
            'depID',
            'depName',
            'fac',
            'fAppDate',
            'desig',
            'salScale',
            'increDate',
            'bSal',
            'increAmount',
            'newBSal',

            'lock',
            'depId',
            'refNo',
            'increYear',
            'ma_name',
            'ma_date',
            'ex_name',
            'ex_position',
            'ex_date',

            'serDate',

            'under_lect_week',
            'under_lect_year',
            'post_lect_week',
            'post_lect_year',
            'under_tutor_week',
            'under_tutor_year',
            'post_tutor_weeek',
            'post_tutor_year',
            'under_pract_week',
            'under_pract_year',
            'post_pract_week',
            'post_pract_year',
            'under_project_sup',
            'post_project_sup',

            'ser_part2',

            'hodr_name',
            'hodr_position',
            'hodr_date',
            'perfomance',
            'conduct',
            'comment',
            'hod_rec',
            'hod_reason',
            'facID',

            'leave_text',

            'dean_name',
            'dean_position',
            'dean_date',
            'observation',
            'dean_reason',
            'dean_rec',

            'vc_name',
            'vc_position',
            'vc_date',
            'vc_reason',
            'vc_status',

            'effective_date'
        ));
    }

    public function increPrint($id)
    {
        $tbID = decrypt($id);

        $emp_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
            ->join('designations', 'designations.id', '=', 'increments_process_acs.desig_id')
            ->join('departments', 'departments.id', '=', 'increments_process_acs.dep_no')
            ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
            ->select(
                'employees.initials',
                'employees.last_name',

                'designations.designation_name',
                'departments.id as depID',
                'departments.department_name',
                'departments.name_status',
                'faculties.id as facID',
                'faculties.faculty_name',
                'categories.category_name',
                't.category_name as title',

                'employees.current_appointment_date',
                'employees.initial_appointment_date',
                'salary_scales.salary_scale_txt',

                'salary_scales.increment_value1',
                'increments_process_acs.emp_no',
                'increments_process_acs.incre_date',
                'increments_process_acs.ref_no',
                'increments_process_acs.id',
                'increments_process_acs.year',
                'increments_process_acs.incre_amount',
                'increments_process_acs.present_sal_step',
                'increments_process_acs.new_sal_step',
                'increments_process_acs.check_date',
                'increments_process_acs.self_eval_date'

            )
            ->where('increments_process_acs.id', $tbID)
            ->get();

        if (count($emp_text) > 0) {
            foreach ($emp_text as $emp_texts) {
                $incre_id = $emp_texts->id;
                $emp_no = $emp_texts->emp_no;
                $name = $emp_texts->title . ' ' . $emp_texts->initials . ' ' . $emp_texts->last_name;
                $appDate = $emp_texts->current_appointment_date;
                $depID = $emp_texts->depID;
                if ($emp_texts->name_status == 1) {
                    $depName = 'Departmen of ' . $emp_texts->department_name;
                } else {
                    $depName = $emp_texts->department_name;
                }

                $fac = $emp_texts->faculty_name;
                $fAppDate = $emp_texts->initial_appointment_date;
                $desig = $emp_texts->designation_name . ' ' . $emp_texts->category_name;
                $salScale = $emp_texts->salary_scale_txt;
                $increDate = $emp_texts->incre_date;
                $bSal = sprintf("%.2f", $emp_texts->present_sal_step);
                $increAmount = sprintf("%.2f", $emp_texts->incre_amount);
                $newBSal = sprintf("%.2f", $emp_texts->new_sal_step);
                $lock = $emp_texts->lock;
                $depId = $emp_texts->depID;
                $refNo = $emp_texts->ref_no;
                $increYear = $emp_texts->year;
                $serDate = $emp_texts->self_eval_date;
                $facID = $emp_texts->facID;
            }

            list($incre_month, $incre_day) = explode('-', $increDate);

            $effective_date = Carbon::create($increYear, $incre_month, $incre_day)->format('Y-m-d');


            $ma_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.check_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.check_date'
                )
                ->where('increments_process_acs.id', $tbID)
                ->get();

            if (count($ma_text) > 0) {
                foreach ($ma_text as $ma_texts) {
                    $ma_name = $ma_texts->title . ' ' . $ma_texts->initials . ' ' . $ma_texts->last_name;
                    $ma_date = $ma_texts->check_date;
                }
            } else {
                $ma_name = "";
                $ma_date = "";
            }

            $ex_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.ex_forward_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as posi', 'posi.id', '=', 'increments_process_acs.ex_position')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'posi.category_name as position',
                    'increments_process_acs.ex_forward_date'
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();

            if (count($ex_text) > 0) {
                foreach ($ex_text as $ex_texts) {
                    $ex_name = $ex_texts->title . ' ' . $ex_texts->initials . ' ' . $ex_texts->last_name;
                    $ex_position = $ex_texts->position;
                    $ex_date = $ex_texts->ex_forward_date;
                }
            } else {
                $ex_name = "";
                $ex_position = "";
                $ex_date = "";
            }

            $ser_part1 = increAcSerMain::where('incre_id', '=', $tbID)
                ->get();
            if (count($ser_part1) > 0) {
                foreach ($ser_part1 as $ser_part1s) {
                    $under_lect_week = $ser_part1s->under_lect_conduct_week;
                    $under_lect_year = $ser_part1s->under_lect_conduct_year;
                    $post_lect_week = $ser_part1s->post_lect_conduct_week;
                    $post_lect_year = $ser_part1s->post_lect_conduct_year;
                    $under_tutor_week = $ser_part1s->under_tutor_conduct_week;
                    $under_tutor_year = $ser_part1s->under_tutor_conduct_year;
                    $post_tutor_weeek = $ser_part1s->post_tutor_conduct_week;
                    $post_tutor_year = $ser_part1s->post_tutor_conduct_year;
                    $under_pract_week = $ser_part1s->under_practical_conduct_week;
                    $under_pract_year = $ser_part1s->under_practical_conduct_year;
                    $post_pract_week = $ser_part1s->post_practical_conduct_week;
                    $post_pract_year = $ser_part1s->post_practical_conduct_year;
                    $under_project_sup = $ser_part1s->under_project_supervised;
                    $post_project_sup = $ser_part1s->post_project_supervised;
                }
            } else {
                $under_lect_week = "";
                $under_lect_year = "";
                $post_lect_week = "";
                $post_lect_year = "";
                $under_tutor_week = "";
                $under_tutor_year = "";
                $post_tutor_weeek = "";
                $post_tutor_year = "";
                $under_pract_week = "";
                $under_pract_year = "";
                $post_pract_week = "";
                $post_pract_year = "";
                $under_project_sup = "";
                $post_project_sup = "";
            }

            $maxSalaryRecord = Increment::where('emp_no', $emp_no)->orderBy('basic_sal', 'desc')->first();
            if ($maxSalaryRecord) {
                $incre_last_date = $maxSalaryRecord->effective_date;

                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->where('leave_academic_summeries.starting_date', '>=', $incre_last_date)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            } else {
                $leave_text = leaveAcademicSummery::join('academic_years', 'academic_years.id', '=', 'leave_academic_summeries.ac_year_id')
                    ->join('categories', 'categories.id', '=', 'leave_academic_summeries.leave_type')
                    ->select(
                        'leave_academic_summeries.*',
                        'academic_years.academic_year_name',
                        'categories.category_name as lave_type'
                    )
                    ->where('leave_academic_summeries.emp_no', $emp_no)
                    ->where('leave_academic_summeries.status', 0)
                    ->orderBy('leave_academic_summeries.end_date', 'desc')
                    ->get();
            }

            $ser_part2 = increAcSerOther::where('incre_id', '=', $tbID)->get();

            $hodR_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.head_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as posi', 'posi.id', '=', 'increments_process_acs.head_position')
                ->join('categories as pt', 'pt.id', '=', 'increments_process_acs.head_position_type')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'posi.category_name as position',
                    'pt.category_name as appType',
                    'increments_process_acs.head_status',
                    'increments_process_acs.head_position_type',
                    'increments_process_acs.head_date',
                    'increments_process_acs.performance',
                    'increments_process_acs.conduct',
                    'increments_process_acs.other_comments',
                    'increments_process_acs.head_reason'
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();
            if (count($hodR_text) > 0) {
                foreach ($hodR_text as $hodR_texts) {
                    $hodr_name = $hodR_texts->title . ' ' . $hodR_texts->initials . ' ' . $hodR_texts->last_name;
                    if ($hodR_texts->head_position_type == 199) {
                        $hodr_position = $hodR_texts->position;
                    } else {
                        $hodr_position = $hodR_texts->position . '( ' . $hodR_texts->appType . ' )';
                    }

                    $hodr_date = $hodR_texts->head_date;
                    $perfomance = $hodR_texts->performance;
                    $conduct = $hodR_texts->conduct;
                    $comment = $hodR_texts->other_comments;
                    $hod_reason = $hodR_texts->head_reason;

                    if ($hodR_texts->head_status == 2) {
                        $hod_rec = "Recommended";
                    } else if ($hodR_texts->head_status == 3) {
                        $hod_rec = "Not Recommended";
                    } else {
                        $hod_rec = "";
                    }
                }
            } else {
                $hodr_name = "";
                $hodr_position = "";
                $hodr_date = "";
                $conduct = "";
                $comment = "";
                $hod_rec = "";
                $hod_reason = "";
            }

            $dean_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.dean_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as posi', 'posi.id', '=', 'increments_process_acs.dean_position')
                ->join('categories as pt', 'pt.id', '=', 'increments_process_acs.dean_position_type')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'posi.category_name as position',
                    'pt.category_name as appType',
                    'increments_process_acs.dean_status',
                    'increments_process_acs.dean_position_type',
                    'increments_process_acs.dean_date',
                    'increments_process_acs.observation',
                    'increments_process_acs.dean_reason'
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();

            if (count($dean_text) > 0) {
                foreach ($dean_text as $dean_texts) {
                    $dean_name = $dean_texts->title . ' ' . $dean_texts->initials . ' ' . $dean_texts->last_name;
                    if ($dean_texts->dean_position_type == 199) {
                        $dean_position = $dean_texts->position;
                    } else {
                        $dean_position = $dean_texts->position . '( ' . $dean_texts->appType . ' )';
                    }

                    $dean_date = $dean_texts->dean_date;
                    $observation = $dean_texts->observation;
                    $dean_reason = $dean_texts->dean_reason;

                    if ($dean_texts->dean_status == 2) {
                        $dean_rec = "Recommended";
                    } else if ($dean_texts->dean_status == 3) {
                        $dean_rec = "Not Recommended";
                    } else {
                        $dean_rec = "";
                    }
                }
            } else {
                $dean_name = "";
                $dean_position = "";
                $dean_date = "";
                $observation = "";
                $dean_reason = "";
                $dean_rec = "";
            }

            $vc_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.vc_user_id')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('categories as posi', 'posi.id', '=', 'increments_process_acs.vc_position')
                ->join('categories as pt', 'pt.id', '=', 'increments_process_acs.vc_position_type')
                ->select(
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'posi.category_name as position',
                    'pt.category_name as appType',
                    'increments_process_acs.vc_status',
                    'increments_process_acs.vc_position_type',
                    'increments_process_acs.vc_date',
                    'increments_process_acs.vc_reason',
                )
                ->where('increments_process_acs.id', '=', $tbID)
                ->get();

            if (count($vc_text) > 0) {
                foreach ($vc_text as $vc_texts) {
                    $vc_name = $vc_texts->title . ' ' . $vc_texts->initials . ' ' . $vc_texts->last_name;
                    if ($vc_texts->vc_position_type == 199) {
                        $vc_position = $vc_texts->position;
                    } else {
                        $vc_position = $vc_texts->position . '( ' . $vc_texts->appType . ' )';
                    }

                    $vc_date = $vc_texts->vc_date;
                    $vc_reason = $vc_texts->vc_reason;
                    $vc_status = $vc_texts->vc_status;
                    if ($vc_texts->vc_status == 2) {
                        $vc_approval = "approved";
                    } else if ($vc_texts->vc_status == 3) {
                        $vc_approval = "not approved";
                    } else {
                        $vc_approval = "";
                    }
                }
            } else {
                $vc_name = "";
                $vc_position = "";
                $vc_date = "";
                $vc_reason = "";
                $vc_status = 0;
            }
        } else {
            $incre_id = 0;
            $emp_no = "";
            $name = "";
            $appDate = "";
            $depID = 0;
            $depName = "";
            $fac = "";
            $fAppDate = "";
            $desig = "";
            $salScale = "";
            $increDate = "";
            $bSal = "";
            $increAmount = "";
            $newBSal = "";

            $lock = 0;
            $depId = 0;
            $refNo = '';
            $increYear = "";
            $ma_name = "";
            $ma_date = "";

            $ex_name = "";
            $ex_position = "";
            $ex_date = "";

            $serDate = "";

            $under_lect_week = "";
            $under_lect_year = "";
            $post_lect_week = "";
            $post_lect_year = "";
            $under_tutor_week = "";
            $under_tutor_year = "";
            $post_tutor_weeek = "";
            $post_tutor_year = "";
            $under_pract_week = "";
            $under_pract_year = "";
            $post_pract_week = "";
            $post_pract_year = "";
            $under_project_sup = "";
            $post_project_sup = "";

            $ser_part2 = array();

            $hodr_name = "";
            $hodr_position = "";
            $hodr_date = "";
            $perfomance = "";
            $conduct = "";
            $comment = "";
            $hod_rec = "";
            $hod_reason = "";

            $facID = 0;

            $leave_text = array();

            $dean_name = "";
            $dean_position = "";
            $dean_date = "";
            $observation = "";
            $dean_reason = "";
            $dean_rec = "";

            $vc_name = "";
            $vc_position = "";
            $vc_date = "";
            $vc_reason = "";
            $vc_status = 0;
            $vc_approval = "";

            $effective_date = "";
        }


        $data = [
            'name' => $name,
            'desig' => $desig,
            'appDate' => $appDate,
            'fAppDate' => $fAppDate,
            'depName' => $depName,
            'salScale' => $salScale,
            'bSal' => $bSal,
            'increDate' => $increDate,
            'increAmount' => $increAmount,
            'newBSal' => $newBSal,
            'leave_text' => $leave_text,
            'ma_name' => $ma_name,
            'ma_date' => $ma_date,
            'ex_name' => $ex_name,
            'ex_position' => $ex_position,
            'ex_date' => $ex_date,
            'under_lect_week' => $under_lect_week,
            'under_lect_year' => $under_lect_year,
            'post_lect_week' => $post_lect_week,
            'post_lect_year' => $post_lect_year,
            'under_tutor_week' => $under_tutor_week,
            'under_tutor_year' => $under_tutor_year,
            'post_tutor_weeek' => $post_tutor_weeek,
            'post_tutor_year' => $post_tutor_year,
            'under_pract_week' => $under_pract_week,
            'under_pract_year' => $under_pract_year,
            'post_pract_week' => $post_pract_week,
            'post_pract_year' => $post_pract_year,
            'under_project_sup' => $under_project_sup,
            'post_project_sup' => $post_project_sup,
            'ser_part2' => $ser_part2,
            'serDate' => $serDate,
            'facID' => $facID,
            'fac' => $fac,
            'perfomance' => $perfomance,
            'conduct' => $conduct,
            'comment' => $comment,
            'hod_rec' => $hod_rec,
            'hod_reason' => $hod_reason,
            'hodr_date' => $hodr_date,
            'hodr_name' => $hodr_name,
            'hodr_position' => $hodr_position,
            'observation' => $observation,
            'dean_rec' => $dean_rec,
            'dean_reason' => $dean_reason,
            'dean_date' => $dean_date,
            'dean_name' => $dean_name,
            'dean_position' => $dean_position,
            'vc_approval' => $vc_approval,
            'vc_status' => $vc_status,
            'vc_reason' => $vc_reason,
            'vc_date' => $vc_date,
            'vc_name' => $vc_name,
            'vc_position' => $vc_position,
            'effective_date' => $effective_date
        ];

        $pdf = FacadePdf::loadView('admin.increment.Academic.increment_certificate', $data)->setPaper('a4');

        // return $pdf->stream('increment_reprot.pdf');
        // Render the PDF (first pass to calculate pages)
        $pdf->render();

        // Now, get the total number of pages
        $canvas = $pdf->getCanvas();
        $canvas->page_text(510, 820, "Page {PAGE_NUM} of {PAGE_COUNT}", null, 10, array(0, 0, 0)); // You can adjust the position and font here

        // Return the generated PDF
        return $pdf->stream('increment_reprot.pdf');
    }

    public function maStore(Request $request)
    {

        if (isset($request->increID)) {
            $save_text = incrementsProcessAc::where('id', $request->increID)->first();
            if ($save_text) {

                switch ($request->input('submitbt')) {
                    case 'finance_list':
                        $save_text->effective_date = Carbon::parse($request->effectDate)->format('Y-m-d');;
                        $save_text->ma_status = 2;
                        $save_text->ma_date = today();
                        $save_text->ma_user_id = auth()->user()->employee_no;
                        $save_text->finance_list_status = 1;
                        $save_text->save();

                        $notification = array(
                            'message' => 'Successfully submitted.',
                            'alert-type' => 'success'
                        );
                        break;
                    case 'complete':
                        $save_text->ma_status = 2;
                        $save_text->ma_date = today();
                        $save_text->ma_user_id = auth()->user()->employee_no;
                        $save_text->status = 1;
                        $save_text->save();

                        $notification = array(
                            'message' => 'Successfully submitted.',
                            'alert-type' => 'success'
                        );
                        break;

                    default:
                        $notification = array(
                            'message' => "Something wrong .Please try again.**",
                            'alert-type' => 'error'
                        );
                        break;
                }
            } else {
                $notification = array(
                    'message' => 'Something wrong .Please try again.',
                    'alert-type' => 'error'
                );
            }
        } else {
            $notification = array(
                'message' => "Something wrong .Please try again.*",
                'alert-type' => 'error'
            );
        }

        //redirect to list
        return redirect()->route('ac.incre.ma.list.open')->with($notification);
    }

    public function financeListOpen()
    {
        $dean_text = array();
        $empNo = auth()->user()->employee_no;
        if (Auth()->user()->hasRole(['administrator'])) {
            $fin_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
                ->join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                //->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select(
                    'employees.employee_no',
                    'employees.initials',
                    'employees.last_name',
                    'designations.designation_name',
                    'departments.department_name',
                    //'faculties.faculty_name',
                    'categories.category_name',
                    'increments_process_acs.ref_no',
                    'increments_process_acs.incre_amount',
                    'increments_process_acs.present_sal_step',
                    'increments_process_acs.new_sal_step',
                    'increments_process_acs.emp_no',
                    'increments_process_acs.effective_date'
                )

                // ->where('employees.assign_ma_user_id', $empNo)
                ->where('increments_process_acs.finance_list_status', 1)
                ->orderBy('increments_process_acs.effective_date', 'ASC')
                ->orderBy('employees.employee_no', 'ASC')
                ->get();
        } else {
            $fin_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
                ->join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                //->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select(
                    'employees.employee_no',
                    'employees.initials',
                    'employees.last_name',
                    'designations.designation_name',
                    'departments.department_name',
                    //'faculties.faculty_name',
                    'categories.category_name',
                    'increments_process_acs.ref_no',
                    'increments_process_acs.incre_amount',
                    'increments_process_acs.present_sal_step',
                    'increments_process_acs.new_sal_step',
                    'increments_process_acs.emp_no',
                    'increments_process_acs.effective_date'
                )

                ->where('employees.assign_ma_user_id', $empNo)
                ->where('increments_process_acs.finance_list_status', 1)
                ->orderBy('increments_process_acs.effective_date', 'ASC')
                ->orderBy('employees.employee_no', 'ASC')
                ->get();
        }
        $currentYear = Carbon::now()->year;
        $currentMonth = Carbon::now()->month;

        $refNo_text = financeListAcIncrement::where('year', $currentYear)
            ->where('add_user_id', $empNo)
            ->get();

        $refText_rowcount = $refNo_text->count() + 1;

        $refNo = "AE/" . $currentYear . "/" . $currentMonth . "/" . $empNo . "/" . substr("000{$refText_rowcount}", -3);

        return view('admin.increment.Academic.financeList', compact('fin_text', 'refNo'));
    }

    public function financeListStore(Request $request)
    {
        $user_emp_no = auth()->user()->employee_no;

        $list_save_text = new financeListAcIncrement();
        $list_save_text->year = Carbon::now()->year;
        $list_save_text->month = Carbon::now()->month;
        $list_save_text->ref_no = $request->refNo;
        $list_save_text->add_user_id = $user_emp_no;
        $list_save_text->add_date = Carbon::today();
        $list_save_text->save();

        $list_text = financeListAcIncrement::where('ref_no', $request->refNo)->get();
        if ($list_text->isNotEmpty()) {
            $refID = $list_text->last()->id;

            $save_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
                ->select(
                    'increments_process_acs.*',
                    'employees.assign_ma_user_id',
                    'employees.designation_id'
                )
                ->where('employees.assign_ma_user_id', $user_emp_no)
                ->where('increments_process_acs.finance_list_status', 1)
                ->get();

            if ($save_text->isNotEmpty()) {
                foreach ($save_text as $save_texts) {
                    $emp_text = Employee::where('employee_no', $save_texts->emp_no)->first();
                    if ($emp_text) {
                        $emp_text->current_basic_salary = $save_texts->new_sal_step;
                        $emp_text->increment_process_active = 0;
                        $emp_text->save();
                    } else {
                        Log::warning("Employee not found for emp_no: {$save_texts->emp_no}");
                    }

                    $newIncrement = new Increment();
                    $newIncrement->emp_no = $save_texts->emp_no;
                    $newIncrement->effective_date = $save_texts->effective_date;
                    $newIncrement->effective_date_arrears = $save_texts->effective_date;
                    $newIncrement->decision = 172;
                    $newIncrement->desgnation_id = $save_texts->designation_id;
                    $newIncrement->basic_sal = $save_texts->new_sal_step;
                    $newIncrement->increment_value = $save_texts->incre_amount;
                    $newIncrement->reason = $save_texts->year . ' annual increment';
                    $newIncrement->add_user_id = $user_emp_no;
                    $newIncrement->save();

                    $save_texts->finance_list_status = 2;
                    $save_texts->finance_list_id = $refID;
                    $save_texts->finance_list_date = Carbon::today();
                    $save_texts->completed_date = Carbon::today();
                    $save_texts->completed_user = $user_emp_no;
                    $save_texts->status = 1;
                    $save_texts->save();

                    $notification = array(
                        'message' => 'Successfully submitted.',
                        'alert-type' => 'success'
                    );
                }
            } else {
                $notification = array(
                    'message' => "Something wrong .Please try again.*",
                    'alert-type' => 'error'
                );
            }
        } else {
            $notification = array(
                'message' => "Something wrong with list id .Please try again.*",
                'alert-type' => 'error'
            );
        }

        return redirect()->route('ac.incre.finance.list.open')->with($notification);
    }

    public function financeListView(Request $request)
    {

        if (isset($request->year) && $request->year != '') {
            $pass_year = $request->year;

            if (Auth()->user()->hasRole(['cc']) || Auth()->user()->hasRole(['sc'])) {

                $data_text = financeListAcIncrement::where('year', $pass_year)
                    ->where('add_user_id', auth()->user()->employee_no)
                    ->get();
            } else {
                $data_text = financeListAcIncrement::where('year', $pass_year)
                    ->get();
            }
        } else {
            $pass_year = '';
            $data_text = array();
        }
        return view('admin.increment.Academic.financeListView', compact('pass_year', 'data_text'));
    }

    public function financeListViewDetails($id)
    {
        $row_id = decrypt($id);
        $data_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            //->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->select(
                't.category_name as title',
                'employees.initials',
                'employees.last_name',
                'designations.designation_name',
                'departments.department_name',
                //'faculties.faculty_name',
                'categories.category_name',
                'increments_process_acs.ref_no',
                'increments_process_acs.incre_amount',
                'increments_process_acs.present_sal_step',
                'increments_process_acs.new_sal_step',
                'increments_process_acs.emp_no',
                'increments_process_acs.effective_date'
            )
            ->where('increments_process_acs.finance_list_id', $row_id)
            ->orderBy('increments_process_acs.effective_date', 'ASC')
            ->orderBy('increments_process_acs.emp_no', 'ASC')
            ->get();

        $list_text = financeListAcIncrement::where('id', $row_id)->get();
        if ($list_text->isNotEmpty()) {
            $refNo = $list_text->last()->ref_no;
        } else {
            $refNo = '';
        }
        return view('admin.increment.Academic.financeListViewDetails', compact('data_text', 'refNo', 'row_id'));
    }

    public function financeListPrint($id)
    {
        $row_id = decrypt($id);

        $data_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            //->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->select(
                't.category_name as title',
                'employees.initials',
                'employees.last_name',
                'designations.designation_name',
                'departments.department_name',
                //'faculties.faculty_name',
                'categories.category_name',
                'increments_process_acs.ref_no',
                'increments_process_acs.incre_amount',
                'increments_process_acs.present_sal_step',
                'increments_process_acs.new_sal_step',
                'increments_process_acs.emp_no',
                'increments_process_acs.effective_date',
                'increments_process_acs.completed_user'
            )
            ->where('increments_process_acs.finance_list_id', $row_id)
            ->orderBy('increments_process_acs.effective_date', 'ASC')
            ->orderBy('increments_process_acs.emp_no', 'ASC')
            ->get();

        if ($data_text->isNotEmpty()) {
            $ma_user_id = $data_text->last()->completed_user;
        }

        $list_text = financeListAcIncrement::where('id', $row_id)->get();
        if ($list_text->isNotEmpty()) {
            $refNo = $list_text->last()->ref_no;
        } else {
            $refNo = '';
        }

        $ma_text = Employee::select(
            'initials',
            'last_name'
        )
            ->where('employee_no', $ma_user_id)
            ->get();
        if ($ma_text->isNotEmpty()) {
            foreach ($ma_text as $ma_texts) {
                $ma_name = $ma_texts->initials . ' ' . $ma_texts->last_name;
            }
        } else {
            $ma_name = '';
        }


        $data = [
            'refNo' => $refNo,
            'data_text' => $data_text,
            'ma_name' => $ma_name
        ];

        $pdf = FacadePdf::loadView('admin.increment.Academic.financeListPrint', $data)->setPaper('a4');

        // return $pdf->stream('increment_reprot.pdf');
        // Render the PDF (first pass to calculate pages)
        $pdf->render();

        // Now, get the total number of pages
        $canvas = $pdf->getCanvas();
        $canvas->page_text(510, 820, "Page {PAGE_NUM} of {PAGE_COUNT}", null, 10, array(0, 0, 0)); // You can adjust the position and font here

        // Return the generated PDF
        return $pdf->stream('finance_list.pdf');
    }
}
