<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class SalaryScale extends Model
{
    use HasFactory,LogsActivity;

    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('hrms_salary_scales')
        ->logOnlyDirty()
        ->dontLogIfAttributesChangedOnly(['updated_at'])
        ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    public function version()
    {
        return $this->belongsTo(SalaryScaleVersion::class, 'salary_scale_version_id');
    }

    public function getEffectiveDateAttribute()
    {
        return $this->version ? $this->version->effective_date : null;
    }

    public function getTerminationDateAttribute()
    {
        return $this->version ? $this->version->termination_date : null;
    }
}
