<?php

namespace App\Http\Controllers;

use App\Models\DepartmentHead;
use App\Models\Employee;
use App\Models\FacultyDean;
use App\Models\incrementsProcessAc;
use App\Models\Leave;
use App\Models\PromotionApplicationsNonacademic;
use App\Models\promotionDuration;
use App\Models\Vacancy;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UsjnetController extends Controller
{

    public function __construct()
    {
        $currentURL = url()->current();
        session()->put('special_callback_url', $currentURL);
        session()->put('special_value', false);
        session()->put('checkReturn',  true);
        $this->middleware('auth');
        $this->middleware('PageControl:1');
    }


    public function headIncrement(Request $request)
    {

        session()->put('special_value', true);
        session()->forget('special_callback_url');
        $userID = Auth()->user()->id;

        if (Auth()->user()->hasRole(['administrator'])) {

            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('non_increments', 'non_increments.employee_no', '=', 'employees.employee_no')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'faculties.faculty_name', 'departments.department_name', 'non_increments.status', 'non_increments.increment_date', 'categories.category_name', 'non_increments.year')
                ->where('non_increments.status', 3)
                //->where('non_increments.year', now()->year)
                ->orderBy('employees.increment_date', 'ASC')
                ->get();
        } else {

            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('non_increments', 'non_increments.employee_no', '=', 'employees.employee_no')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'faculties.faculty_name', 'departments.department_name', 'non_increments.status', 'non_increments.increment_date', 'categories.category_name', 'non_increments.year')
                ->where('non_increments.status', 3)
                ->where('departments.head_user_id', $userID)
                //->where('non_increments.year', now()->year)
                ->orderBy('employees.increment_date', 'ASC')
                ->get();
        }

        return view('admin.increment.NonAcademic.usjnet_list', ['employees' => $employees]);
    }

    public function RegistarIncrement(Request $request)
    {

        session()->put('special_value', true);
        session()->forget('special_callback_url');

        if (Auth()->user()->hasRole(['administrator'])) {

            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('non_increments', 'non_increments.employee_no', '=', 'employees.employee_no')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'faculties.faculty_name', 'departments.department_name', 'non_increments.status', 'non_increments.increment_date', 'categories.category_name', 'non_increments.year')
                ->where('non_increments.status', 5)
                //->where('non_increments.year', now()->year)
                ->orderBy('employees.increment_date', 'ASC')
                ->get();
        } elseif (Auth()->user()->hasRole(['reg'])) {

            $employees =  Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                ->join('non_increments', 'non_increments.employee_no', '=', 'employees.employee_no')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'faculties.faculty_name', 'departments.department_name', 'non_increments.status', 'non_increments.increment_date', 'categories.category_name', 'non_increments.year')
                ->where('non_increments.status', 5)
                //->where('non_increments.year', now()->year)
                ->orderBy('employees.increment_date', 'ASC')
                ->get();
        }

        return view('admin.increment.NonAcademic.usjnet_list', ['employees' => $employees]);
    }

    public function headApprovalOpen()
    {
        session()->put('special_value', true);
        session()->forget('special_callback_url');
        $year = date("Y") - 1;
        $userID = Auth()->user()->id;

        if ($userID == 1 || $userID == 141) {

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'promotion_applications_nonacademics.ar_accept_date', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id')
                ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.head_status', '=', 1)
                //->where('departments.head_user_id','=',auth()->user()->employee_no)
                ->orderBy('promotion_applications_nonacademics.ar_accept_date')
                ->get();
        } else {

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'promotion_applications_nonacademics.ar_accept_date', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id')
                ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.head_status', '=', 1)
                ->where('departments.head_user_id', '=',  $userID)
                ->orderBy('promotion_applications_nonacademics.ar_accept_date')
                ->get();
        }

        return view('admin.promotion.nonAcademic.headApproval', compact('data_text'));
    }

    public function headApprovalDetails($id)
    {

        $appId = decrypt($id);

        $prmo_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
            ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
            ->select('employees.*', 'promotion_applications_nonacademics.*', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName')
            ->where('promotion_applications_nonacademics.id', '=', $appId)
            ->get();

        if ($prmo_text->count() > 0) {
            foreach ($prmo_text as $prmo_texts) {
                $empNo = $prmo_texts->employee_no;

                $year = $prmo_texts->year;
                $nic = $prmo_texts->nic;

                $name = $prmo_texts->initials . " " . $prmo_texts->last_name;
                $fName = $prmo_texts->name_denoted_by_initials . " " . $prmo_texts->last_name;

                $dep = $prmo_texts->department_name;
                $desig = $prmo_texts->designation_name . " " . $prmo_texts->category_name;
                $promoDesig = $prmo_texts->promoDesigName . " " . $prmo_texts->proGName;

                $appDate = Carbon::parse($prmo_texts->created_at)->toDateString();
                $ResDate = $prmo_texts->ar_accept_date;

                $desID = $prmo_texts->current_desigantion_id;
                $promoDesID = $prmo_texts->promotion_designation_id;
            }
        } else {
            $empNo = "";
            $year = "";
            $nic = "";
            $name = "";
            $fName = "";
            $dep = "";
            $desig = "";
            $promoDesig = "";
            $appDate = "";
            $ResDate = "";
            $desID = "";
            $promoDesID = "";
        }

        $leave = Leave::where('leaves.emp_no', '=', $empNo)->orderBy('leaves.year', 'DESC')->get();

        return view('admin.promotion.nonAcademic.headApprovalDetails', compact(
            'empNo',
            'year',
            'nic',
            'name',
            'fName',
            'dep',
            'desig',
            'promoDesig',
            'appDate',
            'ResDate',
            'desID',
            'promoDesID',
            'leave'
        ));
    }

    public function headAppStore(Request $request)
    {

        if ($request->empNo != null) {
            if ($request->proYear != null) {
                if ($request->desID != null) {
                    if ($request->proDesID != null) {

                        $headPosiText = DepartmentHead::where('emp_no', auth()->user()->employee_no)->get();

                        if ($headPosiText->count() > 0) {
                            foreach ($headPosiText as $headPosiTexts) {
                                $headPosition = $headPosiTexts->head_position;
                            }
                        } else {
                            $headPosition = 0;
                        }

                        $exam_text = promotionDuration::where('designation_id', '=', $request->desID)
                            ->where('promo_desig_id', '=', $request->proDesID)
                            ->get();

                        if ($exam_text->count() > 0) {
                            foreach ($exam_text as $exam_texts) {
                                $exam = $exam_texts->trade_test;
                            }
                        } else {

                            $exam = 0;

                            $notification = array(
                                'message' => 'Your recommendation is not successfully save. Please try again.5',
                                'alert-type' => 'error'
                            );

                            return redirect()->route('nac.promo.head.approval.open')->with($notification);
                        }



                        if ($exam == 1) {

                            $promotionApplication = PromotionApplicationsNonacademic::where('emp_no', $request->empNo)
                                ->where('year', $request->proYear)
                                ->first();

                            if ($promotionApplication) {

                                $promotionApplication->head_status = $request->recom;
                                $promotionApplication->head_user_id = auth()->user()->employee_no;
                                $promotionApplication->head_date = Carbon::today();
                                $promotionApplication->exam_status = 1;
                                $promotionApplication->performance = $request->perf;
                                $promotionApplication->skills = $request->skill;
                                $promotionApplication->skill_details = $request->sComment;
                                $promotionApplication->weaknesses = $request->weak;
                                $promotionApplication->weakness_details = $request->wComment;
                                $promotionApplication->head_remark = $request->OtherComment;
                                $promotionApplication->not_rec_reason = $request->notRecomCmm;
                                $promotionApplication->head_position = $headPosition;
                                $promotionApplication->save();
                            }
                            Log::notice('UsjnetController -> promotion application department head check in employee number - ' . $request->empNo . ' ckecked by ' . auth()->user()->employee_no);

                            $notification = array(
                                'message' => 'Your recommendation is successfully submitted.',
                                'alert-type' => 'success'
                            );

                            return redirect()->route('nac.promo.head.approval.open')->with($notification);
                        } else {

                            $promotionApplication = PromotionApplicationsNonacademic::where('emp_no', $request->empNo)
                                ->where('year', $request->proYear)
                                ->first();

                            if ($promotionApplication) {
                                $promotionApplication->head_status = $request->recom;
                                $promotionApplication->head_user_id = auth()->user()->employee_no;
                                $promotionApplication->head_date = Carbon::today();
                                $promotionApplication->exam_status = 10;
                                $promotionApplication->interview_status = 1;
                                $promotionApplication->performance = $request->perf;
                                $promotionApplication->skills = $request->skill;
                                $promotionApplication->skill_details = $request->sComment;
                                $promotionApplication->weaknesses = $request->weak;
                                $promotionApplication->weakness_details = $request->wComment;
                                $promotionApplication->head_remark = $request->OtherComment;
                                $promotionApplication->not_rec_reason = $request->notRecomCmm;
                                $promotionApplication->head_position = $headPosition;
                                $promotionApplication->save();
                            }

                            Log::notice('UsjnetController -> promotion application department head check in employee number - ' . $request->empNo . ' ckecked by ' . auth()->user()->employee_no);

                            $notification = array(
                                'message' => 'Your recommendation is successfully submitted.',
                                'alert-type' => 'success'
                            );

                            return redirect()->route('nac.promo.head.approval.open')->with($notification);
                        }
                    } else {
                        $notification = array(
                            'message' => 'Your recommendation is not successfully save. Please try again.4',
                            'alert-type' => 'error'
                        );

                        return redirect()->route('nac.promo.head.approval.open')->with($notification);
                    }
                } else {
                    $notification = array(
                        'message' => 'Your recommendation is not successfully save. Please try again.3',
                        'alert-type' => 'error'
                    );

                    return redirect()->route('nac.promo.head.approval.open')->with($notification);
                }
            } else {
                $notification = array(
                    'message' => 'Your recommendation is not successfully save. Please try again.2',
                    'alert-type' => 'error'
                );

                return redirect()->route('nac.promo.head.approval.open')->with($notification);
            }
        } else {
            $notification = array(
                'message' => 'Your recommendation is not successfully save. Please try again.1',
                'alert-type' => 'error'
            );

            return redirect()->route('nac.promo.head.approval.open')->with($notification);
        }
    }

    public function VacancyApplicationDeptHeadView()
    {

        session()->put('special_value', true);
        session()->forget('special_callback_url');
        //session()->put('checkReturn',  false);

        $mainBranch = Auth()->user()->main_branch_id;
        $userID = Auth()->user()->id;
        $currentDate = date('Y-m-d');


        if ($userID == 1 || $userID == 2) {

            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancies.vacancy_status_type_id',
                    'vacancies.head_decision_reset',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 33 THEN 1 ELSE 0 END), 0) as available'),
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id IN (34, 35) THEN 1 ELSE 0 END), 0) as booked')
                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'vacancies.head_decision_reset')
                ->orderByDesc('vacancies.id')
                ->whereIn('vacancy_status_type_id', array(29, 258))
                ->get();
        } else {

            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('departments', 'vacancies.department_id', '=', 'departments.id')
                ->leftJoin('applications', 'vacancies.id', '=', 'applications.vacancy_id')
                ->select(
                    'vacancies.id',
                    'vacancies.designation_id',
                    'vacancies.faculty_id',
                    'vacancies.department_id',
                    'vacancies.subject',
                    'categories.display_name',
                    'vacancies.main_category_id',
                    'vacancies.vacancy_status_type_id',
                    'departments.head_user_id',
                    'vacancies.head_decision_reset',
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id = 33 THEN 1 ELSE 0 END), 0) as available'),
                    DB::raw('COALESCE(SUM(CASE WHEN applications.application_decision_id IN (34, 35) THEN 1 ELSE 0 END), 0) as booked')
                )
                ->groupBy('vacancies.id', 'vacancies.designation_id', 'vacancies.faculty_id', 'vacancies.department_id', 'vacancies.subject', 'categories.display_name', 'vacancies.main_category_id', 'vacancies.vacancy_status_type_id', 'departments.head_user_id', 'vacancies.head_decision_reset')
                ->orderByDesc('vacancies.id')
                ->whereIn('vacancy_status_type_id', array(29, 258))
                ->where('departments.head_user_id', '=', auth()->user()->id)
                ->get();
        }
        return view('admin.vacancy.head_check.index', compact('vacancies'));
    }

    public function hodListOpen()
    {
        session()->put('special_value', true);
        session()->forget('special_callback_url');


        if (Auth()->user()->hasRole(['administrator'])) {

            $forwarding_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->select(
                    'increments_process_acs.id',
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.ex_forward_date',
                )
                ->where('increments_process_acs.self_eval_status', '=', 1)
                ->get();

            $rec_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->select(
                    'increments_process_acs.id',
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.self_eval_date',
                )
                ->where('increments_process_acs.head_status', '=', 1)
                ->get();
        } else {
            // $hod_dep_text = Employee::select('department_id')
            //     ->where('employee_no', '=', auth()->user()->employee_no)
            //     ->get();

            $hod_dep_text = DepartmentHead::select('department_id')
                ->where('emp_no', '=', auth()->user()->employee_no)
                ->where('active_status', 1)
                ->get();

            if (count($hod_dep_text) > 0) {
                foreach ($hod_dep_text as  $hod_dep_texts) {
                    $hod_dep_id =  $hod_dep_texts->department_id;
                }
            } else {
                $hod_dep_id = 0;
            }

            $forwarding_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('department_heads', 'department_heads.department_id', '=', 'increments_process_acs.dep_no')
                ->select(
                    'increments_process_acs.id',
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.ex_forward_date',
                )
                ->where('department_heads.emp_no', '=', auth()->user()->employee_no)
                ->where('department_heads.active_status', '=', 1)
                ->where('increments_process_acs.self_eval_status', '=', 1)
                ->get();

            $rec_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('department_heads', 'department_heads.department_id', '=', 'increments_process_acs.dep_no')
                ->select(
                    'increments_process_acs.id',
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'increments_process_acs.self_eval_date',
                )
                ->where('department_heads.emp_no', '=', auth()->user()->employee_no)
                ->where('department_heads.active_status', '=', 1)
                ->where('increments_process_acs.head_status', '=', 1)
                ->get();
        }

        return view('admin.increment.Academic.hodList', compact('forwarding_text', 'rec_text'));
    }

    public function deanListOpen()
    {
        session()->put('special_value', true);
        session()->forget('special_callback_url');


        if (Auth()->user()->hasRole(['administrator'])) {

            $dean_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('categories as dg', 'dg.id', '=', 'designations.staff_grade')
                ->select(
                    'increments_process_acs.id',
                    'increments_process_acs.ref_no',
                    'increments_process_acs.emp_no',
                    'increments_process_acs.incre_date',
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'departments.department_name',
                    'designations.designation_name',
                    'dg.category_name as grade',
                )
                ->where('increments_process_acs.dean_status', '=', 1)
                ->get();
        } else {
            $dean_dep_text = FacultyDean::select('faculty_id')
                ->where('emp_no', '=', auth()->user()->employee_no)
                ->where('faculty_id', '!=', 51)
                ->get();

            if (count($dean_dep_text) > 0) {
                foreach ($dean_dep_text as  $dean_dep_texts) {
                    $dean_fuc_id =  $dean_dep_texts->faculty_id;
                }
            } else {
                $dean_fuc_id = 0;
            }

            $dean_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
                ->join('categories as t', 't.id', '=', 'employees.title_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'employees.designation_id')
                ->join('categories as dg', 'dg.id', '=', 'designations.staff_grade')
                ->select(
                    'increments_process_acs.id',
                    'increments_process_acs.ref_no',
                    'increments_process_acs.emp_no',
                    'increments_process_acs.incre_date',
                    't.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'departments.department_name',
                    'designations.designation_name',
                    'dg.category_name as grade',
                )
                ->where('employees.faculty_id', '=', $dean_fuc_id)
                ->where('increments_process_acs.dean_status', '=', 1)
                ->get();
        }

        return view('admin.increment.Academic.deanList', compact('dean_text'));
    }

    public function vcListOpen()
    {
        session()->put('special_value', true);
        session()->forget('special_callback_url');

        $vc_text = incrementsProcessAc::join('employees', 'employees.employee_no', '=', 'increments_process_acs.emp_no')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as dg', 'dg.id', '=', 'designations.staff_grade')
            ->select(
                'increments_process_acs.id',
                'increments_process_acs.ref_no',
                'increments_process_acs.emp_no',
                'increments_process_acs.incre_date',
                't.category_name as title',
                'employees.initials',
                'employees.last_name',
                'departments.department_name',
                'designations.designation_name',
                'dg.category_name as grade',
            )
            ->where('increments_process_acs.vc_status', '=', 1)
            ->get();


        return view('admin.increment.Academic.vcList', compact('vc_text'));
    }
}
