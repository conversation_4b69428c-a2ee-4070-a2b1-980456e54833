<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Department extends Model
{
    use HasFactory,SoftDeletes,LogsActivity;

    protected $dates = ['deleted_at'];

    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('hrms_departments')
        ->logOnlyDirty()
        ->dontLogIfAttributesChangedOnly(['updated_at'])
        ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    public function faculties()
    {
        return $this->belongsTo(Faculty::class,'faculty_code');
    }

    public function departmentHeads()
    {
        return $this->hasMany(DepartmentHead::class, 'department_id', 'id');
    }

    public function departmentVacancys()
    {
        return $this->hasMany(Vacancy::class, 'department_id', 'id');
    }
}
