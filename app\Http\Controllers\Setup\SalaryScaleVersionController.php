<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\SalaryScaleVersion;
use Illuminate\Http\Request;
use Carbon\Carbon;

class SalaryScaleVersionController extends Controller
{
    public function index()
    {
        $versions = SalaryScaleVersion::orderBy('effective_date', 'desc')->get();
        return view('admin.setups.salary_scale_version.index', compact('versions'));
    }

    public function create()
    {
        return view('admin.setups.salary_scale_version.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'effective_date' => 'required|date',
            'termination_date' => 'nullable|date|after:effective_date'
        ]);

        // Deactivate all currently active versions
        SalaryScaleVersion::where('status', 1)->update(['status' => 0]);

        $version = new SalaryScaleVersion();
        $version->effective_date = $request->effective_date;
        $version->termination_date = $request->termination_date;
        $version->status = 1;
        $version->save();

        $notification = array(
            'message' => 'Salary Scale Version Created Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('salary.scale.version.index')->with($notification);
    }

    public function edit($id)
    {
        $version = SalaryScaleVersion::findOrFail($id);
        return view('admin.setups.salary_scale_version.edit', compact('version'));
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'effective_date' => 'required|date',
            'termination_date' => 'nullable|date|after:effective_date'
        ]);

        $version = SalaryScaleVersion::findOrFail($id);
        $version->effective_date = $request->effective_date;
        $version->termination_date = $request->termination_date;
        $version->save();

        $notification = array(
            'message' => 'Salary Scale Version Updated Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('salary.scale.version.index')->with($notification);
    }

    public function toggleStatus($id)
    {
        $version = SalaryScaleVersion::findOrFail($id);

        if (!$version->status) {
            // If activating, deactivate all other active versions first
            SalaryScaleVersion::where('status', 1)->update(['status' => 0]);
            $version->status = 1;
        } else {
            $version->status = 0;
        }
        $version->save();

        $status = $version->status ? 'activated' : 'deactivated';
        $notification = array(
            'message' => "Salary Scale Version {$status} Successfully",
            'alert-type' => 'success'
        );

        return redirect()->route('salary.scale.version.index')->with($notification);
    }

    public function destroy($id)
    {
        $version = SalaryScaleVersion::findOrFail($id);
        $version->delete();

        $notification = array(
            'message' => 'Salary Scale Version Deleted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('salary.scale.version.index')->with($notification);
    }
}
