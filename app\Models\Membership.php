<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Membership extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function membershipTypeName()
    {
        return $this->belongsTo(Category::class,'membership_type');

    }

    public function membershipCurrentStatusName()
    {
        return $this->belongsTo(Category::class,'membership_active_status');

    }
}
