<?php

namespace App\Http\Controllers\Backend\Promotion;

use App\Http\Controllers\Controller;
use App\Models\Designation;
use App\Models\promotionDuration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class PromotionDurationController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|sc');
    }

    public function index()
    {
        Log::info('PromotionDurationController -> promotion duration data index get started');

        $desig = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('designations.id', 'designations.designation_name', 'categories.category_name', 'designations.salary_code')
            ->where('designations.deleted_at', '=', NULL)
            ->get();

        $proDu = promotionDuration::join('designations', 'designations.id', '=', 'promotion_durations.designation_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->join('designations as promoDes', 'promoDes.id', '=', 'promotion_durations.promo_desig_id')
            ->join('categories as promoDesC', 'promoDesC.id', '=', 'promoDes.staff_grade')
            ->select('promotion_durations.*', 'designations.designation_name', 'categories.category_name', 'designations.salary_code', 'promoDes.designation_name as promoDesName', 'promoDesC.category_name as promoDesG', 'promoDes.salary_code as promoSCode')
            ->get();

        Log::info('PromotionDurationController -> promotion duration data index get ended');

        return view('admin.promotion.promotion_duration', compact('desig', 'proDu'));
    }


    public function store(Request $request)
    {
        Log::info('PromotionDurationController -> promotion duration data index get ended');

        $request->validate(
            [
                'desic' => 'required|unique:promotion_durations,designation_id',
                'desicPromo' => 'required|unique:promotion_durations,promo_desig_id',
                'duration' => 'required'
            ],
            [
                'desic.required' => 'Please select current designation',
                'desic.unique' => 'you selected designation already in the systems',
                'desicPromo.required' => 'Please select promoted designation',
                'desicPromo.unique' => 'you selected promotion designation already in the systems',
                'duration.required' => 'Please enter service period '
            ]
        );

        if ($request->desic != 0) {
            if ($request->desicPromo != 0) {
                if ($request->duration != null) {

                    if ($request->service != null) {
                        $service = $request->service;
                    } else {
                        $service = 0;
                    }

                    $has_record = DB::table('promotion_durations')
                        ->where('designation_id', $request->desic)
                        ->get();

                    $row_count = $has_record->count();

                    if ($row_count > 0) {
                        $notification = array(
                            'message' => 'This designation has a record. You can not submit it',
                            'alert-type' => 'error'
                        );
                    } else {


                        $proDuration = new promotionDuration();
                        $proDuration->designation_id = $request->desic;
                        $proDuration->promo_desig_id = $request->desicPromo;
                        $proDuration->duration = $request->duration;
                        $proDuration->total_service = $service;
                        $proDuration->trade_test = $request->has('tradeTest');
                        $proDuration->add_user_id = auth()->user()->employee_no;
                        $proDuration->save();

                        $notification = array(
                            'message' => 'Promotion Duration data Inserted Successfully',
                            'alert-type' => 'success'
                        );
                    }
                } else {
                    $notification = array(
                        'message' => 'Promotion duration data Inserted Unsuccessfully.Please enter the duration',
                        'alert-type' => 'error'
                    );
                }
            } else {
                $notification = array(
                    'message' => 'Promotion duration data Inserted Unsuccessfully.Please select the promotion designation',
                    'alert-type' => 'error'
                );
            }
        } else {
            $notification = array(
                'message' => 'Promotion duration data Inserted Unsuccessfully.Please select the designation',
                'alert-type' => 'error'
            );
        }

        return redirect()->route('prmotion_duration.add')->with($notification);
    }


    public function edit($id)
    {
        Log::info('PromotionDurationController -> promotion duration data edit started');

        $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('designations.id', 'designations.designation_name', 'categories.category_name', 'designations.salary_code')
            ->where('designations.deleted_at', '=', NULL)
            ->get();

        $editData = promotionDuration::find($id);

        Log::notice('PromotionDurationController -> edit promotion duration data id - ' . $editData->id . ' edited by ' . auth()->user()->employee_no);
        Log::info('PromotionDurationController -> promotion duration edit ended');

        return view('admin.promotion.promotion_duration_edit', compact('editData', 'designations'));
    }


    public function update(Request $request, $id)
    {
        Log::info('PromotionDurationController -> promotion duration data update started');

        $validatedData = $request->validate([
            'designation_id' => ['required', Rule::unique('promotion_durations')->ignore($id)],
            'promo_desig_id' => ['required', Rule::unique('promotion_durations')->ignore($id)],
            'duration' => 'required|numeric',
        ]);


        $proDuration = promotionDuration::find($id);
        $proDuration->designation_id = $request->designation_id;
        $proDuration->promo_desig_id = $request->promo_desig_id;
        $proDuration->duration = $request->duration;
        $proDuration->total_service = $request->total_service;
        $proDuration->trade_test = $request->has('trade_test');
        $proDuration->updated_user_id = auth()->user()->employee_no;
        $proDuration->save();

        Log::warning('PromotionDurationController -> update promotion duration data id - ' . $proDuration->id . ' updated by ' . auth()->user()->employee_no);
        Log::info('PromotionDurationController -> promotion duration data update ended');

        $notification = array(
            'message' => 'Promotion Duration data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('prmotion_duration.add')->with($notification);
    }


    public function delete($id)
    {
        Log::info('PromotionDurationController -> promotion duration data delete started');

        $proDuration = promotionDuration::find($id);
        $proDuration->delete();

        Log::emergency('PromotionDurationController -> delete promotion duration id - ' . $proDuration->id . ' deleted by ' . auth()->user()->employee_no);
        Log::info('PromotionDurationController -> promotion duration data delete ended');


        $notification = array(
            'message' => 'Promotion Duration data Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('prmotion_duration.add')->with($notification);
    }
}
