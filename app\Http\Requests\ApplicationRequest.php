<?php

namespace App\Http\Requests;

use App\Models\AdvanceLevelResultSummary;
use App\Models\Application;
use App\Models\Bond;
use App\Models\Degree;
use App\Models\DegreeCertificate;
use App\Models\DegreeSubject;
use App\Models\Diploma;
use App\Models\DiplomaCertificate;
use App\Models\OrdinaryLevelResultSummary;
use App\Models\Referee;
use App\Models\ReleaseLetter;
use App\Models\Vacancy;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Factory;

class ApplicationRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        // Initialize the $rules array
        $rules = [];

        if(!session()->get('vacancy_id') || !session()->get('reference_no')){

            $notification = array(
                'message' => 'Your application session has been expired',
                'alert-type' => 'error'
             );

             return redirect()->route('home')->with($notification);
        }else{

            $vacancy = Vacancy::find(session()->get('vacancy_id'));
            $olSummary = OrdinaryLevelResultSummary::where('reference_no','=',session()->get('reference_no'))->where('attempt','=',1)->where('file_path','!=',NULL)->count();
            $alSummary = AdvanceLevelResultSummary::where('reference_no','=',session()->get('reference_no'))->where('level','=',1)->where('file_path','!=',NULL)->count();
            $availableDegreeFileCount = DegreeCertificate::where('reference_no','=',session()->get('reference_no'))->count();
            $availableDiplomaFileCount = DiplomaCertificate::where('reference_no','=',session()->get('reference_no'))->count();
            $AppSummary = Application::where('reference_no','=',session()->get('reference_no'))->where('profile_photo','!=',NULL)->count();
            $availableDegreeCount = Degree::where('reference_no','=',session()->get('reference_no'))->where('degree_type',214)->count();
            $availableDiplomaCount = Diploma::where('reference_no','=',session()->get('reference_no'))->count();
            $availableDegreeSubjectCount = DegreeSubject::where('reference_no','=',session()->get('reference_no'))->count();
            $availableBondtCount = Bond::where('reference_no','=',session()->get('reference_no'))->count();
            $availableRefereetCount = Referee::where('reference_no','=',session()->get('reference_no'))->count();
            $availableReleaseLettertCount = ReleaseLetter::where('reference_no','=',session()->get('reference_no'))->count();

        }

        if($this->has('submit1')){

            $rules['titel_id'] = 'required';
            $rules['initials'] = 'required|regex:/^([A-Z]\.)+$/';
            $rules['name_denoted_by_initials'] = 'required';
            $rules['last_name'] = 'required|alpha';
            //$rules['phone_no'] = 'nullable|max:12';
            // $rules['date_of_birth'] = [
            //     'required',
            //     'date',
            //     'before:' .  $dateClosed->subYears($vacancy->min_age)->format('Y-m-d'), // Minimum age: 18
            //     'after:' .  $dateClosed->subYears($vacancy->max_age - $vacancy->min_age)->format('Y-m-d'), // Maximum age: 60
            // ];
            //$rules['gender_id'] = 'required';
            $rules['civil_status_id'] = 'required';
            //$rules['race_id'] = 'required';
            //$rules['religion_id'] = 'required';
            $rules['permanent_add1'] = 'required';
            $rules['permanent_city_id'] = 'required';
            $rules['postal_add1'] = 'required';
            $rules['postal_city_id'] = 'required';
            $rules['citizenship'] = 'required';
            $rules['state_of_citizenship_id'] = 'required';
            $rules['emp_highest_edu_level'] = 'required';
            $rules['profile_photo'] = 'mimes:png,jpg|max:5120';

            if ($AppSummary == 0) {
                $rules['profile_photo'] = 'required';
            }
            $rules['terms'] = 'required';

            if($this->get('state_of_citizenship_id') == 26){

            $rules['citizen_registration_no'] = 'required';

            }

        if ($vacancy->main_category_id == 46) {

            $rules['index_no'] = 'required';
            $rules['year'] = 'required|max:' . (date('Y'));
            $rules['grade.*'] = 'required|max:1|alpha';
            $rules['certificate1'] = 'mimes:pdf';

            if ($olSummary == 0) {
                $rules['certificate1'] = 'required';
            }

            /*******************************************/
            $rules['year2'] = 'max:' . (date('Y'));
            $rules['certificate2'] = 'mimes:pdf';
            $rules['grade2.*'] = 'nullable|max:1|alpha';
            /************************************ */
            $rules['al_index_no'] = 'required';
            $rules['al_year'] = 'required|max:' . (date('Y'));
            $rules['al_attempt'] = 'required';
            $rules['al_stream_id'] = 'required';
            $rules['al_certificate1'] = 'mimes:pdf';

            if($alSummary == 0){
                $rules['al_certificate1'] = 'required';
            }

            $rules['al_subject_name.0'] = 'required|min:3';
            $rules['al_subject_name.1'] = 'required|min:3';
            $rules['al_subject_name.2'] = 'required|min:3';
            $rules['al_grade.*'] = 'max:1';
            $rules['al_grade.0'] = 'required|alpha';
            $rules['al_grade.1'] = 'required|alpha';
            $rules['al_grade.2'] = 'required|alpha';
            $rules['al_grade.3'] = 'nullable|alpha';
            /************************************** */
            $rules['al_year2'] = 'max:' . (date('Y'));
            $rules['al_certificate2'] = 'mimes:pdf';
            $rules['grade2.*'] = 'nullable|max:1|alpha';

        }elseif ($vacancy->main_category_id == 44 || $vacancy->main_category_id == 45 ) {

            if($vacancy->min_qualification == 73 || $vacancy->min_qualification == 74 || $vacancy->min_qualification == 183 || $vacancy->min_qualification == 184){

                if ($availableDiplomaFileCount == 0 && !$this->hasFile('diploma_certificate')) {

                    $rules['diploma_certificate.1'] = 'required|mimes:pdf';

                }else{

                    $rules['diploma_certificate.*'] = 'nullable|mimes:pdf';
                }

                if ($availableDiplomaCount == 0 && !$this->get('diploma_type')) {

                    $rules['diploma_type_1'] = 'required';
                }

                $rules['degree_certificate.*'] = 'mimes:pdf';


            }elseif ($vacancy->min_qualification == 75 || $vacancy->min_qualification == 76 || $vacancy->min_qualification == 77 || $vacancy->min_qualification == 78 || $vacancy->min_qualification == 79 || $vacancy->min_qualification == 80 || $vacancy->min_qualification == 81 || $vacancy->min_qualification == 82) {

                if ($availableDegreeFileCount == 0 && !$this->hasFile('degree_certificate')) {

                    $rules['degree_certificate.1'] = 'required|mimes:pdf';

                }else{

                    $rules['degree_certificate.*'] = 'nullable|mimes:pdf';

                }

                if ($availableDegreeCount == 0 && !$this->get('degree_type')) {

                    $rules['degree_type_1'] = 'required';
                }

                if ($availableDegreeSubjectCount == 0 && !$this->get('degree_main_subject1')) {

                    $rules['degree_main_subject1'] = 'required';

                }

                $rules['diploma_certificate.*'] = 'nullable|mimes:pdf';
            }


            $rules['bondability'] = 'required';

            if($this->get('bondability') == 1){

                if ($availableBondtCount == 0 && !$this->get('bond_institution')) {

                    $rules['bond_institution_1'] = 'required';

                }

            }

            if ($availableRefereetCount == 0 && !$this->get('referee_name')) {

                $rules['referee_name_1'] = 'required';

            }

            $rules['publicSector'] = 'required';

            if($this->get('publicSector') == 1){

                if ($availableReleaseLettertCount == 0 && !$this->hasFile('public_sector_letter')) {

                    $rules['public_sector_letter'] = 'required|mimes:pdf';

                }else{

                    $rules['public_sector_letter'] = 'nullable|mimes:pdf';
                }

            }

            $rules['employment_record_certificate.*'] = 'nullable|mimes:pdf';
            $rules['orcid'] = 'nullable|regex:/^https:\/\/orcid\.org\/[a-zA-Z\d]{4}-[a-zA-Z\d]{4}-[a-zA-Z\d]{4}-[a-zA-Z\d]{4}$/';


        }

        # other validations
      }else if($this->has('save') || $this->has('save1')){

        $rules['initials'] = 'nullable|regex:/^([A-Z]\.)+$/';
        $rules['last_name'] = 'nullable|alpha';
       // $rules['phone_no'] = 'nullable|max:12';
        // $rules['date_of_birth'] = [
        //     'nullable',
        //     'date',
        //     'before:' .  $dateClosed->subYears($vacancy->min_age)->format('Y-m-d'), // Minimum age: 18
        //     'after:' .  $dateClosed->subYears($vacancy->max_age - $vacancy->min_age)->format('Y-m-d'), // Maximum age: 60
        // ];

        $rules['profile_photo'] = 'mimes:png,jpg|max:5120';

        if ($vacancy->main_category_id == 46) {

            $rules['year'] = 'nullable|max:'.(date('Y'));
            $rules['year2'] = 'nullable|max:'.(date('Y'));
            $rules['al_year'] = 'nullable|max:'.(date('Y'));
            $rules['al_year2'] = 'nullable|max:'.(date('Y'));
            $rules['grade.*'] = 'nullable|max:1|alpha';
            $rules['grade2.*'] = 'nullable|max:1|alpha';
            $rules['al_grade.*'] = 'nullable|max:1|alpha';
            $rules['al_grade2.*'] = 'nullable|max:1|alpha';
            $rules['certificate1'] = 'mimes:pdf';
            $rules['certificate2'] = 'mimes:pdf';
            $rules['al_certificate1'] = 'mimes:pdf';
            $rules['al_certificate2'] = 'mimes:pdf';

        }elseif ($vacancy->main_category_id == 44 || $vacancy->main_category_id == 45) {

            $rules['degree_certificate.*'] = 'nullable|mimes:pdf|max:5120';
            $rules['diploma_certificate.*'] = 'nullable|mimes:pdf|max:5120';
            $rules['employment_record_certificate.*'] = 'nullable|mimes:pdf|max:5120';
            $rules['public_sector_letter'] = 'nullable|mimes:pdf|max:5120';
            $rules['orcid'] = 'nullable|regex:/^https:\/\/orcid\.org\/[a-zA-Z\d]{4}-[a-zA-Z\d]{4}-[a-zA-Z\d]{4}-[a-zA-Z\d]{4}$/';
        }

            //$rules['phone_no'] = 'max:12';
            //$rules['profile_photo'] = 'mimes:jpeg,png,jpg|max:2048';

        # other validations
      }else{

        $rules = [];

      }
        return $rules;
    }

    public function messages()
    {
        return [
            'titel_id.required' => 'Title required',
            'initials.required' => 'Initials required',
            'initials.regex' => 'Capital letters and dots only. Ex:- A.B.C.',
            'name_denoted_by_initials.required' => 'Names denoted by initials are required',
            'last_name.required' => 'Last name required',
            'phone_no.max' => 'Entered phone number is invalid',
            'date_of_birth.required' => 'the birthday required',
            'date_of_birth.date' => 'the birthday must be date',
            'date_of_birth.before' => 'Your age is not sufficient to apply for this vacancy',
            'date_of_birth.after' => 'Your age is not sufficient to apply for this vacancy',
            'gender_id.required' => 'the gender required',
            'civil_status_id.required' => 'Civil status required',
            'race_id.required' => 'the race required',
            'religion_id.required' => 'the religion required',
            'permanent_add1.required' => 'Permanent address required',
            'permanent_city_id.required' => 'Permanent city required',
            'postal_add1.required' => 'Postal address required',
            'postal_city_id.required' => 'Postal city required',
            'citizenship.required' => 'Citizenship required',
            'state_of_citizenship_id.required' => 'Citizenship type required',
            'emp_highest_edu_level.required' => 'Highest academic qulification required',
            'profile_photo.mimes' => 'You must upload png or jpg images only',
            'profile_photo.max' => 'Your file size must be smaller than 10Mb',
            'profile_photo.required' => 'You must upload a profile image',
            'terms.required' => 'You must accept the declaration',
            /********************************************************************** */
            'index_no.required' => 'O/L exam number is required',
            'year.required' => 'O/L exam year is required',
            'attempt.required' => 'O/L exam attempt is required',
            'grade.0.required' => 'Religion grade required',
            'grade.0.max' => 'Grade can only one character Ex:-A',
            'grade.0.alpha' => 'grade can only one character Ex:-A',
            'grade.1.required' => 'Language grade required',
            'grade.1.max' => 'grade can only one character Ex:-A',
            'grade.1.alpha' => 'grade can only one character Ex:-A',
            'grade.2.required' => 'Mathematics grade required',
            'grade.2.max' => 'grade can only one character Ex:-A',
            'grade.2.alpha' => 'grade can only one character Ex:-A',
            'grade.3.required' => 'Science grade required',
            'grade.3.max' => 'grade can only one character Ex:-A',
            'grade.3.alpha' => 'grade can only one character Ex:-A',
            'grade.4.required' => 'English grade required',
            'grade.4.max' => 'grade can only one character Ex:-A',
            'grade.4.alpha' => 'grade can only one character Ex:-A',
            'grade.5.required' => 'History grade required',
            'grade.5.max' => 'grade can only one character Ex:-A',
            'grade.5.alpha' => 'grade can only one character Ex:-A',
            'certificate1.mimes' => 'You must upload pdf document only',
            'certificate1.max' => 'You file size must be under 10mb',
            'certificate1.required' => 'you must upload O/L certificate',
            /****************************************************************** */
            'certificate2.mimes' => 'You must upload pdf document only',
            'certificate2.max' => 'You file size must be under 10mb',
            'grade2.0.max' => 'grade can only one character Ex:-A',
            'grade2.0.alpha' => 'grade can only one character Ex:-A',
            'grade2.1.max' => 'grade can only one character Ex:-A',
            'grade2.1.alpha' => 'grade can only one character Ex:-A',
            'grade2.2.max' => 'grade can only one character Ex:-A',
            'grade2.2.alpha' => 'grade can only one character Ex:-A',
            'grade2.3.max' => 'grade can only one character Ex:-A',
            'grade2.3.alpha' => 'grade can only one character Ex:-A',
            'grade2.4.max' => 'grade can only one character Ex:-A',
            'grade2.4.alpha' => 'grade can only one character Ex:-A',
            'grade2.5.max' => 'grade can only one character Ex:-A',
            'grade2.5.alpha' => 'grade can only one character Ex:-A',
            /***************************************************************** */
            'al_index_no.required' => 'A/L exam number is required',
            'al_year.required' => 'A/L exam year is required',
            'al_attempt.required' => 'A/L exam attempt is required',
            'al_stream_id.required' => 'A/L stream is required',
            'al_certificate1.required' => 'you must upload A/L certificate',
            'al_certificate1.mimes' => 'You must upload pdf document only',
            'al_certificate1.max' => 'You file size must be under 10mb',
            'al_grade.0.max' => 'grade can only one character Ex:-A',
            'al_grade.0.alpha' => 'grade can only one character Ex:-A',
            'al_grade.1.max' => 'grade can only one character Ex:-A',
            'al_grade.1.alpha' => 'grade can only one character Ex:-A',
            'al_grade.2.max' => 'grade can only one character Ex:-A',
            'al_grade.2.alpha' => 'grade can only one character Ex:-A',
            'al_grade.3.max' => 'grade can only one character Ex:-A',
            'al_grade.3.alpha' => 'grade can only one character Ex:-A',
            'al_grade.0.required' => 'A/L subject grade required',
            'al_grade.1.required' => 'A/L subject grade required',
            'al_grade.2.required' => 'A/L subject grade required',
            'al_subject_name.0.required' => 'A/L subject name required',
            'al_subject_name.1.required' => 'A/L subject name required',
            'al_subject_name.2.required' => 'A/L subject name required',
            'al_subject_name.0.min' => 'A/L subject full name required',
            'al_subject_name.1.min' => 'A/L subject full name required',
            'al_subject_name.2.min' => 'A/L subject full name required',

            'al_grade2.0.max' => 'grade can only one character Ex:-A',
            'al_grade2.0.alpha' => 'grade can only one character Ex:-A',
            'al_grade2.1.max' => 'grade can only one character Ex:-A',
            'al_grade2.1.alpha' => 'grade can only one character Ex:-A',
            'al_grade2.2.max' => 'grade can only one character Ex:-A',
            'al_grade2.2.alpha' => 'grade can only one character Ex:-A',
            'al_grade2.3.max' => 'grade can only one character Ex:-A',
            'al_grade2.3.alpha' => 'grade can only one character Ex:-A',
            'al_certificate2.mimes' => 'you must upload pdf document only',
            'al_certificate2.max' => 'you file size must be under 10mb',
            /*********************************************************** */
            'degree_certificate.1.required' => 'Degree certificates/transcripts must be uploaded',
            'diploma_certificate.1.required' => 'Diploma certificates must be uploaded',
            'degree_certificate.*.mimes' => 'Degree certificates/transcripts must be in PDF format',
            'diploma_certificate.*.mimes' => 'Diploma certificates must be in PDF format',
            'degree_type_1.required' => 'You must add first degree data',
            'diploma_type_1.required' => 'You must add at least one diploma/cretificate data',
            'degree_main_subject1.required' => 'Main subjects of the first degree is required',
            'bondability.required' => 'You must select bondability status',
            'bond_institution_1.required' => 'You must add at least one bond data.',
            'referee_name_1.required' => 'You must add non related referees data',
            'publicSector.required' => 'You must select, your current working status',
            'public_sector_letter.required' => 'Consent to release letter must be uploaded',
            'employment_record_certificate.*.mimes' => 'Employment record must be in PDF format',
            'public_sector_letter.mimes' => 'Release letter must be in PDF format',
            'degree_certificate.*.max' => 'Degree certificate/s file size must be under 5Mb',
            'diploma_certificate.*.max' => 'Diploma certificate/s file size must be under 5Mb',
            'employment_record_certificate.*.max' => 'Employement record/s file size must be under 5Mb',
            'public_sector_letter.max' => 'Consent to release letter size must be under 5Mb',
        ];
    }

    public function validate(Factory $factory)
    {
        $validator = $factory->make(
            $this->all(), // Input data
            $this->rules() // Validation rules
        );

        if ($validator->fails()) {
            $this->failedValidation($validator);
        }
    }
}
