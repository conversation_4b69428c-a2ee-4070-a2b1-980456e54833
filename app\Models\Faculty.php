<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Faculty extends Model
{
    use HasFactory,SoftDeletes,LogsActivity;

    protected $guarded = [];

    protected $dates = ['deleted_at'];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('hrms_faculties')
        ->logOnlyDirty()
        ->dontLogIfAttributesChangedOnly(['updated_at'])
        ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    public function facultyDeans()
    {
        return $this->hasMany(FacultyDean::class, 'faculty_id', 'id');
    }

    public function facultyVacancys()
    {
        return $this->hasMany(Vacancy::class, 'faculty_id', 'id');
    }
}
