<?php

namespace App\Http\Controllers\Backend\Transfer;

use App\Http\Controllers\Controller;
use App\Models\Designation;
use App\Models\Employee;
use App\Models\ExternalTransfer;
use App\Models\University;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ExternalTransferController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function indexExternal(Request $request)
    {
        if (isset($request->employee_no)) {

            Log::info('ExternalTransferController -> external transfer data getAll started');

            $mainBranch = Auth()->user()->main_branch_id;

            $search_emp_no = $request->employee_no;

            //admin user data collection
            if ($mainBranch == 51) {

                $emp_exter_trans = ExternalTransfer::join('universities', 'universities.id', '=', 'external_transfers.uni_institute')
                    ->join('designations', 'designations.id', '=', 'external_transfers.designation_id')
                    ->join('categories as status', 'external_transfers.status', '=', 'status.id')
                    ->select('external_transfers.*', 'universities.uni_name', 'designations.designation_name', 'designations.salary_code', 'status.category_name as transferType')
                    ->where('external_transfers.emp_no', $search_emp_no)
                    ->where('external_transfers.emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('external_transfers.effective_date')
                    ->get();

                $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                    ->where('employees.employee_no', $search_emp_no)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->get();

                $row_count = $empfetchDatas->count();
            }
            //academic devision data collection
            elseif ($mainBranch == 52) {

                $emp_exter_trans = ExternalTransfer::join('universities', 'universities.id', '=', 'external_transfers.uni_institute')
                    ->join('designations', 'designations.id', '=', 'external_transfers.designation_id')
                    ->join('categories as status', 'external_transfers.status', '=', 'status.id')
                    ->select('external_transfers.*', 'universities.uni_name', 'designations.designation_name', 'designations.salary_code', 'status.category_name as transferType')
                    ->where('external_transfers.emp_no', $search_emp_no)
                    ->where('external_transfers.emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('external_transfers.effective_date')
                    ->get();

                if (Auth()->user()->hasRole(['cc','est-head'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                } elseif (Auth()->user()->hasRole(['sc'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();
            }
            //non academic division data collection
            elseif ($mainBranch == 53) {

                $emp_exter_trans = ExternalTransfer::join('universities', 'universities.id', '=', 'external_transfers.uni_institute')
                    ->join('designations', 'designations.id', '=', 'external_transfers.designation_id')
                    ->join('categories as status', 'external_transfers.status', '=', 'status.id')
                    ->select('external_transfers.*', 'universities.uni_name', 'designations.designation_name', 'designations.salary_code', 'status.category_name as transferType')
                    ->where('external_transfers.emp_no', $search_emp_no)
                    ->where('external_transfers.emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('external_transfers.effective_date')
                    ->get();


                if (Auth()->user()->hasRole(['cc','est-head'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                } elseif (Auth()->user()->hasRole(['sc'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();
            }

            /*********************************************************** */

            if ($row_count > 0) {
                foreach ($empfetchDatas as $empData) {
                    $emp_no = $empData->employee_no;
                    $emp_name = $empData->titleName . " " . $empData->initials . " " . $empData->last_name;
                    $designation_name = $empData->designation_name . " " . $empData->gradeName;
                    $department_name = $empData->department_name;
                }
            } else {
                $emp_no = '';
                $emp_name = '';
                $designation_name = '';
                $department_name = '';
                $emp_exter_trans = array();

                $notification = array(
                    'message' => 'employee number not found or employee profile lock',
                    'alert-type' => 'error'
                );

                return redirect()->back()->with($notification);
            }


            $pass_employee_no = $request->employee_no;
        } else {
            $pass_employee_no = '';
            $emp_no = '';
            $emp_name = '';
            $designation_name = '';
            $department_name = '';
            $emp_exter_trans = array();
        }

        $uni = University::all();

        $desig = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('designations.id', 'designations.designation_name', 'categories.category_name', 'designations.salary_code')
            ->where('designations.deleted_at', '=', NULL)
            ->get();

        $categories = $this->getCategories([30]);
        $tType = $categories->where('category_type_id', '30');
        Log::info('ExternalTransferController -> External trasfer data getAll ended');

        return view("admin.transfer.external_transfer_history", compact('pass_employee_no', 'emp_no', 'emp_name', 'designation_name', 'department_name', 'emp_exter_trans', 'uni', 'desig', 'tType'));
    }

    public function storeExternalHistory(Request $request)
    {
        Log::info('ExternalTransferController -> old external transfer data sotre started');

        if (!isset($request->emp_no)) {
            $notification = array(
                'message' => 'Please Search Employee Before Submit',
                'alert-type' => 'error'
            );

            return redirect()->route('transfer.external.history.index')->with($notification);
        }

        $request->validate(
            ['emp_no' => 'required'],
            ['emp_no.required' => 'Please search employee before the enter old external transfer data']
        );


        if ($request->effDate != null) {

            for ($i = 0; $i < count($request->effDate); $i++) {

                $exterTransferHistory = new ExternalTransfer();
                $exterTransferHistory->emp_no = $request->emp_no;
                $exterTransferHistory->effective_date = date("Y-m-d", strtotime($request->effDate[$i]));
                $exterTransferHistory->designation_id = $request->desigId[$i];
                $exterTransferHistory->uni_institute = $request->uniId[$i];
                $exterTransferHistory->bond = $request->bondId[$i];
                $exterTransferHistory->status = $request->tId[$i];
                $exterTransferHistory->added_user_id = auth()->user()->employee_no;
                $exterTransferHistory->save();
            }

            Log::notice('ExternalTransferController -> Created employee old external transfer data employee number - ' . $request->emp_no . ' created by ' . auth()->user()->employee_no);
            Log::info('ExternalTransferController -> old external transfer data store ended');

            $notification = array(
                'message' => 'Old External Transfer data Inserted Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('transfer.external.history.index', ['employee_no' => $request->emp_no])->with($notification);
        } else {
            $notification = array(
                'message' => 'Old External Transfer data Inserted Unsuccessfully',
                'alert-type' => 'error'
            );

            return redirect()->route('transfer.external.history.index', ['employee_no' => $request->emp_no])->with($notification);
        }
    }

    public function editExternal($id)
    {

        Log::info('ExternalTransferController -> old external transfer data edit started');

        $universities = University::all();

        $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('designations.id', 'designations.designation_name', 'categories.category_name', 'designations.salary_code')
            ->where('designations.deleted_at', '=', NULL)
            ->get();

        $categories = $this->getCategories([30]);
        $transferTypes = $categories->where('category_type_id', '30');
        $editData = ExternalTransfer::find($id);

        Log::notice('ExternalTransferController -> edit old external transfer data id - ' . $editData->id . ' edited by ' . auth()->user()->employee_no);
        Log::info('ExternalTransferController -> old external transfer edit ended');

        return view('admin.transfer.external_transfer_edit', compact('editData', 'universities', 'designations', 'transferTypes'));
    }

    public function updateExternal(Request $request, $id)
    {

        Log::info('ExternalTransferController -> old external transfer data update started');

        $validatedData = $request->validate([
            'status' => 'required',
            'uni_institute' => 'required',
            'effective_date' => 'required|date',
            'designation_id' => 'required',

        ], [
            'status.required' => 'external transfer type required',
            'uni_institute.required' => 'select the relevent university or institute',
            'effective_date.required' => 'select external transfer effective date',
            'designation_id.required' => 'select the realavent designation',
        ]);


        $externalTransfer = ExternalTransfer::find($id);
        $externalTransfer->emp_no = $request->emp_no;
        $externalTransfer->status = $request->status;
        $externalTransfer->uni_institute = $request->uni_institute;
        $externalTransfer->effective_date = date("Y-m-d", strtotime($request->effective_date));
        $externalTransfer->designation_id = $request->designation_id;
        $externalTransfer->bond = $request->bond;
        $externalTransfer->updated_user_id = auth()->user()->employee_no;
        $externalTransfer->save();

        Log::warning('ExternalTransferController -> update old external transfer data employee number - ' . $externalTransfer->emp_no . ' updated by ' . auth()->user()->employee_no);
        Log::info('ExternalTransferController -> old external transfer data update ended');

        $notification = array(
            'message' => 'External Transfer data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('transfer.external.history.index', ['employee_no' => $externalTransfer->emp_no])->with($notification);
    }

    public function deleteExternal($id)
    {

        Log::info('ExternalTransferController -> external transfer data delete started');

        $externalTransfer = ExternalTransfer::find($id);
        $externalTransfer->delete();

        Log::emergency('ExternalTransferController -> delete external transfer data employee number - ' . $externalTransfer->emp_no . ' deleted by ' . auth()->user()->employee_no);
        Log::info('ExternalTransferController -> external transfer data delete ended');


        $notification = array(
            'message' => 'Internal Transfer data Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('transfer.external.history.index', ['employee_no' => $externalTransfer->emp_no])->with($notification);
    }
}
