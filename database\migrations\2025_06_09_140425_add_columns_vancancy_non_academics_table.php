<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vancancy_non_academics', function (Blueprint $table) {
            $table->integer('interview_assign_emp')->default(0)->after('interview_select_date');
            $table->date('interview_assign_date')->nullable()->after('interview_assign_emp');
            $table->date('interview_date')->nullable()->after('interview_board_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vancancy_non_academics', function (Blueprint $table) {
            $table->dropColumn('interview_assign_emp');
            $table->dropColumn('interview_assign_date');
            $table->dropColumn('interview_date');
        });
    }
};
