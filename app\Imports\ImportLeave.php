<?php

namespace App\Imports;

use App\Models\Leave;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Illuminate\Validation\Rule;

class ImportLeave implements ToModel,WithStartRow,WithValidation
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */

    public function startRow(): int
    {
        return 2;
    }

    public function model(array $row)
    {  
        $leave_count = Leave::where('emp_no',$row[0])->where('year', $row[1])->count();

        if($leave_count == 0){
         
            $leave = Leave::updateOrCreate([
                'emp_no' => $row[0],
                'year' => $row[1],
                'casual' => $row[2],
                'sick' => $row[3],
                'medical' => $row[4],
                'nopay' => $row[5],
                'study' => $row[6],
                'added_user_id' => auth()->user()->employee_no,
            ]);

        }else{

            $notification = array(
                'message' => 'Duplicate entity found',
                'alert-type' => 'error'
             );
        }

         

    }

    public function rules(): array
    {
        return [

            '0' => 'required|int',
            '1' => 'required|int',
            '2' => 'required|regex:/^\d+(\.\d{1,2})?$/',
            '3' => 'required|regex:/^\d+(\.\d{1,2})?$/',
            '4' => 'required|regex:/^\d+(\.\d{1,2})?$/',
            '5' => 'required|regex:/^\d+(\.\d{1,2})?$/',
            '6' => 'required|regex:/^\d+(\.\d{1,2})?$/',
        ];
    }

    public function customValidationMessages()
   {
       return [
            '0.integer' => 'Invalid employee number in the Excel',
            '1.integer' => 'Invalid Leave year in the Excel',
            '2.regex' => 'Invalid Casual Leave data in the Excel',
            '3.regex' => 'Invalid Sick Leave data in the Excel',
            '4.regex' => 'Invalid Medical Leave data in the Excel',
            '5.regex' => 'Invalid Nopay Leave data in the Excel',
            '6.regex' => 'Invalid Study Leave data in the Excel',
     ];
   }

}
