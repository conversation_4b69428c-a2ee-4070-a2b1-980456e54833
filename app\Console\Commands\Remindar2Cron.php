<?php

namespace App\Console\Commands;

use App\Mail\Reminder2Mail;
use App\Models\Application;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class Remindar2Cron extends Command
{

    protected $signature = 'remindar2:cron';


    protected $description = 'Vacancy Application Remindar message';


    public function handle()
    {
        $currentDate = Carbon::now(); // Current date and time
        $newDate = $currentDate->addDays(0); // date count for closing date

        $incompleteApplicant = Application::join('vacancies', 'applications.vacancy_id', '=', 'vacancies.id')
                  ->join('designations', 'vacancies.designation_id', '=', 'designations.id')
                  ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                  ->select('applications.*','vacancies.*','categories.display_name')
                  ->where('applications.application_decision_id',33)
                  ->where('applications.email_reminder2',0)
                  ->whereDate('vacancies.date_closed', $newDate->format('Y-m-d'))
                  ->get();

        if ($incompleteApplicant->count() > 0) {

            foreach ($incompleteApplicant as $key => $value) {

                if($value->subject == '' && $value->faculty_id == '' && $value->department_id == ''){

                    //mail data
                $emailData = [
                    'reference_no' => $value->reference_no,
                    'designation' => $value->designations->designation_name,
                    'grade'=> $value->display_name,
                    'faculty' => '',
                    'department' => '',
                    'name_status' => '',
                    'subject' => '',
                    'date_closed' => \Carbon\Carbon::parse($value->date_closed)->format('d.m.Y'),
                ];

                }elseif ($value->subject == '' && $value->faculty_id != '' && $value->department_id == '') {

                //mail data
                $emailData = [
                    'reference_no' => $value->reference_no,
                    'designation' => $value->designations->designation_name,
                    'grade'=> $value->display_name,
                    'faculty' => $value->faculties->faculty_name,
                    'department' => '',
                    'name_status' => '',
                    'subject' => '',
                    'date_closed' => \Carbon\Carbon::parse($value->date_closed)->format('d.m.Y'),
                ];
                    # code...
                }elseif ($value->subject == '' && $value->faculty_id != '' && $value->department_id != ''){

                //mail data
                $emailData = [
                    'reference_no' => $value->reference_no,
                    'designation' => $value->designations->designation_name,
                    'grade'=> $value->display_name,
                    'faculty' => $value->faculties->faculty_name,
                    'department' => $value->departments->department_name,
                    'name_status' => $value->departments->name_status,
                    'subject' => '',
                    'date_closed' => \Carbon\Carbon::parse($value->date_closed)->format('d.m.Y'),
                ];
                }elseif ($value->subject == '' && $value->faculty_id == '' && $value->department_id != ''){

                    $emailData = [
                        'reference_no' => $value->reference_no,
                        'designation' => $value->designations->designation_name,
                        'grade'=> $value->display_name,
                        'faculty' => '',
                        'department' => $value->departments->department_name,
                        'name_status' => $value->departments->name_status,
                        'subject' => '',
                        'date_closed' => \Carbon\Carbon::parse($value->date_closed)->format('d.m.Y'),
                    ];

                }else{

                    $emailData = [
                        'reference_no' => $value->reference_no,
                        'designation' => $value->designations->designation_name,
                        'grade'=> $value->display_name,
                        'faculty' => $value->faculties->faculty_name,
                        'department' => $value->departments->department_name,
                        'name_status' => $value->departments->name_status,
                        'subject' => $value->subject,
                        'date_closed' => \Carbon\Carbon::parse($value->date_closed)->format('d.m.Y'),
                    ];

                }

                $mail = new Reminder2Mail($emailData);
                //sending email
                Mail::to($value->email)->send($mail);

                //update status
                Application::where('reference_no', '=', $value->reference_no)->update(['email_reminder2' => 1]);

                // wait for 2 seconds
                usleep(200000);

            }
        }
        return Command::SUCCESS;
    }
}
