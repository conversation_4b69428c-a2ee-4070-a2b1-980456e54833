<?php

namespace App\Http\Controllers\Backend\Employee;

use App\Http\Controllers\Controller;
use App\Http\Requests\NewEmployeeRequest;
use App\Mail\UJSWelcomeMail;
use App\Models\City;
use App\Models\ContractEmployee;
use App\Models\Department;
use App\Models\DepartmentSub;
use App\Models\Employee;
use App\Models\EmployeeSalaryStatus;
use App\Models\Faculty;
use App\Models\PermanentEmployee;
use App\Models\TemporyEmployee;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class NewEmployeeController extends Controller
{
    public function __construct()
    {
        //session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function newCheckView()
    {

        $roleID = Auth()->user()->role_id;
        $empNo = Auth()->user()->employee_no;


        if (Auth()->user()->hasRole(['super-admin', 'administrator'])) {

            $pemployees = PermanentEmployee::where('completion_status', 0)->get();
            $temployees = TemporyEmployee::where('completion_status', 0)->where('employee_work_type', 140)->get();
            $cemployees = ContractEmployee::where('completion_status', 0)->get();
            $abemployees = TemporyEmployee::where('completion_status', 0)->where('employee_work_type', 142)->get();

        } else {

            $pemployees = PermanentEmployee::where('completion_status', 0)->where('assign_ma_user_id', $empNo)->get();
            $temployees = TemporyEmployee::where('completion_status', 0)->where('assign_ma_user_id', $empNo)->get();
            $cemployees = ContractEmployee::where('completion_status', 0)->where('assign_ma_user_id', $empNo)->get();
            $abemployees = TemporyEmployee::where('completion_status', 0)->where('employee_work_type', 142)->where('assign_ma_user_id', $empNo)->get();
        }

        return view('admin.employee.new.index', compact('pemployees', 'temployees', 'cemployees','abemployees'));
    }

    public function newCheck(Request $request)
    {

        $EmpNum = $request->employee_no;
        session()->get('employee_number');
        session()->forget('employee_number');
        Session::put('employee_number', $EmpNum);

        return redirect()->route('new.employee.add');
    }

    public function newAdd()
    {

        $EmpNum = session()->get('employee_number');

        if (PermanentEmployee::where('id', $EmpNum)->exists()) {

            $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23]);
            $genders = $categories->where('category_type_id', '1');
            $races = $categories->where('category_type_id', '2');
            $religions = $categories->where('category_type_id', '3');
            $civilStatuses = $categories->where('category_type_id', '4');
            $titles = $categories->where('category_type_id', '5');
            $citizenships = $categories->where('category_type_id', '6');
            //$mainBranches = $categories->where('category_type_id', '13');
            $educationLevels = $categories->where('category_type_id', '16');
            $employeeStatusIds = $categories->where('category_type_id', '21');

            $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

            $employeeTypes = $categories->where('category_type_id', '23');
            $cities = City::all();
            $faculties = Faculty::all();
            $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
            $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

            $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
            $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
            } elseif ($mainBranch == 52) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 52)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
            } elseif ($mainBranch == 53) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 53)
                    ->where('active_status', 1)
                    ->get();
                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
            }

            $editData = PermanentEmployee::find($EmpNum);

            return view('admin.employee.new.perement_add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'editData', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'CarderDepartment', 'CarderSubDepartment'));

        } elseif (TemporyEmployee::where('id', $EmpNum)->where('employee_work_type', 140)->exists()) {

            $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
            $genders = $categories->where('category_type_id', '1');
            $races = $categories->where('category_type_id', '2');
            $religions = $categories->where('category_type_id', '3');
            $civilStatuses = $categories->where('category_type_id', '4');
            $titles = $categories->where('category_type_id', '5');
            $citizenships = $categories->where('category_type_id', '6');
            //$mainBranches = $categories->where('category_type_id', '13');
            $educationLevels = $categories->where('category_type_id', '16');
            $employeeStatusIds = $categories->where('category_type_id', '21');

            $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

            $employeeTypes = $categories->where('category_type_id', '23');
            $salaryPaymentTypes = $categories->where('category_type_id', '48');
            $cities = City::all();
            $faculties = Faculty::all();
            $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
            $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

            $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
            $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
            } elseif ($mainBranch == 52) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 52)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
            } elseif ($mainBranch == 53) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 53)
                    ->where('active_status', 1)
                    ->get();
                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
            }

            $editData = TemporyEmployee::find($EmpNum);

            return view('admin.employee.new.tempory_add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'editData', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));

        }elseif (TemporyEmployee::where('id', $EmpNum)->where('employee_work_type', 142)->exists()) {

            $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
            $genders = $categories->where('category_type_id', '1');
            $races = $categories->where('category_type_id', '2');
            $religions = $categories->where('category_type_id', '3');
            $civilStatuses = $categories->where('category_type_id', '4');
            $titles = $categories->where('category_type_id', '5');
            $citizenships = $categories->where('category_type_id', '6');
            //$mainBranches = $categories->where('category_type_id', '13');
            $educationLevels = $categories->where('category_type_id', '16');
            $employeeStatusIds = $categories->where('category_type_id', '21');

            $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

            $employeeTypes = $categories->where('category_type_id', '23');
            $salaryPaymentTypes = $categories->where('category_type_id', '48');
            $cities = City::all();
            $faculties = Faculty::all();
            $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
            $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

            $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
            $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
            } elseif ($mainBranch == 52) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 52)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
            } elseif ($mainBranch == 53) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 53)
                    ->where('active_status', 1)
                    ->get();
                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
            }

            $editData = TemporyEmployee::find($EmpNum);

            return view('admin.employee.new.assignment_basis_add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'editData', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));

        } elseif (ContractEmployee::where('id', $EmpNum)->exists()) {

            $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
            $genders = $categories->where('category_type_id', '1');
            $races = $categories->where('category_type_id', '2');
            $religions = $categories->where('category_type_id', '3');
            $civilStatuses = $categories->where('category_type_id', '4');
            $titles = $categories->where('category_type_id', '5');
            $citizenships = $categories->where('category_type_id', '6');
            //$mainBranches = $categories->where('category_type_id', '13');
            $educationLevels = $categories->where('category_type_id', '16');
            $employeeStatusIds = $categories->where('category_type_id', '21');

            $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

            $employeeTypes = $categories->where('category_type_id', '23');
            $salaryPaymentTypes = $categories->where('category_type_id', '48');
            $cities = City::all();
            $faculties = Faculty::all();
            $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
            $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

            $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
            $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
            } elseif ($mainBranch == 52) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 52)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
            } elseif ($mainBranch == 53) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 53)
                    ->where('active_status', 1)
                    ->get();
                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
            }

            $editData = ContractEmployee::find($EmpNum);

            return view('admin.employee.new.contract_add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'editData', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));
        }
    }

    public function newAddNotification($employee_no, $notification_id)
    {

        $EmpNum = decrypt($employee_no);

        auth()->user()->notifications()->where('id', $notification_id)->update(['read_at' => now()]);

        if (Employee::where('employee_no', $EmpNum)->exists()) {

            $notification = array(
                'message' => 'Employee Already Added',
                'alert-type' => 'warning'
            );

            return redirect()->route('employee.index')->with($notification);
        }

        if (PermanentEmployee::where('id', $EmpNum)->exists()) {

            $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23]);
            $genders = $categories->where('category_type_id', '1');
            $races = $categories->where('category_type_id', '2');
            $religions = $categories->where('category_type_id', '3');
            $civilStatuses = $categories->where('category_type_id', '4');
            $titles = $categories->where('category_type_id', '5');
            $citizenships = $categories->where('category_type_id', '6');
            //$mainBranches = $categories->where('category_type_id', '13');
            $educationLevels = $categories->where('category_type_id', '16');
            $employeeStatusIds = $categories->where('category_type_id', '21');

            $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

            $employeeTypes = $categories->where('category_type_id', '23');
            $cities = City::all();
            $faculties = Faculty::all();
            $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
            $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

            $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
            $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
            } elseif ($mainBranch == 52) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 52)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
            } elseif ($mainBranch == 53) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 53)
                    ->where('active_status', 1)
                    ->get();
                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
            }

            $editData = PermanentEmployee::find($EmpNum);

            return view('admin.employee.new.perement_add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'editData', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'CarderDepartment', 'CarderSubDepartment'));

        } elseif (TemporyEmployee::where('id', $EmpNum)->where('employee_work_type', 140)->exists()) {

            $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
            $genders = $categories->where('category_type_id', '1');
            $races = $categories->where('category_type_id', '2');
            $religions = $categories->where('category_type_id', '3');
            $civilStatuses = $categories->where('category_type_id', '4');
            $titles = $categories->where('category_type_id', '5');
            $citizenships = $categories->where('category_type_id', '6');
            //$mainBranches = $categories->where('category_type_id', '13');
            $educationLevels = $categories->where('category_type_id', '16');
            $employeeStatusIds = $categories->where('category_type_id', '21');

            $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

            $employeeTypes = $categories->where('category_type_id', '23');
            $salaryPaymentTypes = $categories->where('category_type_id', '48');
            $cities = City::all();
            $faculties = Faculty::all();
            $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
            $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

            $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
            $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
            } elseif ($mainBranch == 52) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 52)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
            } elseif ($mainBranch == 53) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 53)
                    ->where('active_status', 1)
                    ->get();
                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
            }

            $editData = TemporyEmployee::find($EmpNum);

            return view('admin.employee.new.tempory_add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'editData', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));

        } elseif (TemporyEmployee::where('id', $EmpNum)->where('employee_work_type', 142)->exists()) {

            $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
            $genders = $categories->where('category_type_id', '1');
            $races = $categories->where('category_type_id', '2');
            $religions = $categories->where('category_type_id', '3');
            $civilStatuses = $categories->where('category_type_id', '4');
            $titles = $categories->where('category_type_id', '5');
            $citizenships = $categories->where('category_type_id', '6');
            //$mainBranches = $categories->where('category_type_id', '13');
            $educationLevels = $categories->where('category_type_id', '16');
            $employeeStatusIds = $categories->where('category_type_id', '21');

            $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

            $employeeTypes = $categories->where('category_type_id', '23');
            $salaryPaymentTypes = $categories->where('category_type_id', '48');
            $cities = City::all();
            $faculties = Faculty::all();
            $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
            $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

            $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
            $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
            } elseif ($mainBranch == 52) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 52)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
            } elseif ($mainBranch == 53) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 53)
                    ->where('active_status', 1)
                    ->get();
                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
            }

            $editData = TemporyEmployee::find($EmpNum);

            return view('admin.employee.new.assignment_basis_add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'editData', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));

        }elseif (ContractEmployee::where('id', $EmpNum)->exists()) {

            $categories = $this->getCategories([1, 2, 3, 4, 5, 6, 13, 16, 21, 22, 23, 48]);
            $genders = $categories->where('category_type_id', '1');
            $races = $categories->where('category_type_id', '2');
            $religions = $categories->where('category_type_id', '3');
            $civilStatuses = $categories->where('category_type_id', '4');
            $titles = $categories->where('category_type_id', '5');
            $citizenships = $categories->where('category_type_id', '6');
            //$mainBranches = $categories->where('category_type_id', '13');
            $educationLevels = $categories->where('category_type_id', '16');
            $employeeStatusIds = $categories->where('category_type_id', '21');

            $employeeStatusTypes = $categories->where('category_type_id', '22')->where('category_code', '=', old('employee_status_id'));

            $employeeTypes = $categories->where('category_type_id', '23');
            $salaryPaymentTypes = $categories->where('category_type_id', '48');
            $cities = City::all();
            $faculties = Faculty::all();
            $departments = Department::where('faculty_code', '=', old('faculty_id'))->get();
            $subDepartments = DepartmentSub::where('department_code', '=', old('sub_department_id'))->get();

            $CarderDepartment = Department::where('faculty_code', '=', old('carder_faculty_id'))->get();
            $CarderSubDepartment = DepartmentSub::where('department_code', '=', old('carder_sub_department_id'))->get();

            $mainBranch = Auth()->user()->main_branch_id;

            if ($mainBranch == 51) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [52, 53]);
            } elseif ($mainBranch == 52) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 52)
                    ->where('active_status', 1)
                    ->get();

                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
            } elseif ($mainBranch == 53) {

                $designations =  DB::table('designations')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('designations.id', 'designations.designation_name', 'designations.salary_code', 'categories.category_name')
                    ->where('designations.deleted_at', '=', NULL)
                    ->where('designation_division', 53)
                    ->where('active_status', 1)
                    ->get();
                $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
            }

            $editData = ContractEmployee::find($EmpNum);

            return view('admin.employee.new.contract_add', compact('genders', 'races', 'religions', 'civilStatuses', 'titles', 'citizenships', 'cities', 'mainBranches', 'faculties', 'designations', 'employeeStatusIds', 'employeeTypes', 'editData', 'educationLevels', 'departments', 'employeeStatusTypes', 'subDepartments', 'salaryPaymentTypes', 'CarderDepartment', 'CarderSubDepartment'));
        }
    }

    public function newStore(NewEmployeeRequest $request)
    {
        if (PermanentEmployee::where('id', $request->employee_no)->exists()) {
            //get next table id
            $maxnumber = DB::table('employees')
                ->select(DB::raw('MAX(id) as value'))
                ->get();

            $maxValue = json_decode($maxnumber, true);

            $nextId = $maxValue[0]["value"] + 1;

            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

            $nicData = json_decode($empDetails->body(), true);

            $employee_data = new Employee();
            $employee_data->id = $nextId;
            $employee_data->employee_no = $request->employee_no;
            $employee_data->file_reference_number = strtoupper($request->file_reference_number);
            $employee_data->main_branch_id = $request->main_branch_id;
            $employee_data->designation_id = $request->designation_id;
            $employee_data->faculty_id = $request->faculty_id;
            $employee_data->department_id = $request->department_id;
            $employee_data->sub_department_id = $request->sub_department_id;

            $employee_data->carder_faculty_id = $request->carder_faculty_id;
            $employee_data->carder_department_id = $request->carder_department_id;
            $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

            $employee_data->initials = strtoupper($request->initials);
            $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
            $employee_data->last_name = ucwords($request->last_name);
            $employee_data->civil_status_id = $request->civil_status_id;
            $employee_data->gender_id = $request->gender_id;
            $employee_data->race_id = $request->race_id;
            $employee_data->religion_id = $request->religion_id;
            $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
            $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
            $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
            $employee_data->permanent_city_id = $request->permanent_city_id;
            $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
            $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
            $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
            $employee_data->postal_city_id = $request->postal_city_id;
            $employee_data->personal_email = $request->personal_email;
            $employee_data->email = $request->email;
            $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
            $employee_data->citizen_registration_no = $request->citizen_registration_no;
            $employee_data->initial_appointment_date = date("Y-m-d", strtotime($request->initial_appointment_date));
            $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
            $employee_data->gratuity_cal_date = date("Y-m-d", strtotime($request->gratuity_cal_date));
            $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
            $employee_data->salary_termination_date_2 = date("Y-m-d", strtotime($request->salary_termination_date_2));
            $employee_data->retirement_date = date("Y-m-d", strtotime($request->retirement_date));
            $employee_data->current_basic_salary = $request->current_basic_salary;
            $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
            $employee_data->added_ma_user_id = Auth()->user()->employee_no;
            $employee_data->added_ma_date = date('Y-m-d');
            //        $employee_data->approved_ar_user_id=$request->approved_ar_user_id;
            //        $employee_data->approved_ar_date=$request->approved_ar_date;
            $employee_data->assign_ma_user_id = Auth()->user()->employee_no;
            $employee_data->assign_ma_date = date('Y-m-d');
            $employee_data->status_id = 1;
            $employee_data->employee_status_id = 110;
            $employee_data->employee_status_type_id = 112;
            $employee_data->employee_work_type = 138;
            if ($request->increment_date == '') {
                $employee_data->increment_date = NULL;
            } else {
                $employee_data->increment_date = date("m-d", strtotime($request->increment_date));
            }
            $employee_data->emp_decision_id = 41;
            $employee_data->mobile_no = $request->mobile_no;
            $employee_data->telephone_no = $request->telephone_no;
            $employee_data->nic = strtoupper($request->nic);

            $employee_data->nic_old = $nicData['oldnic'] ?? null;
            $employee_data->nic_new = $nicData['newnic'] ?? null;
            $employee_data->active_nic = $nicData['activenic'] ?? null;
            $employee_data->dob_gen = $nicData['dob'] ?? null;

            $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
            $employee_data->title_id = $request->titel_id;
            $employee_data->salary_payment_type = 266;
            $employee_data->save();

            $data = PermanentEmployee::find($request->employee_no);
            $data->completion_status = 1;
            $data->updated_at = Carbon::now();
            $data->save();

            $statusRecord = new EmployeeSalaryStatus();
            $statusRecord->employee_no = $employee_data->id;
            $statusRecord->status = 1;
            $statusRecord->created_date = Carbon::now();
            $statusRecord->save();

            Log::notice('EmployeeController -> Created new permanent employee number - ' . $request->employee_no . ' created by ' . auth()->user()->employee_no);
            Log::info('EmployeeController -> new permanent employee creation ended');

            $mailData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('last_name', 'initials', 'category_name', 'employee_no', 'personal_email')
                ->where('employee_no', $request->employee_no)
                ->first();

            $data = [
                'name' => $mailData->category_name . ' ' . $mailData->initials . ' ' . $mailData->last_name,
                'empNo' => $mailData->employee_no
            ];

            $mail = new UJSWelcomeMail($data);

            Mail::to($mailData->personal_email)->send($mail);

            $employee = Employee::find($mailData->employee_no);
            $employee->welcome_mail = 1;
            $employee->save();

            $notification = array(
                'message' => 'New Permanent Employee Inserted Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('new.employee.check.view')->with($notification);

        } elseif (TemporyEmployee::where('id', $request->employee_no)->where('employee_work_type', 140)->exists()) {

            //get next table id
            $maxnumber = DB::table('employees')
                ->select(DB::raw('MAX(id) as value'))
                ->get();

            $maxValue = json_decode($maxnumber, true);

            $nextId = $maxValue[0]["value"] + 1;

            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

            $nicData = json_decode($empDetails->body(), true);

            $employee_data = new Employee();
            $employee_data->id = $nextId;
            $employee_data->employee_no = $request->employee_no;
            $employee_data->file_reference_number = strtoupper($request->file_reference_number);
            $employee_data->main_branch_id = $request->main_branch_id;
            $employee_data->designation_id = $request->designation_id;
            $employee_data->faculty_id = $request->faculty_id;
            $employee_data->department_id = $request->department_id;
            $employee_data->sub_department_id = $request->sub_department_id;

            $employee_data->carder_faculty_id = $request->carder_faculty_id;
            $employee_data->carder_department_id = $request->carder_department_id;
            $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

            $employee_data->initials = strtoupper($request->initials);
            $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
            $employee_data->last_name = ucwords($request->last_name);
            $employee_data->civil_status_id = $request->civil_status_id;
            $employee_data->gender_id = $request->gender_id;
            $employee_data->race_id = $request->race_id;
            $employee_data->religion_id = $request->religion_id;
            $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
            $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
            $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
            $employee_data->permanent_city_id = $request->permanent_city_id;
            $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
            $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
            $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
            $employee_data->postal_city_id = $request->postal_city_id;
            $employee_data->personal_email = $request->personal_email;
            $employee_data->email = $request->email;
            $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
            $employee_data->citizen_registration_no = $request->citizen_registration_no;
            //$employee_data->initial_appointment_date = date("Y-m-d", strtotime($request->initial_appointment_date));
            $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
            //$employee_data->gratuity_cal_date = date("Y-m-d", strtotime($request->gratuity_cal_date));
            $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
            //$employee_data->salary_termination_date_2 = date("Y-m-d", strtotime($request->salary_termination_date_2));
            //$employee_data->retirement_date = date("Y-m-d", strtotime($request->retirement_date));
            $employee_data->current_basic_salary = $request->current_basic_salary;
            $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
            $employee_data->added_ma_user_id = Auth()->user()->employee_no;
            $employee_data->added_ma_date = date('Y-m-d');
            //        $employee_data->approved_ar_user_id=$request->approved_ar_user_id;
            //        $employee_data->approved_ar_date=$request->approved_ar_date;
            $employee_data->assign_ma_user_id = Auth()->user()->employee_no;
            $employee_data->assign_ma_date = date('Y-m-d');
            $employee_data->status_id = 1;
            $employee_data->employee_status_id = 110;
            $employee_data->employee_status_type_id = 112;
            $employee_data->employee_work_type = 140;
            $employee_data->emp_decision_id = 41;
            $employee_data->mobile_no = $request->mobile_no;
            $employee_data->telephone_no = $request->telephone_no;
            $employee_data->nic = strtoupper($request->nic);

            $employee_data->nic_old = $nicData['oldnic'] ?? null;
            $employee_data->nic_new = $nicData['newnic'] ?? null;
            $employee_data->active_nic = $nicData['activenic'] ?? null;
            $employee_data->dob_gen = $nicData['dob'] ?? null;

            $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
            $employee_data->title_id = $request->titel_id;
            $employee_data->salary_payment_type = $request->salary_payment_type;
            $employee_data->save();

            $data = TemporyEmployee::find($request->employee_no);
            $data->completion_status = 1;
            $data->updated_at = Carbon::now();
            $data->save();

            $statusRecord = new EmployeeSalaryStatus();
            $statusRecord->employee_no = $employee_data->id;
            $statusRecord->status = 1;
            $statusRecord->created_date = Carbon::now();
            $statusRecord->save();

            Log::notice('EmployeeController -> Created new tempory employee number - ' . $request->employee_no . ' created by ' . auth()->user()->employee_no);
            Log::info('EmployeeController -> new tempory employee creation ended');

            $mailData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('last_name', 'initials', 'category_name', 'employee_no', 'personal_email')
                ->where('employee_no', $request->employee_no)
                ->first();

            if ($mailData) {
                $email = $mailData->personal_email;

                // Check if the personal_email field is not empty and is a valid email address
                if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $data = [
                        'name' => $mailData->category_name . ' ' . $mailData->initials . ' ' . $mailData->last_name,
                        'empNo' => $mailData->employee_no
                    ];

                    $mail = new UJSWelcomeMail($data);

                    Mail::to($email)->send($mail);

                    $employee = Employee::find($mailData->employee_no);
                    $employee->welcome_mail = 1;
                    $employee->save();

                } else {

                    Log::alert('Invalid or empty personal_email for employee: ' . $request->employee_no);
                }
            } else {

                Log::alert('Employee data not found for employee: ' . $request->employee_no);
            }


            $notification = array(
                'message' => 'New Tempory Employee Inserted Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('new.employee.check.view')->with($notification);

        }elseif (TemporyEmployee::where('id', $request->employee_no)->where('employee_work_type', 142)->exists()) {

            //get next table id
            $maxnumber = DB::table('employees')
                ->select(DB::raw('MAX(id) as value'))
                ->get();

            $maxValue = json_decode($maxnumber, true);

            $nextId = $maxValue[0]["value"] + 1;

            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

            $nicData = json_decode($empDetails->body(), true);

            $employee_data = new Employee();
            $employee_data->id = $nextId;
            $employee_data->employee_no = $request->employee_no;
            $employee_data->file_reference_number = strtoupper($request->file_reference_number);
            $employee_data->main_branch_id = $request->main_branch_id;
            $employee_data->designation_id = $request->designation_id;
            $employee_data->faculty_id = $request->faculty_id;
            $employee_data->department_id = $request->department_id;
            $employee_data->sub_department_id = $request->sub_department_id;

            $employee_data->carder_faculty_id = $request->carder_faculty_id;
            $employee_data->carder_department_id = $request->carder_department_id;
            $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

            $employee_data->initials = strtoupper($request->initials);
            $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
            $employee_data->last_name = ucwords($request->last_name);
            $employee_data->civil_status_id = $request->civil_status_id;
            $employee_data->gender_id = $request->gender_id;
            $employee_data->race_id = $request->race_id;
            $employee_data->religion_id = $request->religion_id;
            $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
            $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
            $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
            $employee_data->permanent_city_id = $request->permanent_city_id;
            $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
            $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
            $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
            $employee_data->postal_city_id = $request->postal_city_id;
            $employee_data->personal_email = $request->personal_email;
            $employee_data->email = $request->email;
            $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
            $employee_data->citizen_registration_no = $request->citizen_registration_no;
            //$employee_data->initial_appointment_date = date("Y-m-d", strtotime($request->initial_appointment_date));
            $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
            //$employee_data->gratuity_cal_date = date("Y-m-d", strtotime($request->gratuity_cal_date));
            $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
            //$employee_data->salary_termination_date_2 = date("Y-m-d", strtotime($request->salary_termination_date_2));
            //$employee_data->retirement_date = date("Y-m-d", strtotime($request->retirement_date));
            $employee_data->current_basic_salary = $request->current_basic_salary;
            $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
            $employee_data->added_ma_user_id = Auth()->user()->employee_no;
            $employee_data->added_ma_date = date('Y-m-d');
            //        $employee_data->approved_ar_user_id=$request->approved_ar_user_id;
            //        $employee_data->approved_ar_date=$request->approved_ar_date;
            $employee_data->assign_ma_user_id = Auth()->user()->employee_no;
            $employee_data->assign_ma_date = date('Y-m-d');
            $employee_data->status_id = 1;
            $employee_data->employee_status_id = 110;
            $employee_data->employee_status_type_id = 112;
            $employee_data->employee_work_type = 142;
            $employee_data->emp_decision_id = 41;
            $employee_data->mobile_no = $request->mobile_no;
            $employee_data->telephone_no = $request->telephone_no;
            $employee_data->nic = strtoupper($request->nic);

            $employee_data->nic_old = $nicData['oldnic'] ?? null;
            $employee_data->nic_new = $nicData['newnic'] ?? null;
            $employee_data->active_nic = $nicData['activenic'] ?? null;
            $employee_data->dob_gen = $nicData['dob'] ?? null;

            $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
            $employee_data->title_id = $request->titel_id;
            $employee_data->salary_payment_type = $request->salary_payment_type;
            $employee_data->save();

            $data = TemporyEmployee::find($request->employee_no);
            $data->completion_status = 1;
            $data->updated_at = Carbon::now();
            $data->save();

            $statusRecord = new EmployeeSalaryStatus();
            $statusRecord->employee_no = $employee_data->id;
            $statusRecord->status = 1;
            $statusRecord->created_date = Carbon::now();
            $statusRecord->save();

            Log::notice('EmployeeController -> Created new assignment basis employee number - ' . $request->employee_no . ' created by ' . auth()->user()->employee_no);
            Log::info('EmployeeController -> new assignment basis employee creation ended');

            $mailData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('last_name', 'initials', 'category_name', 'employee_no', 'personal_email')
                ->where('employee_no', $request->employee_no)
                ->first();

            if ($mailData) {
                $email = $mailData->personal_email;

                // Check if the personal_email field is not empty and is a valid email address
                if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $data = [
                        'name' => $mailData->category_name . ' ' . $mailData->initials . ' ' . $mailData->last_name,
                        'empNo' => $mailData->employee_no
                    ];

                    $mail = new UJSWelcomeMail($data);

                    Mail::to($email)->send($mail);

                    $employee = Employee::find($mailData->employee_no);
                    $employee->welcome_mail = 1;
                    $employee->save();

                } else {

                    Log::alert('Invalid or empty personal_email for employee: ' . $request->employee_no);
                }
            } else {

                Log::alert('Employee data not found for employee: ' . $request->employee_no);
            }


            $notification = array(
                'message' => 'New Assignment Basis Employee Inserted Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('new.employee.check.view')->with($notification);

        } elseif (ContractEmployee::where('id', $request->employee_no)->exists()) {

            //get next table id
            $maxnumber = DB::table('employees')
                ->select(DB::raw('MAX(id) as value'))
                ->get();

            $maxValue = json_decode($maxnumber, true);

            $nextId = $maxValue[0]["value"] + 1;

            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

            $nicData = json_decode($empDetails->body(), true);

            $employee_data = new Employee();
            $employee_data->id = $nextId;
            $employee_data->employee_no = $request->employee_no;
            $employee_data->file_reference_number = strtoupper($request->file_reference_number);
            $employee_data->main_branch_id = $request->main_branch_id;
            $employee_data->designation_id = $request->designation_id;
            $employee_data->faculty_id = $request->faculty_id;
            $employee_data->department_id = $request->department_id;
            $employee_data->sub_department_id = $request->sub_department_id;

            $employee_data->carder_faculty_id = $request->carder_faculty_id;
            $employee_data->carder_department_id = $request->carder_department_id;
            $employee_data->carder_sub_department_id = $request->carder_sub_department_id;

            $employee_data->initials = strtoupper($request->initials);
            $employee_data->name_denoted_by_initials = ucwords($request->name_denoted_by_initials);
            $employee_data->last_name = ucwords($request->last_name);
            $employee_data->civil_status_id = $request->civil_status_id;
            $employee_data->gender_id = $request->gender_id;
            $employee_data->race_id = $request->race_id;
            $employee_data->religion_id = $request->religion_id;
            $employee_data->permanent_add1 = preg_replace("/(\,|\.)/", " ", $request->permanent_add1);
            $employee_data->permanent_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add2));
            $employee_data->permanent_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->permanent_add3));
            $employee_data->permanent_city_id = $request->permanent_city_id;
            $employee_data->postal_add1 = preg_replace("/(\,|\.)/", " ", $request->postal_add1);
            $employee_data->postal_add2 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add2));
            $employee_data->postal_add3 = ucwords(preg_replace("/(\,|\.)/", " ", $request->postal_add3));
            $employee_data->postal_city_id = $request->postal_city_id;
            $employee_data->personal_email = $request->personal_email;
            $employee_data->email = $request->email;
            $employee_data->state_of_citizenship_id = $request->state_of_citizenship_id;
            $employee_data->citizen_registration_no = $request->citizen_registration_no;
            //$employee_data->initial_appointment_date = date("Y-m-d", strtotime($request->initial_appointment_date));
            $employee_data->current_appointment_date = date("Y-m-d", strtotime($request->current_appointment_date));
            //$employee_data->gratuity_cal_date = date("Y-m-d", strtotime($request->gratuity_cal_date));
            $employee_data->salary_termination_date_1 = date("Y-m-d", strtotime($request->salary_termination_date_1));
            $employee_data->sabbatical_start = date("Y-m-d", strtotime($request->sabbatical_start));
            $employee_data->sabbatical_end = date("Y-m-d", strtotime($request->sabbatical_end));
            $employee_data->current_basic_salary = $request->current_basic_salary;
            $employee_data->emp_highest_edu_level = $request->emp_highest_edu_level;
            $employee_data->added_ma_user_id = Auth()->user()->employee_no;
            $employee_data->added_ma_date = date('Y-m-d');
            //        $employee_data->approved_ar_user_id=$request->approved_ar_user_id;
            //        $employee_data->approved_ar_date=$request->approved_ar_date;
            $employee_data->assign_ma_user_id = Auth()->user()->employee_no;
            $employee_data->assign_ma_date = date('Y-m-d');
            $employee_data->status_id = 1;
            $employee_data->employee_status_id = 110;
            $employee_data->employee_status_type_id = 112;
            $employee_data->employee_work_type = 141;
            $employee_data->emp_decision_id = 41;
            $employee_data->mobile_no = $request->mobile_no;
            $employee_data->telephone_no = $request->telephone_no;
            $employee_data->nic = strtoupper($request->nic);

            $employee_data->nic_old = $nicData['oldnic'] ?? null;
            $employee_data->nic_new = $nicData['newnic'] ?? null;
            $employee_data->active_nic = $nicData['activenic'] ?? null;
            $employee_data->dob_gen = $nicData['dob'] ?? null;

            $employee_data->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
            $employee_data->title_id = $request->titel_id;
            $employee_data->salary_payment_type = $request->salary_payment_type;
            $employee_data->save();

            $data = ContractEmployee::find($request->employee_no);
            $data->completion_status = 1;
            $data->updated_at = Carbon::now();
            $data->save();

            $statusRecord = new EmployeeSalaryStatus();
            $statusRecord->employee_no = $employee_data->id;
            $statusRecord->status = 1;
            $statusRecord->created_date = Carbon::now();
            $statusRecord->save();

            Log::notice('EmployeeController -> Created new contract employee number - ' . $request->employee_no . ' created by ' . auth()->user()->employee_no);
            Log::info('EmployeeController -> new contract employee creation ended');

            $mailData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('last_name', 'initials', 'category_name', 'employee_no', 'personal_email')
                ->where('employee_no', $request->employee_no)
                ->first();

            if ($mailData) {
                $email = $mailData->personal_email;

                // Check if the personal_email field is not empty and is a valid email address
                if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $data = [
                        'name' => $mailData->category_name . ' ' . $mailData->initials . ' ' . $mailData->last_name,
                        'empNo' => $mailData->employee_no
                    ];

                    $mail = new UJSWelcomeMail($data);

                    Mail::to($email)->send($mail);

                    $employee = Employee::find($mailData->employee_no);
                    $employee->welcome_mail = 1;
                    $employee->save();

                } else {

                    Log::alert('Invalid or empty personal_email for employee: ' . $request->employee_no);
                }
            } else {

                Log::alert('Employee data not found for employee: ' . $request->employee_no);
            }

            $notification = array(
                'message' => 'New Contract Employee Inserted Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('new.employee.check.view')->with($notification);
        }
    }
}
