<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('application_forms', function (Blueprint $table) {
            $table->id();
            $table->string('reference_number')->unique();
            //$table->string('reference_number')->unique();
            $table->string('title')->nullable();
            $table->string('name_with_initials')->nullable();
            $table->string('name_denoted_by_initials')->nullable();
            $table->integer('telephone_mobile')->nullable();
            $table->integer('telephone_residence')->nullable();
            $table->string('email_address')->nullable();
            $table->string('nic')->nullable();  
            $table->string('nic_copy')->nullable();
            $table->string('permanent_address_line1')->nullable();
            $table->string('permanent_address_line2')->nullable();
            $table->string('permanent_address_line3')->nullable();
            $table->string('permanent_address_city')->nullable();
            $table->string('postal_address_line1')->nullable();
            $table->string('postal_address_line2')->nullable();
            $table->string('postal_address_line3')->nullable();
            $table->string('postal_address_city')->nullable();
            $table->date('date_of_birth')->nullable();
            $table->string('age_application_date')->nullable();
            $table->string('civil_status')->nullable();
            $table->string('citizen_sri_lanka_obtained')->nullable();
            $table->string('citizen_registration_no')->nullable();
            $table->string('academic_qualification')->nullable();
            $table->string('sinhala_exam')->nullable();
            $table->string('tamil_exam')->nullable();
            $table->string('english_exam')->nullable();

            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('application_forms');
    }
};
