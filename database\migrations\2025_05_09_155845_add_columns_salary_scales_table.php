<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('salary_scales', function (Blueprint $table) {
            //
            $table->integer('old_scale_id')->default(0)->after('edited_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('salary_scales', function (Blueprint $table) {
            //
            $table->dropColumn('old_scale_id');
        });
    }
};
