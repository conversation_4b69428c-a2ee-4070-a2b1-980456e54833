<?php

namespace App\Http\Controllers\Setting;

use App\Http\Controllers\Controller;
use App\Models\ContractEmployee;
use App\Models\DepartmentHead;
use App\Models\Employee;
use App\Models\EmployeeChangeHistory;
use App\Models\FacultyDean;
use App\Models\PermanentEmployee;
use App\Models\TemporyEmployee;
use App\Models\User;
use App\Notifications\FileAssignUserChange;
use App\Notifications\MainwBranchChange;
use App\Notifications\NewEmployeeCreation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rule;

class SettingController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function employeeSetting($id)
    {
        $empNo = decrypt($id);
        $employee = Employee::join('designations','designations.id','=','employees.designation_id')
                             ->join('categories as staff_grade', 'staff_grade.id', '=', 'designations.staff_grade')
                             ->select('employees.*','staff_grade.category_name as staff_grade')
                             ->find($empNo);
        return view('admin.setting.employee.index',compact('employee'));
    }

    public function fileAssignChangeView(Request $request,$id)
    {

        auth()->user()->notifications()->where('id', $request->notification_id)->update(['read_at' => now()]);

        $search_emp_no = decrypt($id);

        $mainBranch = Auth()->user()->main_branch_id;

            //admin user data collection
            if ($mainBranch == 51) {


                $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                    ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.nic', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name', 'faculties.faculty_name', 'employees.assign_ma_user_id')
                    ->where('employees.employee_no', $search_emp_no)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->get();

                $row_count = $empfetchDatas->count();

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->whereIn('roles.id', [5, 6])
                    ->distinct()
                    ->get();
            }
            //academic devision data collection
            elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['est-head'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.nic', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name', 'faculties.faculty_name', 'employees.assign_ma_user_id')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        //->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->where('users.main_branch_id', 52)
                    ->whereIn('roles.id', [5, 6])
                    ->distinct()
                    ->get();
            }
            //non academic division data collection
            elseif ($mainBranch == 53) {


                if (Auth()->user()->hasRole(['est-head'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.nic', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name', 'faculties.faculty_name', 'employees.assign_ma_user_id')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        //->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->where('users.main_branch_id', 53)
                    ->whereIn('roles.id', [5, 6])
                    ->distinct()
                    ->get();
            }

            /*********************************************************** */
            if ($row_count > 0) {
                foreach ($empfetchDatas as $empData) {
                    $emp_no = $empData->employee_no;
                    $emp_name = $empData->titleName . " " . $empData->initials . " " . $empData->last_name;
                    $designation_name = $empData->designation_name . "-" . $empData->gradeName;
                    $department_name = $empData->department_name;
                    $nic = $empData->nic;
                    $facultyName = $empData->faculty_name;
                    $assignEmpNo = $empData->assign_ma_user_id;
                }
            } else {
                $emp_no = '';
                $emp_name = '';
                $designation_name = '';
                $department_name = '';
                $nic = '';
                $facultyName = '';
                $assignEmpNo = '';

                $notification = array(
                    'message' => 'Cannot Update own data',
                    'alert-type' => 'error'
                );

                return redirect()->back()->with($notification);
            }



        Log::info('SettingController -> change file assign user data getAll ended');

        return view("admin.setting.employee.file_assign_change", compact('emp_no', 'emp_name', 'designation_name', 'department_name', 'operators', 'nic', 'facultyName', 'assignEmpNo'));
    }

    public function newOperatorStore(Request $request)
    {
        Log::info('SettingController -> new assign new opertor user store started');

        if (!isset($request->emp_no)) {
            $notification = array(
                'message' => 'Please Search Employee Before Submit',
                'alert-type' => 'error'
            );

            return redirect()->route('file.assign.change.view', encrypt($request->emp_no))->with($notification);
        } else {

            if (!isset($request->noperator)) {

                $notification = array(
                    'message' => 'Please Select New Operator',
                    'alert-type' => 'error'
                );

                return redirect()->route('file.assign.change.view', encrypt($request->emp_no))->with($notification);
            } else {

                $users = User::where('employee_no', $request->noperator)->orWhere('id', 1)->get();

                $employee = Employee::find($request->emp_no);
                $employee->assign_ma_user_id = $request->noperator;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type =  292;
                $data->emp_no =  $request->emp_no;
                $data->pervious_record =  $request->coperator;
                $data->new_record =  $request->noperator;
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->created_at = Carbon::now();
                $data->save();

                //DepartmentHead::where('emp_no', $request->emp_no)->count();

                if (DepartmentHead::where('emp_no', $request->emp_no)->count() > 0) {

                    $data = DepartmentHead::where('emp_no', $request->emp_no)->first();
                    $data->added_user_id = $request->noperator;
                    $data->save();
                }

                if (FacultyDean::where('emp_no', $request->emp_no)->count() > 0) {

                    $data = FacultyDean::where('emp_no', $request->emp_no)->first();
                    $data->added_user_id = $request->noperator;
                    $data->save();
                }

                $emailData = [
                    'employee_no' => $request->emp_no
                ];

                Notification::send($users, new FileAssignUserChange($emailData));

                $notification = array(
                    'message' => 'Operator change successfully',
                    'alert-type' => 'success'
                );

                return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
            }
        }
    }

    public function mainBranchChangeView(Request $request,$id)
    {

        if (isset($request->employee_no)) {

            Log::info('SettingController -> change main branch getAll started');

            $mainBranch = Auth()->user()->main_branch_id;
            $search_emp_no = decrypt($id);

            //admin user data collection
            if ($mainBranch == 51) {


                $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                    ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.nic', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name', 'faculties.faculty_name', 'employees.main_branch_id')
                    ->where('employees.employee_no', $search_emp_no)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->get();

                $row_count = $empfetchDatas->count();

                $categories = $this->getCategories([13]);
                $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', array(52, 53));
            }

            //non academic division data collection
            elseif ($mainBranch == 53) {


                if (Auth()->user()->hasRole(['est-head'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.staff_grade', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.nic', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name', 'faculties.faculty_name', 'employees.main_branch_id')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        //->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();

                $categories = $this->getCategories([13]);
                $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', array(52, 53));
            }

            /*********************************************************** */
            if ($row_count > 0) {
                foreach ($empfetchDatas as $empData) {
                    $emp_no = $empData->employee_no;
                    $emp_name = $empData->titleName . " " . $empData->initials . " " . $empData->last_name;
                    $designation_name = $empData->designation_name . "-" . $empData->gradeName;
                    $department_name = $empData->department_name;
                    $nic = $empData->nic;
                    $facultyName = $empData->faculty_name;
                    $empMainBranch = $empData->main_branch_id;
                }
            } else {
                $emp_no = '';
                $emp_name = '';
                $designation_name = '';
                $department_name = '';
                $nic = '';
                $facultyName = '';
                $empMainBranch = '';

                $notification = array(
                    'message' => 'Cannot Update own data',
                    'alert-type' => 'error'
                );

                return redirect()->back()->with($notification);
            }


        }


        Log::info('SettingController -> change main branch getAll ended');

        return view("admin.setting.employee.main_branch_change", compact('emp_no', 'emp_name', 'designation_name', 'department_name', 'nic', 'facultyName', 'empMainBranch', 'mainBranches'));
    }

    public function newBranchStore(Request $request)
    {
        Log::info('SettingController -> new assign new opertor user store started');

        if (!isset($request->emp_no)) {
            $notification = array(
                'message' => 'Please Search Employee Before Submit',
                'alert-type' => 'error'
            );

            return redirect()->route('main.branch.change.view', encrypt($request->emp_no))->with($notification);
        } else {

            if (!isset($request->nbranch)) {

                $notification = array(
                    'message' => 'Please Select New Main Branch',
                    'alert-type' => 'error'
                );

                return redirect()->route('main.branch.change.view', encrypt($request->emp_no))->with($notification);

            } else {

                $users = User::where('main_branch_id', $request->nbranch)
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->whereIn('roles.id', [4])
                    ->where('users.status_id', 1)
                    ->orWhere('users.id', 1)
                    ->distinct()
                    ->get();

                $employee = Employee::find($request->emp_no);
                $employee->main_branch_id = $request->nbranch;
                $employee->assign_ma_user_id = 0;
                $employee->save();

                $data = new EmployeeChangeHistory();
                $data->type =  291;
                $data->emp_no =  $request->emp_no;
                $data->pervious_record =  $request->cbranch;
                $data->new_record =  $request->nbranch;
                $data->date = date("Y-m-d");
                $data->updated_user_id = auth()->user()->employee_no;
                $data->created_at = Carbon::now();
                $data->save();

                $emailData = [
                    'employee_no' => $request->emp_no
                ];

                Notification::send($users, new MainwBranchChange($emailData));

                $notification = array(
                    'message' => 'Main Branch change successfully',
                    'alert-type' => 'success'
                );

                return redirect()->route('employee.updation', encrypt($request->emp_no))->with($notification);
            }
        }
    }

    public function newEmployeeCreate()
    {

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                //->where('users.main_branch_id',53)
                ->whereIn('roles.id', [5, 6])
                ->distinct()
                ->get();
        } else if ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->where('users.main_branch_id', 52)
                    ->whereIn('roles.id', [5, 6])
                    ->distinct()
                    ->get();
            }
        } else if ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->where('users.main_branch_id', 53)
                    ->whereIn('roles.id', [5, 6])
                    ->distinct()
                    ->get();
            }
        }

        return view('admin.setting.employee.employee_add', compact('operators'));
    }

    public function newEmployeeStore(Request $request)
    {


        if ($request->employee_type == 138) {

            $validatedData = $request->validate([
                'employee_type' => 'required',
                'nic' => ['required', 'regex:/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/m'],
                'operator_user' => 'required'
            ], [
                'employee_type.required' => 'Select Relavent Employee Type',
                'nic.required' => 'Please Enter Valid NIC Number',
                'nic.regex' => 'Please Enter Valid NIC Number',
                'nic.unique' => 'Already Create Employee Number',
                'operator_user.required' => 'Select Relevent Operator User'
            ]);

            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

            $nicData = json_decode($empDetails->body(), true);

            $empDataCount = Employee::where(function ($query) use ($nicData) {
                $query->where('nic_old', $nicData['oldnic'])
                    ->orWhere('nic_new', $nicData['newnic']);
            })
                ->where('employee_status_id', 110)
                ->count();

            if ($empDataCount > 0) {

                $empSimilarData = Employee::where(function ($query) use ($nicData) {
                    $query->where('nic_old', $nicData['oldnic'])
                        ->orWhere('nic_new', $nicData['newnic']);
                })
                    ->where('employee_status_id', 110)
                    ->get();

                return view('admin.setting.employee.similar_nic', compact('empSimilarData'));
            }

            $data = new PermanentEmployee();
            $data->nic = strtoupper($request->nic);
            $data->main_branch = auth()->user()->main_branch_id;
            $data->assign_ma_user_id = $request->operator_user;
            $data->assign_ma_date = date("Y-m-d");
            $data->employee_status_id = 110;
            $data->employee_status_type_id = 112;
            $data->employee_work_type = $request->employee_type;
            $data->added_user_id = auth()->user()->employee_no;
            $data->added_date = date("Y-m-d");
            $data->save();

            $users = User::where('employee_no', $request->operator_user)
                ->orWhere('id', 1)
                ->get();

            //set employee number session
            session()->get('employee_no');
            session()->forget('employee_no');
            Session::put('employee_no', $data->id);

            //set nic session
            session()->get('nic');
            session()->forget('nic');
            Session::put('nic', $data->nic);

            //set employee_type session
            session()->get('employee_type');
            session()->forget('employee_type');
            Session::put('employee_type', $data->employee_work_type);

            $emailData = [
                'employee_work_type' => 'Permanent',
                'employee_no' => $data->id,
                'nic' => $data->nic
            ];

            Notification::send($users, new NewEmployeeCreation($emailData));

            $notification = array(
                'message' => 'New Permanent Employee Added',
                'alert-type' => 'success'
            );
            return redirect()->route('new.employee.final')->with($notification);
        } elseif ($request->employee_type == 141) {

            $validatedData = $request->validate([
                'employee_type' => 'required',
                'nic' => ['required', 'regex:/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/m'],
                'operator_user' => 'required'
            ], [
                'employee_type.required' => 'Select Relavent Employee Type',
                'nic.required' => 'Please Enter Valid NIC Number',
                'nic.regex' => 'Please Enter Valid NIC Number',
                'nic.unique' => 'Already Create Employee Number',
                'operator_user.required' => 'Select Relevent Operator User'
            ]);

            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

            $nicData = json_decode($empDetails->body(), true);

            $empDataCount = Employee::where(function ($query) use ($nicData) {
                $query->where('nic_old', $nicData['oldnic'])
                    ->orWhere('nic_new', $nicData['newnic']);
            })
                ->where('employee_status_id', 110)
                ->count();

            if ($empDataCount > 0) {

                $empSimilarData = Employee::where(function ($query) use ($nicData) {
                    $query->where('nic_old', $nicData['oldnic'])
                        ->orWhere('nic_new', $nicData['newnic']);
                })
                    ->where('employee_status_id', 110)
                    ->get();

                return view('admin.setting.employee.similar_nic', compact('empSimilarData'));
            }

            $data = new ContractEmployee();
            $data->nic = strtoupper($request->nic);
            $data->main_branch = auth()->user()->main_branch_id;
            $data->assign_ma_user_id = $request->operator_user;
            $data->assign_ma_date = date("Y-m-d");
            $data->employee_status_id = 110;
            $data->employee_status_type_id = 112;
            $data->employee_work_type = $request->employee_type;
            $data->added_user_id = auth()->user()->employee_no;
            $data->added_date = date("Y-m-d");
            $data->save();

            $users = User::where('employee_no', $request->operator_user)
                ->orWhere('id', 1)
                ->get();

            //set employee number session
            session()->get('employee_no');
            session()->forget('employee_no');
            Session::put('employee_no', $data->id);

            //set nic session
            session()->get('nic');
            session()->forget('nic');
            Session::put('nic', $data->nic);

            //set employee_type session
            session()->get('employee_type');
            session()->forget('employee_type');
            Session::put('employee_type', $data->employee_work_type);


            $emailData = [
                'employee_work_type' => 'Contract',
                'employee_no' => $data->id,
                'nic' => $data->nic
            ];

            Notification::send($users, new NewEmployeeCreation($emailData));


            $notification = array(
                'message' => 'New Contract Employee Added',
                'alert-type' => 'success'
            );
            return redirect()->route('new.employee.final')->with($notification);
        } elseif ($request->employee_type == 140) {

            $validatedData = $request->validate([
                'employee_type' => 'required',
                'nic' => ['required', 'regex:/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/m'],
                'operator_user' => 'required'
            ], [
                'employee_type.required' => 'Select Relavent Employee Type',
                'nic.required' => 'Please Enter Valid NIC Number',
                'nic.regex' => 'Please Enter Valid NIC Number',
                'nic.unique' => 'Already Create Employee Number',
                'operator_user.required' => 'Select Relevent Operator User'
            ]);
            //nic validation change

            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

            $nicData = json_decode($empDetails->body(), true);

            $empDataCount = Employee::where(function ($query) use ($nicData) {
                $query->where('nic_old', $nicData['oldnic'])
                    ->orWhere('nic_new', $nicData['newnic']);
            })
                ->where('employee_status_id', 110)
                ->count();

            if ($empDataCount > 0) {

                $empSimilarData = Employee::where(function ($query) use ($nicData) {
                    $query->where('nic_old', $nicData['oldnic'])
                        ->orWhere('nic_new', $nicData['newnic']);
                })
                    ->where('employee_status_id', 110)
                    ->get();

                return view('admin.setting.employee.similar_nic', compact('empSimilarData'));
            }

            $data = new TemporyEmployee();
            $data->nic = strtoupper($request->nic);
            $data->main_branch = auth()->user()->main_branch_id;
            $data->assign_ma_user_id = $request->operator_user;
            $data->assign_ma_date = date("Y-m-d");
            $data->employee_status_id = 110;
            $data->employee_status_type_id = 112;
            $data->employee_work_type = $request->employee_type;
            $data->added_user_id = auth()->user()->employee_no;
            $data->added_date = date("Y-m-d");
            $data->save();

            $users = User::where('employee_no', $request->operator_user)
                ->orWhere('id', 1)
                ->get();

            //set employee number session
            session()->get('employee_no');
            session()->forget('employee_no');
            Session::put('employee_no', $data->id);

            //set nic session
            session()->get('nic');
            session()->forget('nic');
            Session::put('nic', $data->nic);

            //set employee_type session
            session()->get('employee_type');
            session()->forget('employee_type');
            Session::put('employee_type', $data->employee_work_type);

            $emailData = [
                'employee_work_type' => 'Tempory',
                'employee_no' => $data->id,
                'nic' => $data->nic
            ];

            Notification::send($users, new NewEmployeeCreation($emailData));


            $notification = array(
                'message' => 'New Tempory Employee Added',
                'alert-type' => 'success'
            );
            return redirect()->route('new.employee.final')->with($notification);

        }elseif ($request->employee_type == 142) {

            $validatedData = $request->validate([
                'employee_type' => 'required',
                'nic' => ['required', 'regex:/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/m'],
                'operator_user' => 'required'
            ], [
                'employee_type.required' => 'Select Relavent Employee Type',
                'nic.required' => 'Please Enter Valid NIC Number',
                'nic.regex' => 'Please Enter Valid NIC Number',
                'nic.unique' => 'Already Create Employee Number',
                'operator_user.required' => 'Select Relevent Operator User'
            ]);
            //nic validation change

            //get nic information
            $empDetails = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($request->nic)]);

            $nicData = json_decode($empDetails->body(), true);

            $empDataCount = Employee::where(function ($query) use ($nicData) {
                $query->where('nic_old', $nicData['oldnic'])
                    ->orWhere('nic_new', $nicData['newnic']);
            })
                ->where('employee_status_id', 110)
                ->count();

            if ($empDataCount > 0) {

                $empSimilarData = Employee::where(function ($query) use ($nicData) {
                    $query->where('nic_old', $nicData['oldnic'])
                        ->orWhere('nic_new', $nicData['newnic']);
                })
                    ->where('employee_status_id', 110)
                    ->get();

                return view('admin.setting.employee.similar_nic', compact('empSimilarData'));
            }

            $data = new TemporyEmployee();
            $data->nic = strtoupper($request->nic);
            $data->main_branch = auth()->user()->main_branch_id;
            $data->assign_ma_user_id = $request->operator_user;
            $data->assign_ma_date = date("Y-m-d");
            $data->employee_status_id = 110;
            $data->employee_status_type_id = 112;
            $data->employee_work_type = $request->employee_type;
            $data->added_user_id = auth()->user()->employee_no;
            $data->added_date = date("Y-m-d");
            $data->save();

            $users = User::where('employee_no', $request->operator_user)
                ->orWhere('id', 1)
                ->get();

            //set employee number session
            session()->get('employee_no');
            session()->forget('employee_no');
            Session::put('employee_no', $data->id);

            //set nic session
            session()->get('nic');
            session()->forget('nic');
            Session::put('nic', $data->nic);

            //set employee_type session
            session()->get('employee_type');
            session()->forget('employee_type');
            Session::put('employee_type', $data->employee_work_type);

            $emailData = [
                'employee_work_type' => 'Assignment Basis',
                'employee_no' => $data->id,
                'nic' => $data->nic
            ];

            Notification::send($users, new NewEmployeeCreation($emailData));


            $notification = array(
                'message' => 'New Assignment Basis Employee Added',
                'alert-type' => 'success'
            );
            return redirect()->route('new.employee.final')->with($notification);
        }
    } // End Metod

    public function newEmployeeFinal()
    {
        $empNo = session()->get('employee_no');
        $nic = session()->get('nic');
        $employeeType = session()->get('employee_type');
        return view('admin.setting.employee.employee_add_complete', compact('empNo', 'nic', 'employeeType'));
    }

    public function newEmployeeList(Request $request)
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $employeeType = $request->employee_type;

        if (isset($employeeType)) {

            if ($mainBranch == 51) {

                if ($employeeType == 138) {

                    $employees =  PermanentEmployee::join('employees', 'permanent_employees.assign_ma_user_id', '=', 'employees.employee_no')
                        ->select('permanent_employees.*', 'employees.initials', 'employees.last_name')
                        ->where('permanent_employees.employee_work_type', 138)
                        ->orderBy('permanent_employees.id', 'DESC')
                        ->get();
                } else if ($employeeType == 140) {

                    $employees =  TemporyEmployee::join('employees', 'tempory_employees.assign_ma_user_id', '=', 'employees.employee_no')
                        ->select('tempory_employees.*', 'employees.initials', 'employees.last_name')
                        ->where('tempory_employees.employee_work_type', 140)
                        ->orderBy('tempory_employees.id', 'DESC')
                        ->get();
                } elseif ($employeeType == 141) {

                    $employees =  ContractEmployee::join('employees', 'contract_employees.assign_ma_user_id', '=', 'employees.employee_no')
                        ->select('contract_employees.*', 'employees.initials', 'employees.last_name')
                        ->where('contract_employees.employee_work_type', 141)
                        ->orderBy('contract_employees.id', 'DESC')
                        ->get();
                } else if ($employeeType == 142) {

                    $employees =  TemporyEmployee::join('employees', 'tempory_employees.assign_ma_user_id', '=', 'employees.employee_no')
                        ->select('tempory_employees.*', 'employees.initials', 'employees.last_name')
                        ->where('tempory_employees.employee_work_type', 142)
                        ->orderBy('tempory_employees.id', 'DESC')
                        ->get();
                }

            } elseif ($mainBranch == 52) {

                if ($employeeType == 138) {

                    $employees =  PermanentEmployee::join('employees', 'permanent_employees.assign_ma_user_id', '=', 'employees.employee_no')
                        ->select('permanent_employees.*', 'employees.initials', 'employees.last_name')
                        ->where('permanent_employees.employee_work_type', 138)
                        ->where('permanent_employees.main_branch', 52)
                        ->orderBy('permanent_employees.id', 'DESC')
                        ->get();
                } else if ($employeeType == 140) {

                    $employees =  TemporyEmployee::join('employees', 'tempory_employees.assign_ma_user_id', '=', 'employees.employee_no')
                        ->select('tempory_employees.*', 'employees.initials', 'employees.last_name')
                        ->where('tempory_employees.employee_work_type', 140)
                        ->where('tempory_employees.main_branch', 52)
                        ->orderBy('tempory_employees.id', 'DESC')
                        ->get();
                } elseif ($employeeType == 141) {

                    $employees =  ContractEmployee::join('employees', 'contract_employees.assign_ma_user_id', '=', 'employees.employee_no')
                        ->select('contract_employees.*', 'employees.initials', 'employees.last_name')
                        ->where('contract_employees.employee_work_type', 141)
                        ->where('contract_employees.main_branch', 52)
                        ->orderBy('contract_employees.id', 'DESC')
                        ->get();
                } else if ($employeeType == 142) {

                    $employees =  TemporyEmployee::join('employees', 'tempory_employees.assign_ma_user_id', '=', 'employees.employee_no')
                        ->select('tempory_employees.*', 'employees.initials', 'employees.last_name')
                        ->where('tempory_employees.employee_work_type', 142)
                        ->where('tempory_employees.main_branch', 52)
                        ->orderBy('tempory_employees.id', 'DESC')
                        ->get();
                }

            } elseif ($mainBranch == 53) {

                if ($employeeType == 138) {

                    $employees =  PermanentEmployee::join('employees', 'permanent_employees.assign_ma_user_id', '=', 'employees.employee_no')
                        ->select('permanent_employees.*', 'employees.initials', 'employees.last_name')
                        ->where('permanent_employees.employee_work_type', 138)
                        ->where('permanent_employees.main_branch', 53)
                        ->orderBy('permanent_employees.id', 'DESC')
                        ->get();
                } else if ($employeeType == 140) {

                    $employees =  TemporyEmployee::join('employees', 'tempory_employees.assign_ma_user_id', '=', 'employees.employee_no')
                        ->select('tempory_employees.*', 'employees.initials', 'employees.last_name')
                        ->where('tempory_employees.employee_work_type', 140)
                        ->where('tempory_employees.main_branch', 53)
                        ->orderBy('tempory_employees.id', 'DESC')
                        ->get();
                } elseif ($employeeType == 141) {

                    $employees =  ContractEmployee::join('employees', 'contract_employees.assign_ma_user_id', '=', 'employees.employee_no')
                        ->select('contract_employees.*', 'employees.initials', 'employees.last_name')
                        ->where('contract_employees.employee_work_type', 141)
                        ->where('contract_employees.main_branch', 53)
                        ->orderBy('contract_employees.id', 'DESC')
                        ->get();
                } else if ($employeeType == 142) {

                    $employees =  TemporyEmployee::join('employees', 'tempory_employees.assign_ma_user_id', '=', 'employees.employee_no')
                        ->select('tempory_employees.*', 'employees.initials', 'employees.last_name')
                        ->where('tempory_employees.employee_work_type', 142)
                        ->where('tempory_employees.main_branch', 53)
                        ->orderBy('tempory_employees.id', 'DESC')
                        ->get();
                }
            }

            $currentEmployeeType = $request->employee_type;
        } else {
            $employees = array();
            $currentEmployeeType = "";
        }



        return view('admin.setting.employee.employee_list', compact('employees', 'currentEmployeeType'));
    }

    public function newEmployeeSearch(Request $request)
    {

        $mainBranch = Auth()->user()->main_branch_id;
        $employeeType = $request->employee_type;

        if ($mainBranch == 51) {

            if ($employeeType == 138) {

                $employees =  PermanentEmployee::join('employees', 'permanent_employees.assign_ma_user_id', '=', 'employees.employee_no')
                    ->select('permanent_employees.*', 'employees.initials', 'employees.last_name')
                    ->where('permanent_employees.employee_work_type', 138)
                    ->orderBy('permanent_employees.id', 'DESC')
                    ->get();
            } else if ($employeeType == 140) {

                $employees =  TemporyEmployee::join('employees', 'tempory_employees.assign_ma_user_id', '=', 'employees.employee_no')
                    ->select('tempory_employees.*', 'employees.initials', 'employees.last_name')
                    ->where('tempory_employees.employee_work_type', 140)
                    ->orderBy('tempory_employees.id', 'DESC')
                    ->get();
            } elseif ($employeeType == 141) {

                $employees =  ContractEmployee::join('employees', 'contract_employees.assign_ma_user_id', '=', 'employees.employee_no')
                    ->select('contract_employees.*', 'employees.initials', 'employees.last_name')
                    ->where('contract_employees.employee_work_type', 141)
                    ->orderBy('contract_employees.id', 'DESC')
                    ->get();
            } else if ($employeeType == 142) {

                $employees =  TemporyEmployee::join('employees', 'tempory_employees.assign_ma_user_id', '=', 'employees.employee_no')
                    ->select('tempory_employees.*', 'employees.initials', 'employees.last_name')
                    ->where('tempory_employees.employee_work_type', 142)
                    ->orderBy('tempory_employees.id', 'DESC')
                    ->get();
            }
        } elseif ($mainBranch == 52) {

            if ($employeeType == 138) {


                $employees = PermanentEmployee::join('employees', 'permanent_employees.assign_ma_user_id', '=', 'employees.employee_no')
                    ->select('permanent_employees.*', 'employees.initials', 'employees.last_name')
                    ->where('permanent_employees.employee_work_type', 138)
                    ->where('permanent_employees.main_branch', 52)
                    ->orderBy('permanent_employees.id', 'DESC')
                    ->get();
            } else if ($employeeType == 140) {

                $employees =  TemporyEmployee::join('employees', 'tempory_employees.assign_ma_user_id', '=', 'employees.employee_no')
                    ->select('tempory_employees.*', 'employees.initials', 'employees.last_name')
                    ->where('tempory_employees.employee_work_type', 140)
                    ->where('tempory_employees.main_branch', 52)
                    ->orderBy('tempory_employees.id', 'DESC')
                    ->get();
            } elseif ($employeeType == 141) {

                $employees =  ContractEmployee::join('employees', 'contract_employees.assign_ma_user_id', '=', 'employees.employee_no')
                    ->select('contract_employees.*', 'employees.initials', 'employees.last_name')
                    ->where('contract_employees.employee_work_type', 141)
                    ->where('contract_employees.main_branch', 52)
                    ->orderBy('contract_employees.id', 'DESC')
                    ->get();
            } else if ($employeeType == 142) {

                $employees =  TemporyEmployee::join('employees', 'tempory_employees.assign_ma_user_id', '=', 'employees.employee_no')
                    ->select('tempory_employees.*', 'employees.initials', 'employees.last_name')
                    ->where('tempory_employees.employee_work_type', 142)
                    ->where('tempory_employees.main_branch', 52)
                    ->orderBy('tempory_employees.id', 'DESC')
                    ->get();
            }

        } elseif ($mainBranch == 53) {

            if ($employeeType == 138) {

                $employees =  PermanentEmployee::join('employees', 'permanent_employees.assign_ma_user_id', '=', 'employees.employee_no')
                    ->select('permanent_employees.*', 'employees.initials', 'employees.last_name')
                    ->where('permanent_employees.employee_work_type', 138)
                    ->where('permanent_employees.main_branch', 53)
                    ->orderBy('permanent_employees.id', 'DESC')
                    ->get();
            } else if ($employeeType == 140) {

                $employees =  TemporyEmployee::join('employees', 'tempory_employees.assign_ma_user_id', '=', 'employees.employee_no')
                    ->select('tempory_employees.*', 'employees.initials', 'employees.last_name')
                    ->where('tempory_employees.employee_work_type', 140)
                    ->where('tempory_employees.main_branch', 53)
                    ->orderBy('tempory_employees.id', 'DESC')
                    ->get();
            } elseif ($employeeType == 141) {

                $employees =  ContractEmployee::join('employees', 'contract_employees.assign_ma_user_id', '=', 'employees.employee_no')
                    ->select('contract_employees.*', 'employees.initials', 'employees.last_name')
                    ->where('contract_employees.employee_work_type', 141)
                    ->where('contract_employees.main_branch', 53)
                    ->orderBy('contract_employees.id', 'DESC')
                    ->get();
            } else if ($employeeType == 142) {

                $employees =  TemporyEmployee::join('employees', 'tempory_employees.assign_ma_user_id', '=', 'employees.employee_no')
                    ->select('tempory_employees.*', 'employees.initials', 'employees.last_name')
                    ->where('tempory_employees.employee_work_type', 142)
                    ->where('tempory_employees.main_branch', 53)
                    ->orderBy('tempory_employees.id', 'DESC')
                    ->get();
            }
        }


        $currentEmployeeType = $request->employee_type;

        return view('admin.setting.employee.employee_list', compact('employees', 'currentEmployeeType'));
    }

    public function newEmployeeEdit($id)
    {
        $EmpNum = decrypt($id);
        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                ->whereIn('roles.id', [5, 6])
                ->distinct()
                ->get();
        } else if ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->where('users.main_branch_id', 52)
                    ->whereIn('roles.id', [5, 6])
                    ->distinct()
                    ->get();
            }
        } else if ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                    ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                    ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->where('users.main_branch_id', 53)
                    ->whereIn('roles.id', [5, 6])
                    ->distinct()
                    ->get();
            }
        }

        if (PermanentEmployee::where('id', $EmpNum)->exists()) {

            $editData = PermanentEmployee::find($EmpNum);
        } elseif (TemporyEmployee::where('id', $EmpNum)->exists()) {

            $editData = TemporyEmployee::find($EmpNum);
        } elseif (ContractEmployee::where('id', $EmpNum)->exists()) {

            $editData = ContractEmployee::find($EmpNum);
        }



        //Log::notice('SettingController -> edit new employee data id - ' . $editData->id . ' edited by ' . auth()->user()->employee_no);
        //Log::info('BondController -> old bond edit ended');

        return view('admin.setting.employee.employee_edit', compact('editData', 'operators'));
    }

    public function newEmployeeUpdate(Request $request, $id)
    {

        if ($request->employee_type == 138) {

            $validatedData = $request->validate([
                //'employee_type' => 'required',
                'nic' => ['required', 'regex:/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/m', Rule::unique('permanent_employees')->ignore($id)],
                'operator_user' => 'required'
            ], [
                //'employee_type.required' => 'Select Relavent Employee Type',
                'nic.required' => 'Please Enter Valid NIC Number',
                'nic.regex' => 'Please Enter Valid NIC Number',
                'nic.unique' => 'Already Create Employee Number',
                'operator_user.required' => 'Select Relevent Operator User'
            ]);

            $data = PermanentEmployee::find($id);
            $data->nic = strtoupper($request->nic);
            $data->assign_ma_user_id = $request->operator_user;
            $data->updated_user_id = auth()->user()->employee_no;
            $data->updated_date = date("Y-m-d");
            $data->save();


            $notification = array(
                'message' => 'New Permanent Employee updated',
                'alert-type' => 'info'
            );
            return redirect()->route('new.employee.list', ['employee_type' => $request->employee_type])->with($notification);
        } elseif ($request->employee_type == 141) {

            $validatedData = $request->validate([
                //'employee_type' => 'required',
                'nic' => ['required', 'regex:/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/m', Rule::unique('contract_employees')->ignore($id)],
                'operator_user' => 'required'
            ], [
                //'employee_type.required' => 'Select Relavent Employee Type',
                'nic.required' => 'Please Enter Valid NIC Number',
                'nic.regex' => 'Please Enter Valid NIC Number',
                'nic.unique' => 'Already Create Employee Number',
                'operator_user.required' => 'Select Relevent Operator User'
            ]);

            $data = ContractEmployee::find($id);
            $data->nic = strtoupper($request->nic);
            $data->assign_ma_user_id = $request->operator_user;
            $data->updated_user_id = auth()->user()->employee_no;
            $data->updated_date = date("Y-m-d");
            $data->save();


            $notification = array(
                'message' => 'New Contract Employee updated',
                'alert-type' => 'info'
            );
            return redirect()->route('new.employee.list', ['employee_type' => $request->employee_type])->with($notification);
        } elseif ($request->employee_type == 140) {

            $validatedData = $request->validate([
                //'employee_type' => 'required',
                'nic' => ['required', 'regex:/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/m', Rule::unique('tempory_employees')->ignore($id)],
                'operator_user' => 'required'
            ], [
                //'employee_type.required' => 'Select Relavent Employee Type',
                'nic.required' => 'Please Enter Valid NIC Number',
                'nic.regex' => 'Please Enter Valid NIC Number',
                'nic.unique' => 'Already Create Employee Number',
                'operator_user.required' => 'Select Relevent Operator User'
            ]);

            $data = TemporyEmployee::find($id);
            $data->nic = strtoupper($request->nic);
            $data->assign_ma_user_id = $request->operator_user;
            $data->updated_user_id = auth()->user()->employee_no;
            $data->updated_date = date("Y-m-d");
            $data->save();


            $notification = array(
                'message' => 'New Tempory Employee updated',
                'alert-type' => 'info'
            );
            return redirect()->route('new.employee.list', ['employee_type' => $request->employee_type])->with($notification);

        }elseif ($request->employee_type == 142) {

            $validatedData = $request->validate([
                //'employee_type' => 'required',
                'nic' => ['required', 'regex:/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/m', Rule::unique('tempory_employees')->ignore($id)],
                'operator_user' => 'required'
            ], [
                //'employee_type.required' => 'Select Relavent Employee Type',
                'nic.required' => 'Please Enter Valid NIC Number',
                'nic.regex' => 'Please Enter Valid NIC Number',
                'nic.unique' => 'Already Create Employee Number',
                'operator_user.required' => 'Select Relevent Operator User'
            ]);

            $data = TemporyEmployee::find($id);
            $data->nic = strtoupper($request->nic);
            $data->assign_ma_user_id = $request->operator_user;
            $data->updated_user_id = auth()->user()->employee_no;
            $data->updated_date = date("Y-m-d");
            $data->save();


            $notification = array(
                'message' => 'New Assignment Basis Employee updated',
                'alert-type' => 'info'
            );
            return redirect()->route('new.employee.list', ['employee_type' => $request->employee_type])->with($notification);
        }
    }

    public function newEmployeeDelete($id)
    {

        //Log::info('BondController -> bond data delete started');
        $EmpNum = decrypt($id);
        if (PermanentEmployee::where('id', $EmpNum)->exists()) {

            $data = PermanentEmployee::where('id', $EmpNum);
            $data->delete();
        } elseif (TemporyEmployee::where('id', $EmpNum)->exists()) {

            //$editData = TemporyEmployee::find($EmpNum);
            $data = TemporyEmployee::where('id', $EmpNum);
            $data->delete();
        } elseif (ContractEmployee::where('id', $EmpNum)->exists()) {

            //$editData = ContractEmployee::find($EmpNum);
            $data = ContractEmployee::where('id', $EmpNum);
            $data->delete();
        }


        //Log::emergency('BondController -> delete bond employee number - ' . $bond->emp_no . ' deleted by ' . auth()->user()->employee_no);
        //Log::info('BondController -> bond data delete ended');


        $notification = array(
            'message' => 'New Employee Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('new.employee.list')->with($notification);
    }


}
