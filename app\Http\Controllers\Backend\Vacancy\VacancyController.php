<?php

namespace App\Http\Controllers\Backend\Vacancy;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreVancancyRequest;
use App\Models\Application;
use App\Models\Department;
use App\Models\Designation;
use App\Models\Faculty;
use App\Models\User;
use App\Models\Vacancy;
use App\Models\VacancyNotice;
use App\Models\VacancyOperator;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class VacancyController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head');
    }

    public function VacancyIndex()
    {
        Log::info('VacancyController -> vacancy index started');

        $mainBranch = Auth()->user()->main_branch_id;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {

            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                         ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                         ->select('vacancies.*','categories.display_name')
                         //->orderByDesc('updated_at')
                         //->where('date_closed', '>=', $currentDate)
                         ->orderByDesc('vacancies.id')
                         ->get();

            Log::info('VacancyController -> vancancies Count - ' . $vacancies->count());
            Log::info('VacancyController -> vacancy index ended');

        } elseif ($mainBranch == 52) {

            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                         ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                         ->select('vacancies.*','categories.display_name')
                         ->orderByDesc('vacancies.id')
                         //->whereIn('main_category_id', array(44, 45, 202, 203, 204))
                         ->whereIn('main_category_id', array(44, 45))
                         ->get();

            Log::info('VacancyController -> vancancies Count - ' . $vacancies->count());
            Log::info('VacancyController -> vacancy index ended');

        } elseif ($mainBranch == 53) {

            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                        ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                        ->select('vacancies.*','categories.display_name')
                        ->orderByDesc('vacancies.id')
                        //->whereIn('main_category_id', array(46, 205))
                        ->whereIn('main_category_id', array(46))
                        ->get();

            Log::info('VacancyController -> vancancies Count - ' . $vacancies->count());
            Log::info('VacancyController -> vacancy index ended');
        }

        return view('admin.vacancy.index', compact('vacancies'));
    }

    public function VacancyTrashIndex()
    {
        Log::info('VacancyController -> vacancy index started');
        $trashVacancies = Vacancy::onlyTrashed()->get();

        Log::info('VacancyController -> Trash vacancies Count - ' . $trashVacancies->count());
        Log::info('VacancyController -> trash vacancies index ended');

        return view('admin.vacancy.trash', compact('trashVacancies'));
    }

    public function VacancyAdd()
    {
        Log::info('VacancyController -> vacancy add started');

        $faculties = Faculty::all();
        $departments = Department::all();
        $categories = $this->getCategories([10, 34, 16]);
        $vacancyVisibility = $categories->where('category_type_id', '34');
        $educationLevels = $categories->where('category_type_id', '16');
        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->where('designations.active_status',1)
                          //->where('designations.designation_division',52)
                          ->get();
            $mainEmployeeCategories = $categories->where('category_type_id', '10')->whereIn('id', array(44, 45 ,46));
        } elseif ($mainBranch == 52) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->where('designations.active_status',1)
                          ->where('designations.designation_division',52)
                          ->get();
            $mainEmployeeCategories = $categories->where('category_type_id', '10')->whereIn('id', array(44, 45));
        } elseif ($mainBranch == 53) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->where('designations.active_status',1)
                          ->where('designations.designation_division',53)
                          ->get();
            $mainEmployeeCategories = $categories->where('category_type_id', '10')->whereIn('id', array(46));
        }

        if ($mainBranch == 51) {

            $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                        ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                        ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                        ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->whereIn('roles.id', [5, 6])
                        ->distinct()
                        ->get();


        }else if($mainBranch == 52){

            if (Auth()->user()->hasRole(['est-head'])) {

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                            ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                            ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                            ->where('users.main_branch_id',52)
                            ->whereIn('roles.id', [5, 6])
                            ->distinct()
                            ->get();

            }


        }else if($mainBranch == 53){

            if (Auth()->user()->hasRole(['est-head'])) {

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                            ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                            ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                            ->where('users.main_branch_id',53)
                            ->whereIn('roles.id', [5, 6])
                            ->distinct()
                            ->get();

            }

        }

        Log::info('VacancyController -> vacancy add ended');
        return view('admin.vacancy.add', compact('faculties', 'designations', 'educationLevels', 'departments', 'vacancyVisibility', 'mainEmployeeCategories','operators'));
    }

    public function VacancyStore(StoreVancancyRequest $request)
    {
        Log::info('VacancyController -> Vacancy store started');

        $maxnumber = DB::table('vacancies')
                    ->select(DB::raw('MAX(id) as value'))
                    ->get();

        $maxValue = json_decode($maxnumber, true);
        $nextId = $maxValue[0]["value"] + 1;

        $data = new Vacancy();
        $data->main_category_id =  $request->main_category_id;
        $data->designation_id = $request->designation_id;
        $data->faculty_id = $request->faculty_id;
        $data->department_id = $request->department_id;
        $data->subject = $request->subject;
        $data->min_qualification = $request->min_qualification;
        $data->min_age = 18;
        $data->max_age = 100;
        $data->vacancy_visibility_status = $request->vacancy_visibility_status;
        $data->date_opened = date("Y-m-d", strtotime($request->date_opened));
        $data->date_closed = date("Y-m-d", strtotime($request->date_closed));
        $data->created_at = Carbon::now();
        $data->published_user_id = Auth()->user()->employee_no;
        $data->vacancy_status_id = 0;


        if($request->hasFile('vacancy_notice')){

        $notice = $request->file('vacancy_notice');
        $name_gen = 'Notice'.'.'.$notice->getClientOriginalExtension();
        $path = 'public/uploads/vacancy_notice/'.$nextId;

        if (! File::exists($path)) {
                File::makeDirectory(storage_path().$path, 0777, true, true);
        }
        $request->file('vacancy_notice')->storeAs($path, $name_gen);
        $save_url = $path.'/'.$name_gen;

        $data->file_path = $save_url;

        }
        $data->save();

        for ($i = 0; $i < count($request->operator); $i++) {

        $data1 = new VacancyOperator();
        $data1->vacancy_id = $data->id;
        $data1->employee_no = $request->operator[$i];
        $data1->save();
        }

        Log::notice('VacancyController -> Created vacancy id - ' . $data->id . ' created by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> Vacancy create ended');

        $notification = array(
            'message' => 'New Vacancy Created Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('vacancy.add')->with($notification);
    }

    public function VacancyEdit($id)
    {
        Log::info('VacancyController -> vacancy edit started');

        $vacancyId = decrypt($id);
        $editData = Vacancy::find($vacancyId);
        $faculties = Faculty::all();
        $departments = Department::all();
        $categories = $this->getCategories([10, 34, 16]);
        $vacancyVisibility = $categories->where('category_type_id', '34');
        $educationLevels = $categories->where('category_type_id', '16');
        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->where('designations.active_status',1)
                          ->get();
            $mainEmployeeCategories = $categories->where('category_type_id', '10');
        } elseif ($mainBranch == 52) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->where('designations.active_status',1)
                          ->where('designations.designation_division',52)
                          ->get();
            $mainEmployeeCategories = $categories->where('category_type_id', '10')->whereIn('id', array(44, 45, 202, 203, 204));
        } elseif ($mainBranch == 53) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->where('designations.active_status',1)
                          ->where('designations.designation_division',53)
                          ->get();
            $mainEmployeeCategories = $categories->where('category_type_id', '10')->whereIn('id', array(46, 205));
        }

        if ($mainBranch == 51) {

            $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                        ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                        ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                        ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->whereIn('roles.id', [5, 6])
                        ->distinct()
                        ->get();


        }else if($mainBranch == 52){

            if (Auth()->user()->hasRole(['est-head'])) {

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                            ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                            ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                            ->where('users.main_branch_id',52)
                            ->whereIn('roles.id', [5, 6])
                            ->distinct()
                            ->get();

            }


        }else if($mainBranch == 53){

            if (Auth()->user()->hasRole(['est-head'])) {

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                            ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                            ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                            ->where('users.main_branch_id',53)
                            ->whereIn('roles.id', [5, 6])
                            ->distinct()
                            ->get();

            }

        }

        $oldOperators = VacancyOperator::where('vacancy_id',$editData->id)->get();


        Log::notice('VacancyController -> edit vacancy id - ' . $vacancyId . ' edited by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy edit ended');


        return view('admin.vacancy.edit', compact('editData', 'faculties', 'designations', 'educationLevels', 'departments', 'vacancyVisibility', 'mainEmployeeCategories','operators','oldOperators'));
    }

    public function VacancyUpdate(StoreVancancyRequest $request, $id)
    {

        Log::info('VacancyController -> vacancy update started');

        $data = Vacancy::find($id);
        $data->main_category_id =  $request->main_category_id;
        $data->designation_id = $request->designation_id;
        $data->faculty_id = $request->faculty_id;
        $data->department_id = $request->department_id;
        $data->subject = $request->subject;
        $data->min_qualification = $request->min_qualification;
        // $data->min_age = $request->min_age;
        // $data->max_age = $request->max_age;
        $data->vacancy_visibility_status = $request->vacancy_visibility_status;
        //$data->date_opened = date("Y-m-d", strtotime($request->date_opened));
        //$data->date_closed = date("Y-m-d", strtotime($request->date_closed));
        $data->created_at = Carbon::now();
        $data->updated_user_id = Auth()->user()->employee_no;
        $data->updated_at = Carbon::now();

        if ($request->file('vacancy_notice')) {

        $string = $data->file_path;
        $parts = explode("/", $string);
        $nextId = $parts[3];

        $notice = $request->file('vacancy_notice');
        $name_gen = 'Notice'.'.'.$notice->getClientOriginalExtension();
        $path = 'public/uploads/vacancy_notice/'.$nextId;

        if (! File::exists($path)) {
                File::makeDirectory(storage_path().$path, 0777, true, true);
        }
        $request->file('vacancy_notice')->storeAs($path, $name_gen);
        $save_url = $path.'/'.$name_gen;
        $data->file_path = $save_url;
        }

        $data->save();

        if ($request->newoperator != null) {

        for ($i = 0; $i < count($request->newoperator); $i++) {

            $data1 = new VacancyOperator();
            $data1->vacancy_id = $data->id;
            $data1->employee_no = $request->newoperator[$i];
            $data1->save();
        }

        }

        Log::notice('VacancyController -> update vacancy id - ' . $data->id . ' updated by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy update ended');

        $notification = array(
            'message' => 'Vacancy data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('vacancy.index')->with($notification);
    }

    public function VacancySoftdelete($id)
    {

        Log::info('VacancyController -> vacancy soft delete started');

        $vacancyId = decrypt($id);
        $vacancy = Vacancy::find($vacancyId);
        $vacancy->delete();

        $notification = array(
            'message' => 'Vacancy Deleted Successfully',
            'alert-type' => 'warning'
        );

        Log::notice('VacancyController -> soft delete vacancy id - ' . $vacancy->id . ' deleted by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy soft delete ended');

        return redirect()->route('vacancy.index')->with($notification);
    }

    public function VacancyRestore($id)
    {

        Log::info('VacancyController -> vacancy restore started');

        $vacancyId = decrypt($id);
        $vacancy = Vacancy::withTrashed()->find($vacancyId);
        $vacancy->restore();

        $notification = array(
            'message' => 'Vacancy Restore Successfully',
            'alert-type' => 'success'
        );

        Log::notice('VacancyController -> restore vacancy id - ' . $vacancy->id . ' deleted by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy restore ended');

        return redirect()->route('vacancy.index')->with($notification);
    }

    public function VacancyDelete($id)
    {

        Log::info('VacancyController -> vacancy delete started');

        $vacancyId = decrypt($id);
        $vacancy = Vacancy::onlyTrashed()->find($vacancyId);

        $vacancy->forceDelete();

        $data = VacancyOperator::where('vacancy_id',$vacancyId);
        $data->delete();

        $notification = array(
            'message' => 'Vacancy Permenetly Deleted Successfully',
            'alert-type' => 'error'
        );

        Log::emergency('VacancyController -> delete vacancy id - ' . $vacancy->id . ' deleted by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy delete ended');

        return redirect()->route('vacancy.trash.index')->with($notification);
    }

    public function VacancyInactive($id)
    {

        Log::info('VacancyController -> vacancy inactive started');
        $vacancyId = decrypt($id);
        $vacancy = Vacancy::find($vacancyId);
        $vacancy->vacancy_status_id = 0;
        $vacancy->save();

        $notification = array(
            'message' => 'Academic Support Vacancy Inactive successfully',
            'alert-type' => 'success'
        );

        Log::notice('VacancyController -> inactive vacancy id - ' . $vacancyId . ' inactivate by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> Vacancy inactive ended');

        return redirect()->back()->with($notification);
    } //end method

    public function VacancyActive($id)
    {

        Log::info('VacancyController -> vacancy active started');
        $vacancyId = decrypt($id);
        $vacancy = Vacancy::find($vacancyId);
        $vacancy->vacancy_status_id = 1;
        $vacancy->save();

        $notification = array(
            'message' => 'Academic Support Vacancy Active successfully',
            'alert-type' => 'success'
        );

        Log::notice('VacancyController -> active vacancy id - ' . $vacancyId . ' activate by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy active ended');


        return redirect()->back()->with($notification);
    } //end method

    public function VacancyExtend($id)
    {
        Log::info('VacancyController -> vacancy extend started');

        $vacancyId = decrypt($id);
        $editData = Vacancy::find($vacancyId);
        $faculties = Faculty::all();
        $departments = Department::all();
        $categories = $this->getCategories([10, 34, 16]);
        $vacancyVisibility = $categories->where('category_type_id', '34');
        $educationLevels = $categories->where('category_type_id', '16');
        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->get();
            $mainEmployeeCategories = $categories->where('category_type_id', '10');
        } elseif ($mainBranch == 52) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->get();
            $mainEmployeeCategories = $categories->where('category_type_id', '10')->whereIn('id', array(44, 45, 202, 203, 204));
        } elseif ($mainBranch == 53) {

            $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
                          ->select('designations.*','categories.display_name')
                          ->get();
            $mainEmployeeCategories = $categories->where('category_type_id', '10')->whereIn('id', array(46, 205));
        }

        Log::notice('VacancyController -> extend vacancy id - ' . $vacancyId . ' edited by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy extend ended');


        return view('admin.vacancy.extend', compact('editData', 'faculties', 'designations', 'educationLevels', 'departments', 'vacancyVisibility', 'mainEmployeeCategories'));
    }

    public function VacancyExtendUpdate(Request $request, $id)
    {

        Log::info('VacancyController -> vacancy extend update started');

        $validatedData = $request->validate([

            'date_closed' => 'required|date|after:today'
        ]);

        $data = Vacancy::find($id);

        // Store the previous value of date_closed
        $previousDateClosed = $data->date_closed;

        // Update date_closed with the new value from the request
        $data->date_closed = date("Y-m-d", strtotime($request->date_closed));
        $data->vacancy_status_type_id = 259;
        $data->deadline_extented_status = 1;
        $data->deadline_extented_emp = Auth()->user()->employee_no;

        // Assign the previous value of date_closed to deadline_extented_date
        $data->deadline_extented_date = $previousDateClosed;

        $data->save();


        Log::notice('VacancyController ->vacancy date extend id - ' . $data->id . ' updated by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy extend update ended');

        $notification = array(
            'message' => 'Vacancy date extended Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('vacancy.index')->with($notification);
    }

    public function VacancyOperatorView()
    {
        $mainBranch = Auth()->user()->main_branch_id;
        $currentDate = date('Y-m-d');

        if ($mainBranch == 51) {

            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                         ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                         //->leftJoin('vacancy_operators', 'vacancies.id', '=', 'vacancy_operators.vacancy_id')
                         ->select('vacancies.*','categories.display_name')
                         ->orderByDesc('vacancies.id')
                         ->get();


        } elseif ($mainBranch == 52) {

            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                         ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                         ->select('vacancies.*','categories.display_name')
                         ->orderByDesc('vacancies.id')
                         ->whereIn('main_category_id', array(44, 45))
                         ->get();

        } elseif ($mainBranch == 53) {

            $vacancies = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                        ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                        ->select('vacancies.*','categories.display_name')
                        ->orderByDesc('vacancies.id')
                        ->whereIn('main_category_id', array(46))
                        ->get();

        }

        return view('admin.vacancy.operator', compact('vacancies'));
    }

    public function VacancyOperatorList($id){

        $vacancyId = decrypt($id);
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                   ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                   ->select('vacancies.*','categories.display_name')
                   ->find($vacancyId);
        $vacancyOperator = VacancyOperator::where('vacancy_id',$vacancyId)->get();

        return view('admin.vacancy.operator_list', compact('vacancyOperator','vacancy'));
    }

    public function VacancyOperatorAdd($id){

        $vacancyId = decrypt($id);
        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                         ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                         ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                         ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                         ->whereIn('roles.id', [5, 6])
                         //->where('users.main_branch_id', 52)
                         ->leftJoin('vacancy_operators as vo', function($join) use ($vacancyId) {
                                  $join->on('employees.employee_no', '=', 'vo.employee_no')
                                  ->where('vo.vacancy_id', $vacancyId);
                          })->whereNull('vo.employee_no')
                          ->distinct()
                          ->get();

        }else if($mainBranch == 52){

            if (Auth()->user()->hasRole(['est-head'])) {

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                ->whereIn('roles.id', [5, 6])
                ->where('users.main_branch_id', 52)
                ->leftJoin('vacancy_operators as vo', function($join) use ($vacancyId) {
                         $join->on('employees.employee_no', '=', 'vo.employee_no')
                         ->where('vo.vacancy_id', $vacancyId);
                 })->whereNull('vo.employee_no')
                 ->distinct()
                 ->get();

            }

        }else if($mainBranch == 53){

            if (Auth()->user()->hasRole(['est-head'])) {

                $operators = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name')
                ->whereIn('roles.id', [5, 6])
                ->where('users.main_branch_id', 53)
                ->leftJoin('vacancy_operators as vo', function($join) use ($vacancyId) {
                         $join->on('employees.employee_no', '=', 'vo.employee_no')
                         ->where('vo.vacancy_id', $vacancyId);
                 })->whereNull('vo.employee_no')
                 ->distinct()
                 ->get();

            }

        }

        return view('admin.vacancy.operator_add', compact('operators','vacancyId'));

    }

    public function VacancyOperatorStore(Request $request){

        $validatedData = $request->validate([
            'operator' => 'required',
        ]);

        $data = new VacancyOperator();
        $data->vacancy_id = $request->vacancy_id;
        $data->employee_no = $request->operator;
        $data->save();

        $notification = array(
            'message' => 'New Vacancy Operator Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('vacancy.operator.view')->with($notification);
    }

    public function VacancyOperatorDelete($id)
    {

        Log::info('VacancyController -> vacancy operator delete started');

        //$operastorId = decrypt();
        $data = VacancyOperator::find($id);
        $data->delete();

        Log::emergency('VacancyController -> delete vacancy operator id - ' . $data->id . ' deleted by ' . auth()->user()->employee_no);
        Log::info('VacancyController -> vacancy operator delete ended');

        return response()->json(['message' => 'Record deleted successfully']);
    }

    public function VacancyNoticeIndex()
    {
        $vacancyNotice = VacancyNotice::all();
        return view('admin.vacancy.document.index', compact('vacancyNotice'));
    }

    public function VacancyNoticeAdd()
    {

        $categories = $this->getCategories([10]);
        $mainEmployeeCategories = $categories->where('category_type_id', '10')->whereIn('id', array(44, 45 ,46));

        return view('admin.vacancy.document.add', compact('mainEmployeeCategories'));
    }

    public function VacancyNoticeStore(Request $request)
    {
        $validatedData = $request->validate([
            'main_category_id' => 'required',
            'date_opened' => 'required|date',
            'date_closed' => 'required|date',
            'vacancy_notice' => 'required',
        ]);

        $maxnumber = DB::table('vacancy_notices')
                    ->select(DB::raw('MAX(id) as value'))
                    ->get();

        $maxValue = json_decode($maxnumber, true);
        $nextId = $maxValue[0]["value"] + 1;

        $data = new VacancyNotice();
        $data->type =  $request->main_category_id;
        $data->start_date = date("Y-m-d", strtotime($request->date_opened));
        $data->end_date = date("Y-m-d", strtotime($request->date_closed));
        $data->created_at = Carbon::now();


        if($request->hasFile('vacancy_notice')){

        $notice = $request->file('vacancy_notice');
        $name_gen = $nextId.'.'.$notice->getClientOriginalExtension();
        $path = 'pdf/Notice';

        if (! File::exists($path)) {
                File::makeDirectory(public_path().$path, 0777, true, true);
        }
        $notice->move($path, $name_gen);
        $save_url = $path.'/'.$name_gen;

        $data->save_path = $save_url;

        }
        $data->save();


        $notification = array(
            'message' => 'New Vacancy Notice Created Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('vacancy.notice.index')->with($notification);
    }

    public function VacancyNoticeShow($id){

        $path = public_path('/pdf/Notice/'.$id.'.pdf');

        return response()->file($path, [
            'Content-Type' => 'application/pdf'
        ]);
    }//vacancy notice download

    public function VacancyNoticeEdit($id)
    {

        $editData = VacancyNotice::find($id);
        $categories = $this->getCategories([10]);
        $mainEmployeeCategories = $categories->where('category_type_id', '10')->whereIn('id', array(44, 45 ,46));

        return view('admin.vacancy.document.edit', compact('editData', 'mainEmployeeCategories'));
    }

    public function VacancyNoticeUpdate(Request $request, $id)
    {
        $validatedData = $request->validate([
            'main_category_id' => 'required',
            'date_opened' => 'required|date',
            'date_closed' => 'required|date',
            //'vacancy_notice' => 'required',
        ]);

        $data = VacancyNotice::find($id);
        $data->type =  $request->main_category_id;
        $data->start_date = date("Y-m-d", strtotime($request->date_opened));
        $data->end_date = date("Y-m-d", strtotime($request->date_closed));
        $data->updated_at = Carbon::now();

        if ($request->file('vacancy_notice')) {


        $notice = $request->file('vacancy_notice');
        $name_gen = $data->id.'.'.$notice->getClientOriginalExtension();
        $path = 'pdf/Notice';

        if (! File::exists($path)) {
            File::makeDirectory(public_path().$path, 0777, true, true);
        }
        $notice->move($path, $name_gen);
        $save_url = $path.'/'.$name_gen;
        //$data->file_path = $save_url;
        }

        $data->save();

        $notification = array(
            'message' => 'Vacancy Notice data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('vacancy.notice.index')->with($notification);
    }

    public function VacancyNoticeDelete($id)
    {

        $vacancy = VacancyNotice::find($id);

        $vacancy->delete();

        if (File::exists(public_path($vacancy->save_path))) {
            File::delete(public_path($vacancy->save_path));
            //echo "File deleted successfully.";
        } else {
            //echo "File not found.";
        }

        $notification = array(
            'message' => 'Vacancy Permenetly Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('vacancy.notice.index')->with($notification);
    }

    public function VacancyShow($id){

        $vacancyId = decrypt($id);
        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
        ->join('categories', 'designations.staff_grade', '=', 'categories.id')
        ->select('vacancies.*','categories.display_name')
        ->find($vacancyId);

        $vacancyOperator = VacancyOperator::where('vacancy_id',$vacancyId)->get();

        $completeApplications = Application::where('vacancy_id',$vacancyId)->where('application_decision_id','!=',33)->get();

        return view('admin.vacancy.show', compact('vacancy','vacancyOperator','completeApplications'));

    }

}
