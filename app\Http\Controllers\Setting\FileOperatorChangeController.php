<?php

namespace App\Http\Controllers\Setting;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FileOperatorChangeController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head');
    }

    public function index(){

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
            ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->select('users.id','users.status_id', 'employees.initials', 'employees.last_name','employees.employee_no')
            ->groupBy('users.id','users.status_id', 'employees.initials', 'employees.last_name','employees.employee_no')
            ->selectRaw('GROUP_CONCAT(roles.name) as roles')
            ->whereIn('roles.id', [5, 6])
            ->distinct()
            ->orderby('users.id')
            ->get();

        }elseif ($mainBranch == 52) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
            ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->select('users.id','users.status_id', 'employees.initials', 'employees.last_name','employees.employee_no')
            ->groupBy('users.id','users.status_id', 'employees.initials', 'employees.last_name','employees.employee_no')
            ->selectRaw('GROUP_CONCAT(roles.name) as roles')
            ->whereIn('roles.id', [5, 6])
            ->where('users.main_branch_id', 52)
            ->distinct()
            ->orderby('users.id')
            ->get();

        }elseif ($mainBranch == 53) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
            ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->select('users.id','users.status_id', 'employees.initials', 'employees.last_name','employees.employee_no')
            ->groupBy('users.id','users.status_id', 'employees.initials', 'employees.last_name','employees.employee_no')
            ->selectRaw('GROUP_CONCAT(roles.name) as roles')
            ->whereIn('roles.id', [5, 6])
            ->where('users.main_branch_id', 53)
            ->distinct()
            ->orderby('users.id')
            ->get();

        }

        return view('admin.setting.operation.file_operator_assign.index', compact('users'));
    }

    public function assign(Request $request)
    {
        $validated = $request->validate([
            'current_operator' => 'required',
            'selected_employees' => 'required|array',
            'new_operator' => 'required|integer',
        ]);

        Employee::whereIn('employee_no', $validated['selected_employees'])
            ->update(['assign_ma_user_id' => $validated['new_operator']]);

            $notification = array(
                'message' => 'File Operator Assigned Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('file.operator.change.index')->with($notification);
    }

    public function getEmployees(Request $request)
    {
        $request->validate([
            'operator_id' => 'required'
        ]);

        $employees = Employee::select(
            'employees.id',
            'employees.employee_no',
            'employees.initials',
            'employees.last_name',
            'faculties.faculty_name',
            'departments.department_name',
            'designations.designation_name',
            'categories.category_name'
        )
        ->leftJoin('faculties', 'faculties.id', '=', 'employees.faculty_id')
        ->leftJoin('departments', 'departments.id', '=', 'employees.department_id')
        ->leftJoin('designations', 'designations.id', '=', 'employees.designation_id')
        ->leftJoin('categories', 'categories.id', '=', 'employees.employee_status_id')
        ->where('employees.assign_ma_user_id', $request->operator_id)
        ->orderBy('employees.employee_no')
        ->get();

        return response()->json([
            'success' => true,
            'data' => $employees
        ]);
    }
}
