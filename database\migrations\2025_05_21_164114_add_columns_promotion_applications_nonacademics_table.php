<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('promotion_applications_nonacademics', function (Blueprint $table) {
            //
            $table->text('effect_date_note')->nullable()->after('effective_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('promotion_applications_nonacademics', function (Blueprint $table) {
            //
            $table->dropColumn('effect_date_note');
        });
    }
};
