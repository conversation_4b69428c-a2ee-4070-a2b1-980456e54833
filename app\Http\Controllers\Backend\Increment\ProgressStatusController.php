<?php

namespace App\Http\Controllers\Backend\Increment;

use App\Http\Controllers\Controller;
use App\Models\Increment;
use App\Models\NonIncrement;
use Illuminate\Http\Request;

class ProgressStatusController extends Controller
{
    public function incrementStatus($id)
    {   
        $id = decrypt($id);
        // $id = $id;
        $increment_status = $this->get_non_increment_data($id);
        return view('admin.increment.NonAcademic.increments_progress', compact('increment_status'));
    }

    private function get_non_increment_data($empNo)
    {
        $increment_status = NonIncrement::where('employee_no','=',$empNo)
                            ->orderBy('year','DESC')
                            ->first();

        return $increment_status;
    }

   

    
}
