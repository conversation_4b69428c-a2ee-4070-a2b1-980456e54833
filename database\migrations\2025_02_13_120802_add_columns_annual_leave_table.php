<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('annual_leaves', function (Blueprint $table) {
            $table->integer('summary_table_id')->default(0)->after('ma_forward_status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('annual_leaves', function (Blueprint $table) {
            $table->dropColumn('summary_table_id');
        });
    }
};
