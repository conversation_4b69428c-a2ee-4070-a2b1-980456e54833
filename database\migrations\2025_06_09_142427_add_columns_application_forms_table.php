<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('application_forms', function (Blueprint $table) {
            $table->integer('interview_status')->default(0)->after('short_list_date');
            $table->integer('interview_board_id')->nullable()->after('interview_status');
            $table->date('interview_date')->nullable()->after('interview_board_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('application_forms', function (Blueprint $table) {
            $table->dropColumn('interview_status');
            $table->dropColumn('interview_board_id');
            $table->dropColumn('interview_date');
        });
    }
};
