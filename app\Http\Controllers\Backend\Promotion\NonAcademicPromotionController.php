<?php

namespace App\Http\Controllers\Backend\Promotion;

use App\Http\Controllers\Controller;
use App\Mail\NcaForwardToHead;
use App\Mail\NcaPromoReject;
use App\Models\Category;
use App\Models\Commendation;
use App\Models\DepartmentHead;
use App\Models\Designation;
use App\Models\Employee;
use App\Models\ExamBoard;
use App\Models\ExternalTransfer;
use App\Models\Increment;
use App\Models\InternalTransfer;
use App\Models\InterviewPanel;
use App\Models\interviewPanelMember;
use App\Models\Leave;
use App\Models\promoSalStep;
use App\Models\Promotion;
use App\Models\PromotionApplicationsNonacademic;
use App\Models\promotionDuration;
use Barryvdh\DomPDF\Facade\Pdf as FacadePdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;


class NonAcademicPromotionController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function promotionAllOpen(Request $request)
    {
        Log::info('NonAcademicPromotionController -> non academic promotion application data list show get started');

        if (isset($request->year)) {

            $pass_year = $request->year;
            $mainBranch = Auth()->user()->main_branch_id;


            if ($mainBranch == 51) {

                $empfetchDatas = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                    ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->select('promotion_applications_nonacademics.*', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'categories.category_name')
                    ->where('promotion_applications_nonacademics.year', '=', $request->year)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->get();
            } elseif ($mainBranch == 52) {
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['est-head', 'cc'])) {

                    $empfetchDatas = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                        ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                        ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                        ->select('promotion_applications_nonacademics.*', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'categories.category_name')
                        ->where('promotion_applications_nonacademics.year', '=', $request->year)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->get();
                } elseif (Auth()->user()->hasRole(['sc'])) {

                    $empfetchDatas = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                        ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                        ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                        ->select('promotion_applications_nonacademics.*', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'categories.category_name')
                        ->where('promotion_applications_nonacademics.year', '=', $request->year)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->get();
                }
            }

            // $empfetchDatas = array();
        } else {

            $pass_year = "";
            $empfetchDatas = array();
        }

        Log::info('NonAcademicPromotionController -> non academic promotion application data list show get ended');
        return view('admin.promotion.nonAcademic.applicationAll', compact('pass_year', 'empfetchDatas'));
    }

    public function applicationDetailsOpen($id)
    {

        Log::info('NonAcademicPromotionController -> non academic promotion application detail show get started');

        $appId = decrypt($id);
        $appData = PromotionApplicationsNonacademic::find($appId);
        $empData = Employee::find($appData->emp_no);
        $currentDesignationStaffGrade = Designation::join('categories as staff_grade', 'designations.staff_grade', '=', 'staff_grade.id')
            ->select('staff_grade.category_name as staff_grade')
            ->where('designations.id', $appData->current_desigantion_id)
            ->get();
        $currentDesignationStaffGrade = json_decode($currentDesignationStaffGrade, true);
        $currentDesignationStaffGrade = $currentDesignationStaffGrade[0]["staff_grade"];

        $applyDesignationStaffGrade = Designation::join('categories as staff_grade', 'designations.staff_grade', '=', 'staff_grade.id')
            ->select('staff_grade.category_name as staff_grade')
            ->where('designations.id', $appData->promotion_designation_id)
            ->get();
        $applyDesignationStaffGrade = json_decode($applyDesignationStaffGrade, true);
        $applyDesignationStaffGrade = $applyDesignationStaffGrade[0]["staff_grade"];

        $clerk_name = Employee::where('employee_no', '=', $appData->check_user_id)
            ->select('initials', 'last_name')
            ->get();

        if ($clerk_name->count() > 0) {

            foreach ($clerk_name as $clerk_name) {

                $clerk_name = $clerk_name->initials . " " . $clerk_name->last_name;
            }
        } else {

            $clerk_name = "";
        }

        $ar_name = Employee::where('employee_no', '=', $appData->ar_user_id)
            ->select('initials', 'last_name')
            ->get();

        if ($ar_name->count() > 0) {

            foreach ($ar_name as $ar_name) {

                $ar_name = $ar_name->initials . " " . $ar_name->last_name;
            }
        } else {

            $ar_name = "";
        }

        $head_name = Employee::where('employee_no', '=', $appData->head_user_id)
            ->select('initials', 'last_name')
            ->get();

        if ($head_name->count() > 0) {

            foreach ($head_name as $head_name) {

                $head_name = $head_name->initials . " " . $head_name->last_name;
            }
        } else {

            $head_name = "";
        }

        Log::info('NonAcademicPromotionController -> non academic promotion application detail show get ended');

        return view('admin.promotion.nonAcademic.app_details', compact('appData', 'empData', 'currentDesignationStaffGrade', 'applyDesignationStaffGrade', 'clerk_name', 'ar_name', 'head_name'));
    }
    // ******************* 1st checking ***********************************************************
    public function CheckingOpen()
    {

        $year = date("Y") - 1;
        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id')
                ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.check_status', '=', 0)
                ->orderBy('promotion_applications_nonacademics.id')
                ->get();
        } elseif ($mainBranch == 52) {

            $data_text = array();
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['cc'])) {

                $data_text = array();
            } elseif (Auth()->user()->hasRole(['sc'])) {

                $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                    ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id')
                    ->where('promotion_applications_nonacademics.year', '=', $year)
                    ->where('employees.assign_ma_user_id', '=', auth()->user()->employee_no)
                    ->where('promotion_applications_nonacademics.check_status', '=', 0)
                    ->orderBy('promotion_applications_nonacademics.id')
                    ->get();
            }
        }

        return view('admin.promotion.nonAcademic.1stChecking', compact('data_text'));
    }

    public function checkingDetails($id)
    {
        $appId = decrypt($id);

        $prmo_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
            ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
            ->select('employees.*', 'promotion_applications_nonacademics.*', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName')
            ->where('promotion_applications_nonacademics.id', '=', $appId)
            ->get();

        if ($prmo_text->count() > 0) {
            foreach ($prmo_text as $prmo_texts) {
                $empNo = $prmo_texts->employee_no;
                $refNo = $prmo_texts->ref_no;
                $year = $prmo_texts->year;
                $nic = $prmo_texts->nic;
                $bdate = $prmo_texts->date_of_birth;
                $name = $prmo_texts->initials . " " . $prmo_texts->last_name;
                $fName = $prmo_texts->name_denoted_by_initials . " " . $prmo_texts->last_name;
                $fAppDate = $prmo_texts->initial_appointment_date;
                $cAppDate = $prmo_texts->current_appointment_date;
                $retirmentDate = $prmo_texts->retirement_date;
                $dep = $prmo_texts->department_name;
                $desig = $prmo_texts->designation_name . " " . $prmo_texts->category_name;
                $promoDesig = $prmo_texts->promoDesigName . " " . $prmo_texts->proGName;

                $dt = Carbon::create($year, 12, 31, 0);
                $age = Carbon::parse($bdate)->diff($dt)->format('%y years, %m months and %d days');

                $appDate = Carbon::parse($prmo_texts->created_at)->toDateString();

                $lock = $prmo_texts->lock;
            }

            $empRecord = Promotion::join('categories', 'categories.id', '=', 'promotions.type_id')
                ->join('designations', 'designations.id', '=', 'promotions.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->select('promotions.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname')
                ->where('promotions.employee_no', '=', $empNo)
                ->orderBy('promotions.duty_assumed_date')
                ->get();

            $intTransfer = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                ->select('internal_transfers.*', 'departments.department_name')
                ->where('internal_transfers.emp_no', '=', $empNo)
                ->orderBy('internal_transfers.transfer_date')
                ->get();

            $exTransfer = ExternalTransfer::join('categories', 'categories.id', '=', 'external_transfers.status')
                ->join('designations', 'designations.id', '=', 'external_transfers.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->join('universities', 'universities.id', '=', 'external_transfers.uni_institute')
                ->select('external_transfers.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname', 'universities.uni_name')
                ->where('external_transfers.emp_no', '=', $empNo)
                ->orderBy('external_transfers.effective_date')
                ->get();

            $leave = Leave::where('leaves.emp_no', '=', $empNo)->orderBy('leaves.year', 'DESC')->get();

            $commend = Commendation::join('categories', 'categories.id', '=', 'commendations.type_id')
                ->select('commendations.*', 'categories.category_name')
                ->where('commendations.emp_no', '=', $empNo)
                ->orderByDesc('commendations.letter_date')
                ->get();

            $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                ->where('increments.emp_no', $empNo)
                ->orderBy('increments.effective_date', 'DESC')
                ->get();
        } else {
            $empNo = "";
            $refNo = "";
            $year = "";
            $nic = "";
            $bdate = "";
            $name = "";
            $fName = "";
            $fAppDate = "";
            $cAppDate = "";
            $retirmentDate = "";
            $dep = "";
            $desig = "";
            $promoDesig = "";
            $age = "";
            $appDate = "";
            $empRecord = "";
            $intTransfer = "";
            $exTransfer = "";
            $leave = "";
            $commend = "";
            $lock = "";
            $increments = "";
        }

        return view('admin.promotion.nonAcademic.1stCheckingDetails', compact(
            'empNo',
            'refNo',
            'year',
            'nic',
            'bdate',
            'name',
            'fName',
            'fAppDate',
            'cAppDate',
            'retirmentDate',
            'dep',
            'desig',
            'promoDesig',
            'age',
            'appDate',
            'empRecord',
            'intTransfer',
            'exTransfer',
            'leave',
            'commend',
            'increments',
            'lock'
        ));
    }

    public function checkingSave(Request $request)
    {

        if ($request->empNo != null) {
            if ($request->proYear != null) {

                $promotionApplication = PromotionApplicationsNonacademic::where('emp_no', $request->empNo)->where('year', $request->proYear)->first();

                if ($promotionApplication) {

                    $promotionApplication->check_status = 1;
                    $promotionApplication->check_remark = $request->comment;
                    $promotionApplication->check_user_id = auth()->user()->employee_no;
                    $promotionApplication->check_date = Carbon::today();
                    $promotionApplication->ar_accept_status = 1;
                    $promotionApplication->save();
                }

                Log::notice('NonAcademicPromotionController -> promotion application subject clerk ckeck in employee number - ' . $request->empNo . ' ckecked by ' . auth()->user()->employee_no);

                $notification = array(
                    'message' => 'Your 1st Checking step is successfully completed. Application successfully forwarded to the executive officer.',
                    'alert-type' => 'success'
                );

                return redirect()->route('nac.promo.1stchecking.open')->with($notification);
            } else {
                $notification = array(
                    'message' => 'Your comment is not successfully save. Please try again.y',
                    'alert-type' => 'error'
                );

                return redirect()->route('nac.promo.1stchecking.open')->with($notification);
            }
        } else {
            $notification = array(
                'message' => 'Your comment is not successfully save. Please try again.e',
                'alert-type' => 'error'
            );

            return redirect()->route('nac.promo.1stchecking.open')->with($notification);
        }
    }

    public function PromoEligibility(Request $request)
    {
        if (isset($request->year) && $request->year != 0) {

            $yearV = $request->year;

            if (Auth()->user()->hasRole(['administrator', 'est-head'])) {
                $empDetails = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->where('employees.main_branch_id', 53)
                    ->where('employees.employee_status_id', 110)
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $empDetails = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->where('employees.main_branch_id', 53)
                    ->where('employees.employee_status_id', 110)
                    ->where('employees.assign_ma_user_id', '=', auth()->user()->employee_no)
                    ->get();
            } else {
                $empDetails = array();
            }

            if ($empDetails->count() > 0) {

                foreach ($empDetails as $empDetail) {

                    $empname = $empDetail->initials . " " . $empDetail->last_name;
                    $empNo = $empDetail->employee_no;
                    $empDesig = $empDetail->designation_name . " " . $empDetail->category_name;
                    $empAppDate = $empDetail->current_appointment_date;
                    $desiID = $empDetail->designation_id;
                    $AppDate = Carbon::parse($empDetail->current_appointment_date);
                    $promoEligibility = $empDetail->promo_eligibility;
                    $departmentName = $empDetail->department_name;

                    $promoD = promotionDuration::join('designations', 'designations.id', '=', 'promotion_durations.promo_desig_id')
                        ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                        ->where('designation_id', $empDetail->designation_id)
                        ->get();

                    $promoD_rowCount = $promoD->count();

                    if ($promoD_rowCount > 0) {

                        foreach ($promoD as $promoDs) {

                            $promoDesig = $promoDs->designation_name . " " . $promoDs->category_name;
                            $prmoDesigID = $promoDs->promo_desig_id;
                            $promoYears = $promoDs->duration;
                        }

                        //$currentDate = Carbon::parse('2023-01-01');
                        $currentDate = Carbon::create($request->year + 1, 01, 01);

                        $serviceP = $AppDate->diffInYears($currentDate);

                        $cApply = PromotionApplicationsNonacademic::where('emp_no', $empNo)->where('year', $request->year)->get();

                        $cApply_rowCount = $cApply->count();

                        if ($cApply_rowCount > 0) {

                            $status = 1;
                        } else {

                            $status = 0;
                        }


                        if ($serviceP >= $promoYears) {

                            $data_set1[] = [
                                'empNo' => $empNo,
                                'empname' => $empname,
                                'departmentName' => $departmentName,
                                'empDesig' => $empDesig,
                                'promoDesig' => $promoDesig,
                                'status' => $status,
                                'promoEligibility' => $promoEligibility

                            ];
                        }
                    }
                }
            } else {
                $data_set1[] = [
                    'empNo' => '',
                    'empname' => '',
                    'departmentName' => '',
                    'empDesig' => '',
                    'promoDesig' => '',
                    'status' => '',
                    'promoEligibility' => ''
                ];
            }
        } else {
            $yearV = 0;
            $data_set1[] = [
                'empNo' => '',
                'empname' => '',
                'departmentName' => '',
                'empDesig' => '',
                'promoDesig' => '',
                'status' => '',
                'promoEligibility' => '',

            ];
        }
        //dd($socialNetworks);
        return view('admin.promotion.nonAcademic.eligibility_list', compact('data_set1', 'yearV'));
    }

    public function PromoEligibilityDetails($id1, $id2)
    {
        $empNo = decrypt($id1);
        $year = decrypt($id2);

        $prmo_text = Employee::join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->select('employees.*', 'departments.department_name', 'designations.designation_name', 'categories.category_name',)
            ->where('employees.employee_no', '=', $empNo)
            ->get();

        if ($prmo_text->count() > 0) {
            foreach ($prmo_text as $prmo_texts) {

                $nic = $prmo_texts->nic;
                $bdate = $prmo_texts->date_of_birth;
                $name = $prmo_texts->initials . " " . $prmo_texts->last_name;
                $fName = $prmo_texts->name_denoted_by_initials . " " . $prmo_texts->last_name;
                $fAppDate = $prmo_texts->initial_appointment_date;
                $cAppDate = $prmo_texts->current_appointment_date;
                $retirmentDate = $prmo_texts->retirement_date;
                $dep = $prmo_texts->department_name;
                $desig = $prmo_texts->designation_name . " " . $prmo_texts->category_name;
                $desigID = $prmo_texts->designation_id;

                $depID = $prmo_texts->department_id;
                $empEmail = $prmo_texts->email;

                $dt = Carbon::create($year, 12, 31, 0);
                $age = Carbon::parse($bdate)->diff($dt)->format('%y years, %m months and %d days');

                $appDate = Carbon::parse($prmo_texts->created_at)->toDateString();

                $promoStatus = $prmo_texts->promo_eligibility;
            }

            $empRecord = Promotion::join('categories', 'categories.id', '=', 'promotions.type_id')
                ->join('designations', 'designations.id', '=', 'promotions.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->select('promotions.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname')
                ->where('promotions.employee_no', '=', $empNo)
                ->orderBy('promotions.duty_assumed_date')
                ->get();

            $intTransfer = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                ->select('internal_transfers.*', 'departments.department_name')
                ->where('internal_transfers.emp_no', '=', $empNo)
                ->orderBy('internal_transfers.transfer_date')
                ->get();

            $exTransfer = ExternalTransfer::join('categories', 'categories.id', '=', 'external_transfers.status')
                ->join('designations', 'designations.id', '=', 'external_transfers.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->join('universities', 'universities.id', '=', 'external_transfers.uni_institute')
                ->select('external_transfers.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname', 'universities.uni_name')
                ->where('external_transfers.emp_no', '=', $empNo)
                ->orderBy('external_transfers.effective_date')
                ->get();

            $leave = Leave::where('leaves.emp_no', '=', $empNo)->orderBy('leaves.year', 'DESC')->get();

            $commend = Commendation::join('categories', 'categories.id', '=', 'commendations.type_id')
                ->select('commendations.*', 'categories.category_name')
                ->where('commendations.emp_no', '=', $empNo)
                ->orderByDesc('commendations.letter_date')
                ->get();

            $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                ->where('increments.emp_no', $empNo)
                ->orderBy('increments.effective_date', 'DESC')
                ->get();

            $promo_desi = promotionDuration::join('designations', 'designations.id', '=', 'promotion_durations.promo_desig_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->select('designations.designation_name', 'categories.category_name')
                ->where('promotion_durations.designation_id', $desigID)
                ->get();

            if ($promo_desi->count() > 0) {
                foreach ($promo_desi as $promo_desis) {
                    $promoDesig = $promo_desis->designation_name . " " . $promo_desis->category_name;
                }
            } else {
                $promoDesig = '';
            }
        } else {
            $empNo = "";

            $year = "";
            $nic = "";
            $bdate = "";
            $name = "";
            $fName = "";
            $fAppDate = "";
            $cAppDate = "";
            $retirmentDate = "";
            $dep = "";
            $desig = "";
            $promoDesig = "";
            $age = "";
            $appDate = "";
            $empRecord = "";
            $intTransfer = "";
            $exTransfer = "";
            $leave = "";
            $commend = "";
            $checkUser = "";
            $checkDate = "";
            $checkRemark = "";
            $depHead = "";
            $empEmail = "";
            $headEmpNo = "";
            $headEmail = "";
            $increments = "";
            $promoStatus = 0;
        }

        return view('admin.promotion.nonAcademic.eligibility_Details', compact(
            'empNo',

            'year',
            'nic',
            'bdate',
            'name',
            'fName',
            'fAppDate',
            'cAppDate',
            'retirmentDate',
            'dep',
            'desig',
            'promoDesig',
            'age',
            'appDate',
            'empRecord',
            'intTransfer',
            'exTransfer',
            'leave',
            'commend',
            'increments',
            'promoStatus'
        ));
    }

    public function promoEligibilityStore(Request $request)
    {
        if ($request->empNo != "") {
            switch ($request->input('submitbt')) {
                case 'Eligible':

                    $eligiUpdate = Employee::where('employee_no', '=', $request->empNo)
                        ->update([
                            'promo_eligibility' => 1,
                        ]);

                    $notification = array(
                        'message' => 'Your respond successfully saved.',
                        'alert-type' => 'success'
                    );

                    return redirect()->route('nac.promo.eligible.list')->with($notification);

                    break;
                case 'Not Eligible':
                    $eligiUpdate = Employee::where('employee_no', '=', $request->empNo)
                        ->update([
                            'promo_eligibility' => 0,
                        ]);

                    $notification = array(
                        'message' => 'Your respond successfully saved.',
                        'alert-type' => 'success'
                    );

                    return redirect()->route('nac.promo.eligible.list')->with($notification);
                    break;
            }
        } else {
            $notification = array(
                'message' => 'Something wrong. Please try again..',
                'alert-type' => 'error'
            );

            return redirect()->route('nac.promo.eligible.list')->with($notification);
        }
    }


    // ************** nac promo application - officer approval *******************************************

    public function officerApprovalOpen()
    {

        $year = date("Y") - 1;

        $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
            ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
            ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id')
            ->where('promotion_applications_nonacademics.year', '=', $year)
            ->where('promotion_applications_nonacademics.ar_accept_status', '=', 1)
            ->orderBy('promotion_applications_nonacademics.check_date')
            ->get();


        return view('admin.promotion.nonAcademic.officerApproval', compact('data_text'));
    }

    public function officerApprovalDetails($id)
    {
        $appId = decrypt($id);

        $prmo_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
            ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
            ->select('employees.*', 'promotion_applications_nonacademics.*', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName')
            ->where('promotion_applications_nonacademics.id', '=', $appId)
            ->get();

        if ($prmo_text->count() > 0) {

            foreach ($prmo_text as $prmo_texts) {

                $empNo = $prmo_texts->employee_no;
                $refNo = $prmo_texts->ref_no;
                $year = $prmo_texts->year;
                $nic = $prmo_texts->nic;
                $bdate = $prmo_texts->date_of_birth;
                $name = $prmo_texts->initials . " " . $prmo_texts->last_name;
                $fName = $prmo_texts->name_denoted_by_initials . " " . $prmo_texts->last_name;
                $fAppDate = $prmo_texts->initial_appointment_date;
                $cAppDate = $prmo_texts->current_appointment_date;
                $retirmentDate = $prmo_texts->retirement_date;
                $dep = $prmo_texts->department_name;
                $desig = $prmo_texts->designation_name . " " . $prmo_texts->category_name;
                $promoDesig = $prmo_texts->promoDesigName . " " . $prmo_texts->proGName;
                $checkUserID = $prmo_texts->check_user_id;
                $checkDate = $prmo_texts->check_date;
                $checkRemark = $prmo_texts->check_remark;
                $depID = $prmo_texts->department_id;
                $empEmail = $prmo_texts->email;


                $dt = Carbon::create($year, 12, 31, 0);
                $age = Carbon::parse($bdate)->diff($dt)->format('%y years, %m months and %d days');

                $appDate = Carbon::parse($prmo_texts->created_at)->toDateString();
            }

            $empRecord = Promotion::join('categories', 'categories.id', '=', 'promotions.type_id')
                ->join('designations', 'designations.id', '=', 'promotions.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->select('promotions.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname')
                ->where('promotions.employee_no', '=', $empNo)
                ->orderBy('promotions.duty_assumed_date')
                ->get();

            $intTransfer = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                ->select('internal_transfers.*', 'departments.department_name')
                ->where('internal_transfers.emp_no', '=', $empNo)
                ->orderBy('internal_transfers.transfer_date')
                ->get();

            $exTransfer = ExternalTransfer::join('categories', 'categories.id', '=', 'external_transfers.status')
                ->join('designations', 'designations.id', '=', 'external_transfers.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->join('universities', 'universities.id', '=', 'external_transfers.uni_institute')
                ->select('external_transfers.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname', 'universities.uni_name')
                ->where('external_transfers.emp_no', '=', $empNo)
                ->orderBy('external_transfers.effective_date')
                ->get();

            $leave = Leave::where('leaves.emp_no', '=', $empNo)->orderBy('leaves.year', 'DESC')->get();

            $commend = Commendation::join('categories', 'categories.id', '=', 'commendations.type_id')
                ->select('commendations.*', 'categories.category_name')
                ->where('commendations.emp_no', '=', $empNo)
                ->orderByDesc('commendations.letter_date')
                ->get();

            $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                ->where('increments.emp_no', $empNo)
                ->orderBy('increments.effective_date', 'DESC')
                ->get();

            $checkD = Employee::select('initials', 'last_name')->where('employee_no', '=', $checkUserID)->get();

            if ($checkD->count() > 0) {
                foreach ($checkD as $checkDs) {
                    $checkUser = $checkDs->initials . " " . $checkDs->last_name;
                }
            } else {
                $checkUser = "";
            }

            $deph = DepartmentHead::join('employees', 'employees.employee_no', '=', 'department_heads.emp_no')
                ->join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('employees.*', 'categories.category_name')
                ->where('department_heads.department_id', '=', $depID)
                ->where('department_heads.active_status', '=', 1)
                ->get();

            if ($deph->count() > 0) {
                foreach ($deph as $dephs) {
                    $depHead = $dephs->category_name . " " . $dephs->initials . " " . $dephs->last_name;
                    $headEmpNo = $dephs->employee_no;
                    $headEmail = $dephs->email;
                }
            } else {
                $depHead = "";
                $headEmpNo = "";
                $headEmail = "";
            }
        } else {
            $empNo = "";
            $refNo = "";
            $year = "";
            $nic = "";
            $bdate = "";
            $name = "";
            $fName = "";
            $fAppDate = "";
            $cAppDate = "";
            $retirmentDate = "";
            $dep = "";
            $desig = "";
            $promoDesig = "";
            $age = "";
            $appDate = "";
            $empRecord = "";
            $intTransfer = "";
            $exTransfer = "";
            $leave = "";
            $commend = "";
            $checkUser = "";
            $checkDate = "";
            $checkRemark = "";
            $depHead = "";
            $empEmail = "";
            $headEmpNo = "";
            $headEmail = "";
            $increments = "";
        }

        return view('admin.promotion.nonAcademic.officerApprovalDetails', compact(
            'empNo',
            'refNo',
            'year',
            'nic',
            'bdate',
            'name',
            'fName',
            'fAppDate',
            'cAppDate',
            'retirmentDate',
            'dep',
            'desig',
            'promoDesig',
            'age',
            'appDate',
            'empRecord',
            'intTransfer',
            'exTransfer',
            'leave',
            'commend',
            'checkUser',
            'checkDate',
            'checkRemark',
            'depHead',
            'empEmail',
            'headEmpNo',
            'headEmail',
            'increments'
        ));
    }

    public function officerAppStore(Request $request)
    {
        if ($request->empNo != null) {
            if ($request->proYear != null) {

                $name = $request->name;

                $headPosiText = DepartmentHead::where('emp_no', auth()->user()->employee_no)->get();

                if ($headPosiText->count() > 0) {
                    foreach ($headPosiText as $headPosiTexts) {
                        $arPosition = $headPosiTexts->head_position;
                    }
                } else {
                    $arPosition = 0;
                }

                switch ($request->input('submitbt')) {
                    case 'Forward';

                        if ($request->headEmail != null) {
                            $headEmail = $request->headEmail;
                            $headName = $request->headName;

                            $promotionApplication = PromotionApplicationsNonacademic::where('emp_no', $request->empNo)
                                ->where('year', $request->proYear)
                                ->first();

                            if ($promotionApplication) {

                                $promotionApplication->ar_accept_status = 2;
                                $promotionApplication->ar_accept_remark = $request->comment;
                                $promotionApplication->ar_user_id = auth()->user()->employee_no;
                                $promotionApplication->ar_accept_date = Carbon::today();
                                $promotionApplication->head_status = 1;
                                $promotionApplication->ar_position = $arPosition;
                                $promotionApplication->save();
                            }

                            $promotionActiveStatus = Employee::find($request->empNo);
                            $promotionActiveStatus->promotion_active_status = 1;
                            $promotionActiveStatus->save();


                            Log::notice('NonAcademicPromotionController -> promotion application admistrative officer ckeck in employee number - ' . $request->empNo . ' ckecked by ' . auth()->user()->employee_no);

                            //send email
                            $emailData = [
                                'name' =>  $name,
                                'headName' => $headName,
                                'headEmail' => $headEmail,
                            ];

                            $mail = new NcaForwardToHead($emailData);
                            //sending email
                            Mail::to($headEmail)->send($mail);

                            $notification = array(
                                'message' => 'Your approval step is successfully completed. Application successfully forwarded to the head of relevant the department.',
                                'alert-type' => 'success'
                            );

                            return redirect()->route('nac.promo.officer.approval.open')->with($notification);
                        } else {
                            $notification = array(
                                'message' => 'There is no current head in this applicants department. Then cant forward.',
                                'alert-type' => 'error'
                            );

                            return redirect()->route('nac.promo.officer.approval.open')->with($notification);
                        }

                        break;

                    case '1st Checking':

                        $promotionApplication = PromotionApplicationsNonacademic::where('emp_no', $request->empNo)
                            ->where('year', $request->proYear)
                            ->first();

                        if ($promotionApplication) {

                            $promotionApplication->ar_accept_status = 0;
                            $promotionApplication->check_status = 0;
                            $promotionApplication->save();
                        }

                        $unLockText = Employee::where('employee_no', $request->empNo)->first();
                        if ($unLockText) {
                            $unLockText->lock = 2;
                            $unLockText->save();
                        }

                        Log::notice('NonAcademicPromotionController -> promotion application admistrative officer forword back subject clerk in employee number - ' . $request->empNo . ' forword back by ' . auth()->user()->employee_no);

                        $notification = array(
                            'message' => 'This application successfully forwarded back to the relevant subject Clerk.',
                            'alert-type' => 'success'
                        );

                        return redirect()->route('nac.promo.officer.approval.open')->with($notification);

                        break;

                    case 'Reject':

                        $empData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                            ->select('employees.*', 'categories.*')
                            ->where('employees.employee_no', '=', $request->empNo)
                            ->get();

                        if ($empData->count() > 0) {
                            foreach ($empData as $empDatas) {
                                $empName = $empDatas->category_name . " " . $empDatas->initials . " " . $empDatas->last_name;
                                $empEmail =  $empDatas->email;
                            }
                        } else {
                            $empName = "";
                            $empEmail = "";
                        }

                        $promotionApplication = PromotionApplicationsNonacademic::where('emp_no', $request->empNo)
                            ->where('year', $request->proYear)
                            ->first();

                        if ($promotionApplication) {

                            $promotionApplication->ar_accept_status = 3;
                            $promotionApplication->ar_accept_remark = $request->comment;
                            $promotionApplication->ar_user_id = auth()->user()->employee_no;
                            $promotionApplication->ar_accept_date = Carbon::today();
                            $promotionApplication->ar_position = $arPosition;
                            $promotionApplication->status = 1;
                            $promotionApplication->save();
                        }

                        $pro_clear = Employee::where('employee_no', $request->empNo)->first();
                        if ($pro_clear) {
                            $pro_clear->promo_eligibility = 0;
                            $pro_clear->promotion_active_status = 0;
                            $pro_clear->save();
                        }

                        Log::emergency('NonAcademicPromotionController -> promotion application admistrative officer reject in employee number - ' . $request->empNo . ' rejected by ' . auth()->user()->employee_no);

                        //send email
                        $emailDatas = [
                            'empName' =>  $empName,
                            'empEmail' => $empEmail,
                            'reason' => $request->comment,

                        ];

                        $mail = new NcaPromoReject($emailDatas);
                        //sending email
                        Mail::to($empEmail)->send($mail);

                        $notification = array(
                            'message' => 'This application rejected successfully.',
                            'alert-type' => 'success'
                        );

                        return redirect()->route('nac.promo.officer.approval.open')->with($notification);

                        break;
                }
            } else {
                $notification = array(
                    'message' => 'Your comment is not successfully save. Please try again.',
                    'alert-type' => 'error'
                );

                return redirect()->route('nac.promo.officer.approval.open')->with($notification);
            }
        } else {
            $notification = array(
                'message' => 'Your comment is not successfully save. Please try again.',
                'alert-type' => 'error'
            );

            return redirect()->route('nac.promo.officer.approval.open')->with($notification);
        }
    }
    public function interviewPendingOpen()
    {

        $year = date("Y") - 1;

        if (Auth()->user()->hasRole(['cc', 'sc'])) {

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id')
                ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.interview_status', '=', 1)
                ->where('employees.assign_ma_user_id', '=', auth()->user()->employee_no)
                ->orderBy('promotion_applications_nonacademics.head_date')
                ->get();
        } else if (Auth()->user()->hasRole(['super-admin', 'administrator', 'est-head'])) {

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id')
                ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.interview_status', '=', 1)
                ->orderBy('promotion_applications_nonacademics.head_date')
                ->get();
        } else {
            $data_text = array();
        }


        return view('admin.promotion.nonAcademic.interviewPending', compact('data_text'));
    }

    public function interviewPendingDetails($id)
    {
        $appId = decrypt($id);

        $prmo_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
            ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
            ->join('categories as prefo', 'prefo.id', '=', 'promotion_applications_nonacademics.performance')
            ->select('employees.*', 'promotion_applications_nonacademics.*', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'prefo.category_name as preform')
            ->where('promotion_applications_nonacademics.id', '=', $appId)
            ->get();

        if ($prmo_text->count() > 0) {
            foreach ($prmo_text as $prmo_texts) {

                $empNo = $prmo_texts->employee_no;
                $refNo = $prmo_texts->ref_no;
                $year = $prmo_texts->year;
                $nic = $prmo_texts->nic;
                $bdate = $prmo_texts->date_of_birth;
                $name = $prmo_texts->initials . " " . $prmo_texts->last_name;
                $fName = $prmo_texts->name_denoted_by_initials . " " . $prmo_texts->last_name;
                $fAppDate = $prmo_texts->initial_appointment_date;
                $cAppDate = $prmo_texts->current_appointment_date;
                $retirmentDate = $prmo_texts->retirement_date;
                $dep = $prmo_texts->department_name;
                $desig = $prmo_texts->designation_name . " " . $prmo_texts->category_name;
                $promoDesig = $prmo_texts->promoDesigName . " " . $prmo_texts->proGName;
                $checkUserID = $prmo_texts->check_user_id;
                $checkDate = $prmo_texts->check_date;
                $checkRemark = $prmo_texts->check_remark;
                $depID = $prmo_texts->department_id;
                $empEmail = $prmo_texts->email;

                $officerEmpNo = $prmo_texts->ar_user_id;
                $officerDate = $prmo_texts->ar_accept_date;
                $officerRemark = $prmo_texts->ar_accept_remark;

                $headEmpNo = $prmo_texts->head_user_id;
                $headDate = $prmo_texts->head_date;
                $preform =  $prmo_texts->preform;

                $skill = $prmo_texts->skills;
                if ($skill == 1) {
                    $skills = "Yes";
                } elseif ($skill == 2) {
                    $skills = "No";
                } else {
                    $skills = "null";
                }
                $sCom =  $prmo_texts->skill_details;

                $week = $prmo_texts->weaknesses;
                if ($week == 1) {
                    $weekness = "Yes";
                } elseif ($week == 2) {
                    $weekness = "No";
                } else {
                    $weekness = "null";
                }
                $wCom =  $prmo_texts->weakness_details;
                $otherCom =  $prmo_texts->head_remark;

                $rec = $prmo_texts->head_status;
                if ($rec == 2) {
                    $recC = "Yes";
                } elseif ($rec == 3) {
                    $recC = "No";
                } else {
                    $recC = "null";
                }
                $recNoC =  $prmo_texts->not_rec_reason;


                $dt = Carbon::create($year, 12, 31, 0);
                $age = Carbon::parse($bdate)->diff($dt)->format('%y years, %m months and %d days');

                $appDate = Carbon::parse($prmo_texts->created_at)->toDateString();
            }

            $empRecord = Promotion::join('categories', 'categories.id', '=', 'promotions.type_id')
                ->join('designations', 'designations.id', '=', 'promotions.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->select('promotions.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname')
                ->where('promotions.employee_no', '=', $empNo)
                ->orderBy('promotions.duty_assumed_date')
                ->get();

            $intTransfer = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                ->select('internal_transfers.*', 'departments.department_name')
                ->where('internal_transfers.emp_no', '=', $empNo)
                ->orderBy('internal_transfers.transfer_date')
                ->get();

            $exTransfer = ExternalTransfer::join('categories', 'categories.id', '=', 'external_transfers.status')
                ->join('designations', 'designations.id', '=', 'external_transfers.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->join('universities', 'universities.id', '=', 'external_transfers.uni_institute')
                ->select('external_transfers.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname', 'universities.uni_name')
                ->where('external_transfers.emp_no', '=', $empNo)
                ->orderBy('external_transfers.effective_date')
                ->get();

            $leave = Leave::where('leaves.emp_no', '=', $empNo)->orderBy('leaves.year', 'DESC')->get();

            $commend = Commendation::join('categories', 'categories.id', '=', 'commendations.type_id')
                ->select('commendations.*', 'categories.category_name')
                ->where('commendations.emp_no', '=', $empNo)
                ->orderByDesc('commendations.letter_date')
                ->get();

            $checkD = Employee::select('initials', 'last_name')->where('employee_no', '=', $checkUserID)->get();

            $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                ->where('increments.emp_no', $empNo)
                ->orderBy('increments.effective_date', 'DESC')
                ->get();

            if ($checkD->count() > 0) {
                foreach ($checkD as $checkDs) {
                    $checkUser = $checkDs->initials . " " . $checkDs->last_name;
                }
            } else {
                $checkUser = "";
            }

            $officer = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_no', '=', $officerEmpNo)
                ->get();

            if ($officer->count() > 0) {
                foreach ($officer as $officers) {
                    $officerName = $officers->category_name . " " . $officers->initials . " " . $officers->last_name;
                }
            } else {
                $officerName = "";
            }

            $deph = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_no', '=', $headEmpNo)
                ->get();

            if ($deph->count() > 0) {
                foreach ($deph as $dephs) {
                    $depHead = $dephs->category_name . " " . $dephs->initials . " " . $dephs->last_name;
                }
            } else {
                $depHead = "";
            }
        } else {
            $empNo = "";
            $refNo = "";
            $year = "";
            $nic = "";
            $bdate = "";
            $name = "";
            $fName = "";
            $fAppDate = "";
            $cAppDate = "";
            $retirmentDate = "";
            $dep = "";
            $desig = "";
            $promoDesig = "";
            $age = "";
            $appDate = "";
            $empRecord = "";
            $intTransfer = "";
            $exTransfer = "";
            $leave = "";
            $commend = "";
            $checkUser = "";
            $checkDate = "";
            $checkRemark = "";
            $depHead = "";
            $empEmail = "";
            $officerName = "";
            $officerDate = "";
            $officerRemark = "";
            $headEmpNo = "";

            $headDate = "";
            $preform = "";
            $skills = "";
            $sCom = "";
            $weekness = "";
            $wCom = "";
            $otherCom = "";
            $recC = "";
            $recNoC = "";
            $increments = "";
        }

        return view('admin.promotion.nonAcademic.interviewPendingDetails', compact(
            'empNo',
            'refNo',
            'year',
            'nic',
            'bdate',
            'name',
            'fName',
            'fAppDate',
            'cAppDate',
            'retirmentDate',
            'dep',
            'desig',
            'promoDesig',
            'age',
            'appDate',
            'empRecord',
            'intTransfer',
            'exTransfer',
            'leave',
            'commend',
            'checkUser',
            'checkDate',
            'checkRemark',
            'officerName',
            'officerDate',
            'officerRemark',
            'depHead',
            'empEmail',
            'headEmpNo',
            'headDate',
            'preform',
            'skills',
            'sCom',
            'weekness',
            'wCom',
            'otherCom',
            'recC',
            'recNoC',
            'increments'
        ));
    }

    public function pendingAll(Request $request)
    {
        $type = $request->type;

        if ($type == 1) {

            $year = date("Y") - 1;

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                ->join('employees as sc', 'sc.employee_no', '=', 'employees.assign_ma_user_id')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id', 'sc.initials as rIni', 'sc.last_name as rLast',)
                ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.check_status', '=', 0)
                ->orderBy('promotion_applications_nonacademics.check_date')
                ->get();
        } else if ($type == 2) {

            $year = date("Y") - 1;

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                //->join('employees as sc', 'sc.employee_no', '=', 'employees.assign_ma_user_id')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id')
                ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.ar_accept_status', '=', 1)
                ->orderBy('promotion_applications_nonacademics.check_date')
                ->get();
        } else if ($type == 3) {

            $year = date("Y") - 1;

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('department_heads', 'departments.id', '=', 'department_heads.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                ->join('employees as hu', 'hu.employee_no', '=', 'department_heads.emp_no')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id', 'hu.initials as rIni', 'hu.last_name as rLast',)
                ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.head_status', '=', 1)
                ->orderBy('promotion_applications_nonacademics.check_date')
                ->get();
        } else if ($type == 4) {

            $year = date("Y") - 1;

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                ->join('employees as sc', 'sc.employee_no', '=', 'employees.assign_ma_user_id')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id', 'sc.initials as rIni', 'sc.last_name as rLast')
                ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.interview_status', '=', 1)
                ->orderBy('promotion_applications_nonacademics.check_date')
                ->get();
        } else {

            $data_text = array();
        }


        return view('admin.promotion.nonAcademic.pending_all', compact('data_text', 'type'));
    }

    public function interviewAssignedOpen(Request $request)
    {
        $year = date("Y") - 1;
        $mainBranch = Auth()->user()->main_branch_id;

        if (isset($request->panel)) {

            if ($request->panel > 0) {

                $confText = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                    ->join('designations', 'designations.id', '=', 'interview_panels.designation_id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('interview_panels.*', 'type.category_name as interType', 'designations.designation_name', 'categories.category_name')
                    ->where('interview_panels.id', '=', $request->panel)
                    ->get();

                if ($confText->count() > 0) {
                    foreach ($confText as $confTexts) {
                        $pnID = $confTexts->id;
                        $pID = $confTexts->panel_id;
                        $pType = $confTexts->interType;
                        $design = $confTexts->designation_name . " " . $confTexts->category_name;
                        $pDate = $confTexts->interview_date;
                        $pTime = $confTexts->interview_time;
                        $pVenue = $confTexts->venue;
                        $desigID = $confTexts->designation_id;
                    }

                    $pMember = interviewPanelMember::where('panel_id', $confTexts->panel_id)->get();
                    $interviewPanelApplicants = PromotionApplicationsNonacademic::join('employees', 'promotion_applications_nonacademics.emp_no', '=', 'employees.employee_no')
                        ->join('departments', 'employees.department_id', '=', 'departments.id')
                        ->select('promotion_applications_nonacademics.*', 'employees.email', 'departments.department_name')
                        ->where('interview_board_no', $pnID)->get();

                    if ($pMember->count() > 0) {
                        $canAdd = 1;
                    } else {
                        $canAdd = 0;
                    }

                    $currentPanel = $request->panel;
                } else {
                    $pID = "";
                    $pnID = "";
                    $pType = "";
                    $design = "";
                    $pDate = "";
                    $pTime = "";
                    $pVenue = "";
                    $pMember = array();
                    $canAdd = 0;
                    $desigID = 0;
                    $currentPanel = 0;
                    $interviewPanelApplicants = array();
                }
            } else {
                $pID = "";
                $pnID = "";
                $pType = "";
                $design = "";
                $pDate = "";
                $pTime = "";
                $pVenue = "";
                $pMember = array();
                $canAdd = 0;
                $desigID = 0;
                $currentPanel = 0;
                $interviewPanelApplicants = array();
            }
        } else {
            $pID = "";
            $pnID = "";
            $pType = "";
            $design = "";
            $pDate = "";
            $pTime = "";
            $pVenue = "";
            $pMember = array();
            $canAdd = 0;
            $desigID = 0;
            $currentPanel = 0;
            $interviewPanelApplicants = array();
        }

        if ($mainBranch == 51) {

            $panelText = InterviewPanel::select('id', 'panel_id')->where('interview_panels.interview_date', '>=', today())->get();

            $eList = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'promotion_applications_nonacademics.id')
                // ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.interview_board_no', '=', NULL)
                ->where('promotion_applications_nonacademics.interview_status', '=', 1)
                ->where('promotion_applications_nonacademics.promotion_designation_id', '=', $desigID)
                ->get();
        } elseif ($mainBranch == 53) {
            # code...
            if (Auth()->user()->hasRole(['est-head'])) {

                $panelText = InterviewPanel::select('id', 'panel_id')->where('interview_panels.interview_date', '>=', today())->get();

                $eList = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'promotion_applications_nonacademics.id')
                    // ->where('promotion_applications_nonacademics.year', '=', $year)
                    ->where('promotion_applications_nonacademics.interview_board_no', '=', NULL)
                    ->where('promotion_applications_nonacademics.interview_status', '=', 1)
                    ->where('promotion_applications_nonacademics.promotion_designation_id', '=', $desigID)
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $panelText = InterviewPanel::select('id', 'panel_id')
                    ->where('interview_panels.interview_date', '>=', today())
                    ->where('interview_panels.user_id', '=', auth()->user()->employee_no)
                    ->get();

                $eList = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'promotion_applications_nonacademics.id')
                    // ->where('promotion_applications_nonacademics.year', '=', $year)
                    ->where('promotion_applications_nonacademics.interview_board_no', '=', NULL)
                    ->where('promotion_applications_nonacademics.interview_status', '=', 1)
                    ->where('employees.assign_ma_user_id', '=', auth()->user()->employee_no)
                    ->where('promotion_applications_nonacademics.promotion_designation_id', '=', $desigID)
                    ->get();
            }
        } else {

            $panelText = array();
            $eList = array();
        }

        return view('admin.promotion.nonAcademic.interview_assigned', compact(
            'panelText',
            'pID',
            'pnID',
            'pType',
            'design',
            'pDate',
            'pTime',
            'pVenue',
            'pMember',
            'canAdd',
            'eList',
            'currentPanel',
            'interviewPanelApplicants'
        ));
    }

    public function interviewAssignedStore(Request $request)
    {
        if ($request->interview_applicant != null) {

            for ($i = 0; $i < count($request->interview_applicant); $i++) {

                $effDate = Carbon::createFromFormat('d-M-Y', $request->effDate[$i])->toDateString();

                $promotionApplication = PromotionApplicationsNonacademic::find($request->interview_applicant[$i]);

                if ($promotionApplication) {

                    $promotionApplication->interview_board_no = $request->interview_panel_id;
                    $promotionApplication->interview_date = $request->interview_date;
                    $promotionApplication->effective_date = $effDate;
                    $promotionApplication->effect_date_note = $request->effNote[$i];
                    $promotionApplication->save();


                    Log::notice('NonAcademicPromotionController -> promotion application interview panel set. Promotion ID: ' . $request->interview_applicant[$i] . ' date set by employee ' . auth()->user()->employee_no);
                } else {

                    Log::warning('Promotion application not found for ID: ' . $request->interview_applicant[$i]);
                }
            }

            $notification = array(
                'message' => 'Promotion Applicatnt added to Interview Panel',
                'alert-type' => 'success'
            );

            return redirect()->route('interview.assigned.open', ['panel' => $request->interview_panel_id])->with($notification);
        } else {

            $notification = array(
                'message' => 'Please Select at least one Applicant to Interview Panel',
                'alert-type' => 'error'
            );

            return redirect()->route('interview.assigned.open', ['panel' => $request->interview_panel_id])->with($notification);
        }

        //return redirect()->route('interview.assigned.open')->with($notification);
    }

    public function interviewAssignedDelete($id)
    {

        //Log::info('BondController -> bond data delete started');
        $promotionId = decrypt($id);
        $interviewApplicant = PromotionApplicationsNonacademic::find($promotionId);

        $originalValues = $interviewApplicant->fresh();

        $interviewApplicant->interview_board_no = NULL;
        $interviewApplicant->interview_date = NULL;
        $interviewApplicant->save();

        $previousValue = $originalValues->interview_board_no;

        $notification = array(
            'message' => 'Interview Panel Applicant Removed',
            'alert-type' => 'error'
        );

        return redirect()->route('interview.assigned.open', ['panel' => $previousValue])->with($notification);
    }

    public function promoLetterOpen()
    {


        $typeText = Category::where('categories.category_type_id', '=', 38)->get();

        if (Auth()->user()->hasRole(['super-admin', 'administrator', 'est-head'])) {

            $panelText = InterviewPanel::select('id', 'panel_id')
                ->where('interview_panels.interview_date', '>=', today())
                ->get();
        } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

            $panelText = InterviewPanel::select('id', 'panel_id')
                ->where('interview_panels.interview_date', '>=', today())
                ->where('interview_panels.user_id', '=', auth()->user()->employee_no)
                ->get();
        } else {

            $panelText = array();
        }

        $pID = "";
        $pnID = "";
        $pType = "";
        $design = "";
        $pDate = "";
        $pTime = "";
        $pVenue = "";
        $pMember = array();
        $canAdd = 1;
        $desigID = 0;
        $currentPanel = 0;
        $interviewPanelApplicants = array();

        return view('admin.promotion.nonAcademic.promoLetters', compact(
            'typeText',
            'panelText',
            'pID',
            'pnID',
            'pType',
            'design',
            'pDate',
            'pTime',
            'pVenue',
            'pMember',
            'canAdd',
            'currentPanel',
            'interviewPanelApplicants'
        ));
    }

    public function LetterPanelMemeberAppSearc(Request $request)
    {

        $pID = $request->panel_id;

        if ($pID > 0) {

            $confText = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                ->join('designations', 'designations.id', '=', 'interview_panels.designation_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('interview_panels.*', 'type.category_name as interType', 'designations.designation_name', 'categories.category_name')
                ->where('interview_panels.id', '=', $pID)
                ->get();

            if ($confText->count() > 0) {
                foreach ($confText as $confTexts) {
                    $pnID = $confTexts->id;
                    $pID = $confTexts->panel_id;
                    $pType = $confTexts->interType;
                    $design = $confTexts->designation_name . " " . $confTexts->category_name;
                    $pDate = date("d-M-Y", strtotime($confTexts->interview_date));
                    $pTime = $confTexts->interview_time;
                    $pVenue = $confTexts->venue;
                }

                $pMember = interviewPanelMember::where('panel_id', $confTexts->panel_id)->get();

                $interviewPanelApplicants = PromotionApplicationsNonacademic::join('employees', 'promotion_applications_nonacademics.emp_no', '=', 'employees.employee_no')
                    ->join('departments', 'employees.department_id', '=', 'departments.id')
                    ->select('promotion_applications_nonacademics.*', 'employees.email', 'departments.department_name')
                    ->where('interview_board_no', $pnID)->get();

                if ($pMember->count() > 0) {
                    $canAdd = 1;
                } else {
                    $canAdd = 0;
                }

                $currentPanel = $request->panel;
            } else {
                $pID = "";
                $pnID = "";
                $pType = "";
                $design = "";
                $pDate = "";
                $pTime = "";
                $pVenue = "";
                $pMember = array();
                $canAdd = 0;
            }
        } else {
            $pID = "";
            $pnID = "";
            $pType = "";
            $design = "";
            $pDate = "";
            $pTime = "";
            $pVenue = "";
            $pMember = array();
            $canAdd = 0;
        }


        return json_encode([
            "panel_id" => $pID,
            "venue" => $pVenue,
            "int_type" => $pType,
            "desig" => $design,
            "int_date" => $pDate,
            "int_time" => $pTime,
            "pmeber" => $pMember
        ]);
    }

    public function panelMembAppLetterPrint(Request $request)
    {

        $pID = $request->pID;
        $design = $request->intDesig;
        $pDate = date("d F,Y", strtotime($request->intDate));
        $pTime = $request->intTime;
        $pVenue = $request->venue;

        $head_posi_text = DepartmentHead::join('categories', 'categories.id', '=', 'department_heads.head_position')
            ->where('department_heads.department_id', 5004)->get();

        if (count($head_posi_text) > 0) {
            foreach ($head_posi_text as $head_posi_texts) {
                $ex_position = $head_posi_texts->category_name;
            }
        } else {
            $ex_position = '';
        }

        $pMember = interviewPanelMember::where('panel_id', $request->pID)->get();

        if ($pMember->count() > 0) {

            $data = [
                'desig' => $design,
                'pdate' => $pDate,
                'ptime' => $pTime,
                'pvenue' => $pVenue,
                'pMembers' => $pMember,
                'exPosition' => $ex_position

            ];

            $pdf = FacadePdf::loadView('admin.promotion.nonAcademic.appLetterPM', $data)->setPaper('a4');

            return $pdf->stream('Interview Panle Member Letter.pdf');
        }
    }

    public function int_shedule_open(Request $request)
    {
        $checkDate = '2024-01-01';

        if (Auth()->user()->hasRole(['super-admin', 'administrator', 'est-head'])) {

            $panelText = InterviewPanel::select('id', 'panel_id')
                ->where('interview_panels.interview_date', '>=', $checkDate)
                ->get();
        } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

            $panelText = InterviewPanel::select('id', 'panel_id')
                ->where('interview_panels.interview_date', '>=', $checkDate)
                ->where('interview_panels.user_id', '=', auth()->user()->employee_no)
                ->get();
        } else {

            $panelText = array();
        }

        if (isset($request->panel)) {

            $panel = $request->panel;

            $intText = InterviewPanel::join('designations', 'designations.id', '=', 'interview_panels.designation_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('interview_panels.*', 'designations.designation_name', 'categories.category_name')
                ->where('interview_panels.id', '=', $request->panel)
                ->get();

            if ($intText->count() > 0) {
                foreach ($intText as $intTexts) {
                    $pID = $intTexts->panel_id;
                    $design = $intTexts->designation_name . " " . $intTexts->category_name;
                    $pDate = date("d-M-Y", strtotime($intTexts->interview_date));
                    $pTime = $intTexts->interview_time;
                    $proDesigID = $intTexts->designation_id;
                }

                $pMemb = interviewPanelMember::where('panel_id', '=', $pID)->where('status', '=', 1)->get();

                $interviewee = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                    ->join('categories', 'categories.id', '=', 'employees.title_id')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'categories.category_name')
                    ->where('promotion_applications_nonacademics.interview_board_no', '=', $request->panel)
                    ->where('promotion_applications_nonacademics.interview_status', '=', 1)
                    ->get();

                $promoD = promotionDuration::join('designations', 'designations.id', '=', 'promotion_durations.designation_id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->select('promotion_durations.*', 'designations.designation_name', 'categories.category_name')
                    ->where('promotion_durations.promo_desig_id', '=', $proDesigID)
                    ->get();

                if ($promoD->count() > 0) {
                    foreach ($promoD as $promoDs) {
                        $Cdesign = $promoDs->designation_name . " " . $promoDs->category_name;
                        $deuration = $promoDs->duration;
                        if ($promoDs->trade_test == 0) {
                            $sMethod = "Selection by an interview";
                        } elseif ($promoDs->trade_test == 1) {
                            $sMethod = "Selection by test and interview";
                        } else {
                            $sMethod = "";
                        }
                        $basPromo = "{$Cdesign} who has been confirmed in their post and Having completed {$deuration} years of satisfactory service in the post of {$Cdesign}";
                    }
                } else {
                    $Cdesign = "";
                    $deuration = "";
                    $sMethod = "";
                    $basPromo = "";
                }
            } else {
                $pID = "";
                $design = "";
                $pDate = "";
                $pTime = "";
                $panel = "";
                $pMemb = array();
                $interviewee = array();
                $Cdesign = "";
                $deuration = "";
                $sMethod = "";
                $basPromo = "";
            }
        } else {
            $pID = "";
            $design = "";
            $pDate = "";
            $pTime = "";
            $panel = "";
            $pMemb = array();
            $interviewee = array();
            $Cdesign = "";
            $deuration = "";
            $sMethod = "";
            $basPromo = "";
        }

        return view('admin.promotion.nonAcademic.interview_shedule', compact(
            'panelText',
            'pID',
            'design',
            'pDate',
            'pTime',
            'pMemb',
            'interviewee',
            'panel',
            'Cdesign',
            'deuration',
            'sMethod',
            'basPromo'
        ));
    }

    public function int_shedule_print(Request $request)
    {

        if ($request->pID != "") {

            $intText = InterviewPanel::join('designations', 'designations.id', '=', 'interview_panels.designation_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('interview_panels.*', 'designations.designation_name', 'categories.category_name')
                ->where('interview_panels.id', '=', $request->panel)
                ->get();

            if ($intText->count() > 0) {
                foreach ($intText as $intTexts) {

                    $pID = $intTexts->panel_id;
                    $design = $intTexts->designation_name . " " . $intTexts->category_name;
                    $pDate = $intTexts->interview_date;
                    $pTime = $intTexts->interview_time;
                }

                $pMemb = interviewPanelMember::where('panel_id', '=', $pID)->where('status', '=', 1)->orderBy('m_type')->get();

                $interviewee = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                    ->join('categories', 'categories.id', '=', 'employees.title_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
                    ->select(
                        'employees.employee_no',
                        'employees.initials',
                        'employees.last_name',
                        'employees.current_appointment_date',
                        'categories.category_name',
                        'departments.department_name',
                        'designations.designation_name',
                        'g.category_name as grade',
                        'promotion_applications_nonacademics.head_status',
                        'promotion_applications_nonacademics.current_desigantion_id',
                        'promotion_applications_nonacademics.effective_date',
                        'promotion_applications_nonacademics.effect_date_note',
                        'promotion_applications_nonacademics.promotion_designation_id',
                        'promotion_applications_nonacademics.id'
                    )
                    ->where('promotion_applications_nonacademics.interview_board_no', '=', $request->panel)
                    ->where('promotion_applications_nonacademics.interview_status', '=', 1)
                    ->get();

                $qulifi = $request->basisPromo;
                $sMethod = $request->sMethod;
                $panelID = $request->pID;

                $data = [

                    'desig' => $design,
                    'pdate' => $pDate,
                    'ptime' => $pTime,
                    'interviewee' => $interviewee,
                    'qulifi' => $qulifi,
                    'sMethod' => $sMethod,
                    'panelID' => $panelID

                ];

                // $contxt = stream_context_create([
                //     'ssl' => [
                //         'verify_peer' => FALSE,
                //         'verify_peer_name' => FALSE,
                //         'allow_self_signed' => TRUE,
                //     ]
                // ]);

                // $pdf = FacadePdf::setOptions(['isHTML5ParserEnabled' => true, 'isRemoteEnabled' => true]);
                // $pdf->getDomPDF()->setHttpContext($contxt);

                $pdf = FacadePdf::loadView('admin.promotion.nonAcademic.int_shedule', $data)->setPaper('legal', 'landscape');

                return $pdf->stream('interview_shedule.pdf');
            } else {

                $notification = array(
                    'message' => 'Something wrong please try again',
                    'alert-type' => 'error'
                );

                return redirect()->route('nac.interview.shedule.open')->with($notification);
            }
        } else {

            $notification = array(
                'message' => 'Please search panel before print',
                'alert-type' => 'error'
            );

            return redirect()->route('nac.interview.shedule.open')->with($notification);
        }
    }

    public function LetterIntervieweeSearc(Request $request)
    {

        $pID = $request->panel_id;

        if ($pID > 0) {

            $confText = InterviewPanel::join('categories as type', 'type.id', '=', 'interview_panels.interview_type')
                ->join('designations', 'designations.id', '=', 'interview_panels.designation_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->select('interview_panels.*', 'type.category_name as interType', 'designations.designation_name', 'categories.category_name')
                ->where('interview_panels.id', '=', $pID)
                ->get();

            if ($confText->count() > 0) {
                foreach ($confText as $confTexts) {

                    $pnID = $confTexts->id;
                    $pID = $confTexts->panel_id;
                    $pType = $confTexts->interType;
                    $design = $confTexts->designation_name . " " . $confTexts->category_name;
                    $pDate = date("jS F Y", strtotime($confTexts->interview_date));
                    $pTime = $confTexts->interview_time;
                    $pVenue = $confTexts->venue;
                }

                $interviewPanelApplicants = PromotionApplicationsNonacademic::join('employees', 'promotion_applications_nonacademics.emp_no', '=', 'employees.employee_no')
                    ->join('departments', 'employees.department_id', '=', 'departments.id')
                    ->select('employees.last_name', 'employees.initials', 'departments.department_name')
                    ->where('interview_board_no', $pnID)->get();

                if ($interviewPanelApplicants->count() > 0) {
                    $canAdd = 1;
                } else {
                    $canAdd = 0;
                }

                $currentPanel = $request->panel;
            } else {
                $pID = "";
                $pnID = "";
                $pType = "";
                $design = "";
                $pDate = "";
                $pTime = "";
                $pVenue = "";
                $interviewPanelApplicants = array();
                $canAdd = 0;
            }
        } else {
            $pID = "";
            $pnID = "";
            $pType = "";
            $design = "";
            $pDate = "";
            $pTime = "";
            $pVenue = "";
            $interviewPanelApplicants = array();
            $canAdd = 0;
        }


        return json_encode([
            "panel_id" => $pID,
            "pn_id" => $pnID,
            "venue" => $pVenue,
            "int_type" => $pType,
            "desig" => $design,
            "int_date" => $pDate,
            "int_time" => $pTime,
            "pmeber" => $interviewPanelApplicants
        ]);
    }

    public function intervieweesLetterPrint(Request $request)
    {

        $pID = $request->panelIDInt;
        $design = $request->intDesigInt;
        $pDate = $request->intDateInt;
        $pTime = $request->intTimeInt;
        $pVenue = $request->venueInt;

        $head_posi_text = DepartmentHead::join('categories', 'categories.id', '=', 'department_heads.head_position')
            ->where('department_heads.department_id', 5004)->get();
        if (count($head_posi_text) > 0) {
            foreach ($head_posi_text as $head_posi_texts) {
                $ex_position = $head_posi_texts->category_name;
            }
        } else {
            $ex_position = '';
        }


        $pMember = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
            ->join('categories as t', 't.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'departments.faculty_code')
            ->join('department_heads', 'department_heads.department_id', '=', 'departments.id')
            ->join('categories as hp', 'hp.id', '=', 'department_heads.head_position')
            ->select(
                't.category_name as title',
                'employees.initials',
                'employees.last_name',
                'designations.designation_name',
                'g.category_name as grad',
                'departments.department_name',
                'faculties.id',
                'faculties.faculty_name',
                'hp.category_name as headPosition',
                'departments.id as depID',
                'department_heads.head_position as hpID',
                'departments.name_status',
            )
            ->where('promotion_applications_nonacademics.interview_board_no', '=', $pID)
            ->get();

        if ($pMember->count() > 0) {

            $data = [
                'desig' => $design,
                'pdate' => $pDate,
                'ptime' => $pTime,
                'pvenue' => $pVenue,
                'pMembers' => $pMember,
                'exPosition' => $ex_position
            ];

            $pdf = FacadePdf::loadView('admin.promotion.nonAcademic.intervieweesLetter', $data)->setPaper('a4');

            return $pdf->stream('interviewees_Letter.pdf');
        }
    }

    public function interviwResultsOpen(Request $request)
    {
        $year = date("Y") - 1;

        $threeMonthsAgo = Carbon::now()->subMonths(3);

        if (Auth()->user()->hasRole(['super-admin', 'administrator'])) {

            $panelText = InterviewPanel::join('designations', 'designations.id', '=', 'interview_panels.designation_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->select(
                    'interview_panels.id',
                    'interview_panels.panel_id',
                    'designations.designation_name',
                    'categories.category_name'
                )
                ->where('interview_panels.interview_date', '<=', today())
                //->where('interview_panels.interview_date', '>=', $threeMonthsAgo)
                ->get();
        } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

            $panelText = InterviewPanel::join('designations', 'designations.id', '=', 'interview_panels.designation_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->select(
                    'interview_panels.id',
                    'interview_panels.panel_id',
                    'designations.designation_name',
                    'categories.category_name'
                )
                ->where('interview_panels.interview_date', '<=', today())
                //->where('interview_panels.interview_date', '>=', $threeMonthsAgo)
                ->where('interview_panels.user_id', '=', auth()->user()->employee_no)
                ->get();
        } else {
            $panelText = array();
        }

        $pID = $request->panel;

        if ($pID > 0) {
            if (Auth()->user()->hasRole(['super-admin', 'administrator'])) {

                $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                    ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id')
                    ->where('promotion_applications_nonacademics.interview_board_no', '=', $pID)
                    ->where('promotion_applications_nonacademics.interview_status', '=', 1)
                    ->get();
            } else {

                $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                    ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                    ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id')
                    ->where('promotion_applications_nonacademics.interview_board_no', '=', $pID)
                    ->where('promotion_applications_nonacademics.interview_status', '=', 1)
                    ->where('employees.assign_ma_user_id', '=', auth()->user()->employee_no)
                    ->get();
            }
        } else {
            $data_text = array();
        }
        return view('admin.promotion.nonAcademic.interview_results', compact('panelText', 'data_text'));
    }

    public function interviewResultsDetails($id)
    {
        $appId = decrypt($id);

        $prmo_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
            ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
            ->join('categories as prefo', 'prefo.id', '=', 'promotion_applications_nonacademics.performance')
            ->select('employees.*', 'promotion_applications_nonacademics.*', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'prefo.category_name as preform', 'employees.increment_date as incriDate')
            ->where('promotion_applications_nonacademics.id', '=', $appId)
            ->get();

        if ($prmo_text->count() > 0) {
            foreach ($prmo_text as $prmo_texts) {

                $empNo = $prmo_texts->employee_no;
                $refNo = $prmo_texts->ref_no;
                $year = $prmo_texts->year;
                $nic = $prmo_texts->nic;
                $bdate = $prmo_texts->date_of_birth;
                $name = $prmo_texts->initials . " " . $prmo_texts->last_name;
                $fName = $prmo_texts->name_denoted_by_initials . " " . $prmo_texts->last_name;
                $fAppDate = $prmo_texts->initial_appointment_date;
                $cAppDate = $prmo_texts->current_appointment_date;
                $retirmentDate = $prmo_texts->retirement_date;
                $dep = $prmo_texts->department_name;
                $desig = $prmo_texts->designation_name . " " . $prmo_texts->category_name;
                $promoDesig = $prmo_texts->promoDesigName . " " . $prmo_texts->proGName;
                $checkUserID = $prmo_texts->check_user_id;
                $checkDate = $prmo_texts->check_date;
                $checkRemark = $prmo_texts->check_remark;
                $depID = $prmo_texts->department_id;
                $empEmail = $prmo_texts->email;

                $officerEmpNo = $prmo_texts->ar_user_id;
                $officerDate = $prmo_texts->ar_accept_date;
                $officerRemark = $prmo_texts->ar_accept_remark;

                $headEmpNo = $prmo_texts->head_user_id;
                $headDate = $prmo_texts->head_date;
                $preform =  $prmo_texts->preform;

                $skill = $prmo_texts->skills;
                if ($skill == 1) {
                    $skills = "Yes";
                } elseif ($skill == 2) {
                    $skills = "No";
                } else {
                    $skills = "null";
                }
                $sCom =  $prmo_texts->skill_details;

                $week = $prmo_texts->weaknesses;
                if ($week == 1) {
                    $weekness = "Yes";
                } elseif ($week == 2) {
                    $weekness = "No";
                } else {
                    $weekness = "null";
                }
                $wCom =  $prmo_texts->weakness_details;
                $otherCom =  $prmo_texts->head_remark;

                $rec = $prmo_texts->head_status;
                if ($rec == 2) {
                    $recC = "Yes";
                } elseif ($rec == 3) {
                    $recC = "No";
                } else {
                    $recC = "null";
                }
                $recNoC =  $prmo_texts->not_rec_reason;

                $pID = $prmo_texts->interview_board_no;
                $effectiveD = $prmo_texts->effective_date;


                $dt = Carbon::create($year, 12, 31, 0);
                $age = Carbon::parse($bdate)->diff($dt)->format('%y years, %m months and %d days');

                $appDate = Carbon::parse($prmo_texts->created_at)->toDateString();

                $incerment_date = date("m-d", strtotime($prmo_texts->incriDate));
                //$incerment_date = '2023-10-15';

                $desigID = $prmo_texts->current_desigantion_id;
                $promoDesigID = $prmo_texts->promotion_designation_id;
            }

            $empRecord = Promotion::join('categories', 'categories.id', '=', 'promotions.type_id')
                ->join('designations', 'designations.id', '=', 'promotions.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->select('promotions.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname')
                ->where('promotions.employee_no', '=', $empNo)
                ->orderBy('promotions.duty_assumed_date')
                ->get();

            $intTransfer = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                ->select('internal_transfers.*', 'departments.department_name')
                ->where('internal_transfers.emp_no', '=', $empNo)
                ->orderBy('internal_transfers.transfer_date')
                ->get();

            $exTransfer = ExternalTransfer::join('categories', 'categories.id', '=', 'external_transfers.status')
                ->join('designations', 'designations.id', '=', 'external_transfers.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->join('universities', 'universities.id', '=', 'external_transfers.uni_institute')
                ->select('external_transfers.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname', 'universities.uni_name')
                ->where('external_transfers.emp_no', '=', $empNo)
                ->orderBy('external_transfers.effective_date')
                ->get();

            $leave = Leave::where('leaves.emp_no', '=', $empNo)->orderBy('leaves.year', 'DESC')->get();

            $commend = Commendation::join('categories', 'categories.id', '=', 'commendations.type_id')
                ->select('commendations.*', 'categories.category_name')
                ->where('commendations.emp_no', '=', $empNo)
                ->orderByDesc('commendations.letter_date')
                ->get();

            $checkD = Employee::select('initials', 'last_name')->where('employee_no', '=', $checkUserID)->get();

            $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                ->where('increments.emp_no', $empNo)
                ->orderBy('increments.effective_date', 'DESC')
                ->get();

            if ($checkD->count() > 0) {
                foreach ($checkD as $checkDs) {
                    $checkUser = $checkDs->initials . " " . $checkDs->last_name;
                }
            } else {
                $checkUser = "";
            }

            $officer = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_no', '=', $officerEmpNo)
                ->get();

            if ($officer->count() > 0) {
                foreach ($officer as $officers) {
                    $officerName = $officers->category_name . " " . $officers->initials . " " . $officers->last_name;
                }
            } else {
                $officerName = "";
            }

            $deph = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_no', '=', $headEmpNo)
                ->get();

            if ($deph->count() > 0) {
                foreach ($deph as $dephs) {
                    $depHead = $dephs->category_name . " " . $dephs->initials . " " . $dephs->last_name;
                }
            } else {
                $depHead = "";
            }

            $IntPanel = InterviewPanel::where('id', '=', $pID)->get();

            if ($IntPanel->count() > 0) {
                foreach ($IntPanel as $IntPanels) {

                    $int_date = $IntPanels->interview_date;
                    $int_time = date('h:i a', strtotime($IntPanels->interview_time));
                    $venue = $IntPanels->venue;
                    $Panel_code = $IntPanels->panel_id;
                }
            } else {
                $int_date = "";
                $int_time = "";
                $venue = "";
                $Panel_code = "";
            }

            $pMember = interviewPanelMember::join('categories as type', 'type.id', '=', 'interview_panel_members.member_type')
                ->join('categories as mtype', 'mtype.id', '=', 'interview_panel_members.m_type')
                ->select('interview_panel_members.*', 'type.category_name as type_name', 'mtype.category_name as mtype_name')
                ->where('interview_panel_members.panel_id', '=', $Panel_code)
                ->where('interview_panel_members.status', '=', 1)
                ->orderBy('interview_panel_members.m_type')
                ->get();

            $pro_duration = promotionDuration::where('designation_id', '=', $desigID)
                ->where('promo_desig_id', '=', $promoDesigID)
                ->get();

            if ($pro_duration->count() > 0) {
                foreach ($pro_duration as $pro_durations) {
                    $tTest = $pro_durations->trade_test;
                }
            } else {
                $tTest = 0;
            }

            $promo_exam = PromotionApplicationsNonacademic::join('exam_boards', 'exam_boards.id', '=', 'promotion_applications_nonacademics.exam_id')
                ->join('employees', 'employees.employee_no', '=', 'exam_boards.examiner_id')
                ->join('categories', 'categories.id', '=', 'employees.title_id')
                ->select(
                    'promotion_applications_nonacademics.*',
                    'exam_boards.*',
                    'categories.category_name',
                    'employees.initials',
                    'employees.last_name'
                )
                ->where('promotion_applications_nonacademics.id', '=', $appId)
                ->get();

            if ($promo_exam->count() > 0) {
                foreach ($promo_exam as $promo_exams) {

                    $exam_date = $promo_exams->exam_date;
                    $exam_time = $promo_exams->exam_time;
                    $exam_venue = $promo_exams->exam_venue;
                    $exam_ref_no = $promo_exams->board_ref_no;
                    $examiner_name = $promo_exams->category_name . " " . $promo_exams->initials . " " . $promo_exams->last_name;
                    $results_date = $promo_exams->exam_date;
                    $mark1 = $promo_exams->exam_marks1;
                    $mark2 = $promo_exams->exam_marks2;
                }
            } else {
                $exam_date = "";
                $exam_time = "";
                $exam_venue = "";
                $exam_ref_no = "";
                $examiner_name = "";
                $results_date = "";
                $mark1 = 10000;
                $mark2 = 10000;
            }

            if ($mark1 == 10000) {
                $result1 = "Not applicable";
            } else {
                if ($mark1 == 20000) {
                    $result1 = "Not applicable";
                } else {
                    $result1 = $mark1;
                }
            }

            if ($mark2 == 10000) {
                $result2 = "Not applicable";
            } else {
                if ($mark2 == 20000) {
                    $result2 = "Not applicable";
                } else {
                    $result2 = $mark2;
                }
            }

            $salType = Category::where('category_type_id', 50)->get();
        } else {
            $empNo = "";
            $refNo = "";
            $year = "";
            $nic = "";
            $bdate = "";
            $name = "";
            $fName = "";
            $fAppDate = "";
            $cAppDate = "";
            $retirmentDate = "";
            $dep = "";
            $desig = "";
            $promoDesig = "";
            $age = "";
            $appDate = "";
            $empRecord = [];
            $intTransfer = [];
            $exTransfer = [];
            $leave = [];
            $commend = [];
            $checkUser = "";
            $checkDate = "";
            $checkRemark = "";
            $depHead = "";
            $empEmail = "";
            $officerName = "";
            $officerDate = "";
            $officerRemark = "";
            $headEmpNo = "";

            $headDate = "";
            $preform = "";
            $skills = "";
            $sCom = "";
            $weekness = "";
            $wCom = "";
            $otherCom = "";
            $recC = "";
            $recNoC = "";
            $increments = [];

            $effectiveD = "";
            $int_date = "";
            $int_time = "";
            $venue = "";
            $Panel_code = "";

            $pMember = array();

            $incerment_date = "";

            $tTest = 0;

            $exam_date = "";
            $exam_time = "";
            $exam_venue = "";
            $exam_ref_no = "";
            $examiner_name = "";
            $results_date = "";
            $result1 = "";
            $result2 = "";

            $salType = array();
        }

        return view('admin.promotion.nonAcademic.interviewResultsDetails', compact(
            'empNo',
            'refNo',
            'year',
            'nic',
            'bdate',
            'name',
            'fName',
            'fAppDate',
            'cAppDate',
            'retirmentDate',
            'dep',
            'desig',
            'promoDesig',
            'age',
            'appDate',
            'empRecord',
            'intTransfer',
            'exTransfer',
            'leave',
            'commend',
            'checkUser',
            'checkDate',
            'checkRemark',
            'officerName',
            'officerDate',
            'officerRemark',
            'depHead',
            'empEmail',
            'headEmpNo',
            'headDate',
            'preform',
            'skills',
            'sCom',
            'weekness',
            'wCom',
            'otherCom',
            'recC',
            'recNoC',
            'increments',
            'effectiveD',
            'int_date',
            'int_time',
            'venue',
            'Panel_code',
            'pMember',
            'incerment_date',
            'tTest',
            'exam_date',
            'exam_time',
            'exam_venue',
            'exam_ref_no',
            'examiner_name',
            'results_date',
            'result1',
            'result2',
            'appId',
            'salType'
        ));
    }

    public function interviewResultStore(Request $request)
    {

        if ($request->promoId != "") {
            if (is_numeric($request->iniBSal)) {
                if ($request->increment_date != "") {
                    if (is_numeric($request->BSal)) {

                        $promotionApplication = PromotionApplicationsNonacademic::find($request->promoId);

                        if ($promotionApplication) {

                            $promotionApplication->interview_status = 2;
                            $promotionApplication->initial_bSal = $request->iniBSal;
                            $promotionApplication->current_bSal = $request->BSal;
                            $promotionApplication->increment_date = date("m-d", strtotime($request->increment_date));
                            $promotionApplication->inter_results_inter_user_id = auth()->user()->employee_no;
                            $promotionApplication->inter_results_enter_date = today();
                            $promotionApplication->interview_remark = $request->intRemark;
                            $promotionApplication->duty_assumed_status = 1;
                            $promotionApplication->save();
                        }

                        if ($request->promoType != null) {
                            for ($i = 0; $i < count($request->promoType); $i++) {

                                $salStep = new promoSalStep();
                                $salStep->promo_id = $request->promoId;
                                $salStep->emp_no = $request->emp_no;
                                $salStep->type_id = $request->promoType[$i];
                                $salStep->ass_date = date("Y-m-d", strtotime($request->assumedDate[$i]));
                                $salStep->sal_step = $request->promoBSal[$i];
                                $salStep->increment_val = $request->incremetVal[$i];
                                $salStep->enter_user_id = auth()->user()->employee_no;
                                $salStep->save();
                            }
                        }

                        $notification = array(
                            'message' => 'Interview results details save successfully',
                            'alert-type' => 'success'
                        );

                        return redirect()->route('interview.results.open')->with($notification);
                    } else {
                        $notification = array(
                            'message' => 'Please enter current basic salary',
                            'alert-type' => 'error'
                        );

                        return redirect()->route('nac.promo.interview.results.detais', encrypt($request->promoId))->with($notification);
                    }
                } else {
                    $notification = array(
                        'message' => 'Please enter increment date',
                        'alert-type' => 'error'
                    );

                    return redirect()->route('nac.promo.interview.results.detais', encrypt($request->promoId))->with($notification);
                }
            } else {
                $notification = array(
                    'message' => 'Please enter initial basic salary',
                    'alert-type' => 'error'
                );

                return redirect()->route('nac.promo.interview.results.detais', encrypt($request->promoId))->with($notification);
            }
        } else {
            $notification = array(
                'message' => 'Something wrong please try again',
                'alert-type' => 'error'
            );
            //route('nac.promo.interview.results.detais', encrypt($data_texts->id))
            return redirect()->route('interview.results.open')->with($notification);
        }
    }

    public function examPendingOpen()
    {

        $year = date("Y") - 1;

        if (Auth()->user()->hasRole(['cc', 'sc'])) {

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id')
                ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.exam_status', '=', 1)
                ->where('employees.assign_ma_user_id', '=', auth()->user()->employee_no)
                ->orderBy('promotion_applications_nonacademics.head_date')
                ->get();
        } else if (Auth()->user()->hasRole(['super-admin', 'administrator', 'est-head'])) {

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'promotion_applications_nonacademics.id')
                ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.exam_status', '=', 1)
                ->orderBy('promotion_applications_nonacademics.head_date')
                ->get();
        } else {

            $data_text = array();
        }


        return view('admin.promotion.nonAcademic.interviewPending', compact('data_text'));
    }

    public function examPendingDetails($id)
    {
        $appId = decrypt($id);

        $prmo_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
            ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
            ->join('categories as prefo', 'prefo.id', '=', 'promotion_applications_nonacademics.performance')
            ->select('employees.*', 'promotion_applications_nonacademics.*', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'prefo.category_name as preform')
            ->where('promotion_applications_nonacademics.id', '=', $appId)
            ->get();

        if ($prmo_text->count() > 0) {
            foreach ($prmo_text as $prmo_texts) {
                $empNo = $prmo_texts->employee_no;
                $refNo = $prmo_texts->ref_no;
                $year = $prmo_texts->year;
                $nic = $prmo_texts->nic;
                $bdate = $prmo_texts->date_of_birth;
                $name = $prmo_texts->initials . " " . $prmo_texts->last_name;
                $fName = $prmo_texts->name_denoted_by_initials . " " . $prmo_texts->last_name;
                $fAppDate = $prmo_texts->initial_appointment_date;
                $cAppDate = $prmo_texts->current_appointment_date;
                $retirmentDate = $prmo_texts->retirement_date;
                $dep = $prmo_texts->department_name;
                $desig = $prmo_texts->designation_name . " " . $prmo_texts->category_name;
                $promoDesig = $prmo_texts->promoDesigName . " " . $prmo_texts->proGName;
                $checkUserID = $prmo_texts->check_user_id;
                $checkDate = $prmo_texts->check_date;
                $checkRemark = $prmo_texts->check_remark;
                $depID = $prmo_texts->department_id;
                $empEmail = $prmo_texts->email;

                $officerEmpNo = $prmo_texts->ar_user_id;
                $officerDate = $prmo_texts->ar_accept_date;
                $officerRemark = $prmo_texts->ar_accept_remark;

                $headEmpNo = $prmo_texts->head_user_id;
                $headDate = $prmo_texts->head_date;
                $preform =  $prmo_texts->preform;

                $skill = $prmo_texts->skills;
                if ($skill == 1) {
                    $skills = "Yes";
                } elseif ($skill == 2) {
                    $skills = "No";
                } else {
                    $skills = "null";
                }
                $sCom =  $prmo_texts->skill_details;

                $week = $prmo_texts->weaknesses;
                if ($week == 1) {
                    $weekness = "Yes";
                } elseif ($week == 2) {
                    $weekness = "No";
                } else {
                    $weekness = "null";
                }
                $wCom =  $prmo_texts->weakness_details;
                $otherCom =  $prmo_texts->head_remark;

                $rec = $prmo_texts->head_status;
                if ($rec == 2) {
                    $recC = "Yes";
                } elseif ($rec == 3) {
                    $recC = "No";
                } else {
                    $recC = "null";
                }
                $recNoC =  $prmo_texts->not_rec_reason;


                $dt = Carbon::create($year, 12, 31, 0);
                $age = Carbon::parse($bdate)->diff($dt)->format('%y years, %m months and %d days');

                $appDate = Carbon::parse($prmo_texts->created_at)->toDateString();
            }

            $empRecord = Promotion::join('categories', 'categories.id', '=', 'promotions.type_id')
                ->join('designations', 'designations.id', '=', 'promotions.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->select('promotions.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname')
                ->where('promotions.employee_no', '=', $empNo)
                ->orderBy('promotions.duty_assumed_date')
                ->get();

            $intTransfer = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                ->select('internal_transfers.*', 'departments.department_name')
                ->where('internal_transfers.emp_no', '=', $empNo)
                ->orderBy('internal_transfers.transfer_date')
                ->get();

            $exTransfer = ExternalTransfer::join('categories', 'categories.id', '=', 'external_transfers.status')
                ->join('designations', 'designations.id', '=', 'external_transfers.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->join('universities', 'universities.id', '=', 'external_transfers.uni_institute')
                ->select('external_transfers.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname', 'universities.uni_name')
                ->where('external_transfers.emp_no', '=', $empNo)
                ->orderBy('external_transfers.effective_date')
                ->get();

            $leave = Leave::where('leaves.emp_no', '=', $empNo)->orderBy('leaves.year', 'DESC')->get();

            $commend = Commendation::join('categories', 'categories.id', '=', 'commendations.type_id')
                ->select('commendations.*', 'categories.category_name')
                ->where('commendations.emp_no', '=', $empNo)
                ->orderByDesc('commendations.letter_date')
                ->get();

            $checkD = Employee::select('initials', 'last_name')->where('employee_no', '=', $checkUserID)->get();

            $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                ->where('increments.emp_no', $empNo)
                ->orderBy('increments.effective_date', 'DESC')
                ->get();

            if ($checkD->count() > 0) {
                foreach ($checkD as $checkDs) {
                    $checkUser = $checkDs->initials . " " . $checkDs->last_name;
                }
            } else {
                $checkUser = "";
            }

            $officer = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_no', '=', $officerEmpNo)
                ->get();

            if ($officer->count() > 0) {
                foreach ($officer as $officers) {
                    $officerName = $officers->category_name . " " . $officers->initials . " " . $officers->last_name;
                }
            } else {
                $officerName = "";
            }

            $deph = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_no', '=', $headEmpNo)
                ->get();

            if ($deph->count() > 0) {
                foreach ($deph as $dephs) {
                    $depHead = $dephs->category_name . " " . $dephs->initials . " " . $dephs->last_name;
                }
            } else {
                $depHead = "";
            }
        } else {
            $empNo = "";
            $refNo = "";
            $year = "";
            $nic = "";
            $bdate = "";
            $name = "";
            $fName = "";
            $fAppDate = "";
            $cAppDate = "";
            $retirmentDate = "";
            $dep = "";
            $desig = "";
            $promoDesig = "";
            $age = "";
            $appDate = "";
            $empRecord = "";
            $intTransfer = "";
            $exTransfer = "";
            $leave = "";
            $commend = "";
            $checkUser = "";
            $checkDate = "";
            $checkRemark = "";
            $depHead = "";
            $empEmail = "";
            $officerName = "";
            $officerDate = "";
            $officerRemark = "";
            $headEmpNo = "";

            $headDate = "";
            $preform = "";
            $skills = "";
            $sCom = "";
            $weekness = "";
            $wCom = "";
            $otherCom = "";
            $recC = "";
            $recNoC = "";
            $increments = "";
        }

        return view('admin.promotion.nonAcademic.interviewPendingDetails', compact(
            'empNo',
            'refNo',
            'year',
            'nic',
            'bdate',
            'name',
            'fName',
            'fAppDate',
            'cAppDate',
            'retirmentDate',
            'dep',
            'desig',
            'promoDesig',
            'age',
            'appDate',
            'empRecord',
            'intTransfer',
            'exTransfer',
            'leave',
            'commend',
            'checkUser',
            'checkDate',
            'checkRemark',
            'officerName',
            'officerDate',
            'officerRemark',
            'depHead',
            'empEmail',
            'headEmpNo',
            'headDate',
            'preform',
            'skills',
            'sCom',
            'weekness',
            'wCom',
            'otherCom',
            'recC',
            'recNoC',
            'increments'

        ));
    }

    public function examAssingOpen(Request $request)
    {

        $year = date("Y") - 1;
        $mainBranch = Auth()->user()->main_branch_id;

        $sixMonthsAgo = Carbon::now()->subMonths(6);

        if ($mainBranch == 51) {

            $panelText = ExamBoard::join('designations', 'designations.id', '=', 'exam_boards.desig_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->select('exam_boards.*', 'designations.designation_name', 'categories.category_name')
                ->where('exam_boards.type_id', '=', 256)
                ->where('exam_boards.created_at', '>=', $sixMonthsAgo)
                ->get();
        } else {
            if (Auth()->user()->hasRole(['est-head'])) {

                $panelText = ExamBoard::join('designations', 'designations.id', '=', 'exam_boards.desig_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->select('exam_boards.*', 'designations.designation_name', 'categories.category_name')
                    ->where('exam_boards.type_id', '=', 256)
                    ->where('exam_boards.created_at', '>=', $sixMonthsAgo)
                    ->where('exam_boards.division_id', '=', $mainBranch)
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $panelText = ExamBoard::join('designations', 'designations.id', '=', 'exam_boards.desig_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->select('exam_boards.*', 'designations.designation_name', 'categories.category_name')
                    ->where('exam_boards.type_id', '=', 256)
                    ->where('exam_boards.created_at', '>=', $sixMonthsAgo)
                    ->where('exam_boards.division_id', '=', $mainBranch)
                    ->where('exam_boards.enter_user', '=', auth()->user()->employee_no)
                    ->get();
            }
        }

        if ($request->panel > 0) {

            $confText = ExamBoard::join('categories as type', 'type.id', '=', 'exam_boards.type_id')
                ->join('designations', 'designations.id', '=', 'exam_boards.desig_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('employees', 'employees.employee_no', '=', 'exam_boards.examiner_id')
                ->join('categories as emTitle', 'emTitle.id', '=', 'employees.title_id')
                ->select(
                    'exam_boards.*',
                    'type.category_name as interType',
                    'designations.designation_name',
                    'categories.category_name',
                    'emTitle.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'designations.id as desID'
                )
                ->where('exam_boards.id', '=', $request->panel)
                ->get();

            if ($confText->count() > 0) {
                foreach ($confText as $confTexts) {
                    $refNo = $confTexts->board_ref_no;
                    $desig = $confTexts->designation_name . " " . $confTexts->category_name;
                    $examiner = $confTexts->title . " " . $confTexts->initials . " " . $confTexts->last_name;
                    $ExDate = $confTexts->exam_date;
                    $exTime = $confTexts->exam_time;
                    $exVenue = $confTexts->exam_venue;
                    $canAdd = 1;
                    $exID = $confTexts->id;
                    $desigID = $confTexts->desID;
                }
            } else {
                $refNo = "";
                $desig = "";
                $examiner = "";
                $ExDate = "";
                $exTime = "";
                $exVenue = "";
                $canAdd = 0;
                $exID = "";
                $desigID = 0;
            }

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('categories as eTitle', 'eTitle.id', '=', 'employees.title_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->select(
                    'promotion_applications_nonacademics.id',
                    'promotion_applications_nonacademics.ref_no',
                    'eTitle.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'departments.department_name'
                )
                ->where('promotion_applications_nonacademics.exam_id', '=', $request->panel)
                ->get();

            $eList = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->select('employees.employee_no', 'employees.initials', 'employees.last_name', 'promotion_applications_nonacademics.id')
                ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.exam_id', '=', 0)
                ->where('promotion_applications_nonacademics.exam_status', '=', 1)
                //->where('promotion_applications_nonacademics.promotion_designation_id', '=', $desigID)
                ->get();
        } else {

            $refNo = "";
            $desig = "";
            $examiner = "";
            $ExDate = "";
            $exTime = "";
            $exVenue = "";
            $canAdd = 0;
            $exID = "";
            $data_text = array();
            $eList = "";
        }

        return view('admin.promotion.nonAcademic.exam_assigned', compact(
            'panelText',
            'refNo',
            'desig',
            'examiner',
            'ExDate',
            'exTime',
            'exVenue',
            'canAdd',
            'exID',
            'data_text',
            'eList'
        ));
    }

    public function examAssignedStore(Request $request)
    {
        if ($request->exam_applicant != null) {
            if ($request->exam_panel_id != "") {

                for ($i = 0; $i < count($request->exam_applicant); $i++) {

                    $promotionApplication = PromotionApplicationsNonacademic::find($request->exam_applicant[$i]);

                    if ($promotionApplication) {

                        $promotionApplication->exam_id = $request->exam_panel_id;
                        $promotionApplication->save();


                        Log::notice('NonAcademicPromotionController -> promotion application exam panel set. Promotion ID: ' . $request->exam_applicant[$i] . ' set by employee ' . auth()->user()->employee_no);
                    } else {

                        Log::warning('Promotion application not found for ID: ' . $request->exam_applicant[$i]);
                    }
                }

                $notification = array(
                    'message' => 'Promotion Applicatnt added to exam',
                    'alert-type' => 'success'
                );

                return redirect()->route('exam.assigned.open', ['panel' => $request->exam_panel_id])->with($notification);
            } else {
                $notification = array(
                    'message' => 'Something wrong please try again',
                    'alert-type' => 'error'
                );

                return redirect()->route('exam.assigned.open', ['panel' => $request->exam_panel_id])->with($notification);
            }
        } else {

            $notification = array(
                'message' => 'Please Select at least one Applicant to Exam',
                'alert-type' => 'error'
            );

            return redirect()->route('exam.assigned.open', ['panel' => $request->exam_panel_id])->with($notification);
        }
    }

    public function examAssignedDelete($id)
    {

        //Log::info('BondController -> bond data delete started');
        $promotionId = decrypt($id);
        $interviewApplicant = PromotionApplicationsNonacademic::find($promotionId);
        $originalValues = $interviewApplicant->fresh();
        $interviewApplicant->exam_id = 0;
        $interviewApplicant->save();

        $previousValue = $originalValues->interview_board_no;

        $notification = array(
            'message' => 'exam Panel Applicant Removed',
            'alert-type' => 'error'
        );

        return redirect()->route('exam.assigned.open', ['panel' => $previousValue])->with($notification);
    }

    public function examResultsOpen(Request $request)
    {

        $year = date("Y") - 1;
        $mainBranch = Auth()->user()->main_branch_id;

        $sixMonthsAgo = Carbon::now()->subMonths(6);

        if ($mainBranch == 51) {

            $panelText = ExamBoard::join('designations', 'designations.id', '=', 'exam_boards.desig_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->select('exam_boards.*', 'designations.designation_name', 'categories.category_name')
                ->where('exam_boards.type_id', '=', 256)
                ->where('exam_boards.created_at', '>=', $sixMonthsAgo)
                ->get();
        } else {
            if (Auth()->user()->hasRole(['est-head'])) {

                $panelText = ExamBoard::join('designations', 'designations.id', '=', 'exam_boards.desig_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->select('exam_boards.*', 'designations.designation_name', 'categories.category_name')
                    ->where('exam_boards.type_id', '=', 256)
                    ->where('exam_boards.created_at', '>=', $sixMonthsAgo)
                    ->where('exam_boards.division_id', '=', $mainBranch)
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $panelText = ExamBoard::join('designations', 'designations.id', '=', 'exam_boards.desig_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->select('exam_boards.*', 'designations.designation_name', 'categories.category_name')
                    ->where('exam_boards.type_id', '=', 256)
                    ->where('exam_boards.created_at', '>=', $sixMonthsAgo)
                    ->where('exam_boards.division_id', '=', $mainBranch)
                    ->where('exam_boards.enter_user', '=', auth()->user()->employee_no)
                    ->get();
            }
        }

        if ($request->panel > 0) {
            $reqCanAdd = 1;
            $confText = ExamBoard::join('categories as type', 'type.id', '=', 'exam_boards.type_id')
                ->join('designations', 'designations.id', '=', 'exam_boards.desig_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('employees', 'employees.employee_no', '=', 'exam_boards.examiner_id')
                ->join('categories as emTitle', 'emTitle.id', '=', 'employees.title_id')
                ->select(
                    'exam_boards.*',
                    'type.category_name as interType',
                    'designations.designation_name',
                    'categories.category_name',
                    'emTitle.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'designations.id as desID'
                )
                ->where('exam_boards.id', '=', $request->panel)
                ->get();

            if ($confText->count() > 0) {
                foreach ($confText as $confTexts) {
                    $refNo = $confTexts->board_ref_no;
                    $desig = $confTexts->designation_name . " " . $confTexts->category_name;
                    $examiner = $confTexts->title . " " . $confTexts->initials . " " . $confTexts->last_name;
                    $ExDate = $confTexts->exam_date;
                    $exTime = $confTexts->exam_time;
                    $exVenue = $confTexts->exam_venue;

                    $exID = $confTexts->id;
                    $desigID = $confTexts->desID;
                }
            } else {
                $refNo = "";
                $desig = "";
                $examiner = "";
                $ExDate = "";
                $exTime = "";
                $exVenue = "";

                $exID = "";
                $desigID = 0;
            }

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('categories as eTitle', 'eTitle.id', '=', 'employees.title_id')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->select(
                    'promotion_applications_nonacademics.id',
                    'promotion_applications_nonacademics.ref_no',
                    'eTitle.category_name as title',
                    'employees.initials',
                    'employees.last_name',
                    'departments.department_name'
                )
                ->where('promotion_applications_nonacademics.exam_id', '=', $request->panel)
                ->where('promotion_applications_nonacademics.exam_marks1', '=', '10000')
                ->get();

            if ($data_text->count() > 0) {
                $canAdd = 1;
            } else {
                $canAdd = 0;
            }
        } else {

            $refNo = "";
            $desig = "";
            $examiner = "";
            $ExDate = "";
            $exTime = "";
            $exVenue = "";
            $canAdd = 0;
            $exID = "";
            $data_text = array();
            $reqCanAdd = 0;
        }

        return view('admin.promotion.nonAcademic.exam_results', compact(
            'panelText',
            'refNo',
            'desig',
            'examiner',
            'ExDate',
            'exTime',
            'exVenue',
            'canAdd',
            'exID',
            'data_text',
            'reqCanAdd'
        ));
    }

    public function examResultsStore(Request $request)
    {

        $request->validate(
            [
                'examDate' => 'required',

            ],
            [
                'examDate.required' => 'Please select the exam date',
            ]
        );
        if ($request->proId != null) {

            for ($i = 0; $i < count($request->proId); $i++) {

                $mark1 = is_numeric($request->exam1[$i]) ? $request->exam1[$i] : 20000;
                $mark2 = is_numeric($request->exam2[$i]) ? $request->exam2[$i] : 20000;


                $promotionApplication = PromotionApplicationsNonacademic::find($request->proId[$i]);

                if ($promotionApplication) {

                    $promotionApplication->exam_marks1 = $mark1;
                    $promotionApplication->exam_marks2 = $mark2;
                    $promotionApplication->exam_result_date = Carbon::parse($request->examDate)->format('Y-m-d');
                    $promotionApplication->exam_results_enter_user_id = auth()->user()->employee_no;
                    $promotionApplication->exam_results_enter_date = today();
                    $promotionApplication->interview_status = 1;
                    $promotionApplication->save();


                    Log::notice('NonAcademicPromotionController -> promotion exam results set. Promotion ID: ' . $request->proId[$i] . ' set by employee ' . auth()->user()->employee_no);
                } else {

                    Log::warning('Promotion application not found for ID: ' . $request->proId[$i]);
                }
            }

            $notification = array(
                'message' => 'Promotion exam results successfully added',
                'alert-type' => 'success'
            );
        } else {
            $notification = array(
                'message' => 'Something wrong. Please try again.',
                'alert-type' => 'error'
            );
        }
        return redirect()->route('exam.results.open')->with($notification);
    }

    public function dutyAssumedOpen()
    {

        $year = date("Y") - 1;
        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {
            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                ->select(
                    'employees.employee_no',
                    'employees.initials',
                    'employees.last_name',
                    'departments.department_name',
                    'designations.designation_name',
                    'categories.category_name',
                    'promoDesig.designation_name as promoDesigName',
                    'proGrade.category_name as proGName',
                    'promotion_applications_nonacademics.id'
                )
                //->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.duty_assumed_status', '=', 1)
                ->orderBy('promotion_applications_nonacademics.interview_date')
                ->get();
        } else {
            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                ->select(
                    'employees.employee_no',
                    'employees.initials',
                    'employees.last_name',
                    'departments.department_name',
                    'designations.designation_name',
                    'categories.category_name',
                    'promoDesig.designation_name as promoDesigName',
                    'proGrade.category_name as proGName',
                    'promotion_applications_nonacademics.id'
                )
                //->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.duty_assumed_status', '=', 1)
                ->where('employees.assign_ma_user_id', '=', auth()->user()->employee_no)
                ->orderBy('promotion_applications_nonacademics.interview_date')
                ->get();
        }

        return view('admin.promotion.nonAcademic.dutyAssumed', compact('data_text'));
    }

    public function dutyAssumedDetails($id)
    {
        $appId = decrypt($id);

        $prmo_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
            ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
            ->join('categories as prefo', 'prefo.id', '=', 'promotion_applications_nonacademics.performance')
            ->select('employees.*', 'promotion_applications_nonacademics.*', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'prefo.category_name as preform', 'employees.increment_date as incriDate')
            ->where('promotion_applications_nonacademics.id', '=', $appId)
            ->get();

        if ($prmo_text->count() > 0) {
            foreach ($prmo_text as $prmo_texts) {

                $empNo = $prmo_texts->employee_no;
                $refNo = $prmo_texts->ref_no;
                $year = $prmo_texts->year;
                $nic = $prmo_texts->nic;
                $bdate = $prmo_texts->date_of_birth;
                $name = $prmo_texts->initials . " " . $prmo_texts->last_name;
                $fName = $prmo_texts->name_denoted_by_initials . " " . $prmo_texts->last_name;
                $fAppDate = $prmo_texts->initial_appointment_date;
                $cAppDate = $prmo_texts->current_appointment_date;
                $retirmentDate = $prmo_texts->retirement_date;
                $dep = $prmo_texts->department_name;
                $desig = $prmo_texts->designation_name . " " . $prmo_texts->category_name;
                $promoDesig = $prmo_texts->promoDesigName . " " . $prmo_texts->proGName;
                $checkUserID = $prmo_texts->check_user_id;
                $checkDate = $prmo_texts->check_date;
                $checkRemark = $prmo_texts->check_remark;
                $depID = $prmo_texts->department_id;
                $empEmail = $prmo_texts->email;

                $officerEmpNo = $prmo_texts->ar_user_id;
                $officerDate = $prmo_texts->ar_accept_date;
                $officerRemark = $prmo_texts->ar_accept_remark;

                $headEmpNo = $prmo_texts->head_user_id;
                $headDate = $prmo_texts->head_date;
                $preform =  $prmo_texts->preform;

                $skill = $prmo_texts->skills;
                if ($skill == 1) {
                    $skills = "Yes";
                } elseif ($skill == 2) {
                    $skills = "No";
                } else {
                    $skills = "null";
                }
                $sCom =  $prmo_texts->skill_details;

                $week = $prmo_texts->weaknesses;
                if ($week == 1) {
                    $weekness = "Yes";
                } elseif ($week == 2) {
                    $weekness = "No";
                } else {
                    $weekness = "null";
                }
                $wCom =  $prmo_texts->weakness_details;
                $otherCom =  $prmo_texts->head_remark;

                $rec = $prmo_texts->head_status;
                if ($rec == 2) {
                    $recC = "Yes";
                } elseif ($rec == 3) {
                    $recC = "No";
                } else {
                    $recC = "null";
                }
                $recNoC =  $prmo_texts->not_rec_reason;

                $pID = $prmo_texts->interview_board_no;
                $effectiveD = $prmo_texts->effective_date;


                $dt = Carbon::create($year, 12, 31, 0);
                $age = Carbon::parse($bdate)->diff($dt)->format('%y years, %m months and %d days');

                $appDate = Carbon::parse($prmo_texts->created_at)->toDateString();

                $incerment_date = date("m-d", strtotime($prmo_texts->incriDate));
                //$incerment_date = '2023-10-15';

                $desigID = $prmo_texts->current_desigantion_id;
                $promoDesigID = $prmo_texts->promotion_designation_id;
            }

            $empRecord = Promotion::join('categories', 'categories.id', '=', 'promotions.type_id')
                ->join('designations', 'designations.id', '=', 'promotions.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->select('promotions.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname')
                ->where('promotions.employee_no', '=', $empNo)
                ->orderBy('promotions.duty_assumed_date')
                ->get();

            $intTransfer = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                ->select('internal_transfers.*', 'departments.department_name')
                ->where('internal_transfers.emp_no', '=', $empNo)
                ->orderBy('internal_transfers.transfer_date')
                ->get();

            $exTransfer = ExternalTransfer::join('categories', 'categories.id', '=', 'external_transfers.status')
                ->join('designations', 'designations.id', '=', 'external_transfers.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->join('universities', 'universities.id', '=', 'external_transfers.uni_institute')
                ->select('external_transfers.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname', 'universities.uni_name')
                ->where('external_transfers.emp_no', '=', $empNo)
                ->orderBy('external_transfers.effective_date')
                ->get();

            $leave = Leave::where('leaves.emp_no', '=', $empNo)->orderBy('leaves.year', 'DESC')->get();

            $commend = Commendation::join('categories', 'categories.id', '=', 'commendations.type_id')
                ->select('commendations.*', 'categories.category_name')
                ->where('commendations.emp_no', '=', $empNo)
                ->orderByDesc('commendations.letter_date')
                ->get();

            $checkD = Employee::select('initials', 'last_name')->where('employee_no', '=', $checkUserID)->get();

            $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                ->where('increments.emp_no', $empNo)
                ->orderBy('increments.effective_date', 'DESC')
                ->get();

            if ($checkD->count() > 0) {
                foreach ($checkD as $checkDs) {
                    $checkUser = $checkDs->initials . " " . $checkDs->last_name;
                }
            } else {
                $checkUser = "";
            }

            $officer = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_no', '=', $officerEmpNo)
                ->get();

            if ($officer->count() > 0) {
                foreach ($officer as $officers) {
                    $officerName = $officers->category_name . " " . $officers->initials . " " . $officers->last_name;
                }
            } else {
                $officerName = "";
            }

            $deph = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_no', '=', $headEmpNo)
                ->get();

            if ($deph->count() > 0) {
                foreach ($deph as $dephs) {
                    $depHead = $dephs->category_name . " " . $dephs->initials . " " . $dephs->last_name;
                }
            } else {
                $depHead = "";
            }

            $IntPanel = InterviewPanel::where('id', '=', $pID)->get();

            if ($IntPanel->count() > 0) {
                foreach ($IntPanel as $IntPanels) {
                    $int_date = $IntPanels->interview_date;
                    $int_time = date('h:i a', strtotime($IntPanels->interview_time));
                    $venue = $IntPanels->venue;
                    $Panel_code = $IntPanels->panel_id;
                }
            } else {
                $int_date = "";
                $int_time = "";
                $venue = "";
                $Panel_code = "";
            }

            $pMember = interviewPanelMember::join('categories as type', 'type.id', '=', 'interview_panel_members.member_type')
                ->join('categories as mtype', 'mtype.id', '=', 'interview_panel_members.m_type')
                ->select('interview_panel_members.*', 'type.category_name as type_name', 'mtype.category_name as mtype_name')
                ->where('interview_panel_members.panel_id', '=', $Panel_code)
                ->where('interview_panel_members.status', '=', 1)
                ->orderBy('interview_panel_members.m_type')
                ->get();

            $pro_duration = promotionDuration::where('designation_id', '=', $desigID)
                ->where('promo_desig_id', '=', $promoDesigID)
                ->get();

            if ($pro_duration->count() > 0) {
                foreach ($pro_duration as $pro_durations) {
                    $tTest = $pro_durations->trade_test;
                }
            } else {
                $tTest = 0;
            }

            $promo_exam = PromotionApplicationsNonacademic::join('exam_boards', 'exam_boards.id', '=', 'promotion_applications_nonacademics.exam_id')
                ->join('employees', 'employees.employee_no', '=', 'exam_boards.examiner_id')
                ->join('categories', 'categories.id', '=', 'employees.title_id')
                ->select(
                    'promotion_applications_nonacademics.*',
                    'exam_boards.*',
                    'categories.category_name',
                    'employees.initials',
                    'employees.last_name'
                )
                ->where('promotion_applications_nonacademics.id', '=', $appId)
                ->get();

            if ($promo_exam->count() > 0) {
                foreach ($promo_exam as $promo_exams) {
                    $exam_date = $promo_exams->exam_date;
                    $exam_time = $promo_exams->exam_time;
                    $exam_venue = $promo_exams->exam_venue;
                    $exam_ref_no = $promo_exams->board_ref_no;
                    $examiner_name = $promo_exams->category_name . " " . $promo_exams->initials . " " . $promo_exams->last_name;
                    $results_date = $promo_exams->exam_date;
                    $mark1 = $promo_exams->exam_marks1;
                    $mark2 = $promo_exams->exam_marks2;
                }
            } else {
                $exam_date = "";
                $exam_time = "";
                $exam_venue = "";
                $exam_ref_no = "";
                $examiner_name = "";
                $results_date = "";
                $mark1 = 10000;
                $mark2 = 10000;
            }

            if ($mark1 == 10000) {
                $result1 = "Not applicable";
            } else {
                if ($mark1 == 20000) {
                    $result1 = "Not applicable";
                } else {
                    $result1 = $mark1;
                }
            }

            if ($mark2 == 10000) {
                $result2 = "Not applicable";
            } else {
                if ($mark2 == 20000) {
                    $result2 = "Not applicable";
                } else {
                    $result2 = $mark2;
                }
            }

            $int_result = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.inter_results_inter_user_id')
                ->join('categories', 'categories.id', '=', 'employees.title_id')
                ->select(
                    'promotion_applications_nonacademics.*',
                    'categories.category_name',
                    'employees.initials',
                    'employees.last_name',
                )
                ->where('promotion_applications_nonacademics.id', '=', $appId)
                ->get();

            if ($int_result->count() > 0) {
                foreach ($int_result as $int_results) {
                    $effDate = $int_results->effective_date;
                    $ini_bSal = $int_results->initial_bSal;
                    $incri_date = $int_results->increment_date;
                    $bSal = $int_results->current_bSal;
                    $int_remark = $int_results->interview_remark;
                    $int_enter_user = $int_results->category_name . " " . $int_results->initials . " " . $int_results->last_name;
                    $int_enter_date = $int_results->inter_results_enter_date;
                }
            } else {
                $effDate = "";
                $ini_bSal = "";
                $incri_date = "";
                $bSal = "";
                $int_remark = "";
                $int_enter_user = "";
                $int_enter_date = "";
            }

            $proSalStep = promoSalStep::select('promo_sal_steps.*', 'categories.category_name')
                ->join('categories', 'categories.id', '=', 'promo_sal_steps.type_id')
                ->where('promo_sal_steps.promo_id', $appId)
                ->orderBy('promo_sal_steps.type_id')
                ->get();
        } else {
            $empNo = "";
            $refNo = "";
            $year = "";
            $nic = "";
            $bdate = "";
            $name = "";
            $fName = "";
            $fAppDate = "";
            $cAppDate = "";
            $retirmentDate = "";
            $dep = "";
            $desig = "";
            $promoDesig = "";
            $age = "";
            $appDate = "";
            $empRecord = "";
            $intTransfer = "";
            $exTransfer = "";
            $leave = "";
            $commend = "";
            $checkUser = "";
            $checkDate = "";
            $checkRemark = "";
            $depHead = "";
            $empEmail = "";
            $officerName = "";
            $officerDate = "";
            $officerRemark = "";
            $headEmpNo = "";

            $headDate = "";
            $preform = "";
            $skills = "";
            $sCom = "";
            $weekness = "";
            $wCom = "";
            $otherCom = "";
            $recC = "";
            $recNoC = "";
            $increments = "";

            $effectiveD = "";
            $int_date = "";
            $int_time = "";
            $venue = "";
            $Panel_code = "";

            $pMember = array();

            $incerment_date = "";

            $tTest = 0;

            $exam_date = "";
            $exam_time = "";
            $exam_venue = "";
            $exam_ref_no = "";
            $examiner_name = "";
            $results_date = "";
            $result1 = "";
            $result2 = "";

            $effDate = "";
            $ini_bSal = "";
            $incri_date = "";
            $bSal = "";
            $int_remark = "";
            $int_enter_user = "";
            $int_enter_date = "";

            $proSalStep = array();
        }

        return view('admin.promotion.nonAcademic.dutyAssumedDetails', compact(
            'empNo',
            'refNo',
            'year',
            'nic',
            'bdate',
            'name',
            'fName',
            'fAppDate',
            'cAppDate',
            'retirmentDate',
            'dep',
            'desig',
            'promoDesig',
            'age',
            'appDate',
            'empRecord',
            'intTransfer',
            'exTransfer',
            'leave',
            'commend',
            'checkUser',
            'checkDate',
            'checkRemark',
            'officerName',
            'officerDate',
            'officerRemark',
            'depHead',
            'empEmail',
            'headEmpNo',
            'headDate',
            'preform',
            'skills',
            'sCom',
            'weekness',
            'wCom',
            'otherCom',
            'recC',
            'recNoC',
            'increments',
            'effectiveD',
            'int_date',
            'int_time',
            'venue',
            'Panel_code',
            'pMember',
            'incerment_date',
            'tTest',
            'exam_date',
            'exam_time',
            'exam_venue',
            'exam_ref_no',
            'examiner_name',
            'results_date',
            'result1',
            'result2',
            'appId',
            'effDate',
            'ini_bSal',
            'incri_date',
            'bSal',
            'int_remark',
            'int_enter_user',
            'int_enter_date',
            'proSalStep'
        ));
    }

    public function dutyAssumeStore(Request $request)
    {
        if ($request->promoId != "") {


            switch ($request->input('submitbt')) {

                case 'Save':
                    if ($request->dutyDate != "") {

                        $promotionApplication = PromotionApplicationsNonacademic::find($request->promoId);

                        if ($promotionApplication) {

                            $promotionApplication->duty_assumed_status = 2;
                            $promotionApplication->duty_assumed_date = Carbon::parse($request->dutyDate)->format('Y-m-d');
                            $promotionApplication->duty_assumed_user_id = auth()->user()->employee_no;
                            $promotionApplication->duty_assumed_enter_date = today();
                            $promotionApplication->confirm_status = 1;
                            $promotionApplication->save();
                        }

                        $notification = array(
                            'message' => 'Duty assumed details save successfully',
                            'alert-type' => 'success'
                        );

                        return redirect()->route('nac.promo.duty.assumed.open')->with($notification);
                    } else {
                        $notification = array(
                            'message' => 'Please select the duty assumed date',
                            'alert-type' => 'error'
                        );

                        return redirect()->route('nac.promo.duty.assumed.detais', encrypt($request->promoId))->with($notification);
                    }

                    break;

                case 'Interview Result':

                    $promotionApplication = PromotionApplicationsNonacademic::find($request->promoId);

                    if ($promotionApplication) {
                        $promotionApplication->duty_assumed_status = 0;
                        $promotionApplication->interview_status = 1;
                        $promotionApplication->save();
                    }

                    promoSalStep::where('promo_id', $request->promoId)->delete();

                    $notification = array(
                        'message' => 'Forward back to interview results is successful.',
                        'alert-type' => 'success'
                    );

                    return redirect()->route('nac.promo.duty.assumed.open')->with($notification);


                    break;

                default:
                    break;
            }
        } else {
            $notification = array(
                'message' => 'Something wrong. Please try again.',
                'alert-type' => 'error'
            );
            return redirect()->route('nac.promo.duty.assumed.open')->with($notification);
        }
    }

    public function officerConfirmOpen()
    {

        $year = date("Y") - 1;
        $mainBranch = Auth()->user()->main_branch_id;


        $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
            ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
            ->select(
                'employees.employee_no',
                'employees.initials',
                'employees.last_name',
                'departments.department_name',
                'designations.designation_name',
                'categories.category_name',
                'promoDesig.designation_name as promoDesigName',
                'proGrade.category_name as proGName',
                'promotion_applications_nonacademics.id'
            )
            //->where('promotion_applications_nonacademics.year', '=', $year)
            ->where('promotion_applications_nonacademics.confirm_status', '=', 1)
            ->orderBy('promotion_applications_nonacademics.duty_assumed_enter_date')
            ->get();

        return view('admin.promotion.nonAcademic.officerConfirm', compact('data_text'));
    }

    public function officerConfirmDetails($id)
    {
        $appId = decrypt($id);

        $prmo_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
            ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
            ->join('categories as prefo', 'prefo.id', '=', 'promotion_applications_nonacademics.performance')
            ->select('employees.*', 'promotion_applications_nonacademics.*', 'departments.department_name', 'designations.designation_name', 'categories.category_name', 'promoDesig.designation_name as promoDesigName', 'proGrade.category_name as proGName', 'prefo.category_name as preform', 'employees.increment_date as incriDate')
            ->where('promotion_applications_nonacademics.id', '=', $appId)
            ->get();

        if ($prmo_text->count() > 0) {
            foreach ($prmo_text as $prmo_texts) {

                $empNo = $prmo_texts->employee_no;
                $refNo = $prmo_texts->ref_no;
                $year = $prmo_texts->year;
                $nic = $prmo_texts->nic;
                $bdate = $prmo_texts->date_of_birth;
                $name = $prmo_texts->initials . " " . $prmo_texts->last_name;
                $fName = $prmo_texts->name_denoted_by_initials . " " . $prmo_texts->last_name;
                $fAppDate = $prmo_texts->initial_appointment_date;
                $cAppDate = $prmo_texts->current_appointment_date;
                $retirmentDate = $prmo_texts->retirement_date;
                $dep = $prmo_texts->department_name;
                $desig = $prmo_texts->designation_name . " " . $prmo_texts->category_name;
                $promoDesig = $prmo_texts->promoDesigName . " " . $prmo_texts->proGName;
                $checkUserID = $prmo_texts->check_user_id;
                $checkDate = $prmo_texts->check_date;
                $checkRemark = $prmo_texts->check_remark;
                $depID = $prmo_texts->department_id;
                $empEmail = $prmo_texts->email;

                $officerEmpNo = $prmo_texts->ar_user_id;
                $officerDate = $prmo_texts->ar_accept_date;
                $officerRemark = $prmo_texts->ar_accept_remark;

                $headEmpNo = $prmo_texts->head_user_id;
                $headDate = $prmo_texts->head_date;
                $preform =  $prmo_texts->preform;

                $skill = $prmo_texts->skills;
                if ($skill == 1) {
                    $skills = "Yes";
                } elseif ($skill == 2) {
                    $skills = "No";
                } else {
                    $skills = "null";
                }
                $sCom =  $prmo_texts->skill_details;

                $week = $prmo_texts->weaknesses;
                if ($week == 1) {
                    $weekness = "Yes";
                } elseif ($week == 2) {
                    $weekness = "No";
                } else {
                    $weekness = "null";
                }
                $wCom =  $prmo_texts->weakness_details;
                $otherCom =  $prmo_texts->head_remark;

                $rec = $prmo_texts->head_status;
                if ($rec == 2) {
                    $recC = "Yes";
                } elseif ($rec == 3) {
                    $recC = "No";
                } else {
                    $recC = "null";
                }
                $recNoC =  $prmo_texts->not_rec_reason;

                $pID = $prmo_texts->interview_board_no;
                $effectiveD = $prmo_texts->effective_date;


                $dt = Carbon::create($year, 12, 31, 0);
                $age = Carbon::parse($bdate)->diff($dt)->format('%y years, %m months and %d days');

                $appDate = Carbon::parse($prmo_texts->created_at)->toDateString();

                $incerment_date = date("m-d", strtotime($prmo_texts->incriDate));
                //$incerment_date = '2023-10-15';

                $desigID = $prmo_texts->current_desigantion_id;
                $promoDesigID = $prmo_texts->promotion_designation_id;
            }

            $empRecord = Promotion::join('categories', 'categories.id', '=', 'promotions.type_id')
                ->join('designations', 'designations.id', '=', 'promotions.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->select('promotions.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname')
                ->where('promotions.employee_no', '=', $empNo)
                ->orderBy('promotions.duty_assumed_date')
                ->get();

            $intTransfer = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                ->select('internal_transfers.*', 'departments.department_name')
                ->where('internal_transfers.emp_no', '=', $empNo)
                ->orderBy('internal_transfers.transfer_date')
                ->get();

            $exTransfer = ExternalTransfer::join('categories', 'categories.id', '=', 'external_transfers.status')
                ->join('designations', 'designations.id', '=', 'external_transfers.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->join('universities', 'universities.id', '=', 'external_transfers.uni_institute')
                ->select('external_transfers.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname', 'universities.uni_name')
                ->where('external_transfers.emp_no', '=', $empNo)
                ->orderBy('external_transfers.effective_date')
                ->get();

            $leave = Leave::where('leaves.emp_no', '=', $empNo)->orderBy('leaves.year', 'DESC')->get();

            $commend = Commendation::join('categories', 'categories.id', '=', 'commendations.type_id')
                ->select('commendations.*', 'categories.category_name')
                ->where('commendations.emp_no', '=', $empNo)
                ->orderByDesc('commendations.letter_date')
                ->get();

            $checkD = Employee::select('initials', 'last_name')->where('employee_no', '=', $checkUserID)->get();

            $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                ->where('increments.emp_no', $empNo)
                ->orderBy('increments.effective_date', 'DESC')
                ->get();

            if ($checkD->count() > 0) {
                foreach ($checkD as $checkDs) {
                    $checkUser = $checkDs->initials . " " . $checkDs->last_name;
                }
            } else {
                $checkUser = "";
            }

            $officer = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_no', '=', $officerEmpNo)
                ->get();

            if ($officer->count() > 0) {
                foreach ($officer as $officers) {
                    $officerName = $officers->category_name . " " . $officers->initials . " " . $officers->last_name;
                }
            } else {
                $officerName = "";
            }

            $deph = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                ->select('employees.*', 'categories.category_name')
                ->where('employee_no', '=', $headEmpNo)
                ->get();

            if ($deph->count() > 0) {
                foreach ($deph as $dephs) {
                    $depHead = $dephs->category_name . " " . $dephs->initials . " " . $dephs->last_name;
                }
            } else {
                $depHead = "";
            }

            $IntPanel = InterviewPanel::where('id', '=', $pID)->get();

            if ($IntPanel->count() > 0) {
                foreach ($IntPanel as $IntPanels) {
                    $int_date = $IntPanels->interview_date;
                    $int_time = date('h:i a', strtotime($IntPanels->interview_time));
                    $venue = $IntPanels->venue;
                    $Panel_code = $IntPanels->panel_id;
                }
            } else {
                $int_date = "";
                $int_time = "";
                $venue = "";
                $Panel_code = "";
            }

            $pMember = interviewPanelMember::join('categories as type', 'type.id', '=', 'interview_panel_members.member_type')
                ->join('categories as mtype', 'mtype.id', '=', 'interview_panel_members.m_type')
                ->select('interview_panel_members.*', 'type.category_name as type_name', 'mtype.category_name as mtype_name')
                ->where('interview_panel_members.panel_id', '=', $Panel_code)
                ->where('interview_panel_members.status', '=', 1)
                ->orderBy('interview_panel_members.m_type')
                ->get();

            $pro_duration = promotionDuration::where('designation_id', '=', $desigID)->where('promo_desig_id', '=', $promoDesigID)->get();

            if ($pro_duration->count() > 0) {
                foreach ($pro_duration as $pro_durations) {
                    $tTest = $pro_durations->trade_test;
                }
            } else {
                $tTest = 0;
            }

            $promo_exam = PromotionApplicationsNonacademic::join('exam_boards', 'exam_boards.id', '=', 'promotion_applications_nonacademics.exam_id')
                ->join('employees', 'employees.employee_no', '=', 'exam_boards.examiner_id')
                ->join('categories', 'categories.id', '=', 'employees.title_id')
                ->select(
                    'promotion_applications_nonacademics.*',
                    'exam_boards.*',
                    'categories.category_name',
                    'employees.initials',
                    'employees.last_name'
                )
                ->where('promotion_applications_nonacademics.id', '=', $appId)
                ->get();

            if ($promo_exam->count() > 0) {
                foreach ($promo_exam as $promo_exams) {
                    $exam_date = $promo_exams->exam_date;
                    $exam_time = $promo_exams->exam_time;
                    $exam_venue = $promo_exams->exam_venue;
                    $exam_ref_no = $promo_exams->board_ref_no;
                    $examiner_name = $promo_exams->category_name . " " . $promo_exams->initials . " " . $promo_exams->last_name;
                    $results_date = $promo_exams->exam_date;
                    $mark1 = $promo_exams->exam_marks1;
                    $mark2 = $promo_exams->exam_marks2;
                }
            } else {
                $exam_date = "";
                $exam_time = "";
                $exam_venue = "";
                $exam_ref_no = "";
                $examiner_name = "";
                $results_date = "";
                $mark1 = 10000;
                $mark2 = 10000;
            }

            if ($mark1 == 10000) {
                $result1 = "Not applicable";
            } else {
                if ($mark1 == 20000) {
                    $result1 = "Not applicable";
                } else {
                    $result1 = $mark1;
                }
            }

            if ($mark2 == 10000) {
                $result2 = "Not applicable";
            } else {
                if ($mark2 == 20000) {
                    $result2 = "Not applicable";
                } else {
                    $result2 = $mark2;
                }
            }

            $int_result = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.inter_results_inter_user_id')
                ->join('categories', 'categories.id', '=', 'employees.title_id')
                ->select(
                    'promotion_applications_nonacademics.*',
                    'categories.category_name',
                    'employees.initials',
                    'employees.last_name',
                )
                ->where('promotion_applications_nonacademics.id', '=', $appId)
                ->get();

            if ($int_result->count() > 0) {
                foreach ($int_result as $int_results) {
                    $effDate = $int_results->effective_date;
                    $ini_bSal = $int_results->initial_bSal;
                    $incri_date = $int_results->increment_date;
                    $bSal = $int_results->current_bSal;
                    $int_remark = $int_results->interview_remark;
                    $int_enter_user = $int_results->category_name . " " . $int_results->initials . " " . $int_results->last_name;
                    $int_enter_date = $int_results->inter_results_enter_date;
                }
            } else {
                $effDate = "";
                $ini_bSal = "";
                $incri_date = "";
                $bSal = "";
                $int_remark = "";
                $int_enter_user = "";
                $int_enter_date = "";
            }

            $duty_assum = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.duty_assumed_user_id')
                ->join('categories', 'categories.id', '=', 'employees.title_id')
                ->select(
                    'promotion_applications_nonacademics.*',
                    'categories.category_name',
                    'employees.initials',
                    'employees.last_name',
                )
                ->where('promotion_applications_nonacademics.id', '=', $appId)
                ->get();

            if ($duty_assum->count() > 0) {
                foreach ($duty_assum as $duty_assums) {
                    $duty_assDate = $duty_assums->duty_assumed_date;

                    $duty_enter_user = $duty_assums->category_name . " " . $duty_assums->initials . " " . $duty_assums->last_name;
                    $duty_enter_date = $duty_assums->duty_assumed_enter_date;
                }
            } else {
                $duty_assDate = "";

                $duty_enter_user = "";
                $duty_enter_date = "";
            }

            $proSalStep = promoSalStep::select('promo_sal_steps.*', 'categories.category_name')
                ->join('categories', 'categories.id', '=', 'promo_sal_steps.type_id')
                ->where('promo_sal_steps.promo_id', $appId)
                ->orderBy('promo_sal_steps.type_id')
                ->get();
        } else {
            $empNo = "";
            $refNo = "";
            $year = "";
            $nic = "";
            $bdate = "";
            $name = "";
            $fName = "";
            $fAppDate = "";
            $cAppDate = "";
            $retirmentDate = "";
            $dep = "";
            $desig = "";
            $promoDesig = "";
            $age = "";
            $appDate = "";
            $empRecord = "";
            $intTransfer = "";
            $exTransfer = "";
            $leave = "";
            $commend = "";
            $checkUser = "";
            $checkDate = "";
            $checkRemark = "";
            $depHead = "";
            $empEmail = "";
            $officerName = "";
            $officerDate = "";
            $officerRemark = "";
            $headEmpNo = "";

            $headDate = "";
            $preform = "";
            $skills = "";
            $sCom = "";
            $weekness = "";
            $wCom = "";
            $otherCom = "";
            $recC = "";
            $recNoC = "";
            $increments = "";

            $effectiveD = "";
            $int_date = "";
            $int_time = "";
            $venue = "";
            $Panel_code = "";

            $pMember = array();

            $incerment_date = "";

            $tTest = 0;

            $exam_date = "";
            $exam_time = "";
            $exam_venue = "";
            $exam_ref_no = "";
            $examiner_name = "";
            $results_date = "";
            $result1 = "";
            $result2 = "";

            $effDate = "";
            $ini_bSal = "";
            $incri_date = "";
            $bSal = "";
            $int_remark = "";
            $int_enter_user = "";
            $int_enter_date = "";

            $duty_assDate = "";

            $duty_enter_user = "";
            $duty_enter_date = "";

            $proSalStep = array();
        }

        return view('admin.promotion.nonAcademic.officerConfirmDetails', compact(
            'empNo',
            'refNo',
            'year',
            'nic',
            'bdate',
            'name',
            'fName',
            'fAppDate',
            'cAppDate',
            'retirmentDate',
            'dep',
            'desig',
            'promoDesig',
            'age',
            'appDate',
            'empRecord',
            'intTransfer',
            'exTransfer',
            'leave',
            'commend',
            'checkUser',
            'checkDate',
            'checkRemark',
            'officerName',
            'officerDate',
            'officerRemark',
            'depHead',
            'empEmail',
            'headEmpNo',
            'headDate',
            'preform',
            'skills',
            'sCom',
            'weekness',
            'wCom',
            'otherCom',
            'recC',
            'recNoC',
            'increments',
            'effectiveD',
            'int_date',
            'int_time',
            'venue',
            'Panel_code',
            'pMember',
            'incerment_date',
            'tTest',
            'exam_date',
            'exam_time',
            'exam_venue',
            'exam_ref_no',
            'examiner_name',
            'results_date',
            'result1',
            'result2',
            'appId',
            'effDate',
            'ini_bSal',
            'incri_date',
            'bSal',
            'int_remark',
            'int_enter_user',
            'int_enter_date',
            'duty_assDate',
            'duty_enter_user',
            'duty_enter_date',
            'promoDesigID',
            'proSalStep'
        ));
    }

    public function officerConfirmStore(Request $request)
    {
        if ($request->promoId != "" && $request->empNo != "" && $request->desigId != "" && $request->dutyAssDate != "" && $request->iniBSal != "" && $request->cBSal != "" && $request->increDate != "") {

            switch ($request->input('submitbt')) {

                case 'Confirm':

                    $promotionApplication = PromotionApplicationsNonacademic::find($request->promoId);

                    if ($promotionApplication) {

                        $promotionApplication->confirm_status = 2;
                        $promotionApplication->confirm_user_id = auth()->user()->employee_no;
                        $promotionApplication->confirm_date = today();
                        $promotionApplication->status = 1;
                        $promotionApplication->save();
                    }

                    $employee = Employee::where('employee_no', $request->empNo)->first();

                    if ($employee) {

                        $employee->designation_id = $request->desigId;
                        $employee->current_appointment_date = $request->dutyAssDate;
                        $employee->current_basic_salary = $request->cBSal;
                        $employee->increment_date = $request->increDate;
                        $employee->promotion_active_status = 0;
                        $employee->promo_eligibility = 0;
                        $employee->save();
                    }

                    $promotion = new Promotion();
                    $promotion->employee_no = $request->empNo;
                    $promotion->type_id = 177;
                    $promotion->designation_id = $request->desigId;
                    $promotion->duty_assumed_date = date("Y-m-d", strtotime($request->dutyAssDate));
                    $promotion->basic_salary = $request->iniBSal;
                    $promotion->last_working_date = '1970-01-01';
                    $promotion->descriptions = "";
                    $promotion->added_user_id = auth()->user()->employee_no;
                    $promotion->save();

                    $sourceData = promoSalStep::select('promo_sal_steps.*', 'categories.category_name', 'promotion_applications_nonacademics.*')
                        ->join('categories', 'categories.id', '=', 'promo_sal_steps.type_id')
                        ->join('promotion_applications_nonacademics', 'promotion_applications_nonacademics.id', '=', 'promo_sal_steps.promo_id')
                        ->where('promo_sal_steps.promo_id', $request->promoId)
                        ->get();

                    foreach ($sourceData as $sourceDatas) {

                        if ($sourceDatas->type_id == 273 || $sourceDatas->type_id == 274 || $sourceDatas->type_id == 275) {

                            $salIncrement = new Increment();
                            $salIncrement->emp_no = $request->empNo;
                            $salIncrement->effective_date = $sourceDatas->ass_date;
                            $salIncrement->effective_date_arrears = $sourceDatas->ass_date;
                            $salIncrement->decision = 172;
                            $salIncrement->desgnation_id = $request->desigId;
                            $salIncrement->basic_sal = $sourceDatas->sal_step;
                            $salIncrement->increment_value = $sourceDatas->increment_val;
                            $salIncrement->increment_type = 331;
                            $salIncrement->reason = 'Promotion ' . $sourceDatas->year . ' - ' . $sourceDatas->category_name;
                            $salIncrement->add_user_id = $sourceDatas->inter_results_inter_user_id;
                            $salIncrement->save();
                        }
                    }

                    $notification = array(
                        'message' => 'Promotion procedure successfully completed',
                        'alert-type' => 'success'
                    );

                    return redirect()->route('nac.promo.confirm.open')->with($notification);

                    break;

                case 'Duty Assumed':
                    $promotionApplication = PromotionApplicationsNonacademic::find($request->promoId);

                    if ($promotionApplication) {

                        $promotionApplication->confirm_status = 0;
                        $promotionApplication->duty_assumed_status = 1;
                        $promotionApplication->save();
                    }

                    $notification = array(
                        'message' => 'Forward back to duty assumed .',
                        'alert-type' => 'success'
                    );

                    return redirect()->route('nac.promo.confirm.open')->with($notification);

                    break;
                case 'Interview Result':
                    $promotionApplication = PromotionApplicationsNonacademic::find($request->promoId);

                    if ($promotionApplication) {

                        $promotionApplication->confirm_status = 0;
                        $promotionApplication->duty_assumed_status = 0;
                        $promotionApplication->interview_status = 1;
                        $promotionApplication->save();
                    }

                    promoSalStep::where('promo_id', $request->promoId)->delete();

                    $notification = array(
                        'message' => 'Forward back to interview results .',
                        'alert-type' => 'success'
                    );

                    return redirect()->route('nac.promo.confirm.open')->with($notification);

                    break;
            }
        } else {

            $notification = array(
                'message' => 'Something wrong. Please try again.',
                'alert-type' => 'error'
            );
            return redirect()->route('nac.promo.confirm.open')->with($notification);
        }
    }

    public function applicantReportOpen()
    {

        $year = date("Y") - 1;
        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                ->select(
                    'employees.employee_no',
                    'employees.initials',
                    'employees.last_name',
                    'departments.department_name',
                    'designations.designation_name',
                    'categories.category_name',
                    'promoDesig.designation_name as promoDesigName',
                    'proGrade.category_name as proGName',
                    'promotion_applications_nonacademics.id'
                )
                ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.ar_accept_status', '>', 1)
                ->orderBy('promotion_applications_nonacademics.id')
                ->get();
        } else {

            $data_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
                ->join('departments', 'departments.id', '=', 'employees.department_id')
                ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
                ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
                ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
                ->select(
                    'employees.employee_no',
                    'employees.initials',
                    'employees.last_name',
                    'departments.department_name',
                    'designations.designation_name',
                    'categories.category_name',
                    'promoDesig.designation_name as promoDesigName',
                    'proGrade.category_name as proGName',
                    'promotion_applications_nonacademics.id'
                )
                ->where('promotion_applications_nonacademics.year', '=', $year)
                ->where('promotion_applications_nonacademics.ar_accept_status', '>', 1)
                ->where('employees.assign_ma_user_id', '=', auth()->user()->employee_no)
                ->orderBy('promotion_applications_nonacademics.id')
                ->get();
        }

        return view('admin.promotion.nonAcademic.applicant_report_list', compact('data_text'));
    }

    public function applicantReportPrint($id)
    {
        $appId = decrypt($id);

        $prmo_text = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.emp_no')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('designations', 'designations.id', '=', 'promotion_applications_nonacademics.current_desigantion_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_applications_nonacademics.promotion_designation_id')
            ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
            ->select(
                'employees.*',
                'promotion_applications_nonacademics.*',
                'departments.department_name',
                'designations.designation_name',
                'categories.category_name',
                'promoDesig.designation_name as promoDesigName',
                'proGrade.category_name as proGName',
                'employees.increment_date as incriDate'
            )
            ->where('promotion_applications_nonacademics.id', '=', $appId)
            ->get();

        if ($prmo_text->count() > 0) {
            foreach ($prmo_text as $prmo_texts) {

                $empNo = $prmo_texts->employee_no;
                $refNo = $prmo_texts->ref_no;
                $year = $prmo_texts->year;
                $nic = $prmo_texts->nic;
                $bdate = $prmo_texts->date_of_birth;
                $name = $prmo_texts->initials . " " . $prmo_texts->last_name;
                $fName = $prmo_texts->name_denoted_by_initials . " " . $prmo_texts->last_name;
                $fAppDate = $prmo_texts->initial_appointment_date;
                $cAppDate = $prmo_texts->current_appointment_date;
                $retirmentDate = $prmo_texts->retirement_date;
                $dep = $prmo_texts->department_name;
                $desig = $prmo_texts->designation_name . " " . $prmo_texts->category_name;
                $promoDesig = $prmo_texts->promoDesigName . " " . $prmo_texts->proGName;
                $checkUserID = $prmo_texts->check_user_id;
                $checkDate = $prmo_texts->check_date;
                $checkRemark = $prmo_texts->check_remark;
                $depID = $prmo_texts->department_id;
                $empEmail = $prmo_texts->email;

                $officerEmpNo = $prmo_texts->ar_user_id;
                $officerDate = $prmo_texts->ar_accept_date;
                $officerRemark = $prmo_texts->ar_accept_remark;

                $officerStatus = $prmo_texts->ar_accept_status;
                $headStatus = $prmo_texts->head_status;

                $dt = Carbon::create($year, 12, 31, 0);
                $age = Carbon::parse($bdate)->diff($dt)->format('%y years, %m months and %d days');

                $appDate = Carbon::parse($prmo_texts->created_at)->toDateString();

                $incerment_date = date("m-d", strtotime($prmo_texts->incriDate));
                //$incerment_date = '2023-10-15';

                $desigID = $prmo_texts->current_desigantion_id;
                $promoDesigID = $prmo_texts->promotion_designation_id;
            }

            $empRecord = Promotion::join('categories', 'categories.id', '=', 'promotions.type_id')
                ->join('designations', 'designations.id', '=', 'promotions.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->select('promotions.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname')
                ->where('promotions.employee_no', '=', $empNo)
                ->where('promotions.type_id', '!=', 207)
                ->orderBy('promotions.duty_assumed_date')
                ->get();

            $intTransfer = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                ->select('internal_transfers.*', 'departments.department_name')
                ->where('internal_transfers.emp_no', '=', $empNo)
                ->orderBy('internal_transfers.transfer_date')
                ->get();

            $exTransfer = ExternalTransfer::join('categories', 'categories.id', '=', 'external_transfers.status')
                ->join('designations', 'designations.id', '=', 'external_transfers.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->join('universities', 'universities.id', '=', 'external_transfers.uni_institute')
                ->select('external_transfers.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname', 'universities.uni_name')
                ->where('external_transfers.emp_no', '=', $empNo)
                ->orderBy('external_transfers.effective_date')
                ->get();

            if ($promoDesigID == 289 || $promoDesigID == 653 || $promoDesigID == 663 || $promoDesigID == 658 || $promoDesigID == 512 || $promoDesigID == 262) {
                $currentYear = date('Y');
                $yearL = $currentYear - 20;
            } else {
                $laveDate = date_create($cAppDate);
                $yearL = date_format($laveDate, "Y");
            }

            $leave = Leave::where('leaves.emp_no', '=', $empNo)->where('year', '>=', $yearL)->orderBy('leaves.year', 'DESC')->get();

            $checkD = Employee::select('initials', 'last_name')
                ->where('employee_no', '=', $checkUserID)
                ->get();

            if ($checkD->count() > 0) {
                foreach ($checkD as $checkDs) {
                    $checkUser = $checkDs->initials . " " . $checkDs->last_name;
                }
            } else {
                $checkUser = "";
            }

            $officer = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.ar_user_id')
                ->join('categories', 'categories.id', '=', 'employees.title_id')
                ->join('categories as headP', 'headP.id', '=', 'promotion_applications_nonacademics.ar_position')
                ->select('employees.*', 'categories.category_name', 'headP.category_name as position')
                ->where('promotion_applications_nonacademics.id', '=', $appId)
                ->get();

            if ($officer->count() > 0) {
                foreach ($officer as $officers) {
                    $officerName = $officers->category_name . " " . $officers->initials . " " . $officers->last_name;
                    $position = $officers->position;
                }
            } else {
                $officerName = "";
                $position = "";
            }

            $deph = PromotionApplicationsNonacademic::join('employees', 'employees.employee_no', '=', 'promotion_applications_nonacademics.head_user_id')
                ->join('categories', 'categories.id', '=', 'employees.title_id')
                ->join('categories as prefo', 'prefo.id', '=', 'promotion_applications_nonacademics.performance')
                ->join('categories as headP', 'headP.id', '=', 'promotion_applications_nonacademics.head_position')
                ->select(
                    'promotion_applications_nonacademics.*',
                    'employees.initials',
                    'employees.last_name',
                    'categories.category_name',
                    'prefo.category_name as preform',
                    'headP.category_name as position'
                )
                ->where('promotion_applications_nonacademics.id', '=', $appId)
                ->get();

            if ($deph->count() > 0) {
                foreach ($deph as $dephs) {
                    $depHead = $dephs->category_name . " " . $dephs->initials . " " . $dephs->last_name;
                    $hedPosition = $dephs->position;
                    $headDate = $dephs->head_date;
                    $preform =  $dephs->preform;

                    $skill = $dephs->skills;
                    if ($skill == 1) {
                        $skills = "Yes";
                    } elseif ($skill == 2) {
                        $skills = "No";
                    } else {
                        $skills = "null";
                    }
                    $sCom =  $dephs->skill_details;

                    $week = $dephs->weaknesses;
                    if ($week == 1) {
                        $weekness = "Yes";
                    } elseif ($week == 2) {
                        $weekness = "No";
                    } else {
                        $weekness = "null";
                    }
                    $wCom =  $dephs->weakness_details;
                    $otherCom =  $dephs->head_remark;

                    $rec = $dephs->head_status;
                    if ($rec == 2) {
                        $recC = "Yes";
                    } elseif ($rec == 3) {
                        $recC = "No";
                    } else {
                        $recC = "null";
                    }
                    $recNoC =  $dephs->not_rec_reason;
                }
            } else {
                $depHead = "";
                $headDate = "";
                $preform = "";
                $skills = "";
                $sCom = "";
                $weekness = "";
                $wCom = "";
                $otherCom = "";
                $recC = "";
                $recNoC = "";
                $hedPosition = "";
            }


            $data = [
                'refNo' => $refNo,
                'year' => $year,
                'empNo' => $empNo,
                'nic' => $nic,
                'name' => $name,
                'fName' => $fName,
                'bdate' => $bdate,
                'age' => $age,
                'retirmentDate' => $retirmentDate,
                'fAppDate' => $fAppDate,
                'cAppDate' => $cAppDate,
                'dep' => $dep,
                'appDate' => $appDate,
                'desig' => $desig,
                'promoDesig' => $promoDesig,
                'empRecord' => $empRecord,
                'intTransfer' => $intTransfer,
                'exTransfer' => $exTransfer,
                'leave' => $leave,
                'checkUser' => $checkUser,
                'checkDate' => $checkDate,
                'officerStatus' => $officerStatus,
                'officerName' => $officerName,
                'officerDate' => $officerDate,
                'officerRemark' => $officerRemark,
                'position' => $position,
                'headStatus' => $headStatus,
                'depHead' => $depHead,
                'headDate' => $headDate,
                'preform' => $preform,
                'skills' => $skills,
                'sCom' => $sCom,
                'weekness' => $weekness,
                'wCom' => $wCom,
                'otherCom' => $otherCom,
                'recC' => $recC,
                'recNoC' => $recNoC,
                'hedPosition' => $hedPosition

            ];

            $pdf = FacadePdf::loadView('admin.promotion.nonAcademic.applicant_report', $data)->setPaper('a4');

            return $pdf->stream('applicant_reprot.pdf');
        } else {
        }
    }

    public function budgetList(Request $request)
    {
        if (isset($request->year) && $request->year != '') {

            $yearV = $request->year;

            if (Auth()->user()->hasRole(['administrator', 'est-head'])) {
                $empDetails = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->where('employees.main_branch_id', 53)
                    ->where('employees.employee_status_id', 110)
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {
                $empDetails = Employee::join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->where('employees.main_branch_id', 53)
                    ->where('employees.employee_status_id', 110)
                    ->where('employees.assign_ma_user_id', '=', auth()->user()->employee_no)
                    ->get();
            } else {
                $empDetails = array();
            }



            //dd($empDetails);
            if ($empDetails->count() > 0) {

                foreach ($empDetails as $empDetail) {

                    $empname = $empDetail->initials . " " . $empDetail->last_name;
                    //$empNIC = $empDetail->nic;
                    $empNo = $empDetail->employee_no;
                    $empDesig = $empDetail->designation_name . " " . $empDetail->category_name;
                    $empAppDate = $empDetail->current_appointment_date;
                    $desiID = $empDetail->designation_id;
                    $AppDate = Carbon::parse($empDetail->current_appointment_date);
                    $promoEligibility = $empDetail->promo_eligibility;
                    $departmentName = $empDetail->department_name;
                    $upf = $empDetail->upf_no;
                    $sal = $empDetail->current_basic_salary;
                    $cSalCode = $empDetail->salary_code;
                    $ma = $empDetail->assign_ma_user_id;
                    $fAppDate = Carbon::parse($empDetail->initial_appointment_date);

                    $ma_text = Employee::select('initials', 'last_name')
                        ->where('employee_no', '=', $ma)
                        ->get();
                    if (count($ma_text) > 0) {
                        foreach ($ma_text as $ma_texts) {
                            $ma_name = $ma_texts->initials . ' ' . $ma_texts->last_name;
                        }
                    } else {
                        $ma_name = '';
                    }


                    $promoD = promotionDuration::join('designations', 'designations.id', '=', 'promotion_durations.promo_desig_id')
                        ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                        ->where('designation_id', $empDetail->designation_id)
                        ->get();

                    $promoD_rowCount = $promoD->count();

                    if ($promoD_rowCount > 0) {

                        foreach ($promoD as $promoDs) {

                            $promoDesig = $promoDs->designation_name . " " . $promoDs->category_name;
                            $prmoDesigID = $promoDs->promo_desig_id;
                            $promoYears = $promoDs->duration;
                            $proSalCode = $promoDs->salary_code;
                            $tService = $promoDs->total_service;
                        }


                        //$currentDate = Carbon::parse('2023-01-01');
                        // $currentDate = Carbon::create($request->year + 1, 01, 01);

                        // $serviceP = $AppDate->diffInYears($currentDate);


                        // if ($serviceP >= $promoYears) {

                        //     $data_set1[] = [
                        //         'empNo' => $empNo,
                        //         'empname' => $empname,
                        //         'departmentName' => $departmentName,
                        //         'empDesig' => $empDesig,
                        //         'promoDesig' => $promoDesig,
                        //         'promoEligibility' => $promoEligibility,
                        //         'upf' => $upf,
                        //         'sal' => $sal,
                        //         'CSalCode' => $cSalCode,
                        //         'proSalCode' => $proSalCode,
                        //         'maName' => $ma_name

                        //     ];
                        // }

                        $appYear = $AppDate->year;
                        $proYear = $appYear + $promoYears;

                        $appYearF = $fAppDate->year;
                        $proYeart = $appYearF + $tService;

                        $currentDate = Carbon::create($request->year, 01, 01);

                        $serviceP = $AppDate->diffInYears($currentDate);

                        $servicePt = $fAppDate->diffInYears($currentDate);

                        if (($serviceP >= $promoYears) && ($servicePt >= $tService) && (($proYear >= ($yearV - 1)) || ($proYeart >= ($yearV - 1)))) {
                            $data_set1[] = [
                                'empNo' => $empNo,
                                'empname' => $empname,
                                'departmentName' => $departmentName,
                                'empDesig' => $empDesig,
                                'promoDesig' => $promoDesig,
                                'promoEligibility' => $promoEligibility,
                                'upf' => $upf,
                                'sal' => $sal,
                                'CSalCode' => $cSalCode,
                                'proSalCode' => $proSalCode,
                                'maName' => $ma_name

                            ];
                        }
                    }
                }
            } else {
                $data_set1[] = [
                    'empNo' => '',
                    'empname' => '',
                    'departmentName' => '',
                    'empDesig' => '',
                    'promoDesig' => '',
                    'promoEligibility' => '',
                    'upf' => '',
                    'sal' => '',
                    'CSalCode' => '',
                    'proSalCode' => '',
                    'maName' => ''
                ];
            }
        } else {
            $yearV = 0;
            $data_set1[] = [
                'empNo' => '',
                'empname' => '',
                'departmentName' => '',
                'empDesig' => '',
                'promoDesig' => '',
                'promoEligibility' => '',
                'upf' => '',
                'sal' => '',
                'CSalCode' => '',
                'proSalCode' => '',
                'maName' => ''

            ];
        }
        //dd($socialNetworks);
        return view('admin.promotion.nonAcademic.budget_list', compact('data_set1', 'yearV'));
    }

    // ******************* 1st checking MA promotion ***********************************************************
    public function maPromoCheckingOpen()
    {

        $year = date("Y") - 1;
        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            if (Auth()->user()->hasRole(['super-admin|administrator'])) {

                $data_text = Employee::join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->join('promotion_durations', 'promotion_durations.designation_id', '=', 'employees.designation_id')
                    ->join('designations as promoDesigTable', 'promoDesigTable.id', '=', 'promotion_durations.promo_desig_id')
                    ->join('categories as g', 'g.id', '=', 'promoDesigTable.staff_grade')
                    ->select(
                        'employees.employee_no',
                        'employees.initials',
                        'employees.last_name',
                        'departments.department_name',
                        'designations.designation_name',
                        'categories.category_name',
                        'promoDesigTable.designation_name as promoDesigName',
                        'g.category_name as proGName'
                    )
                    ->where('employees.ma_promo_exam', 1)
                    ->orderBy('employees.employee_no')
                    ->get();
            } else {
                $data_text = array();
            }
        } elseif ($mainBranch == 52) {

            $data_text = array();
        } elseif ($mainBranch == 53) {

            if (Auth()->user()->hasRole(['cc'])) {

                $data_text = array();
            } elseif (Auth()->user()->hasRole(['sc'])) {

                $data_text = Employee::join('departments', 'departments.id', '=', 'employees.department_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories', 'categories.id', '=', 'designations.staff_grade')
                    ->join('promotion_durations', 'promotion_durations.designation_id', '=', 'employees.designation_id')
                    ->join('designations as promoDesigTable', 'promoDesigTable.id', '=', 'promotion_durations.promo_desig_id')
                    ->join('categories as g', 'g.id', '=', 'promoDesigTable.staff_grade')
                    ->select(
                        'employees.employee_no',
                        'employees.initials',
                        'employees.last_name',
                        'departments.department_name',
                        'designations.designation_name',
                        'categories.category_name',
                        'promoDesigTable.designation_name as promoDesigName',
                        'g.category_name as proGName'
                    )
                    ->where('employees.ma_promo_exam', 1)
                    ->where('employees.assign_ma_user_id', '=', auth()->user()->employee_no)
                    ->orderBy('employees.employee_no')
                    ->get();
            }
        }

        return view('admin.promotion.nonAcademic.1stCheckingMAPromo', compact('data_text'));
    }

    public function checkingDetailsMAPromo($id)
    {
        $appId = decrypt($id);

        $prmo_text = Employee::join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories', 'categories.id', '=', 'designations.staff_grade')
            ->join('promotion_durations', 'promotion_durations.designation_id', '=', 'employees.designation_id')
            ->join('designations as promoDesig', 'promoDesig.id', '=', 'promotion_durations.promo_desig_id')
            ->join('categories as proGrade', 'proGrade.id', '=', 'promoDesig.staff_grade')
            ->select(
                'employees.*',
                'departments.department_name',
                'designations.designation_name',
                'categories.category_name',
                'promoDesig.id as promoDesigID',
                'promoDesig.designation_name as promoDesigName',
                'proGrade.category_name as proGName'
            )
            ->where('employees.employee_no', '=', $appId)
            ->get();

        if ($prmo_text->count() > 0) {
            foreach ($prmo_text as $prmo_texts) {
                $empNo = $prmo_texts->employee_no;
                $refNo = '';
                $year = 2025;
                $nic = $prmo_texts->nic;
                $bdate = $prmo_texts->date_of_birth;
                $name = $prmo_texts->initials . " " . $prmo_texts->last_name;
                $fName = $prmo_texts->name_denoted_by_initials . " " . $prmo_texts->last_name;
                $fAppDate = $prmo_texts->initial_appointment_date;
                $cAppDate = $prmo_texts->current_appointment_date;
                $retirmentDate = $prmo_texts->retirement_date;
                $dep = $prmo_texts->department_name;
                $desigID = $prmo_texts->designation_id;
                $desig = $prmo_texts->designation_name . " " . $prmo_texts->category_name;
                $promoDesig = $prmo_texts->promoDesigName . " " . $prmo_texts->proGName;
                $promoDesigID = $prmo_texts->promoDesigID;

                $dt = Carbon::create($year, 12, 31, 0);
                $age = Carbon::parse($bdate)->diff($dt)->format('%y years, %m months and %d days');

                $appDate = today();

                $lock = $prmo_texts->lock;
            }

            $empRecord = Promotion::join('categories', 'categories.id', '=', 'promotions.type_id')
                ->join('designations', 'designations.id', '=', 'promotions.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->select('promotions.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname')
                ->where('promotions.employee_no', '=', $empNo)
                ->orderBy('promotions.duty_assumed_date')
                ->get();

            $intTransfer = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                ->select('internal_transfers.*', 'departments.department_name')
                ->where('internal_transfers.emp_no', '=', $empNo)
                ->orderBy('internal_transfers.transfer_date')
                ->get();

            $exTransfer = ExternalTransfer::join('categories', 'categories.id', '=', 'external_transfers.status')
                ->join('designations', 'designations.id', '=', 'external_transfers.designation_id')
                ->join('categories as grade', 'grade.id', '=', 'designations.staff_grade')
                ->join('universities', 'universities.id', '=', 'external_transfers.uni_institute')
                ->select('external_transfers.*', 'categories.category_name', 'designations.designation_name', 'grade.category_name as gname', 'universities.uni_name')
                ->where('external_transfers.emp_no', '=', $empNo)
                ->orderBy('external_transfers.effective_date')
                ->get();

            $leave = Leave::where('leaves.emp_no', '=', $empNo)->orderBy('leaves.year', 'DESC')->get();

            $commend = Commendation::join('categories', 'categories.id', '=', 'commendations.type_id')
                ->select('commendations.*', 'categories.category_name')
                ->where('commendations.emp_no', '=', $empNo)
                ->orderByDesc('commendations.letter_date')
                ->get();

            $increments = Increment::join('designations', 'increments.desgnation_id', '=', 'designations.id')
                ->join('categories as diff', 'increments.decision', '=', 'diff.id')
                ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                ->select('increments.effective_date', 'designations.designation_name', 'designations.salary_code', 'diff.category_name as dicision', 'increments.basic_sal', 'increments.salary_step', 'increments.increment_value', 'increments.id', 'gread.category_name as gName')
                ->where('increments.emp_no', $empNo)
                ->orderBy('increments.effective_date', 'DESC')
                ->get();
        } else {
            $empNo = "";
            $refNo = "";
            $year = "";
            $nic = "";
            $bdate = "";
            $name = "";
            $fName = "";
            $fAppDate = "";
            $cAppDate = "";
            $retirmentDate = "";
            $dep = "";
            $desig = "";
            $promoDesig = "";
            $age = "";
            $appDate = "";
            $empRecord = array();
            $intTransfer = array();
            $exTransfer = array();
            $leave = array();
            $commend = array();
            $lock = "";
            $increments = array();
            $desigID = 0;
            $promoDesigID = 0;
        }

        return view('admin.promotion.nonAcademic.1stCheckingDetailsMAPromo', compact(
            'empNo',
            'refNo',
            'year',
            'nic',
            'bdate',
            'name',
            'fName',
            'fAppDate',
            'cAppDate',
            'retirmentDate',
            'dep',
            'desig',
            'promoDesig',
            'age',
            'appDate',
            'empRecord',
            'intTransfer',
            'exTransfer',
            'leave',
            'commend',
            'increments',
            'lock',
            'desigID',
            'promoDesigID'
        ));
    }

    public function maPromo1stCheckingStore(Request $request)
    {
        $ref_text = PromotionApplicationsNonacademic::where('year',2025)->get();
        $ref_no = '2025/MA/'.(count($ref_text)+1);

        $save_text = new PromotionApplicationsNonacademic();
        $save_text->year = 2025;
        $save_text->emp_no = $request->empNo;
        $save_text->current_desigantion_id = $request->desigID;
        $save_text->promotion_designation_id = $request->proDesig;
        $save_text->ref_no = $ref_no;
        $save_text->apply_date = today();
        $save_text->check_status = 1;
        $save_text->check_user_id = auth()->user()->employee_no;
        $save_text->check_date = today();
        $save_text->head_status = 2;
        $save_text->exam_status = 2;
        $save_text->exam_marks1 = $request->paper1;
        $save_text->exam_marks2 = $request->paper2;
        $save_text->interview_status = 1;      
        $save_text->save();

        $update_text = Employee::where('employee_no',$request->empNo)->first();
        if ($update_text) {
            $update_text->ma_promo_exam = 0;
            $update_text->save();
        }

        $notification = array(
            'message' => 'Submitted successfully.',
            'alert-type' => 'success'
        );

        return redirect()->route('nac.promo.maPromo.1stchecking.list.open')->with($notification);
    }
}
