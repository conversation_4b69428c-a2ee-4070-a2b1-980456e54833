<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdminOfficerNewEmployee extends Notification implements ShouldQueue
{
    use Queueable;
    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }


    public function via($notifiable)
    {
        return ['database','mail'];
    }


    public function toMail($notifiable)
    {
        return (new MailMessage)
                    ->from('<EMAIL>', 'USJ HRM SYSTEM')
                    ->subject('New Employee Data Added for generate Employee Number')
                    ->greeting('New '. $this->data['employee_work_type'].' Employee Data Added')
                    ->line('Assign new employee for generate employee number with NIC ' .$this->data['nic'])
                    ->action('Generate Employee Number', url('https://hrms.sjp.ac.lk/employee/generate/add/list'))
                    ->line('Thank you for using our application!');
    }

    public function toArray($notifiable)
    {
        return [
            'headline' => 'New '. $this->data['employee_work_type'].' Employee data Added',
            'message' => 'Assign new employee for generate employee number with NIC ' .$this->data['nic'],
            'data' => $this->data,
            'type' => 322
         ];
    }
}
