<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('annual_leaves', function (Blueprint $table) {
            $table->integer('designation_id')->default(0)->after('empNo');
            $table->string('dc_remark')->nullable()->after('summary_table_id');
            $table->integer('dc_empNo')->default(0)->after('dc_remark');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('annual_leaves', function (Blueprint $table) {
            $table->dropColumn('designation_id');
            $table->dropColumn('dc_remark');
            $table->dropColumn('dc_empNo');
        });
    }
};
