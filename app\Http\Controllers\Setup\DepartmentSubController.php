<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\Department;
use App\Models\DepartmentSub;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class DepartmentSubController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator');
    }

    public function departmentSubIndex()
    {
        Log::info('DepartmentSubController -> sub department index started');
        $subDepartments = DepartmentSub::all();
        $trashSubDepartments = DepartmentSub::onlyTrashed()->get();
        return view('admin.setups.department_sub.index', compact('subDepartments', 'trashSubDepartments'));

        Log::notice('DepartmentSubController -> sub department Count - ' . $subDepartments->count());
        Log::info('DepartmentSubController -> sub department index ended');
    }

    public function departmentSubAdd()
    {
        Log::info('DepartmentSubController -> sub department add started');

        $departments = Department::all();
        return view('admin.setups.department_sub.add', compact('departments'));

        Log::info('DepartmentSubController -> sub department add ended');
    }

    public function departmentSubStore(Request $request)
    {
        Log::info('DepartmentSubController -> sub department store started');

        $validatedData = $request->validate([
            'sub_departmet_name' => 'required|unique:department_subs,sub_departmet_name',
            'department_code' => 'required',
        ]);

        /***************************************** */
        $maxnumber = DB::table('department_subs')
            ->where('department_code', $request->department_code)
            ->select(DB::raw('MAX(sub_department_code) as value'))
            ->get();

        $maxValue = json_decode($maxnumber, true);
        $sortnumber = chr(ord(substr($maxValue[0]["value"], -1)) + 1);

        if ($sortnumber == "\x01") {
            $sub_departmet_code = $request->department_code . 'A';
        } else {
            $sub_departmet_code = $request->department_code . $sortnumber;
        }


        /******************************************* */

        $data = new DepartmentSub();
        $data->sub_departmet_name = $request->sub_departmet_name;
        $data->department_code = $request->department_code;
        $data->sub_department_code = $sub_departmet_code;
        $data->created_at = Carbon::now();
        $data->save();

        Log::notice('DepartmentSubController -> Created sub department id - ' . $data->id . ' created by ' . auth()->user()->id);
        Log::info('DepartmentSubController -> sub department create ended');

        $notification = array(
            'message' => 'New Sub Department Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('department.sub.index')->with($notification);
    }

    public function departmentSubEdit($id)
    {
        Log::info('DepartmentSubController -> sub department edit started');
        $editData = DepartmentSub::find($id);
        $departments = Department::all();
        return view('admin.setups.department_sub.edit', compact('editData', 'departments'));
        Log::notice('DepartmentSubController -> edit sub department id - ' . $editData->id . ' edited by ' . auth()->user()->id);
        Log::info('DepartmentSubController -> sub department edit ended');
    }

    public function departmentSubUpdate(Request $request, $id)
    {

        Log::info('DepartmentSubController -> sub department update started');
        $validatedData = $request->validate([
            'sub_departmet_name' => ['required', Rule::unique('department_subs')->ignore($id)],

        ]);


        $data = DepartmentSub::find($id);
        $data->sub_departmet_name = $request->sub_departmet_name;
        $data->updated_at = Carbon::now();
        $data->save();

        Log::notice('DepartmentSubController -> update sub department id - ' . $data->id . ' updated by ' . auth()->user()->id);
        Log::info('DepartmentSubController -> sub department update ended');

        $notification = array(
            'message' => 'Sub Department Data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('department.sub.index')->with($notification);
    }

    public function departmentSubSoftdelete($id)
    {

        Log::info('DepartmentSubController -> sub department soft delete started');
        $departmentSub = DepartmentSub::find($id);
        $departmentSub->delete();

        $notification = array(
            'message' => 'Sub Department Deleted Successfully',
            'alert-type' => 'warning'
        );

        return redirect()->route('department.sub.index')->with($notification);

        Log::notice('DepartmentSubController -> soft delete sub department id - ' . $departmentSub->id . ' deleted by ' . auth()->user()->id);
        Log::info('DepartmentSubController -> Sub department soft delete ended');
    }

    public function departmentSubRestore($id)
    {

        Log::info('DepartmentSubController -> sub department restore started');

        $departmentSub = DepartmentSub::withTrashed()->find($id);
        $departmentSub->restore();

        $notification = array(
            'message' => 'Sub Department Restore Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('department.sub.index')->with($notification);

        Log::notice('DepartmentSubController -> restore seb department id - ' . $departmentSub->id . ' deleted by ' . auth()->user()->id);
        Log::info('DepartmentSubController -> sub department restore ended');
    }

    public function departmentSubDelete($id)
    {

        Log::info('DepartmentSubController -> sub department delete started');

        $departmentSub = DepartmentSub::onlyTrashed()->find($id);
        $departmentSub->forceDelete();

        $notification = array(
            'message' => 'Sub Department Permenetly Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('department.sub.index')->with($notification);

        Log::emergency('DepartmentSubController -> delete sub department id - ' . $departmentSub->id . ' deleted by ' . auth()->user()->id);
        Log::info('DepartmentSubController -> sub department delete ended');
    }
}
