<?php

namespace App\Http\Controllers\Backend\SalaryRevision;

use App\Http\Controllers\Controller;
use App\Models\Designation;
use App\Models\Employee;
use App\Models\Increment;
use App\Models\Promotion;
use App\Models\salaryRevision2025;
use Barryvdh\DomPDF\Facade\Pdf as FacadePdf;
use Carbon\Carbon;
use Illuminate\Http\Request;

class SalaryRevision2025Controller extends Controller
{
   public function __construct()
   {
      session()->put('special_callback_url', "");
      $this->middleware('auth');
      $this->middleware('role:super-admin|administrator|est-head|cc|sc');
   }

   public function allListOpen()
   {
      $mainBranch = Auth()->user()->main_branch_id;

      if (Auth()->user()->hasRole(['administrator'])) {
         $data_text = salaryRevision2025::join('employees', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
            ->select(
               'salary_revision2025s.id',
               'salary_revision2025s.emp_no',
               'salary_revision2025s.status',
               'salary_revision2025s.checking2_status',
               'salary_revision2025s.accept_status',
               'categories.category_name as title',
               'employees.initials',
               'employees.last_name',
               'designations.designation_name',
               'g.category_name as grade',
            )
            ->orderby('salary_revision2025s.id')
            ->get();
      } else {
         $data_text = salaryRevision2025::join('employees', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
            ->select(
               'salary_revision2025s.id',
               'salary_revision2025s.emp_no',
               'salary_revision2025s.status',
               'salary_revision2025s.checking2_status',
               'salary_revision2025s.accept_status',
               'categories.category_name as title',
               'employees.initials',
               'employees.last_name',
               'designations.designation_name',
               'g.category_name as grade',
            )
            ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
            ->orderby('salary_revision2025s.id')
            ->get();
      }
      return view('admin.sal_revision.completedList', compact('data_text'));
   }

   public function checkingListOpen()
   {

      $mainBranch = Auth()->user()->main_branch_id;

      if (Auth()->user()->hasRole(['administrator'])) {
         $data_text = salaryRevision2025::join('employees', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
            ->select(
               'salary_revision2025s.id',
               'salary_revision2025s.emp_no',
               'categories.category_name as title',
               'employees.initials',
               'employees.last_name',
               'designations.designation_name',
               'g.category_name as grade',
            )
            ->where('salary_revision2025s.status', 0)
            ->orderby('salary_revision2025s.id')
            ->get();
      } else {
         $data_text = salaryRevision2025::join('employees', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
            ->select(
               'salary_revision2025s.id',
               'salary_revision2025s.emp_no',
               'categories.category_name as title',
               'employees.initials',
               'employees.last_name',
               'designations.designation_name',
               'g.category_name as grade',
            )
            ->where('salary_revision2025s.status', 0)
            ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
            ->orderby('salary_revision2025s.id')
            ->get();
      }

      return view('admin.sal_revision.1stCheckingList', compact('data_text'));
   }

   public function checkingDetailsOpen($id)
   {

      $appId = decrypt($id);

      $data_text = salaryRevision2025::join('employees', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
         ->join('categories', 'categories.id', '=', 'employees.title_id')
         ->join('designations', 'designations.id', '=', 'employees.designation_id')
         ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
         ->join('departments', 'departments.id', '=', 'employees.department_id')
         ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
         ->select(
            'salary_revision2025s.*',
            'categories.category_name as title',
            'employees.initials',
            'employees.last_name',
            'employees.designation_id',
            'employees.increment_date',
            'designations.designation_name',
            'g.category_name as gradeName',
            'departments.name_status',
            'departments.department_name',
            'faculties.id as facID',
            'faculties.faculty_name'
         )
         ->where('salary_revision2025s.id', $appId)
         ->get();

      if (count($data_text) > 0) {
         foreach ($data_text as $data_texts) {
            $empNo = $data_texts->emp_no;
            $name = $data_texts->title . ' ' . $data_texts->initials . ' ' . $data_texts->last_name;
            if ($data_texts->name_status == 0) {
               $depName = $data_texts->department_name;
            } else {
               $depName = 'Department of ' . $data_texts->department_name;
            }
            if ($data_texts->facID == 50 || $data_texts->facID == 51) {
               $facName = '';
            } else {
               $facName = $data_texts->faculty_name;
            }

            $desig = $data_texts->designation_name . ' ' . $data_texts->gradeName;
            $desigInEX = $data_texts->designation . ' ' . $data_texts->grade;

            $sal_step_2024 = $data_texts->sal_step_2024;
            $bSal_2024 = $data_texts->bSal_2024;
            $sal_2024 = $data_texts->sal_2024;
            $sal_step_2025 = $data_texts->sal_step_2025;
            $bSal_2025 = $data_texts->bSal_2025;
            $sal_2025 = $data_texts->sal_2025;
            $unpaid_2025 = $data_texts->unpaid_2025;
            $paid_2025 = $data_texts->paid_2025;
            $sal_step_withIncre_2025 = $data_texts->sal_step_withIncre_2025;
            $bSal_withIncre_2025 = $data_texts->bSal_withIncre_2025;
            $sal_withIncre_2025 = $data_texts->sal_withIncre_2025;
            $unpaid_withIncre_2025 = $data_texts->unpaid_withIncre_2025;
            $paid_withIncre_2025 = $data_texts->paid_withIncre_2025;

            $unpaid_2026 = $data_texts->unpaid_2026;
            $paid_2026 = $data_texts->paid_2026;
            $sal_step_withIncre_2026 = $data_texts->sal_step_withIncre_2026;
            $bSal_withIncre_2026 = $data_texts->bSal_withIncre_2026;
            $sal_withIncre_2026 = $data_texts->sal_withIncre_2026;
            $unpaid_withIncre_2026 = $data_texts->unpaid_withIncre_2026;
            $paid_withIncre_2026 = $data_texts->paid_withIncre_2026;

            $sal_2027 = $data_texts->sal_2027;

            $desig_grade = $data_texts->gradeName;

            $desig_id = $data_texts->designation_id;

            $sal_code_data_sheet = $data_texts->sal_code;
            $incre_date = $data_texts->increment_date;

            // $desig_text1 = Designation::join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
            //    ->join('categories', 'categories.id', '=', 'designations.service_category_id')
            //    ->select(
            //       'salary_scales.salary_scale_version_id',
            //       'salary_scales.salary_code',
            //       'salary_scales.salary_scale_txt',
            //       'categories.category_name as serviceCategory',
            //    )
            //    ->where('designations.id', $desig_id)
            //    ->where('salary_scale_version_id', 1)
            //    ->get();

            // if (count($desig_text1) > 0) {

            //    foreach ($desig_text1 as $desig_text1s) {


            //       $service_category_2024 = $desig_text1s->serviceCategory;
            //       $sal_code_2024 = $desig_text1s->salary_code;
            //       $sal_scale_2024 = $desig_text1s->salary_scale_txt;
            //    }
            // } else {
            //    $service_category_2024 = '';
            //    $sal_code_2024 = '';
            //    $sal_scale_2024 = '';
            // }

            $desig_text2 = Designation::join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
               ->join('categories', 'categories.id', '=', 'designations.service_category_id')
               ->join('salary_scales as salary_scales2', 'salary_scales2.id', '=', 'salary_scales.old_scale_id')
               ->select(
                  'salary_scales.salary_scale_version_id',
                  'salary_scales.salary_code',
                  'salary_scales.salary_scale_txt',
                  'categories.category_name as serviceCategory',
                  'salary_scales2.salary_code as salary_code2024',
                  'salary_scales2.salary_scale_txt as salary_scale_txt2024',
               )
               ->where('designations.id', $desig_id)
               ->get();

            if (count($desig_text2) > 0) {

               foreach ($desig_text2 as $desig_text2s) {

                  $service_category_2025 = $desig_text2s->serviceCategory;
                  $sal_code_2025 = $desig_text2s->salary_code;
                  $sal_scale_2025 = $desig_text2s->salary_scale_txt;

                  $service_category_2024 = $desig_text2s->serviceCategory;
                  $sal_code_2024 = $desig_text2s->salary_code2024;
                  $sal_scale_2024 = $desig_text2s->salary_scale_txt2024;
               }
            } else {
               $service_category_2025 = '';
               $sal_code_2025 = '';
               $sal_scale_2025 = '';
               $service_category_2024 = '';
               $sal_code_2024 = '';
               $sal_scale_2024 = '';
            }

            $last_eff_date = Increment::where('emp_no', $empNo)
               ->where('decision', 172)
               ->orderByDesc('effective_date')
               ->value('effective_date');

            $result = Increment::where('emp_no', $empNo)
               ->where('decision', 172)
               ->whereYear('effective_date', 2025)
               ->get();
            if (count($result) > 0) {
               $incre_have = 1;
               foreach ($result as $results) {
                  $effectiveDate = $results->effective_date;
                  $basicSalary = $results->basic_sal;
                  $increDesig = $results->desgnation_id;
               }
            } else {
               $effectiveDate = '';
               $basicSalary = '';
               $incre_have = 0;
               $increDesig = 0;
            }
            if ($sal_code_2024 == $sal_code_data_sheet) {
               $sal_code_match = 0;
            } else {
               $sal_code_match = 1;
            }
         }
      } else {
         $empNo = '';
         $name = '';
         $depName = '';
         $facName = '';
         $desig = '';
         $desigInEX = '';
         $sal_step_2024 = '';
         $bSal_2024 = '';
         $sal_2024 = '';
         $sal_step_2025 = '';
         $bSal_2025 = '';
         $sal_2025 = '';
         $unpaid_2025 = '';
         $paid_2025 = '';
         $sal_step_withIncre_2025 = '';
         $bSal_withIncre_2025 = '';
         $sal_withIncre_2025 = '';
         $unpaid_withIncre_2025 = '';
         $paid_withIncre_2025 = '';

         $unpaid_2026 = '';
         $paid_2026 = '';
         $sal_step_withIncre_2026 = '';
         $data_texts = '';
         $sal_withIncre_2026 = '';
         $unpaid_withIncre_2026 = '';
         $paid_withIncre_2026 = '';

         $sal_2027 = '';

         $desig_grade = '';

         $service_category_2024 = '';
         $sal_code_2024 = '';
         $sal_scale_2024 = '';
         $service_category_2025 = '';
         $sal_code_2025 = '';
         $sal_scale_2025 = '';

         $last_eff_date = '';

         $effectiveDate = '';
         $basicSalary = '';
         $incre_have = 0;
         $increDesig = 0;
         $desig_id = 0;

         $sal_code_match = 1;
         $incre_date = '';
      }


      return view('admin.sal_revision.1stcheckingDetails', compact(
         'empNo',
         'name',
         'depName',
         'facName',
         'desig',
         'desigInEX',
         'sal_step_2024',
         'bSal_2024',
         'sal_2024',
         'sal_step_2025',
         'bSal_2025',
         'sal_2025',
         'unpaid_2025',
         'paid_2025',
         'sal_step_withIncre_2025',
         'bSal_withIncre_2025',
         'sal_withIncre_2025',
         'unpaid_withIncre_2025',
         'paid_withIncre_2025',

         'unpaid_2026',
         'paid_2026',
         'sal_step_withIncre_2026',
         'bSal_withIncre_2026',
         'sal_withIncre_2026',
         'unpaid_withIncre_2026',
         'paid_withIncre_2026',

         'sal_2027',

         'desig_grade',

         'service_category_2024',
         'sal_code_2024',
         'sal_scale_2024',
         'service_category_2025',
         'sal_code_2025',
         'sal_scale_2025',

         'last_eff_date',

         'effectiveDate',
         'basicSalary',
         'incre_have',
         'increDesig',
         'desig_id',
         'sal_code_match',
         'incre_date'
      ));
   }

   public function checkingSave(Request $request)
   {

      // if ($request->IncreHave == 1) {
      //    $emp_bsal = $request->bSal_withIncre_2025;

      //    $increment_text = new Increment();
      //    $increment_text->emp_no = $request->empNo;
      //    $increment_text->effective_date = $request->increEffectiveDate;
      //    $increment_text->decision = 172;
      //    $increment_text->desgnation_id = $request->IncreDesig;
      //    $increment_text->basic_sal = $request->bSal_withIncre_2025;
      //    $increment_text->salary_step = $request->sal_step_withIncre_2025;
      //    $increment_text->increment_value = 0;
      //    $increment_text->reason = 'Salary revision';
      //    $increment_text->add_user_id = auth()->user()->employee_no;
      //    $increment_text->save();


      // } else {
      //    $emp_bsal = $request->bSal_2025;
      // }

      // $emp_update_text = Employee::where('employee_no',$request->empNo)->first();
      // if($emp_update_text){
      //    $emp_update_text->current_basic_salary = $emp_bsal;
      //    $emp_update_text->save();
      // }

      // $sal_revi_text = new Promotion();
      // $sal_revi_text->employee_no = $request->empNo;
      // $sal_revi_text->type_id = 369;
      // $sal_revi_text->designation_id = $request->desigID;
      // $sal_revi_text->duty_assumed_date = Carbon::parse('2025-01-01');
      // $sal_revi_text->last_working_date = Carbon::parse('1970-01-01');
      // $sal_revi_text->basic_salary = $request->bSal_2025;
      // $sal_revi_text->descriptions = 'Salary Revision';
      // $sal_revi_text->added_user_id = auth()->user()->employee_no;
      // $sal_revi_text->save();


      $update_back = salaryRevision2025::where('emp_no', $request->empNo)->first();
      if ($update_back) {
         $update_back->sal_step_2024 = $request->sal_step_2024;
         $update_back->bSal_2024 = $request->bSal_2024;
         $update_back->sal_2024 = $request->sal_2024;
         $update_back->sal_step_2025 = $request->sal_step_2025;
         $update_back->bSal_2025 = $request->bSal_2025;
         $update_back->sal_2025 = $request->sal_2025;
         $update_back->unpaid_2025 = $request->unpaid_2025;
         $update_back->paid_2025 = $request->paid_2025;
         $update_back->sal_step_withIncre_2025 = $request->sal_step_withIncre_2025;
         $update_back->bSal_withIncre_2025 = $request->bSal_withIncre_2025;
         $update_back->sal_withIncre_2025 = $request->sal_withIncre_2025;
         $update_back->unpaid_withIncre_2025 = $request->unpaid_withIncre_2025;
         $update_back->paid_withIncre_2025 = $request->paid_withIncre_2025;
         $update_back->unpaid_2026 = $request->unpaid_2026;
         $update_back->paid_2026 = $request->paid_2026;
         $update_back->sal_step_withIncre_2026 = $request->sal_step_withIncre_2026;
         $update_back->bSal_withIncre_2026 = $request->bSal_withIncre_2026;
         $update_back->sal_withIncre_2026 = $request->sal_withIncre_2026;
         $update_back->unpaid_withIncre_2026 = $request->unpaid_withIncre_2026;
         $update_back->paid_withIncre_2026 = $request->paid_withIncre_2026;
         $update_back->sal_2027 = $request->sal_2027;
         $update_back->status = 1;
         $update_back->check_user = auth()->user()->employee_no;
         $update_back->check_date = today();
         $update_back->checking2_status = 1;
         $update_back->save();

         $notification = array(
            'message' => 'Save Successfully.',
            'alert-type' => 'success'
         );
      } else {
         $notification = array(
            'message' => 'Something wrong. Please try again.',
            'alert-type' => 'error'
         );
      }


      return redirect()->route('salary.revision.1stCheckingList.open')->with($notification);
   }

   public function completedDetailsOpen($id)
   {

      $appId = decrypt($id);

      $data_text = salaryRevision2025::join('employees', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
         ->join('categories', 'categories.id', '=', 'employees.title_id')
         ->join('designations', 'designations.id', '=', 'employees.designation_id')
         ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
         ->join('departments', 'departments.id', '=', 'employees.department_id')
         ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
         ->select(
            'salary_revision2025s.*',
            'categories.category_name as title',
            'employees.initials',
            'employees.last_name',
            'employees.designation_id',
            'designations.designation_name',
            'g.category_name as gradeName',
            'departments.name_status',
            'departments.department_name',
            'faculties.id as facID',
            'faculties.faculty_name'
         )
         ->where('salary_revision2025s.id', $appId)
         ->get();

      if (count($data_text) > 0) {
         foreach ($data_text as $data_texts) {
            $empNo = $data_texts->emp_no;
            $name = $data_texts->title . ' ' . $data_texts->initials . ' ' . $data_texts->last_name;
            if ($data_texts->name_status == 0) {
               $depName = $data_texts->department_name;
            } else {
               $depName = 'Department of ' . $data_texts->department_name;
            }
            if ($data_texts->facID == 50 || $data_texts->facID == 51) {
               $facName = '';
            } else {
               $facName = $data_texts->faculty_name;
            }

            $desig = $data_texts->designation_name . ' ' . $data_texts->gradeName;
            $desigInEX = $data_texts->designation . ' ' . $data_texts->grade;

            $sal_step_2024 = $data_texts->sal_step_2024;
            $bSal_2024 = $data_texts->bSal_2024;
            $sal_2024 = $data_texts->sal_2024;
            $sal_step_2025 = $data_texts->sal_step_2025;
            $bSal_2025 = $data_texts->bSal_2025;
            $sal_2025 = $data_texts->sal_2025;
            $unpaid_2025 = $data_texts->unpaid_2025;
            $paid_2025 = $data_texts->paid_2025;
            $sal_step_withIncre_2025 = $data_texts->sal_step_withIncre_2025;
            $bSal_withIncre_2025 = $data_texts->bSal_withIncre_2025;
            $sal_withIncre_2025 = $data_texts->sal_withIncre_2025;
            $unpaid_withIncre_2025 = $data_texts->unpaid_withIncre_2025;
            $paid_withIncre_2025 = $data_texts->paid_withIncre_2025;

            $unpaid_2026 = $data_texts->unpaid_2026;
            $paid_2026 = $data_texts->paid_2026;
            $sal_step_withIncre_2026 = $data_texts->sal_step_withIncre_2026;
            $bSal_withIncre_2026 = $data_texts->bSal_withIncre_2026;
            $sal_withIncre_2026 = $data_texts->sal_withIncre_2026;
            $unpaid_withIncre_2026 = $data_texts->unpaid_withIncre_2026;
            $paid_withIncre_2026 = $data_texts->paid_withIncre_2026;

            $sal_2027 = $data_texts->sal_2027;

            $desig_grade = $data_texts->gradeName;

            $desig_id = $data_texts->designation_id;

            $sal_code_data_sheet = $data_texts->sal_code;
            $row_id = $data_texts->id;
            $accept = $data_texts->accept_status;

            $desig_text2 = Designation::join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
               ->join('categories', 'categories.id', '=', 'designations.service_category_id')
               ->join('salary_scales as salary_scales2', 'salary_scales2.id', '=', 'salary_scales.old_scale_id')
               ->select(
                  'salary_scales.salary_scale_version_id',
                  'salary_scales.salary_code',
                  'salary_scales.salary_scale_txt',
                  'categories.category_name as serviceCategory',
                  'salary_scales2.salary_code as salary_code2024',
                  'salary_scales2.salary_scale_txt as salary_scale_txt2024',
               )
               ->where('designations.id', $desig_id)
               ->get();

            if (count($desig_text2) > 0) {

               foreach ($desig_text2 as $desig_text2s) {

                  $service_category_2025 = $desig_text2s->serviceCategory;
                  $sal_code_2025 = $desig_text2s->salary_code;
                  $sal_scale_2025 = $desig_text2s->salary_scale_txt;

                  $service_category_2024 = $desig_text2s->serviceCategory;
                  $sal_code_2024 = $desig_text2s->salary_code2024;
                  $sal_scale_2024 = $desig_text2s->salary_scale_txt2024;
               }
            } else {
               $service_category_2025 = '';
               $sal_code_2025 = '';
               $sal_scale_2025 = '';
               $service_category_2024 = '';
               $sal_code_2024 = '';
               $sal_scale_2024 = '';
            }

            $last_eff_date = Increment::where('emp_no', $empNo)
               ->where('decision', 172)
               ->orderByDesc('effective_date')
               ->value('effective_date');

            $result = Increment::where('emp_no', $empNo)
               ->where('decision', 172)
               ->whereYear('effective_date', 2025)
               ->get();
            if (count($result) > 0) {
               $incre_have = 1;
               foreach ($result as $results) {
                  $effectiveDate = $results->effective_date;
                  $basicSalary = $results->basic_sal;
                  $increDesig = $results->desgnation_id;
               }
            } else {
               $effectiveDate = '';
               $basicSalary = '';
               $incre_have = 0;
               $increDesig = 0;
            }
            if ($sal_code_2024 == $sal_code_data_sheet) {
               $sal_code_match = 0;
            } else {
               $sal_code_match = 1;
            }
         }
      } else {
         $empNo = '';
         $name = '';
         $depName = '';
         $facName = '';
         $desig = '';
         $desigInEX = '';
         $sal_step_2024 = '';
         $bSal_2024 = '';
         $sal_2024 = '';
         $sal_step_2025 = '';
         $bSal_2025 = '';
         $sal_2025 = '';
         $unpaid_2025 = '';
         $paid_2025 = '';
         $sal_step_withIncre_2025 = '';
         $bSal_withIncre_2025 = '';
         $sal_withIncre_2025 = '';
         $unpaid_withIncre_2025 = '';
         $paid_withIncre_2025 = '';

         $unpaid_2026 = '';
         $paid_2026 = '';
         $sal_step_withIncre_2026 = '';
         $data_texts = '';
         $sal_withIncre_2026 = '';
         $unpaid_withIncre_2026 = '';
         $paid_withIncre_2026 = '';

         $sal_2027 = '';

         $desig_grade = '';

         $service_category_2024 = '';
         $sal_code_2024 = '';
         $sal_scale_2024 = '';
         $service_category_2025 = '';
         $sal_code_2025 = '';
         $sal_scale_2025 = '';

         $last_eff_date = '';

         $effectiveDate = '';
         $basicSalary = '';
         $incre_have = 0;
         $increDesig = 0;
         $desig_id = 0;

         $sal_code_match = 1;
         $row_id = 0;
         $accept = 0;
      }


      return view('admin.sal_revision.completedDetails', compact(
         'empNo',
         'name',
         'depName',
         'facName',
         'desig',
         'desigInEX',
         'sal_step_2024',
         'bSal_2024',
         'sal_2024',
         'sal_step_2025',
         'bSal_2025',
         'sal_2025',
         'unpaid_2025',
         'paid_2025',
         'sal_step_withIncre_2025',
         'bSal_withIncre_2025',
         'sal_withIncre_2025',
         'unpaid_withIncre_2025',
         'paid_withIncre_2025',

         'unpaid_2026',
         'paid_2026',
         'sal_step_withIncre_2026',
         'bSal_withIncre_2026',
         'sal_withIncre_2026',
         'unpaid_withIncre_2026',
         'paid_withIncre_2026',

         'sal_2027',

         'desig_grade',

         'service_category_2024',
         'sal_code_2024',
         'sal_scale_2024',
         'service_category_2025',
         'sal_code_2025',
         'sal_scale_2025',

         'last_eff_date',

         'effectiveDate',
         'basicSalary',
         'incre_have',
         'increDesig',
         'desig_id',
         'sal_code_match',
         'row_id',
         'accept'
      ));
   }

   public function checking2Open()
   {
      $mainBranch = Auth()->user()->main_branch_id;

      if (Auth()->user()->hasRole(['administrator'])) {
         $data_text = salaryRevision2025::join('employees', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
            ->select(
               'salary_revision2025s.id',
               'salary_revision2025s.emp_no',
               'categories.category_name as title',
               'employees.initials',
               'employees.last_name',
               'designations.designation_name',
               'g.category_name as grade',
            )
            ->where('salary_revision2025s.checking2_status', 1)
            ->orderby('salary_revision2025s.id')
            ->get();
      } else {
         $data_text = salaryRevision2025::join('employees', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
            ->select(
               'salary_revision2025s.id',
               'salary_revision2025s.emp_no',
               'categories.category_name as title',
               'employees.initials',
               'employees.last_name',
               'designations.designation_name',
               'g.category_name as grade',
            )
            ->where('salary_revision2025s.checking2_status', 1)
            ->where('employees.main_branch_id', Auth()->user()->main_branch_id)
            ->orderby('salary_revision2025s.id')
            ->get();
      }
      return view('admin.sal_revision.2ndCheckingList', compact('data_text'));
   }

   public function checking2DetailsOpen($id)
   {

      $appId = decrypt($id);

      $data_text = salaryRevision2025::join('employees', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
         ->join('categories', 'categories.id', '=', 'employees.title_id')
         ->join('designations', 'designations.id', '=', 'employees.designation_id')
         ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
         ->join('departments', 'departments.id', '=', 'employees.department_id')
         ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
         ->select(
            'salary_revision2025s.*',
            'categories.category_name as title',
            'employees.initials',
            'employees.last_name',
            'employees.designation_id',
            'employees.increment_date',
            'designations.designation_name',
            'g.category_name as gradeName',
            'departments.name_status',
            'departments.department_name',
            'faculties.id as facID',
            'faculties.faculty_name'
         )
         ->where('salary_revision2025s.id', $appId)
         ->get();

      if (count($data_text) > 0) {
         foreach ($data_text as $data_texts) {
            $empNo = $data_texts->emp_no;
            $name = $data_texts->title . ' ' . $data_texts->initials . ' ' . $data_texts->last_name;
            if ($data_texts->name_status == 0) {
               $depName = $data_texts->department_name;
            } else {
               $depName = 'Department of ' . $data_texts->department_name;
            }
            if ($data_texts->facID == 50 || $data_texts->facID == 51) {
               $facName = '';
            } else {
               $facName = $data_texts->faculty_name;
            }

            $desig = $data_texts->designation_name . ' ' . $data_texts->gradeName;
            $desigInEX = $data_texts->designation . ' ' . $data_texts->grade;

            $sal_step_2024 = $data_texts->sal_step_2024;
            $bSal_2024 = $data_texts->bSal_2024;
            $sal_2024 = $data_texts->sal_2024;
            $sal_step_2025 = $data_texts->sal_step_2025;
            $bSal_2025 = $data_texts->bSal_2025;
            $sal_2025 = $data_texts->sal_2025;
            $unpaid_2025 = $data_texts->unpaid_2025;
            $paid_2025 = $data_texts->paid_2025;
            $sal_step_withIncre_2025 = $data_texts->sal_step_withIncre_2025;
            $bSal_withIncre_2025 = $data_texts->bSal_withIncre_2025;
            $sal_withIncre_2025 = $data_texts->sal_withIncre_2025;
            $unpaid_withIncre_2025 = $data_texts->unpaid_withIncre_2025;
            $paid_withIncre_2025 = $data_texts->paid_withIncre_2025;

            $unpaid_2026 = $data_texts->unpaid_2026;
            $paid_2026 = $data_texts->paid_2026;
            $sal_step_withIncre_2026 = $data_texts->sal_step_withIncre_2026;
            $bSal_withIncre_2026 = $data_texts->bSal_withIncre_2026;
            $sal_withIncre_2026 = $data_texts->sal_withIncre_2026;
            $unpaid_withIncre_2026 = $data_texts->unpaid_withIncre_2026;
            $paid_withIncre_2026 = $data_texts->paid_withIncre_2026;

            $sal_2027 = $data_texts->sal_2027;

            $desig_grade = $data_texts->gradeName;

            $desig_id = $data_texts->designation_id;

            $sal_code_data_sheet = $data_texts->sal_code;
            $incre_date = $data_texts->increment_date;
            $row_id = $data_texts->id;

            $desig_text2 = Designation::join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
               ->join('categories', 'categories.id', '=', 'designations.service_category_id')
               ->join('salary_scales as salary_scales2', 'salary_scales2.id', '=', 'salary_scales.old_scale_id')
               ->select(
                  'salary_scales.salary_scale_version_id',
                  'salary_scales.salary_code',
                  'salary_scales.salary_scale_txt',
                  'categories.category_name as serviceCategory',
                  'salary_scales2.salary_code as salary_code2024',
                  'salary_scales2.salary_scale_txt as salary_scale_txt2024',
               )
               ->where('designations.id', $desig_id)
               ->get();

            if (count($desig_text2) > 0) {

               foreach ($desig_text2 as $desig_text2s) {

                  $service_category_2025 = $desig_text2s->serviceCategory;
                  $sal_code_2025 = $desig_text2s->salary_code;
                  $sal_scale_2025 = $desig_text2s->salary_scale_txt;

                  $service_category_2024 = $desig_text2s->serviceCategory;
                  $sal_code_2024 = $desig_text2s->salary_code2024;
                  $sal_scale_2024 = $desig_text2s->salary_scale_txt2024;
               }
            } else {
               $service_category_2025 = '';
               $sal_code_2025 = '';
               $sal_scale_2025 = '';
               $service_category_2024 = '';
               $sal_code_2024 = '';
               $sal_scale_2024 = '';
            }


            $last_eff_date = Increment::where('emp_no', $empNo)
               ->where('decision', 172)
               ->orderByDesc('effective_date')
               ->value('effective_date');

            $result = Increment::where('emp_no', $empNo)
               ->where('decision', 172)
               ->whereYear('effective_date', 2025)
               ->get();
            if (count($result) > 0) {
               $incre_have = 1;
               foreach ($result as $results) {
                  $effectiveDate = $results->effective_date;
                  $basicSalary = $results->basic_sal;
                  $increDesig = $results->desgnation_id;
               }
            } else {
               $effectiveDate = '';
               $basicSalary = '';
               $incre_have = 0;
               $increDesig = 0;
            }
            if ($sal_code_2024 == $sal_code_data_sheet) {
               $sal_code_match = 0;
            } else {
               $sal_code_match = 1;
            }
         }
      } else {
         $empNo = '';
         $name = '';
         $depName = '';
         $facName = '';
         $desig = '';
         $desigInEX = '';
         $sal_step_2024 = '';
         $bSal_2024 = '';
         $sal_2024 = '';
         $sal_step_2025 = '';
         $bSal_2025 = '';
         $sal_2025 = '';
         $unpaid_2025 = '';
         $paid_2025 = '';
         $sal_step_withIncre_2025 = '';
         $bSal_withIncre_2025 = '';
         $sal_withIncre_2025 = '';
         $unpaid_withIncre_2025 = '';
         $paid_withIncre_2025 = '';

         $unpaid_2026 = '';
         $paid_2026 = '';
         $sal_step_withIncre_2026 = '';
         $data_texts = '';
         $sal_withIncre_2026 = '';
         $unpaid_withIncre_2026 = '';
         $paid_withIncre_2026 = '';

         $sal_2027 = '';

         $desig_grade = '';

         $service_category_2024 = '';
         $sal_code_2024 = '';
         $sal_scale_2024 = '';
         $service_category_2025 = '';
         $sal_code_2025 = '';
         $sal_scale_2025 = '';

         $last_eff_date = '';

         $effectiveDate = '';
         $basicSalary = '';
         $incre_have = 0;
         $increDesig = 0;
         $desig_id = 0;

         $sal_code_match = 1;
         $incre_date = '';
         $row_id = 0;
      }


      return view('admin.sal_revision.2ndCheckingDetails', compact(
         'empNo',
         'name',
         'depName',
         'facName',
         'desig',
         'desigInEX',
         'sal_step_2024',
         'bSal_2024',
         'sal_2024',
         'sal_step_2025',
         'bSal_2025',
         'sal_2025',
         'unpaid_2025',
         'paid_2025',
         'sal_step_withIncre_2025',
         'bSal_withIncre_2025',
         'sal_withIncre_2025',
         'unpaid_withIncre_2025',
         'paid_withIncre_2025',

         'unpaid_2026',
         'paid_2026',
         'sal_step_withIncre_2026',
         'bSal_withIncre_2026',
         'sal_withIncre_2026',
         'unpaid_withIncre_2026',
         'paid_withIncre_2026',

         'sal_2027',

         'desig_grade',

         'service_category_2024',
         'sal_code_2024',
         'sal_scale_2024',
         'service_category_2025',
         'sal_code_2025',
         'sal_scale_2025',

         'last_eff_date',

         'effectiveDate',
         'basicSalary',
         'incre_have',
         'increDesig',
         'desig_id',
         'sal_code_match',
         'incre_date',
         'row_id'
      ));
   }

   public function check2_save($id)
   {
      $appId = decrypt($id);

      $update_text = salaryRevision2025::where('id', $appId)->first();

      if ($update_text) {
         $update_text->checking2_status = 2;
         $update_text->checking2_user = auth()->user()->employee_no;
         $update_text->checking2_date = today();
         $update_text->accept_status = 1;
         $update_text->save();

         $notification = array(
            'message' => 'Save Successfully.',
            'alert-type' => 'success'
         );
      } else {
         $notification = array(
            'message' => 'Something wrong. Please try again.',
            'alert-type' => 'error'
         );
      }

      return redirect()->route('salary.revision.2ndCheckingList.open')->with($notification);
   }

   public function check2_back_to_checking1($id)
   {
      $appId = decrypt($id);

      $update_text = salaryRevision2025::where('id', $appId)->first();

      if ($update_text) {
         $update_text->status = 0;
         $update_text->checking2_status = 0;
         $update_text->save();

         $notification = array(
            'message' => 'Save Successfully.',
            'alert-type' => 'success'
         );
      } else {
         $notification = array(
            'message' => 'Something wrong. Please try again.',
            'alert-type' => 'error'
         );
      }

      return redirect()->route('salary.revision.2ndCheckingList.open')->with($notification);
   }

   public function acceptListOpen()
   {
      $mainBranch = Auth()->user()->main_branch_id;

      if (Auth()->user()->hasRole(['administrator'])) {
         $data_text = salaryRevision2025::join('employees', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
            ->select(
               'salary_revision2025s.id',
               'salary_revision2025s.emp_no',
               'categories.category_name as title',
               'employees.initials',
               'employees.last_name',
               'designations.designation_name',
               'g.category_name as grade',
            )
            ->where('salary_revision2025s.accept_status', 1)
            ->orderby('salary_revision2025s.id')
            ->get();
      } else {
         $data_text = salaryRevision2025::join('employees', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
            ->join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
            ->select(
               'salary_revision2025s.id',
               'salary_revision2025s.emp_no',
               'categories.category_name as title',
               'employees.initials',
               'employees.last_name',
               'designations.designation_name',
               'g.category_name as grade',
            )
            ->where('salary_revision2025s.accept_status', 1)
            ->where('employees.main_branch_id', Auth()->user()->main_branch_id)
            ->orderby('salary_revision2025s.id')
            ->get();
      }
      return view('admin.sal_revision.acceptList', compact('data_text'));
   }

   public function accept_back_to_checking1($id)
   {
      $appId = decrypt($id);

      $update_text = salaryRevision2025::where('id', $appId)->first();

      if ($update_text) {
         $update_text->status = 0;
         $update_text->checking2_status = 0;
         $update_text->accept_status = 0;
         $update_text->save();

         $notification = array(
            'message' => 'Save Successfully.',
            'alert-type' => 'success'
         );
      } else {
         $notification = array(
            'message' => 'Something wrong. Please try again.',
            'alert-type' => 'error'
         );
      }

      return redirect()->route('salary.revision.acceptList.open')->with($notification);
   }

   public function acceptDetailsOpen($id)
   {

      $appId = decrypt($id);

      $data_text = salaryRevision2025::join('employees', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
         ->join('categories', 'categories.id', '=', 'employees.title_id')
         ->join('designations', 'designations.id', '=', 'employees.designation_id')
         ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
         ->join('departments', 'departments.id', '=', 'employees.department_id')
         ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
         ->select(
            'salary_revision2025s.*',
            'categories.category_name as title',
            'employees.initials',
            'employees.last_name',
            'employees.designation_id',
            'employees.increment_date',
            'designations.designation_name',
            'g.category_name as gradeName',
            'departments.name_status',
            'departments.department_name',
            'faculties.id as facID',
            'faculties.faculty_name'
         )
         ->where('salary_revision2025s.id', $appId)
         ->get();

      if (count($data_text) > 0) {
         foreach ($data_text as $data_texts) {
            $empNo = $data_texts->emp_no;
            $name = $data_texts->title . ' ' . $data_texts->initials . ' ' . $data_texts->last_name;
            if ($data_texts->name_status == 0) {
               $depName = $data_texts->department_name;
            } else {
               $depName = 'Department of ' . $data_texts->department_name;
            }
            if ($data_texts->facID == 50 || $data_texts->facID == 51) {
               $facName = '';
            } else {
               $facName = $data_texts->faculty_name;
            }

            $desig = $data_texts->designation_name . ' ' . $data_texts->gradeName;
            $desigInEX = $data_texts->designation . ' ' . $data_texts->grade;

            $sal_step_2024 = $data_texts->sal_step_2024;
            $bSal_2024 = $data_texts->bSal_2024;
            $sal_2024 = $data_texts->sal_2024;
            $sal_step_2025 = $data_texts->sal_step_2025;
            $bSal_2025 = $data_texts->bSal_2025;
            $sal_2025 = $data_texts->sal_2025;
            $unpaid_2025 = $data_texts->unpaid_2025;
            $paid_2025 = $data_texts->paid_2025;
            $sal_step_withIncre_2025 = $data_texts->sal_step_withIncre_2025;
            $bSal_withIncre_2025 = $data_texts->bSal_withIncre_2025;
            $sal_withIncre_2025 = $data_texts->sal_withIncre_2025;
            $unpaid_withIncre_2025 = $data_texts->unpaid_withIncre_2025;
            $paid_withIncre_2025 = $data_texts->paid_withIncre_2025;

            $unpaid_2026 = $data_texts->unpaid_2026;
            $paid_2026 = $data_texts->paid_2026;
            $sal_step_withIncre_2026 = $data_texts->sal_step_withIncre_2026;
            $bSal_withIncre_2026 = $data_texts->bSal_withIncre_2026;
            $sal_withIncre_2026 = $data_texts->sal_withIncre_2026;
            $unpaid_withIncre_2026 = $data_texts->unpaid_withIncre_2026;
            $paid_withIncre_2026 = $data_texts->paid_withIncre_2026;

            $sal_2027 = $data_texts->sal_2027;

            $desig_grade = $data_texts->gradeName;

            $desig_id = $data_texts->designation_id;

            $sal_code_data_sheet = $data_texts->sal_code;
            $incre_date = $data_texts->increment_date;
            $row_id = $data_texts->id;

            $desig_text2 = Designation::join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
               ->join('categories', 'categories.id', '=', 'designations.service_category_id')
               ->join('salary_scales as salary_scales2', 'salary_scales2.id', '=', 'salary_scales.old_scale_id')
               ->select(
                  'salary_scales.salary_scale_version_id',
                  'salary_scales.salary_code',
                  'salary_scales.salary_scale_txt',
                  'categories.category_name as serviceCategory',
                  'salary_scales2.salary_code as salary_code2024',
                  'salary_scales2.salary_scale_txt as salary_scale_txt2024',
               )
               ->where('designations.id', $desig_id)
               ->get();

            if (count($desig_text2) > 0) {

               foreach ($desig_text2 as $desig_text2s) {

                  $service_category_2025 = $desig_text2s->serviceCategory;
                  $sal_code_2025 = $desig_text2s->salary_code;
                  $sal_scale_2025 = $desig_text2s->salary_scale_txt;

                  $service_category_2024 = $desig_text2s->serviceCategory;
                  $sal_code_2024 = $desig_text2s->salary_code2024;
                  $sal_scale_2024 = $desig_text2s->salary_scale_txt2024;
               }
            } else {
               $service_category_2025 = '';
               $sal_code_2025 = '';
               $sal_scale_2025 = '';
               $service_category_2024 = '';
               $sal_code_2024 = '';
               $sal_scale_2024 = '';
            }


            $last_eff_date = Increment::where('emp_no', $empNo)
               ->where('decision', 172)
               ->orderByDesc('effective_date')
               ->value('effective_date');

            $result = Increment::where('emp_no', $empNo)
               ->where('decision', 172)
               ->whereYear('effective_date', 2025)
               ->get();
            if (count($result) > 0) {
               $incre_have = 1;
               foreach ($result as $results) {
                  $effectiveDate = $results->effective_date;
                  $basicSalary = $results->basic_sal;
                  $increDesig = $results->desgnation_id;
               }
            } else {
               $effectiveDate = '';
               $basicSalary = '';
               $incre_have = 0;
               $increDesig = 0;
            }
            if ($sal_code_2024 == $sal_code_data_sheet) {
               $sal_code_match = 0;
            } else {
               $sal_code_match = 1;
            }
         }
      } else {
         $empNo = '';
         $name = '';
         $depName = '';
         $facName = '';
         $desig = '';
         $desigInEX = '';
         $sal_step_2024 = '';
         $bSal_2024 = '';
         $sal_2024 = '';
         $sal_step_2025 = '';
         $bSal_2025 = '';
         $sal_2025 = '';
         $unpaid_2025 = '';
         $paid_2025 = '';
         $sal_step_withIncre_2025 = '';
         $bSal_withIncre_2025 = '';
         $sal_withIncre_2025 = '';
         $unpaid_withIncre_2025 = '';
         $paid_withIncre_2025 = '';

         $unpaid_2026 = '';
         $paid_2026 = '';
         $sal_step_withIncre_2026 = '';
         $data_texts = '';
         $sal_withIncre_2026 = '';
         $unpaid_withIncre_2026 = '';
         $paid_withIncre_2026 = '';

         $sal_2027 = '';

         $desig_grade = '';

         $service_category_2024 = '';
         $sal_code_2024 = '';
         $sal_scale_2024 = '';
         $service_category_2025 = '';
         $sal_code_2025 = '';
         $sal_scale_2025 = '';

         $last_eff_date = '';

         $effectiveDate = '';
         $basicSalary = '';
         $incre_have = 0;
         $increDesig = 0;
         $desig_id = 0;

         $sal_code_match = 1;
         $incre_date = '';
         $row_id = 0;
      }


      return view('admin.sal_revision.acceptDetails', compact(
         'empNo',
         'name',
         'depName',
         'facName',
         'desig',
         'desigInEX',
         'sal_step_2024',
         'bSal_2024',
         'sal_2024',
         'sal_step_2025',
         'bSal_2025',
         'sal_2025',
         'unpaid_2025',
         'paid_2025',
         'sal_step_withIncre_2025',
         'bSal_withIncre_2025',
         'sal_withIncre_2025',
         'unpaid_withIncre_2025',
         'paid_withIncre_2025',

         'unpaid_2026',
         'paid_2026',
         'sal_step_withIncre_2026',
         'bSal_withIncre_2026',
         'sal_withIncre_2026',
         'unpaid_withIncre_2026',
         'paid_withIncre_2026',

         'sal_2027',

         'desig_grade',

         'service_category_2024',
         'sal_code_2024',
         'sal_scale_2024',
         'service_category_2025',
         'sal_code_2025',
         'sal_scale_2025',

         'last_eff_date',

         'effectiveDate',
         'basicSalary',
         'incre_have',
         'increDesig',
         'desig_id',
         'sal_code_match',
         'incre_date',
         'row_id'
      ));
   }

   public function acceptSave($id)
   {
      try {
         $appId = decrypt($id);
      } catch (\Exception $e) {
         return redirect()->back()->withErrors('Invalid ID.');
      }

      $select_text = salaryRevision2025::where('id', $appId)->first();

      if ($select_text) {

         $result = Increment::where('emp_no', $select_text->emp_no)
            ->where('decision', 172)
            ->whereYear('effective_date', 2025)
            ->first();
         if ($result) {
            $increment_text = new Increment();
            $increment_text->emp_no = $select_text->emp_no;
            $increment_text->effective_date = $result->effective_date;
            $increment_text->decision = 172;
            $increment_text->desgnation_id = $result->desgnation_id;
            $increment_text->basic_sal = $select_text->bSal_withIncre_2025;
            $increment_text->salary_step = $select_text->sal_step_withIncre_2025;
            $increment_text->increment_value = 0;
            $increment_text->reason = 'Salary Revision';
            $increment_text->add_user_id = $select_text->check_user;
            $increment_text->save();

            $emp_bsal = $select_text->bSal_withIncre_2025;
         } else {
            $emp_bsal = $select_text->bSal_2025;
         }

         $emp_update_text = Employee::where('employee_no', $select_text->emp_no)->first();
         if ($emp_update_text) {
            $emp_update_text->current_basic_salary = $emp_bsal;
            $emp_update_text->save();

            $sal_revi_text = new Promotion();
            $sal_revi_text->employee_no = $select_text->emp_no;
            $sal_revi_text->type_id = 369;
            $sal_revi_text->designation_id = $emp_update_text->designation_id;
            $sal_revi_text->duty_assumed_date = Carbon::parse('2025-01-01');
            $sal_revi_text->last_working_date = Carbon::parse('1970-01-01');
            $sal_revi_text->basic_salary = $select_text->bSal_2025;
            $sal_revi_text->descriptions = 'Salary Revision';
            $sal_revi_text->added_user_id = $select_text->check_user;
            $sal_revi_text->save();

            $select_text->accept_status = 2;
            $select_text->accept_user = auth()->user()->employee_no;
            $select_text->accept_date = today();
            $select_text->save();

            $notification = array(
               'message' => 'Save Successfully.',
               'alert-type' => 'success'
            );

            return redirect()->route('salary.revision.acceptList.open')->with($notification);
         } else {
            return redirect()->back()->withErrors('Something wrong. Please try again.');
         }
      } else {
         return redirect()->back()->withErrors('Record not found.');
      }
   }

   public function acceptSaveAll()
   {

      $select_text2 = salaryRevision2025::where('accept_status', 1)->get();

      if (count($select_text2) > 0) {
         foreach ($select_text2 as $select_text) {

            $result = Increment::where('emp_no', $select_text->emp_no)
               ->where('decision', 172)
               ->whereYear('effective_date', 2025)
               ->first();
            if ($result) {
               $increment_text = new Increment();
               $increment_text->emp_no = $select_text->emp_no;
               $increment_text->effective_date = $result->effective_date;
               $increment_text->decision = 172;
               $increment_text->desgnation_id = $result->desgnation_id;
               $increment_text->basic_sal = $select_text->bSal_withIncre_2025;
               $increment_text->salary_step = $select_text->sal_step_withIncre_2025;
               $increment_text->increment_value = 0;
               $increment_text->reason = 'Salary Revision';
               $increment_text->add_user_id = $select_text->check_user;
               $increment_text->save();

               $emp_bsal = $select_text->bSal_withIncre_2025;
            } else {
               $emp_bsal = $select_text->bSal_2025;
            }

            $emp_update_text = Employee::where('employee_no', $select_text->emp_no)->first();
            if ($emp_update_text) {
               $emp_update_text->current_basic_salary = $emp_bsal;
               $emp_update_text->save();

               $sal_revi_text = new Promotion();
               $sal_revi_text->employee_no = $select_text->emp_no;
               $sal_revi_text->type_id = 369;
               $sal_revi_text->designation_id = $emp_update_text->designation_id;
               $sal_revi_text->duty_assumed_date = Carbon::parse('2025-01-01');
               $sal_revi_text->last_working_date = Carbon::parse('1970-01-01');
               $sal_revi_text->basic_salary = $select_text->bSal_2025;
               $sal_revi_text->descriptions = 'Salary Revision';
               $sal_revi_text->added_user_id = $select_text->check_user;
               $sal_revi_text->save();

               $select_text->accept_status = 2;
               $select_text->accept_user = auth()->user()->employee_no;
               $select_text->accept_date = today();
               $select_text->save();

            } else {
               return redirect()->back()->withErrors('Something wrong. Please try again.');
            }
         }
         $notification = array(
            'message' => 'Save Successfully.',
            'alert-type' => 'success'
         );

         return redirect()->route('salary.revision.acceptList.open')->with($notification);

      } else {
         return redirect()->back()->withErrors('Record not found.');
      }
   }


   public function print2025($id)
   {
      $appId = decrypt($id);
      $today = today();

      $data_text = salaryRevision2025::join('employees', 'employees.employee_no', '=', 'salary_revision2025s.emp_no')
         ->join('categories', 'categories.id', '=', 'employees.title_id')
         ->join('designations', 'designations.id', '=', 'employees.designation_id')
         ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
         ->join('departments', 'departments.id', '=', 'employees.department_id')
         ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
         ->select(
            'salary_revision2025s.*',
            'categories.category_name as title',
            'employees.initials',
            'employees.last_name',
            'employees.designation_id',
            'employees.increment_date',
            'employees.file_reference_number',
            'designations.designation_name',
            'g.category_name as gradeName',
            'departments.name_status',
            'departments.department_name',
            'faculties.id as facID',
            'faculties.faculty_name'
         )
         ->where('salary_revision2025s.id', $appId)
         ->get();

      if (count($data_text) > 0) {
         foreach ($data_text as $data_texts) {
            $empNo = $data_texts->emp_no;
            $name = $data_texts->title . ' ' . $data_texts->initials . ' ' . $data_texts->last_name;
            if ($data_texts->name_status == 0) {
               $depName = $data_texts->department_name;
            } else {
               $depName = 'Department of ' . $data_texts->department_name;
            }
            if ($data_texts->facID == 50 || $data_texts->facID == 51 || $data_texts->facID == 7) {
               $facName = '';
            } else {
               $facName = $data_texts->faculty_name;
            }

            $desig = $data_texts->designation_name . ' ' . $data_texts->gradeName;
            $desigInEX = $data_texts->designation . ' ' . $data_texts->grade;

            $sal_step_2024 = $data_texts->sal_step_2024;
            $bSal_2024 = $data_texts->bSal_2024;
            $sal_2024 = $data_texts->sal_2024;
            $sal_step_2025 = $data_texts->sal_step_2025;
            $bSal_2025 = $data_texts->bSal_2025;
            $sal_2025 = $data_texts->sal_2025;
            $unpaid_2025 = $data_texts->unpaid_2025;
            $paid_2025 = $data_texts->paid_2025;
            $sal_step_withIncre_2025 = $data_texts->sal_step_withIncre_2025;
            $bSal_withIncre_2025 = $data_texts->bSal_withIncre_2025;
            $sal_withIncre_2025 = $data_texts->sal_withIncre_2025;
            $unpaid_withIncre_2025 = $data_texts->unpaid_withIncre_2025;
            $paid_withIncre_2025 = $data_texts->paid_withIncre_2025;

            $unpaid_2026 = $data_texts->unpaid_2026;
            $paid_2026 = $data_texts->paid_2026;
            $sal_step_withIncre_2026 = $data_texts->sal_step_withIncre_2026;
            $bSal_withIncre_2026 = $data_texts->bSal_withIncre_2026;
            $sal_withIncre_2026 = $data_texts->sal_withIncre_2026;
            $unpaid_withIncre_2026 = $data_texts->unpaid_withIncre_2026;
            $paid_withIncre_2026 = $data_texts->paid_withIncre_2026;

            $sal_2027 = $data_texts->sal_2027;

            $desig_grade = $data_texts->gradeName;

            $desig_id = $data_texts->designation_id;

            $sal_code_data_sheet = $data_texts->sal_code;
            $incre_date = $data_texts->increment_date;
            $row_id = $data_texts->id;

            $check1_empNo = $data_texts->check_user;
            $check1_date = $data_texts->check_date;
            $check2_empNo = $data_texts->checking2_user;
            $check2_date = $data_texts->checking2_date;
            $accept_empNo = $data_texts->accept_user;
            $accept_date = $data_texts->accept_date;

            $file_no = $data_texts->file_reference_number;

            $facID = $data_texts->facID;

            $desig_text2 = Designation::join('salary_scales', 'salary_scales.id', '=', 'designations.salary_scale')
               ->join('categories', 'categories.id', '=', 'designations.service_category_id')
               ->join('salary_scales as salary_scales2', 'salary_scales2.id', '=', 'salary_scales.old_scale_id')
               ->select(
                  'salary_scales.salary_scale_version_id',
                  'salary_scales.salary_code',
                  'salary_scales.salary_scale_txt',
                  'categories.category_name as serviceCategory',
                  'salary_scales2.salary_code as salary_code2024',
                  'salary_scales2.salary_scale_txt as salary_scale_txt2024',
               )
               ->where('designations.id', $desig_id)
               ->get();

            if (count($desig_text2) > 0) {

               foreach ($desig_text2 as $desig_text2s) {

                  $service_category_2025 = $desig_text2s->serviceCategory;
                  $sal_code_2025 = $desig_text2s->salary_code;
                  $sal_scale_2025 = $desig_text2s->salary_scale_txt;

                  $service_category_2024 = $desig_text2s->serviceCategory;
                  $sal_code_2024 = $desig_text2s->salary_code2024;
                  $sal_scale_2024 = $desig_text2s->salary_scale_txt2024;
               }
            } else {
               $service_category_2025 = '';
               $sal_code_2025 = '';
               $sal_scale_2025 = '';
               $service_category_2024 = '';
               $sal_code_2024 = '';
               $sal_scale_2024 = '';
            }


            $last_eff_date = Increment::where('emp_no', $empNo)
               ->where('decision', 172)
               ->orderByDesc('effective_date')
               ->value('effective_date');

            $result = Increment::where('emp_no', $empNo)
               ->where('decision', 172)
               ->whereYear('effective_date', 2025)
               ->get();
            if (count($result) > 0) {
               $incre_have = 1;
               foreach ($result as $results) {
                  $effectiveDate = $results->effective_date;
                  $basicSalary = $results->basic_sal;
                  $increDesig = $results->desgnation_id;
               }
            } else {
               $effectiveDate = '';
               $basicSalary = '';
               $incre_have = 0;
               $increDesig = 0;
            }

         }

         $check1_text = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
         ->join('designations', 'designations.id', '=', 'employees.designation_id')
         ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
         ->select(

            'categories.category_name as title',
            'employees.initials',
            'employees.last_name',
            'designations.designation_name',
            'g.category_name as gradeName',
         )
         ->where('employees.employee_no',$check1_empNo)
         ->first();
         if ($check1_text) {
            $check1_name =  $check1_text->title . ' ' . $check1_text->initials . ' ' . $check1_text->last_name;
            if ($check1_text->gradeName == '') {
               $check1_desig = $check1_text->designation_name;
            } else {
               $check1_desig = $check1_text->designation_name . ' (' . $check1_text->gradeName.')';
            }


         } else {
            $check1_name = '';
            $check1_desig = '';
         }

          $check2_text = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
         ->join('designations', 'designations.id', '=', 'employees.designation_id')
         ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
         ->select(

            'categories.category_name as title',
            'employees.initials',
            'employees.last_name',
            'designations.designation_name',
            'g.category_name as gradeName',
         )
         ->where('employees.employee_no',$check2_empNo)
         ->first();
         if ($check2_text) {
            $check2_name =  $check2_text->title . ' ' . $check2_text->initials . ' ' . $check2_text->last_name;
            if ($check2_text->gradeName == '') {
               $check2_desig = $check2_text->designation_name;
            } else {
                $check2_desig = $check2_text->designation_name . ' (' . $check2_text->gradeName.')';
            }


         } else {
            $check2_name = '';
            $check2_desig = '';
         }

          $accept_text = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
         ->join('designations', 'designations.id', '=', 'employees.designation_id')
         ->join('categories as g', 'g.id', '=', 'designations.staff_grade')
         ->join('departments','departments.id','=','employees.department_id')
         ->select(

            'categories.category_name as title',
            'employees.initials',
            'employees.last_name',
            'designations.designation_name',
            'g.category_name as gradeName',
            'departments.department_name'
         )
         ->where('employees.employee_no',$accept_empNo)
         ->first();
         if ($accept_text) {
            $accept_name =  $accept_text->title . ' ' . $accept_text->initials . ' ' . $accept_text->last_name;
            $accept_desig = $accept_text->designation_name . ' ' . $accept_text->gradeName;
            $accept_depName = $accept_text->department_name;
         } else {
            $accept_name = '';
            $accept_desig = '';
            $accept_depName = '';
         }


      } else {
         $empNo = '';
         $name = '';
         $depName = '';
         $facName = '';
         $desig = '';
         $desigInEX = '';
         $sal_step_2024 = '';
         $bSal_2024 = '';
         $sal_2024 = '';
         $sal_step_2025 = '';
         $bSal_2025 = '';
         $sal_2025 = '';
         $unpaid_2025 = '';
         $paid_2025 = '';
         $sal_step_withIncre_2025 = '';
         $bSal_withIncre_2025 = '';
         $sal_withIncre_2025 = '';
         $unpaid_withIncre_2025 = '';
         $paid_withIncre_2025 = '';

         $unpaid_2026 = '';
         $paid_2026 = '';
         $sal_step_withIncre_2026 = '';
         $data_texts = '';
         $sal_withIncre_2026 = '';
         $unpaid_withIncre_2026 = '';
         $paid_withIncre_2026 = '';

         $sal_2027 = '';

         $desig_grade = '';

         $service_category_2024 = '';
         $sal_code_2024 = '';
         $sal_scale_2024 = '';
         $service_category_2025 = '';
         $sal_code_2025 = '';
         $sal_scale_2025 = '';

         $last_eff_date = '';

         $effectiveDate = '';
         $basicSalary = '';
         $incre_have = 0;
         $increDesig = 0;
         $desig_id = 0;


         $incre_date = '';
         $row_id = 0;

         $check1_name = '';
         $check1_desig = '';
         $check1_date = '';

         $check2_name = '';
         $check2_desig = '';
         $check2_date = '';

          $accept_name = '';
         $accept_desig = '';
         $accept_date = '';

         $file_no = '';
         $accept_depName = '';

         $facID = 0;
      }

      $data = [
         'empNo' => $empNo,
         'name' => $name,
         'desig' => $desig,
         'depName' => $depName,
         'facName' => $facName,
         'service_category_2024' => $service_category_2024,
         'sal_code_2024' => $sal_code_2024,
         'sal_scale_2024' => $sal_scale_2024,
         'desig_grade' => $desig_grade,
         'sal_step_2024' => $sal_step_2024,
         'bSal_2024' => $bSal_2024,
         'sal_2024' => $sal_2024,
         'last_eff_date' => $last_eff_date,
         'service_category_2025' => $service_category_2025,
         'sal_code_2025' => $sal_code_2025,
         'sal_scale_2025' => $sal_scale_2025,
         'sal_step_2025' => $sal_step_2025,
         'bSal_2025' => $bSal_2025,
         'sal_2025' => $sal_2025,
         'bSal_withIncre_2025' => $bSal_withIncre_2025,
         'unpaid_2025' =>$unpaid_2025,
         'paid_2025' => $paid_2025,
         'incre_date' => $incre_date,
         'sal_withIncre_2025' => $sal_withIncre_2025,
         'unpaid_withIncre_2025' => $unpaid_withIncre_2025,
         'paid_withIncre_2025' => $paid_withIncre_2025,

         'check1_name' => $check1_name,
         'check1_desig' => $check1_desig,
         'check1_date' => $check1_date,

         'check2_name' => $check2_name,
         'check2_desig' => $check2_desig,
         'check2_date' => $check2_date,

         'accept_name' => $accept_name,
         'accept_desig' => $accept_desig,
         'accept_date' => $accept_date,
         'file_no' => $file_no,
         'accept_depName' => $accept_depName,
         'facID' => $facID,
         'today' => $today
      ];
      //new comment for test update
      $pdf = FacadePdf::loadView('admin.sal_revision.sal_letter_2025', $data)->setPaper('a4');

      return $pdf->stream('Salary_revision_letter_2025.pdf');
   }
}
