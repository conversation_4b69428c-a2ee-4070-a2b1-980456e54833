<div class="card card-outline card-danger">
      <div class="card-header">
        <div class="row">
            <div class="col-12 col-sm-12 col-md-12 col-lg-12"> <!-- Adjust the column sizes based on your layout -->
                <h1 class="card-title"><strong>Employment Records (From First Employment to Upwards)</strong></h1>
                <br>
                <p style="font-size: 14px;">Enter the following details and click the "Add" button to add the record to the table. You are allowed to insert multiple records one by one.</p>
            </div>
        </div>
    </div>
    <div class="card-body accordion-body" id="collapseO2">
        <div class="row">
            <div class="col-md-6 col-sm-12">
                <div class="form-group">
                    <label for="experience_designation">Designation</label>
                    <input type="text" name="experience_designation_1" class="form-control" id="experience_designation" maxlength="250"/>
                </div>
            </div>
            <div class="col-md-6 col-sm-12">
                <div class="form-group">
                    <label for="experience_institution">Institution</label>
                    <input type="text" name="experience_institution_1" class="form-control" id="experience_institution" maxlength="250"/>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 col-sm-12">
                <div class="form-group">
                    <label for="experience_start_date">Start Date</label>
                    <div class="input-group date" id="proDateShow1" data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input" data-target="#proDateShow1" name="experience_start_date_1" id="experience_start_date" placeholder="01-Jan-2000" data-target="#proDateShow1" data-toggle="datetimepicker">
                        <div class="input-group-append" data-target="#proDateShow1" data-toggle="datetimepicker">
                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-sm-12">
                <div class="form-group">
                    <label for="experience_end_year">End Date</label>
                    <div class="input-group date" id="proDateShow2" data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input" data-target="#proDateShow2" name="experience_end_date_1" id="experience_end_date" placeholder="01-Jan-2000" data-target="#proDateShow2" data-toggle="datetimepicker">
                        <div class="input-group-append" data-target="#proDateShow2" data-toggle="datetimepicker">
                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                        </div>
                    </div>
                    <div class="form-check mt-2">
                        <input type="checkbox" class="form-check-input" id="currently_working" name="currently_working">
                        <label class="form-check-label" for="currently_working">
                            Currently Working
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 col-sm-12">
                <div class="form-group">
                    <label for="experience_last_month_salary">Last Month Salary</label>
                    <input type="number" name="experience_last_month_salary_1" class="form-control" id="experience_last_month_salary"/>
                </div>
            </div>
            <div class="col-md-6 col-sm-12">
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 col-sm-12">
              <input type="button" onclick="append_row5();" name="submit" value="Add" class="btn btn-warning" style="float: right;">
            </div>
        </div>
        <br>
        <div class="row">
            <div class="col col-md-12 col-sm-12 table-responsive">

                <table class="table table-bordered" id="experiences" name="experiences">
                    <thead>
                        <tr>
                            <th >Designation&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th >Institution/University</th>
                            <th >Working Period</th>
                            {{-- <th >To</th> --}}
                            <th >Last Monthly Salary</th>
                            <th data-priority="1">Action</th>
                        </tr>
                    </thead>

                    <tbody>
                    @if(old('experience_designation'))
                        @foreach($employmentRecords as $key => $value)
                          <tr style="font-size: 14px;">
                                <td><textarea class="form-control readOnlyInput" rows="3" readonly>{{ $value->designation }}</textarea></td>
                                <td><textarea class="form-control readOnlyInput" rows="3" readonly>{{ $value->institution }}</textarea></td>
                                <td><b>From -</b><textarea class="form-control readOnlyInput" rows="2" readonly>{{ date("d-M-Y", strtotime($value->start_date)) }}</textarea><b>To -</b><textarea class="form-control readOnlyInput" rows="2" readonly>{{ $value->end_date != '1970-01-01'  ?  date("d-M-Y", strtotime($value->end_date)) : 'Currently Working' }}</textarea></td>
                                {{-- <td></td> --}}
                                <td><textarea class="form-control readOnlyInput" rows="2" readonly>{{ $value->last_month_salary != 0.00 ? $value->last_month_salary : "" }}</textarea></td>
                                <td><button class="btn btn-sm btn-danger delete-record-employmentrecord remove-button-5" data-record-id="{{$value->id }}">Remove</button></td>
                        <!-- Add more columns as needed -->
                           </tr>
                        @endforeach
                        @foreach(old('experience_designation') as $key => $value)
                            <tr style="font-size: 14px;">
                                <td><textarea class="form-control readOnlyInput" name="experience_designation[]" rows="2" readonly>{{ old('experience_designation.'.$key) }}</textarea></td>
                                <td><textarea class="form-control readOnlyInput" name="experience_institution[]" rows="2" readonly>{{ old('experience_institution.'.$key) }}</textarea></td>
                                <td><b>From -</b><textarea class="form-control readOnlyInput" name="experience_start_date[]" rows="2" readonly>{{ old('experience_start_date.'.$key) }}</textarea><b>To -</b><textarea class="form-control readOnlyInput" name="experience_end_date[]" rows="2" readonly>{{ old('experience_end_date.'.$key) }}</textarea></td>
                                {{-- <td></td> --}}
                                <td><textarea class="form-control readOnlyInput" name="experience_last_month_salary[]" rows="2" readonly>{{ old('experience_last_month_salary.'.$key) }}</textarea></td>
                                <td><button class="btn btn-sm btn-danger delete-record-old-employmentrecord remove-button-5" data-record-old-id="{{ $key }}">Remove</button></td>
                                <!-- Add more columns as needed -->
                            </tr>
                        @endforeach

                    @else
                    @foreach($employmentRecords as $key => $value)
                    <tr style="font-size: 14px;">
                        <td><textarea class="form-control readOnlyInput" rows="3" readonly>{{ $value->designation }}</textarea></td>
                        <td><textarea class="form-control readOnlyInput" rows="3" readonly>{{ $value->institution }}</textarea></td>
                        <td><b>From -</b><textarea class="form-control readOnlyInput" rows="1" readonly>{{ date("d-M-Y", strtotime($value->start_date)) }}</textarea><b>To -</b><textarea class="form-control readOnlyInput" rows="1" readonly>{{ $value->end_date != '1970-01-01'  ?  date("d-M-Y", strtotime($value->end_date)) : 'Currently Working' }}</textarea></td>
                        {{-- <td></td> --}}
                        <td><textarea class="form-control readOnlyInput" rows="1" readonly>{{ $value->last_month_salary != 0.00 ? $value->last_month_salary : "" }}</textarea></td>
                        <td><button class="btn btn-sm btn-danger delete-record-employmentrecord remove-button-5" data-record-id="{{$value->id }}">Remove</button></td>
                <!-- Add more columns as needed -->
                   </tr>
                        @endforeach
                    @endif
                    </tbody>
                </table>
            </div>
        </div>
        <br>

        <div class="row">
            <div class="col col-md-12 col-sm-12">
              <div class="form-group">
                <label for="degree_certificate">Please Upload the Scanned Copies of the Employment Records Mentioned Above (PDF Format Only)</label>
                <div class="input-group">
                  <div class="custom-file">
                    <input type="file" class="custom-file-input" id="employment_record_certificate" name="employment_record_certificate[]" multiple accept=".pdf">
                    <label class="custom-file-label" for="employment_record_certificate">Choose File/Files</label>
                  </div>
                </div>
                @foreach($employmentRecordCertificates as $key => $value)
                @if( $value->save_path != '')
                <div class="old-certificate">
            <a href="{{ route('application.data.download.employment.record', $value->id) }}" class="btn btn-white"><span class="text-primary">{{ substr($value->save_path, 44) }} <i class="fa fa-arrow-circle-down" aria-hidden="true"></i></span></a>
            &nbsp;&nbsp;
            <button class="btn btn-white delete-record-certificate-employment-record" data-record-id="{{$value->id }}"><span class="text-danger">{{ substr($value->save_path, 44) }} <i class="fa fa-trash" aria-hidden="true"></i></span></button><br>
                </div>
            @endif
                @endforeach
                <ul id="file-list2" class="mt-3">
                    <!-- Selected file list will be dynamically populated here -->
                </ul>
                <span class="text-danger">@error('employment_record_certificate.*'){{$message}}@enderror</span>
              </div>
            </div>
          </div>
    </div>
</div>

<script>
function append_row5() {
    var experience_designation = document.getElementById('experience_designation').value;
    var experience_institution = document.getElementById('experience_institution').value;
    var experience_start_date = document.getElementById('experience_start_date').value;
    var experience_end_date = document.getElementById('experience_end_date').value;
    var experience_last_month_salary = document.getElementById('experience_last_month_salary').value;
    var currently_working = document.getElementById('currently_working').checked;

    if (!experience_designation || !experience_institution || !experience_start_date) {
        alert('Please fill all required fields');
        return;
    }

    // If not currently working, end date is required
    if (!currently_working && !experience_end_date) {
        alert('End date is required when not currently working');
        return;
    }

    var table = document.getElementById('experiences').getElementsByTagName('tbody')[0];
    var newRow = table.insertRow();

    var workingPeriod = '<b>From -</b>' + experience_start_date + '<b>To -</b>' + (currently_working ? 'Present' : experience_end_date);

    var cells = [
        experience_designation,
        experience_institution,
        workingPeriod,
        experience_last_month_salary
    ].map(value => {
        var cell = newRow.insertCell();
        var textarea = document.createElement('textarea');
        textarea.className = 'form-control readOnlyInput';
        textarea.readOnly = true;
        textarea.value = value;
        cell.appendChild(textarea);
        return cell;
    });

    var actionCell = newRow.insertCell();
    actionCell.innerHTML = '<button type="button" class="btn btn-danger" onclick="delete_row(this)">Delete</button>';

    // Update hidden input with table data
    updateExperienceHiddenInput();

    // Clear form fields
    document.getElementById('experience_designation').value = '';
    document.getElementById('experience_institution').value = '';
    document.getElementById('experience_start_date').value = '';
    document.getElementById('experience_end_date').value = '';
    document.getElementById('experience_last_month_salary').value = '';
    document.getElementById('currently_working').checked = false;
}

function delete_row(btn) {
    var row = btn.parentNode.parentNode;
    row.parentNode.removeChild(row);

    // Check if table is empty and add "No data" message if needed
    var table = document.getElementById('experiences');
    var tbody = table.getElementsByTagName('tbody')[0];
    if (tbody.rows.length === 0) {
        var noDataRow = tbody.insertRow();
        var noDataCell = noDataRow.insertCell();
        noDataCell.colSpan = 5;
        noDataCell.textContent = "No data available in table";
        noDataCell.style.textAlign = "center";
    }

    // Update hidden input after deletion
    updateExperienceHiddenInput();
}

function updateExperienceHiddenInput() {
    var table = document.getElementById('experiences');
    var rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    var data = [];

    for (var i = 0; i < rows.length; i++) {
        // Skip the "No data available" row if present
        if (rows[i].cells.length === 1 && rows[i].cells[0].colSpan === 5) {
            continue;
        }

        var cells = rows[i].getElementsByTagName('textarea');
        if (cells.length === 4) { // Make sure it's a data row
            var workingPeriod = cells[2].value;
            var endDateText = workingPeriod.split('To -')[1] ? workingPeriod.split('To -')[1].trim() : '';
            var isCurrentlyWorking = endDateText === 'Present';

            data.push({
                designation: cells[0].value,
                institution: cells[1].value,
                start_date: workingPeriod.split('From -')[1].split('To -')[0].trim(),
                end_date: isCurrentlyWorking ? '' : endDateText,
                currently_working: isCurrentlyWorking,
                last_month_salary: cells[3].value
            });
        }
    }

    // Add a hidden input field to your form to store this data
    if (!document.getElementById('emp_data')) {
        var hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.id = 'emp_data';
        hiddenInput.name = 'emp_data';
        document.querySelector('.card-body').appendChild(hiddenInput);
    }

    document.getElementById('emp_data').value = JSON.stringify(data);
}

// Initialize the table when the page loads
document.addEventListener('DOMContentLoaded', function() {
    var form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function() {
            updateExperienceHiddenInput();
        });
    }

    // Handle currently working checkbox
    var currentlyWorkingCheckbox = document.getElementById('currently_working');
    var endDateInput = document.getElementById('experience_end_date');

    if (currentlyWorkingCheckbox && endDateInput) {
        currentlyWorkingCheckbox.addEventListener('change', function() {
            if (this.checked) {
                endDateInput.value = '';
                endDateInput.disabled = true;
                endDateInput.parentElement.style.opacity = '0.5';
            } else {
                endDateInput.disabled = false;
                endDateInput.parentElement.style.opacity = '1';
            }
        });
    }
});
</script>

