<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\DB;

class UserRegistrationEdit implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    public function passes($attribute, $value)
    {
        return DB::table('applicant_verifications')
               ->where('nic',$value)
               ->where('applicant_verifications.status_id', 1)
               ->count() == 1;
    }


    public function message()
    {
        return 'You are not registered selected job vancancy.Please job home page registered first';
    }
}
