<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class nav_extra_curricular_pdf extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'user_id',
        'extra_curricular_id',
        'file_path',
        'original_filename',
        'status'
    ];
    
    // Relationship with extra curricular activity
    public function extraCurricular()
    {
        return $this->belongsTo(nav_extra_curricular::class, 'extra_curricular_id');
    }
}
