<?php
 
namespace App\Http\Middleware;

use App\Models\LoginHistory;
use Closure;
use Illuminate\Session\Store;
use Auth;
use Session;
 
class SessionExpired {
    
    protected $session;
    protected $timeout = 7200;//time out 20 minitues
     
    public function __construct(Store $session){
        $this->session = $session;
    }
    public function handle($request, Closure $next){
        $isLoggedIn = $request->path() != 'admin/logout';
        if(! session('lastActivityTime'))
            $this->session->put('lastActivityTime', time());
        elseif(time() - $this->session->get('lastActivityTime') > $this->timeout){
            $this->session->forget('lastActivityTime');
            $cookie = cookie('intend', $isLoggedIn ? url()->current() : 'dashboard');
            LoginHistory::where('user_id', '=', auth()->user()->id)->orderBy('created_at', 'desc')->limit(1)->update([
                'logout_time' => now(), 
                'updated_at' => now(),
             ]);
            auth()->logout();
        }
        $isLoggedIn ? $this->session->put('lastActivityTime', time()) : $this->session->forget('lastActivityTime');
        return $next($request);
    }
}
