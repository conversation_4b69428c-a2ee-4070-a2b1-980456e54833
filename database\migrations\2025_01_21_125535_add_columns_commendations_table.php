<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('commendations', function (Blueprint $table) {
            //
            $table->integer('process_table_id')->default(0)->after('add_user_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('commendations', function (Blueprint $table) {
            //
            $table->dropColumn('process_table_id');
        });
    }
};
