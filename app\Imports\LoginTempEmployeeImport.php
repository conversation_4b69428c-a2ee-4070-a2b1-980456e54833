<?php

namespace App\Imports;

use App\Models\LoginTempEmployee;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithValidation;

class LoginTempEmployeeImport implements ToModel,WithStartRow,WithValidation
{
    public function startRow(): int
    {
        return 2;
    }

    public function model(array $row)
    {  
        $employee_count = LoginTempEmployee::where('employee_no',$row[0])->count();

        if($employee_count == 0){

        $loginTempEmployee = LoginTempEmployee::updateOrCreate([
            'employee_no' => $row[0],
            'email' => $row[1],
            'mobile_no' => $row[2],
            'current_appointment_date' =>  \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row[3])->format('Y-m-d'),
            'etf_no' => $row[4],
            'upf_no' => $row[5],
            'pension_reference_no' => $row[6],
        ]);

       }
    }

    public function rules(): array
    {
        return [

            '0' => 'required|int',
        ];
    }

    public function customValidationMessages()
   {
       return [
            //'0.unique' => 'employee number already exist in the database',
            
     ];
   }

}
