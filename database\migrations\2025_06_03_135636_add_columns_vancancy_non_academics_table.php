<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vancancy_non_academics', function (Blueprint $table) {
            $table->integer('min_age')->default(0)->after('designation_category');
            $table->integer('max_age')->default(0)->after('min_age');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vancancy_non_academics', function (Blueprint $table) {
            $table->dropColumn('min_age');
            $table->dropColumn('max_age');
        });
    }
};
