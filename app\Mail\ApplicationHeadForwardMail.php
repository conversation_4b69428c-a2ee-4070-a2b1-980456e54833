<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ApplicationHeadForwardMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }


    public function envelope()
    {
        return new Envelope(
            subject: 'USJHRMS - Applications for Academic Vacancies',
            tags: ['recrutement'],
        );
    }


    public function content()
    {
        return new Content(
            markdown: 'emails.application_head_forward',
            with: [
                'data' => $this->data
            ],
        );
    }


    public function attachments()
    {
        return [];
    }
}
