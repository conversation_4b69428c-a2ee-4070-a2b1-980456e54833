<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class HeadConfirmationResetMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }


    public function envelope()
    {
        return new Envelope(
            subject: 'HRMS USJ - VACANCY FINAL CONFIRMATION RESET',
            tags: ['reset'],
        );
    }


    public function content()
    {
        return new Content(
            markdown: 'emails.confirmation_reset',
            with: [
                'data' => $this->data
            ],
        );
    }


    public function attachments()
    {
        return [];
    }
}
