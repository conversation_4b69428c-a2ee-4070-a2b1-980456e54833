<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('employees', function (Blueprint $table) {
            $table->integer('previous_assign_ma_user_id')->after('assign_ma_date')->nullable();
            $table->date('previous_assign_ma_change_date')->after('previous_assign_ma_user_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('employees', function (Blueprint $table) {
            $table->dropColumn('previous_assign_ma_user_id');
            $table->dropColumn('previous_assign_ma_change_date');
        });
    }
};
