<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class acIncreForwardVCMail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    
    public function envelope()
    {
        return new Envelope(
            subject: 'Increment certification form for academic staff',
            tags: ['increment'],
        );
    }

   
    public function content()
    {
        return new Content(
            markdown: 'emails.ac_incre_forward_vc',
            with: [
                'data' => $this->data
            ],
        );
    }

   
    public function attachments()
    {
        return [];
    }
}
