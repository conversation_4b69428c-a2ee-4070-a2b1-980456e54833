<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('salary_scales', function (Blueprint $table) {
            $table->integer('salary_scale_start_step')->after('emp_classification_id')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('salary_scales', function (Blueprint $table) {
            $table->dropColumn('salary_scale_start_step');
        });
    }
};
