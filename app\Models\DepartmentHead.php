<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class DepartmentHead extends Model
{
    use HasFactory,LogsActivity;

    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('hrms_department_heads')
        ->logOnlyDirty()
        ->dontLogIfAttributesChangedOnly(['updated_at'])
        ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    public function departmentName()
    {
        return $this->belongsTo(Department::class,'department_id');

    }

    public function employeeName()
    {
        return $this->belongsTo(Employee::class,'emp_no');

    }

    public function AppointmentTypeName()
    {
        return $this->belongsTo(Category::class,'appointmemt_type');

    }

    public function HeadPositionName()
    {
        return $this->belongsTo(Category::class,'head_position');

    }

    protected static function boot()
    {
        parent::boot();

        static::updating(function ($model) {

            $original = $model->getOriginal();
            $newModel = new DepartmentHeadHistory();
            $newModel->fill($original);
            $newModel->updated_at = now();
            $newModel->updated_user_id = Auth()->check() ? Auth()->user()->employee_no : 0;
            $newModel->save();

        });
    }

    public function department()
    {
        return $this->belongsTo(department::class, 'department_id', 'id');
    }

    public function Assignuser()
    {
        return $this->belongsTo(Employee::class, 'added_user_id');
    }
}
