<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class acIncreForwardToEmpMail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;
    protected $ccRecipients;

    public function __construct($data,$ccRecipients)
    {
        $this->data = $data;
        $this->ccRecipients = $ccRecipients; // Store CC recipients
    }

    
    public function envelope()
    {
        return (new Envelope)
            ->subject('Increment certification form for academic staff')
            ->tags(['increment'])
            ->cc($this->ccRecipients); // Add CC recipients here
    }

   
    public function content()
    {
        return new Content(
            markdown: 'emails.ac_incre_forwardTo_emp',
            with: [
                'data' => $this->data
            ],
        );
    }

   
    public function attachments()
    {
        return [];
    }
}
