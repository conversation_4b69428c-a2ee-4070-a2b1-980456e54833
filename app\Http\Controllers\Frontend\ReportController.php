<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Application;
use App\Models\Bond;
use App\Models\Degree;
use App\Models\DegreeCertificate;
use App\Models\DegreeSubject;
use App\Models\Diploma;
use App\Models\DiplomaCertificate;
use App\Models\EmploymentRecord;
use App\Models\EmploymentRecordCertificate;
use App\Models\Membership;
use App\Models\ProfessionalQualification;
use App\Models\Referee;
use App\Models\ReleaseLetter;
use App\Models\Research;
use App\Models\SpecialQulification;
use App\Models\Vacancy;
use Barryvdh\DomPDF\Facade\Pdf as FacadePdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class ReportController extends Controller
{
    public function finalReportDownload(){

        if (!session()->get('vacancy_id') || !session()->get('reference_no')) {

            $notification = array(
                'message' => 'Your application session has been expired',
                'alert-type' => 'error'
             );

             return redirect()->route('home')->with($notification);
        }

        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                  ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                  ->select('vacancies.*','categories.display_name')
                  ->find(session()->get('vacancy_id'));
        $appData = Application::find(session()->get('reference_no'));
        $firstDegrees = Degree::where('reference_no','=',session()->get('reference_no'))->where('degree_type',214)->get();
        $degreeSubjects = DegreeSubject::where('reference_no','=',session()->get('reference_no'))->get();
        $postDegrees = Degree::where('reference_no','=',session()->get('reference_no'))->where('degree_type',215)->get();
        $diplomas = Diploma::where('reference_no','=',session()->get('reference_no'))->get();
        $research = Research::where('reference_no','=',session()->get('reference_no'))->get();
        $specialQulificationList = SpecialQulification::where('reference_no','=',session()->get('reference_no'))->get();
        $memberships = Membership::where('reference_no','=',session()->get('reference_no'))->get();
        $professionalQulificationList = ProfessionalQualification::where('reference_no','=',session()->get('reference_no'))->get();
        $employmentRecords = EmploymentRecord::where('reference_no','=',session()->get('reference_no'))->orderBy('start_date')->get();
        $employmentBonds = Bond::where('reference_no','=',session()->get('reference_no'))->get();
        $refereeList = Referee::where('reference_no','=',session()->get('reference_no'))->get();

        $degreeCertificatesCount = DegreeCertificate::where('reference_no','=',session()->get('reference_no'))->count();
        $diplomaCertificatesCount = DiplomaCertificate::where('reference_no','=',session()->get('reference_no'))->count();
        $employmentRecordCertificatesCount = EmploymentRecordCertificate::where('reference_no','=',session()->get('reference_no'))->count();
        $releaseLettersCount = ReleaseLetter::where('reference_no','=',session()->get('reference_no'))->count();


        $data = [
            'vacancy' => $vacancy,
            'appData' => $appData,
            'firstDegrees' => $firstDegrees,
            'degreeSubjects' => $degreeSubjects,
            'postDegrees' => $postDegrees,
            'diplomas' => $diplomas,
            'research' => $research,
            'specialQulificationList' => $specialQulificationList,
            'memberships' => $memberships,
            'professionalQulificationList' => $professionalQulificationList,
            'employmentRecords' => $employmentRecords,
            'employmentBonds' => $employmentBonds,
            'refereeList' => $refereeList,
            'degreeCertificatesCount' => $degreeCertificatesCount,
            'diplomaCertificatesCount' => $diplomaCertificatesCount,
            'employmentRecordCertificatesCount' => $employmentRecordCertificatesCount,
            'releaseLettersCount' => $releaseLettersCount
        ];


        $pdf = FacadePdf::loadView('frontend.application.report.app_report', $data)->setPaper('a4');
        return $pdf->download($appData->reference_no.' Final Application.pdf');

    }

    public function listReportDownload(Request $request){


       $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                  ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                  ->select('vacancies.*','categories.display_name')
                  ->find($request->vacancy_id);
        $appData = Application::find($request->reference_no);
        $firstDegrees = Degree::where('reference_no','=',$request->reference_no)->where('degree_type',214)->get();
        $degreeSubjects = DegreeSubject::where('reference_no','=',$request->reference_no)->get();
        $postDegrees = Degree::where('reference_no','=',$request->reference_no)->where('degree_type',215)->get();
        $diplomas = Diploma::where('reference_no','=',$request->reference_no)->get();
        $research = Research::where('reference_no','=',$request->reference_no)->get();
        $specialQulificationList = SpecialQulification::where('reference_no','=',$request->reference_no)->get();
        $memberships = Membership::where('reference_no','=',$request->reference_no)->get();
        $professionalQulificationList = ProfessionalQualification::where('reference_no','=',$request->reference_no)->get();
        $employmentRecords = EmploymentRecord::where('reference_no','=',$request->reference_no)->orderBy('start_date')->get();
        $employmentBonds = Bond::where('reference_no','=',$request->reference_no)->get();
        $refereeList = Referee::where('reference_no','=',$request->reference_no)->get();

        $degreeCertificatesCount = DegreeCertificate::where('reference_no','=',$request->reference_no)->count();
        $diplomaCertificatesCount = DiplomaCertificate::where('reference_no','=',$request->reference_no)->count();
        $employmentRecordCertificatesCount = EmploymentRecordCertificate::where('reference_no','=',$request->reference_no)->count();
        $releaseLettersCount = ReleaseLetter::where('reference_no','=',$request->reference_no)->count();


        $data = [
            'vacancy' => $vacancy,
            'appData' => $appData,
            'firstDegrees' => $firstDegrees,
            'degreeSubjects' => $degreeSubjects,
            'postDegrees' => $postDegrees,
            'diplomas' => $diplomas,
            'research' => $research,
            'specialQulificationList' => $specialQulificationList,
            'memberships' => $memberships,
            'professionalQulificationList' => $professionalQulificationList,
            'employmentRecords' => $employmentRecords,
            'employmentBonds' => $employmentBonds,
            'refereeList' => $refereeList,
            'degreeCertificatesCount' => $degreeCertificatesCount,
            'diplomaCertificatesCount' => $diplomaCertificatesCount,
            'employmentRecordCertificatesCount' => $employmentRecordCertificatesCount,
            'releaseLettersCount' => $releaseLettersCount
        ];


        $pdf = FacadePdf::loadView('frontend.application.report.app_report', $data)->setPaper('a4');

        return $pdf->download($appData->reference_no.' Final Application.pdf');

    }

    public function mergeReportDownload(){

        $vacancy = Vacancy::join('designations', 'vacancies.designation_id', '=', 'designations.id')
                  ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                  ->select('vacancies.*','categories.display_name')
                  ->find(session()->get('vacancy_id'));
        $appData = Application::find(session()->get('reference_no'));
        $firstDegrees = Degree::where('reference_no','=',session()->get('reference_no'))->where('degree_type',214)->get();
        $degreeSubjects = DegreeSubject::where('reference_no','=',session()->get('reference_no'))->get();
        $postDegrees = Degree::where('reference_no','=',session()->get('reference_no'))->where('degree_type',215)->get();
        $diplomas = Diploma::where('reference_no','=',session()->get('reference_no'))->get();
        $research = Research::where('reference_no','=',session()->get('reference_no'))->get();
        $specialQulificationList = SpecialQulification::where('reference_no','=',session()->get('reference_no'))->get();
        $memberships = Membership::where('reference_no','=',session()->get('reference_no'))->get();
        $professionalQulificationList = ProfessionalQualification::where('reference_no','=',session()->get('reference_no'))->get();
        $employmentRecords = EmploymentRecord::where('reference_no','=',session()->get('reference_no'))->orderBy('start_date')->get();
        $employmentBonds = Bond::where('reference_no','=',session()->get('reference_no'))->get();
        $refereeList = Referee::where('reference_no','=',session()->get('reference_no'))->get();

        $degreeCertificatesCount = DegreeCertificate::where('reference_no','=',session()->get('reference_no'))->count();
        $diplomaCertificatesCount = DiplomaCertificate::where('reference_no','=',session()->get('reference_no'))->count();
        $employmentRecordCertificatesCount = EmploymentRecordCertificate::where('reference_no','=',session()->get('reference_no'))->count();
        $releaseLettersCount = ReleaseLetter::where('reference_no','=',session()->get('reference_no'))->count();


        $degreeCertificates = DegreeCertificate::where('reference_no','=',session()->get('reference_no'))->get();
        $diplomaCertificates = DiplomaCertificate::where('reference_no','=',session()->get('reference_no'))->get();
        $employmentRecordCertificates = EmploymentRecordCertificate::where('reference_no','=',session()->get('reference_no'))->get();
        $releaseLetters = ReleaseLetter::where('reference_no','=',session()->get('reference_no'))->get();


        $data = [
            'vacancy' => $vacancy,
            'appData' => $appData,
            'firstDegrees' => $firstDegrees,
            'degreeSubjects' => $degreeSubjects,
            'postDegrees' => $postDegrees,
            'diplomas' => $diplomas,
            'research' => $research,
            'specialQulificationList' => $specialQulificationList,
            'memberships' => $memberships,
            'professionalQulificationList' => $professionalQulificationList,
            'employmentRecords' => $employmentRecords,
            'employmentBonds' => $employmentBonds,
            'refereeList' => $refereeList,
            'degreeCertificatesCount' => $degreeCertificatesCount,
            'diplomaCertificatesCount' => $diplomaCertificatesCount,
            'employmentRecordCertificatesCount' => $employmentRecordCertificatesCount,
            'releaseLettersCount' => $releaseLettersCount
        ];


        $pdf = FacadePdf::loadView('frontend.application.report.app_report', $data)->setPaper('a4');

        $path = 'public/uploads/vacancy/'.session()->get('reference_no').'/application/';

        if (! File::exists($path)) {
                File::makeDirectory(storage_path().$path, 0777, true, true);
        }

        $pdf->set_option("isPhpEnabled", true);
        Storage::put($path .'final.pdf', $pdf->output());
        // return $pdf->download($appData->reference_no.' Final Application.pdf');

        $pdf1Path = storage_path('app/'.$path.'final.pdf');

        // Create a new instance of Fpdi
       $pdf = new \setasign\Fpdi\Fpdi();

       // Add the pages from the first PDF to the merged PDF
         $pageCount1 = $pdf->setSourceFile($pdf1Path);
          for ($pageNo = 1; $pageNo <= $pageCount1; $pageNo++) {
              $template = $pdf->importPage($pageNo);
              $pdf->AddPage();
              $pdf->useTemplate($template);
          }

       // Merge the PDFs
       foreach ($releaseLetters as $pdfPath) {
        $pageCount = $pdf->setSourceFile(storage_path('app/' . $pdfPath->save_path));
        for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
            $template = $pdf->importPage($pageNo);
            $pdf->AddPage();
            $pdf->useTemplate($template);
        }
      }

       // Merge the PDFs
       foreach ($degreeCertificates as $pdfPath) {
           $pageCount = $pdf->setSourceFile(storage_path('app/' . $pdfPath->save_path));
           for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
               $template = $pdf->importPage($pageNo);
               $pdf->AddPage();
               $pdf->useTemplate($template);
           }
       }

       // Merge the PDFs
       foreach ($diplomaCertificates as $pdfPath) {
           $pageCount = $pdf->setSourceFile(storage_path('app/' . $pdfPath->save_path));
           for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
               $template = $pdf->importPage($pageNo);
               $pdf->AddPage();
               $pdf->useTemplate($template);
           }
       }

       // Merge the PDFs
       foreach ($employmentRecordCertificates as $pdfPath) {
           $pageCount = $pdf->setSourceFile(storage_path('app/' . $pdfPath->save_path));
           for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
               $template = $pdf->importPage($pageNo);
               $pdf->AddPage();
               $pdf->useTemplate($template);
           }
       }



       // Save the merged PDF to the "storage" directory with the custom file name
       $customFileName = 'final_application.pdf';
       $base = 'public/uploads/vacancy/'.session()->get('reference_no').'/final/';
       $outputPath = storage_path('app/public/uploads/vacancy/'.session()->get('reference_no').'/final/'. $customFileName);

       if (! File::exists( $base)) {
        File::makeDirectory(storage_path('app/'). $base, 0777, true, true);
      }
       $pdf->Output($outputPath, 'F');
       //$pdf->Output($outputPath, 'I');
       //$pdf->Output($outputPath, 'D');
       //$pdf->Output($outputPath, 'FI');

       // Return the file as a download using the Response facade
       return Response::download($outputPath, $customFileName);
    }


}
