<?php

namespace App\Http\Controllers\Summary;

use App\Http\Controllers\Controller;
use App\Ldap\Secondary\User as SecondaryUser;
use App\Ldap\User;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class ExternalSystemSummaryController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin');
    }

    public function externalSystem()
    {

        return view('admin.summary.external_index');
    }
    public function usjNetUserEmployeeNo()
    {
        // Retrieve employee data

        $employeeData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select(
                'employees.employee_no',
                'employees.email',
                'employees.personal_email',
                'employees.mobile_no',
                'employees.initials',
                'employees.last_name',
                'categories.category_name as title',
            )->where('employee_status_id', 110)->get();

        // Retrieve USJNet data
        $empDatas = Http::withHeaders([
            'Api-Key' => 'b138892c-64cb-4a92-a4d5-a2d40dc03f8b',
        ])->get('https://usjnetsso.sjp.ac.lk/api/usjnet/active_member_list.php');
        $usjnetData = json_decode($empDatas->body(), true);


        // Remove leading zeros from reg_no in usjnetData
        foreach ($usjnetData as &$usjnetMember) {
            $usjnetMember['reg_no'] = ltrim($usjnetMember['reg_no'], '0');
        }

        // Initialize lists
        $employeesWithUsjnetData = [];
        $employeesWithoutUsjnetData = [];
        $usjnetDataWithoutEmployees = [];

        // Loop through employee data and check for corresponding data in USJNet
        foreach ($employeeData as $employee) {
            $found = false;
            foreach ($usjnetData as $usjnetMember) {
                if ($employee->employee_no == $usjnetMember['reg_no']) {
                    // Employee with corresponding data in USJNet
                    $employeeWithUsjnetData = [
                        'employee' => $employee,
                        'tp' => $usjnetMember['tp'],
                        'sjp_mail' => $usjnetMember['sjp_mail']
                    ];
                    $employeesWithUsjnetData[] = $employeeWithUsjnetData;
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Employee without corresponding data in USJNet
                $employeesWithoutUsjnetData[] = $employee;
            }
        }

        // Order employeesWithUsjnetData by employee_no
        $employeesWithUsjnetData = collect($employeesWithUsjnetData)->sortBy('employee.employee_no')->values()->all();

        // Loop through USJNet data and check for corresponding employees
        foreach ($usjnetData as $usjnetMember) {
            $found = false;
            foreach ($employeeData as $employee) {
                if ($employee->employee_no == $usjnetMember['reg_no']) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Data in USJNet without corresponding employee
                $usjnetDataWithoutEmployees[] = $usjnetMember;
            }
        }

        // Pass lists to the view
        return view('admin.summary.usjnet.index', compact('employeesWithUsjnetData', 'employeesWithoutUsjnetData', 'usjnetDataWithoutEmployees'));
    }

    public function usjNetUserEmail()
    {
        // Retrieve employee data

        $employeeData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select(
                'employees.employee_no',
                'employees.email',
                'employees.personal_email',
                'employees.mobile_no',
                'employees.initials',
                'employees.last_name',
                'categories.category_name as title',
            )->where('employee_status_id', 110)->get();

        // Retrieve USJNet data
        $empDatas = Http::withHeaders([
            'Api-Key' => 'b138892c-64cb-4a92-a4d5-a2d40dc03f8b',
        ])->get('https://usjnetsso.sjp.ac.lk/api/usjnet/active_member_list.php');
        $usjnetData = json_decode($empDatas->body(), true);

        // Initialize lists
        $employeesWithUsjnetData = [];
        $employeesWithoutUsjnetData = [];
        $usjnetDataWithoutEmployees = [];

        // Loop through employee data and check for corresponding data in USJNet
        foreach ($employeeData as $employee) {
            $found = false;
            foreach ($usjnetData as $usjnetMember) {
                if ($employee->email == $usjnetMember['sjp_mail']) {
                    // Employee with corresponding data in USJNet
                    $employeeWithUsjnetData = [
                        'employee' => $employee,
                        'tp' => $usjnetMember['tp'],
                        'sjp_mail' => $usjnetMember['sjp_mail'],
                        'reg_no' => $usjnetMember['reg_no']
                    ];
                    $employeesWithUsjnetData[] = $employeeWithUsjnetData;
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Employee without corresponding data in USJNet
                $employeesWithoutUsjnetData[] = $employee;
            }
        }

        // Order employeesWithUsjnetData by employee_no
        $employeesWithUsjnetData = collect($employeesWithUsjnetData)->sortBy('employee.employee_no')->values()->all();

        // Loop through USJNet data and check for corresponding employees
        foreach ($usjnetData as $usjnetMember) {
            $found = false;
            foreach ($employeeData as $employee) {
                if ($employee->email == $usjnetMember['sjp_mail']) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Data in USJNet without corresponding employee
                $usjnetDataWithoutEmployees[] = $usjnetMember;
            }
        }
        //dd($employeesWithoutUsjnetData);

        // Pass lists to the view
        return view('admin.summary.usjnet.index_email', compact('employeesWithUsjnetData', 'employeesWithoutUsjnetData', 'usjnetDataWithoutEmployees'));
    }

    public function emailRequest()
    {
        // Retrieve employee data

        $employeeData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select(
                'employees.employee_no',
                'employees.email',
                'employees.personal_email',
                'employees.mobile_no',
                'employees.initials',
                'employees.last_name',
                'categories.category_name as title',
            )->where('employee_status_id', 110)->get();

        // Retrieve USJNet data
        $empDatas = Http::withHeaders([
            'Api-Key' => 'b8ce31d2-5501-4a38-b13b-5ab39b7de573',
        ])->get('https://usjnetsso.sjp.ac.lk/api/usjnet/email_data_list.php');
        $usjnetData = json_decode($empDatas->body(), true);


        // Remove leading zeros from reg_no in usjnetData
        foreach ($usjnetData as &$usjnetMember) {
            $usjnetMember['reg_no'] = ltrim($usjnetMember['reg_no'], '0');
        }

        // Initialize lists
        $employeesWithUsjnetData = [];
        $employeesWithoutUsjnetData = [];
        $usjnetDataWithoutEmployees = [];

        // Loop through employee data and check for corresponding data in USJNet
        foreach ($employeeData as $employee) {
            $found = false;
            foreach ($usjnetData as $usjnetMember) {
                if ($employee->employee_no == $usjnetMember['reg_no']) {
                    // Employee with corresponding data in USJNet
                    $employeeWithUsjnetData = [
                        'employee' => $employee,
                        'sjp_mail' => $usjnetMember['finalUSJEmail'].'@sjp.ac.lk',
                        'status' => $usjnetMember['done']
                    ];
                    $employeesWithUsjnetData[] = $employeeWithUsjnetData;
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Employee without corresponding data in USJNet
                $employeesWithoutUsjnetData[] = $employee;
            }
        }

        // Order employeesWithUsjnetData by employee_no
        $employeesWithUsjnetData = collect($employeesWithUsjnetData)->sortBy('employee.employee_no')->values()->all();

        // Loop through USJNet data and check for corresponding employees
        foreach ($usjnetData as $usjnetMember) {
            $found = false;
            foreach ($employeeData as $employee) {
                if ($employee->employee_no == $usjnetMember['reg_no']) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Data in USJNet without corresponding employee
                $usjnetDataWithoutEmployees[] = $usjnetMember;
            }
        }

        // Pass lists to the view
        return view('admin.summary.email.index', compact('employeesWithUsjnetData', 'employeesWithoutUsjnetData', 'usjnetDataWithoutEmployees'));
    }


    public function salaryUser()
    {
        // Retrieve employee data
        $employeeData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('categories as status', 'status.id', '=', 'employees.employee_status_id')
            ->join('categories as status_type', 'status_type.id', '=', 'employees.employee_status_type_id')
            ->join('categories as fund_type', 'fund_type.id', '=', 'employees.salary_payment_type')
            ->select(
                'employees.employee_no',
                'employees.email',
                'employees.mobile_no',
                'employees.initials',
                'employees.last_name',
                'employees.employee_status_id',
                'employees.current_appointment_date',
                'categories.category_name as title',
                'status.category_name as status_name',
                'status_type.category_name as status_type_name',
                'fund_type.category_name as fund_source'
            )->get();

        // Retrieve Salary data
        $empDatas = Http::withHeaders([
            'Api-Key' => '0843c103-933f-4596-8297-86e60d1d73da',
        ])->get('https://usjnetsso.sjp.ac.lk/api/usjnet/salary_active_member_list.php');
        $salaryData = json_decode($empDatas->body(), true);

        // Remove leading zeros from reg_no in usjnetData
        foreach ($salaryData as &$salaryMember) {
            $salaryMember['strEmpNo'] = ltrim($salaryMember['strEmpNo'], '0');
        }

        // Initialize lists
        $employeesWithSalaryData = [];
        $employeesWithoutSalaryData = [];
        $salaryDataWithoutEmployees = [];

        // Loop through employee data and check for corresponding data in Salary System
        foreach ($employeeData as $employee) {
            $found = false;
            foreach ($salaryData as $salaryMember) {
                if ($employee->employee_no == $salaryMember['strEmpNo']) {
                    // Append employee with corresponding data in Salary System
                    $employeesWithSalaryData[] = $employee;
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Employee without corresponding data in USJNet
                $employeesWithoutSalaryData[] = $employee;
            }
        }

        // Order employeesWithUsjnetData by employee_no
        $employeesWithSalaryData = collect($employeesWithSalaryData)->sortBy('employee.employee_no')->values()->all();

        // Loop through USJNet data and check for corresponding employees
        foreach ($salaryData as $salaryMember) {
            $found = false;
            foreach ($employeeData as $employee) {
                if ($employee->employee_no == $salaryMember['strEmpNo']) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Data in USJNet without corresponding employee
                $salaryDataWithoutEmployees[] = $salaryMember;
            }
        }

        // Pass lists to the view
        return view('admin.summary.salary.index', compact('employeesWithSalaryData', 'employeesWithoutSalaryData', 'salaryDataWithoutEmployees'));
    }

    public function usjNetsalaryUser()
    {

        // Retrieve USJNet data
        $empDatas = Http::withHeaders([
            'Api-Key' => 'b138892c-64cb-4a92-a4d5-a2d40dc03f8b',
        ])->get('https://usjnetsso.sjp.ac.lk/api/usjnet/active_member_list.php');
        $usjnetData = json_decode($empDatas->body(), true);

        // Retrieve Salary data
        $empDatas = Http::withHeaders([
            'Api-Key' => '0843c103-933f-4596-8297-86e60d1d73da',
        ])->get('https://usjnetsso.sjp.ac.lk/api/usjnet/salary_active_member_list.php');
        $salaryData = json_decode($empDatas->body(), true);

        $bothSystemUsers = [];
        $onlyUsjnetUsers = [];
        $onlySalaryUsers = [];

        // Loop through USJNet data
        foreach ($usjnetData as $usjnetUser) {
            $foundInSalary = false;
            // Loop through Salary data to check if user exists
            foreach ($salaryData as $salaryUser) {
                if ($usjnetUser['reg_no'] === $salaryUser['strEmpNo']) {
                    // Combine both arrays to include additional fields from Salary system
                    $bothSystemUsers[] = array_merge($usjnetUser, [
                        'strEmpNo' => $salaryUser['strEmpNo'],
                        'strEmTitle' => $salaryUser['strEmTitle'],
                        'strEmIni' => $salaryUser['strEmIni'],
                        'strEmName' => $salaryUser['strEmName'],
                        'strEmEmail' => $salaryUser['strEmEmail']
                    ]);
                    $foundInSalary = true;
                    break; // Break the loop once found
                }
            }

            if (!$foundInSalary) {
                $onlyUsjnetUsers[] = $usjnetUser;
            }
        }

        // Loop through Salary data to find users not in USJNet data
        foreach ($salaryData as $salaryUser) {
            $foundInUsjnet = false;
            foreach ($usjnetData as $usjnetUser) {
                if ($usjnetUser['reg_no'] === $salaryUser['strEmpNo']) {
                    $foundInUsjnet = true;
                    break; // Break the loop once found
                }
            }
            if (!$foundInUsjnet) {
                $onlySalaryUsers[] = $salaryUser;
            }
        }

        //dd($onlySalaryUsers);

        // You can pass these arrays to your view
        return view('admin.summary.usjnetsalary.index', [
            'bothSystemUsers' => $bothSystemUsers,
            'onlyUsjnetUsers' => $onlyUsjnetUsers,
            'onlySalaryUsers' => $onlySalaryUsers,
        ]);
    }

    public function salaryActiveUser()
    {
        // Retrieve employee data
        $employeeData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('categories as status', 'status.id', '=', 'employees.employee_status_id')
            ->join('categories as status_type', 'status_type.id', '=', 'employees.employee_status_type_id')
            ->join('categories as fund_type', 'fund_type.id', '=', 'employees.salary_payment_type')
            ->join('categories as main_branch', 'main_branch.id', '=', 'employees.main_branch_id')
            ->select(
                'employees.employee_no',
                'employees.email',
                'employees.nic',
                'employees.nic_old',
                'employees.nic_new',
                'employees.current_basic_salary',
                'employees.mobile_no',
                'employees.initials',
                'employees.last_name',
                'employees.employee_status_id',
                'employees.current_appointment_date',
                'categories.category_name as title',
                'status.category_name as status_name',
                'status_type.category_name as status_type_name',
                'fund_type.category_name as fund_source',
                'main_branch.category_name as main_branch'
            )
            //->where('employee_status_id', 110)
            ->get();

        // Retrieve Salary data
        $empDatas = Http::withHeaders([
            'Api-Key' => 'd1b0ce57-de2c-45ba-bfe5-43685d29acdb',
        ])->get('https://usjnetsso.sjp.ac.lk/api/usjnet/salary_data_list.php');
        $salaryData = json_decode($empDatas->body(), true);

        // Remove leading zeros from reg_no in usjnetData
        foreach ($salaryData as &$salaryMember) {
            $salaryMember['strEmpNo'] = ltrim($salaryMember['strEmpNo'], '0');
        }

        // Initialize lists
        $employeesWithSalaryData = [];
        $employeesWithoutSalaryData = [];
        $salaryDataWithoutEmployees = [];

        // Loop through employee data and check for corresponding data in Salary System
        foreach ($employeeData as $employee) {
            $found = false;
            foreach ($salaryData as $salaryMember) {
                if ($employee->employee_no == $salaryMember['strEmpNo']) {
                    // Employee with corresponding data in USJNet
                    $employeesWithSalaryData[] = [
                        'employee' => $employee,
                        'nic' => $salaryMember['strEmIDNum'],
                        'bsal' => $salaryMember['nBasicSalary']
                    ];
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Employee without corresponding data in USJNet
                $employeesWithoutSalaryData[] = $employee;
            }
        }

        // Loop through USJNet data and check for corresponding employees
        foreach ($salaryData as $salaryMember) {
            $found = false;
            foreach ($employeeData as $employee) {
                if ($employee->employee_no == $salaryMember['strEmpNo']) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Data in USJNet without corresponding employee
                $salaryDataWithoutEmployees[] = $salaryMember;
            }
        }


        // Pass lists to the view
        return view('admin.summary.salary.active_list', compact('employeesWithSalaryData', 'employeesWithoutSalaryData', 'salaryDataWithoutEmployees'));
    }

    public function bankDetailEmployee()
    {
        // Retrieve employee data

        $ActiveEmployeeData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('categories as status', 'status.id', '=', 'employees.employee_status_id')
            ->join('categories as main_branch', 'main_branch.id', '=', 'employees.main_branch_id')
            ->select(
                'employees.employee_no',
                'employees.initials',
                'employees.last_name',
                'categories.category_name as title',
                'department_name',
                'designation_name',
                'status.category_name as status',
                'main_branch.category_name as main_branch'
            )->orderBy('employees.employee_no')->get();;

        // Retrieve USJNet data
        $empDatas = Http::withHeaders([
            'Api-Key' => '********-c7f0-41a6-af14-6821bf205960',
        ])->get('https://usjnetsso.sjp.ac.lk/api/usjnet/account_number_all_get.php');

        $salaryData = json_decode($empDatas->body(), true);

        $activeEmployeesWithSalaryData = [];
        $activeEmployeesWithoutSalaryData = [];

        foreach ($ActiveEmployeeData as $employee) {
            $found = false;

            // Check if $salaryData is not null and is an array
            if ($salaryData !== null && is_array($salaryData)) {
                foreach ($salaryData as $salaryMember) {
                    if ($employee->employee_no == $salaryMember['strEmpNo']) {
                        // Employee with corresponding data in USJNet
                        $activeEmployeesWithSalaryData[] = [
                            'employee' => $employee,
                            'strBankCode' => $salaryMember['strBankCode'],
                            'strBankName' => $salaryMember['strBankName'],
                            'strBankBranch' => $salaryMember['strBankBranch'],
                            'strEmAcctNumber' => $salaryMember['strEmAcctNumber'],
                            'strBranchLocation' => $salaryMember['strBranchLocation']
                        ];
                        $found = true;
                        break;
                    }
                }
            } else {
                // Handle the case where $salaryData is null or not an array
                // You can log an error message or take appropriate action here
            }

            if (!$found) {
                // Employee without corresponding data in USJNet
                $activeEmployeesWithoutSalaryData[] = $employee;
            }
        }




        return view('admin.summary.bank.index', compact('activeEmployeesWithSalaryData', 'activeEmployeesWithoutSalaryData'));
    }

    public function upfEtfEmployee()
    {
        // Retrieve employee data

        $ActiveEmployeeData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('designations', 'designations.id', '=', 'employees.designation_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('categories as status', 'status.id', '=', 'employees.employee_status_id')
            ->join('categories as main_branch', 'main_branch.id', '=', 'employees.main_branch_id')
            ->select(
                'employees.employee_no',
                'employees.initials',
                'employees.last_name',
                'employees.etf_no',
                'employees.upf_no',
                'employees.pension_reference_no',
                'categories.category_name as title',
                'department_name',
                'designation_name',
                'status.category_name as status',
                'main_branch.category_name as main_branch'
            )->orderBy('employees.employee_no')->get();;

        // Retrieve USJNet data
        $empDatas = Http::withHeaders([
            'Api-Key' => 'e954952c-e9bf-4662-829d-515e54402d2d',
        ])->get('https://usjnetsso.sjp.ac.lk/api/usjnet/efp_data_list.php');

        $salaryData = json_decode($empDatas->body(), true);

        $employeesWithSalaryData = [];
        $employeesWithoutSalaryData = [];

        foreach ($ActiveEmployeeData as $employee) {
            $found = false;

            // Check if $salaryData is not null and is an array
            if ($salaryData !== null && is_array($salaryData)) {
                foreach ($salaryData as $salaryMember) {
                    if ($employee->employee_no == $salaryMember['strEmpNo']) {
                        // Employee with corresponding data in USJNet
                        $employeesWithSalaryData[] = [
                            'employee' => $employee,
                            'strUpfNo' => $salaryMember['strUpfNo'],
                            'strPentionNumber' => $salaryMember['strPentionNumber'],
                            'nETFNo' => $salaryMember['nETFNo']
                        ];
                        $found = true;
                        break;
                    }
                }
            } else {
                // Handle the case where $salaryData is null or not an array
                // You can log an error message or take appropriate action here
            }

            if (!$found) {
                // Employee without corresponding data in USJNet
                $employeesWithoutSalaryData[] = $employee;
            }
        }




        return view('admin.summary.upf.index', compact('employeesWithSalaryData', 'employeesWithoutSalaryData'));
    }

    public function NetMobileUpdate(Request $request, $id)
    {

        $data = Employee::find($id);
        $data->mobile_no = $request->net_mobile;
        $data->save();

        $notification = array(
            'message' => 'mobile number update successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('net.user.employeeno')->with($notification);
    }

    public function salaryUpFUpdate(Request $request, $id)
    {

        $data = Employee::find($id);
        $data->upf_no = $request->salary_upf;
        $data->save();

        $notification = array(
            'message' => 'upf number update successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('upf.etf.employee')->with($notification);
    }

    public function salaryEtfUpdate(Request $request, $id)
    {

        $data = Employee::find($id);
        $data->etf_no = $request->salary_etf;
        $data->save();

        $notification = array(
            'message' => 'etf number update successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('upf.etf.employee')->with($notification);
    }

    public function ldap1Employee()
    {
        $employeeData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select(
                'employees.employee_no',
                'employees.email',
                'employees.mobile_no',
                'employees.initials',
                'employees.last_name',
                'categories.category_name as title'
            )->where('employee_status_id', 110)->get();

            $response = Http::withHeaders([
                'X-API-KEY' => 'e28c73d6-8c4a-4f15-a43a-97187259412c'
            ])->get('https://usjnetsso.sjp.ac.lk/sso/api/ldap/employee/all');

            $ldapEmployeeData = json_decode($response->body(), true);

        //$ldapEmployeeData = User::whereIN('description', ['employee'])->get();

        // Initialize lists
        $employeesWithLdapEmployeeData = [];
        $employeesWithoutLdapEmployeeData = [];
        $ldapEmployeeDataWithoutEmployees = [];

        // Loop through employee data and check for corresponding data in LDAP
        foreach ($employeeData as $employee) {
            $found = false;
            foreach ($ldapEmployeeData as $ldapEmployee) {
                if (isset($ldapEmployee['employeenumber'][0]) && $employee->employee_no == $ldapEmployee['employeenumber'][0]) {
                    // Employee with corresponding data in LDAP
                    $employeesWithLdapEmployeeData[] = [
                        'employee' => $employee,
                        'cn' => trim($ldapEmployee['cn'][0] ?? ''),
                        'sn' => trim($ldapEmployee['sn'][0] ?? ''),
                        'mail' => $ldapEmployee['mail'][0] ?? '',
                        'mobile' => $ldapEmployee['mobile'][0] ?? '',
                        //'employeeNumber' => $ldapEmployee->employeeNumber[0] ?? '',
                    ];
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Employee without corresponding data in LDAP
                $employeesWithoutLdapEmployeeData[] = $employee;
            }
        }

        // Loop through LDAP data and check for corresponding employees
        foreach ($ldapEmployeeData as $ldapEmployee) {
            $found = false;
            foreach ($employeeData as $employee) {
                if ($employee->employee_no == $ldapEmployee['employeenumber'][0]) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Data in LDAP without corresponding employee
                $ldapEmployeeDataWithoutEmployees[] = $ldapEmployee;
            }
        }

        // Debugging output
        //dd($employeesWithLdapEmployeeData);

        return view('admin.summary.ldap.ldap1_index', compact('employeesWithLdapEmployeeData','employeesWithoutLdapEmployeeData','ldapEmployeeDataWithoutEmployees'));
    }


    public function ldap2Employee()
    {

        $employeeData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select(
                'employees.employee_no',
                'employees.email',
                'employees.mobile_no',
                'employees.initials',
                'employees.last_name',
                'categories.category_name as title'
            )->where('employee_status_id', 110)->get();

        $ldapEmployeeData = SecondaryUser::whereIN('description', ['employee'])->get();

        // Initialize lists
        $employeesWithLdapEmployeeData = [];
        $employeesWithoutLdapEmployeeData = [];
        $ldapEmployeeDataWithoutEmployees = [];

        // Loop through employee data and check for corresponding data in LDAP
        foreach ($employeeData as $employee) {
            $found = false;
            foreach ($ldapEmployeeData as $ldapEmployee) {
                if (isset($ldapEmployee->employeeNumber[0]) && $employee->employee_no == $ldapEmployee->employeeNumber[0]) {
                    // Employee with corresponding data in LDAP
                    $employeesWithLdapEmployeeData[] = [
                        'employee' => $employee,
                        'cn' => trim($ldapEmployee->cn[0] ?? ''),
                        'sn' => trim($ldapEmployee->sn[0] ?? ''),
                        'mail' => $ldapEmployee->mail[0] ?? '',
                        'mobile' => $ldapEmployee->mobile[0] ?? '',
                        //'employeeNumber' => $ldapEmployee->employeeNumber[0] ?? '',
                    ];
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Employee without corresponding data in LDAP
                $employeesWithoutLdapEmployeeData[] = $employee;
            }
        }

        // Loop through LDAP data and check for corresponding employees
        foreach ($ldapEmployeeData as $ldapEmployee) {
            $found = false;
            foreach ($employeeData as $employee) {
                if ($employee->employee_no == $ldapEmployee->employeeNumber[0]) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Data in LDAP without corresponding employee
                $ldapEmployeeDataWithoutEmployees[] = $ldapEmployee;
            }
        }

        // Debugging output
        //dd($employeesWithLdapEmployeeData);

        return view('admin.summary.ldap.ldap2_index', compact('employeesWithLdapEmployeeData','employeesWithoutLdapEmployeeData','ldapEmployeeDataWithoutEmployees'));
    }

    public function ldap1EmployeeEmail()
    {
        $employeeData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select(
                'employees.employee_no',
                'employees.email',
                'employees.mobile_no',
                'employees.initials',
                'employees.last_name',
                'categories.category_name as title'
            )->where('employee_status_id', 110)->get();



        $response = Http::withHeaders([
            'X-API-KEY' => 'e28c73d6-8c4a-4f15-a43a-97187259412c'
        ])->get('https://usjnetsso.sjp.ac.lk/sso/api/ldap/employee/all');

        $ldapEmployeeData = json_decode($response->body(), true);

        //dd($ldapEmployeeData);

        // Initialize lists
        $employeesWithLdapEmployeeData = [];
        $employeesWithoutLdapEmployeeData = [];
        $ldapEmployeeDataWithoutEmployees = [];

        // Loop through employee data and check for corresponding data in LDAP
        foreach ($employeeData as $employee) {
            $found = false;
            foreach ($ldapEmployeeData as $ldapEmployee) {
                if (isset($ldapEmployee['mail'][0]) && $employee->email == $ldapEmployee['mail'][0]) {
                    // Employee with corresponding data in LDAP
                    $employeesWithLdapEmployeeData[] = [
                        'employee' => $employee,
                        'cn' => trim($ldapEmployee['cn'][0] ?? ''),
                        'sn' => trim($ldapEmployee['sn'][0] ?? ''),
                        'mail' => $ldapEmployee['mail'][0] ?? '',
                        'mobile' => $ldapEmployee['mobile'][0] ?? '',
                        'employeeNumber' => $ldapEmployee['employeenumber'][0] ?? '',
                    ];
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Employee without corresponding data in LDAP
                $employeesWithoutLdapEmployeeData[] = $employee;
            }
        }

        // Loop through LDAP data and check for corresponding employees
        foreach ($ldapEmployeeData as $ldapEmployee) {
            $found = false;
            foreach ($employeeData as $employee) {
                if ($employee->email == $ldapEmployee['mail'][0]) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Data in LDAP without corresponding employee
                $ldapEmployeeDataWithoutEmployees[] = $ldapEmployee;
            }
        }

        // Debugging output
        //dd($employeesWithLdapEmployeeData);

        return view('admin.summary.ldap.ldap1_email_index', compact('employeesWithLdapEmployeeData','employeesWithoutLdapEmployeeData','ldapEmployeeDataWithoutEmployees'));
    }

    public function zoomEmployee()
    {
        $employeeData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->join('categories as status', 'status.id', '=', 'employees.employee_status_id')
            ->join('designations','designations.id','=','employees.designation_id')
            ->join('categories as staff_grade', 'staff_grade.id', '=', 'designations.staff_grade')
            ->join('categories as main_group', 'main_group.id', '=', 'designations.main_group')
            ->select(
                'employees.employee_no',
                'employees.email',
                'employees.mobile_no',
                'employees.initials',
                'employees.last_name',
                'categories.category_name as title',
                'status.category_name as status',
                'departments.department_name',
                'faculties.faculty_name',
                'employees.employee_status_id',
                'designations.designation_name',
                'designations.salary_code',
                'staff_grade.category_name as staff_grade',
                'main_group.category_name as main_group'
            )->where('zoom_active_status',1)->get();

            $response = Http::withHeaders([
                'X-API-KEY' => 'e28c73d6-8c4a-4f15-a43a-97187259412c'
            ])->get('https://usjnetsso.sjp.ac.lk/sso/api/ldap/zoom/list');

            $ldapEmployeeData = json_decode($response->body(), true);

            //dd($ldapEmployeeData);

        // Initialize lists
        $employeesWithLdapEmployeeData = [];
        $employeesWithoutLdapEmployeeData = [];
        $ldapEmployeeDataWithoutEmployees = [];

        // Loop through employee data and check for corresponding data in LDAP
        foreach ($employeeData as $employee) {
            $found = false;
            foreach ($ldapEmployeeData as $ldapEmployee) {
                if (isset($ldapEmployee['employeenumber'][0]) && $employee->employee_no == $ldapEmployee['employeenumber'][0]) {
                    // Employee with corresponding data in LDAP
                    $employeesWithLdapEmployeeData[] = [
                        'employee' => $employee,
                        'cn' => trim($ldapEmployee->cn[0] ?? ''),
                        'sn' => trim($ldapEmployee->sn[0] ?? ''),
                        'mail' => $ldapEmployee->mail[0] ?? '',
                        'mobile' => $ldapEmployee->mobile[0] ?? '',
                        //'employeeNumber' => $ldapEmployee->employeeNumber[0] ?? '',
                    ];
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Employee without corresponding data in LDAP
                $employeesWithoutLdapEmployeeData[] = $employee;
            }
        }

        // Loop through LDAP data and check for corresponding employees
        foreach ($ldapEmployeeData as $ldapEmployee) {
            $found = false;
            foreach ($employeeData as $employee) {
                if ($employee->employee_no == $ldapEmployee['employeenumber'][0]) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Data in LDAP without corresponding employee
                $ldapEmployeeDataWithoutEmployees[] = $ldapEmployee;
            }
        }

        // Debugging output
        //dd($employeesWithLdapEmployeeData);

        return view('admin.summary.ldap.zoom_index', compact('employeesWithLdapEmployeeData','employeesWithoutLdapEmployeeData','ldapEmployeeDataWithoutEmployees'));
    }

    public function academicEmployee()
    {
        $employeeData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->join('categories as status', 'status.id', '=', 'employees.employee_status_id')
            ->join('designations','designations.id','=','employees.designation_id')
            ->join('categories as staff_grade', 'staff_grade.id', '=', 'designations.staff_grade')
            ->join('categories as ugc_mis', 'ugc_mis.id', '=', 'designations.ugc_mis')
            ->select(
                'employees.employee_no',
                'employees.email',
                'employees.mobile_no',
                'employees.initials',
                'employees.last_name',
                'categories.category_name as title',
                'status.category_name as status',
                'departments.department_name',
                'faculties.faculty_name',
                'employees.employee_status_id',
                'designations.designation_name',
                'designations.salary_code',
                'staff_grade.category_name as staff_grade'
            )->whereIN('ugc_mis',[135])->get();

            $response = Http::withHeaders([
                'X-API-KEY' => 'e28c73d6-8c4a-4f15-a43a-97187259412c'
            ])->get('https://usjnetsso.sjp.ac.lk/sso/api/ldap/academic/list');

            $ldapEmployeeData = json_decode($response->body(), true);

        //dd($employeeData);

        // Initialize lists
        $employeesWithLdapEmployeeData = [];
        $employeesWithoutLdapEmployeeData = [];
        $ldapEmployeeDataWithoutEmployees = [];

        // Loop through employee data and check for corresponding data in LDAP
        foreach ($employeeData as $employee) {
            $found = false;
            foreach ($ldapEmployeeData as $ldapEmployee) {
                if (isset($ldapEmployee['employeenumber'][0]) && $employee->employee_no == $ldapEmployee['employeenumber'][0]) {
                    // Employee with corresponding data in LDAP
                    $employeesWithLdapEmployeeData[] = [
                        'employee' => $employee,
                        'cn' => trim($ldapEmployee->cn[0] ?? ''),
                        'sn' => trim($ldapEmployee->sn[0] ?? ''),
                        'mail' => $ldapEmployee->mail[0] ?? '',
                        'mobile' => $ldapEmployee->mobile[0] ?? '',
                        //'employeeNumber' => $ldapEmployee->employeeNumber[0] ?? '',
                    ];
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Employee without corresponding data in LDAP
                $employeesWithoutLdapEmployeeData[] = $employee;
            }
        }

        // Loop through LDAP data and check for corresponding employees
        foreach ($ldapEmployeeData as $ldapEmployee) {
            $found = false;
            foreach ($employeeData as $employee) {
                if (isset($ldapEmployee['employeenumber'][0]) && $employee->employee_no == $ldapEmployee['employeenumber'][0]) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Data in LDAP without corresponding employee
                $ldapEmployeeDataWithoutEmployees[] = $ldapEmployee;
            }
        }

        // Debugging output
        //dd($employeesWithLdapEmployeeData);

        return view('admin.summary.ldap.academic_index', compact('employeesWithLdapEmployeeData','employeesWithoutLdapEmployeeData','ldapEmployeeDataWithoutEmployees'));
    }

    public function nonAcademicEmployee()
    {
        $employeeData = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->join('departments', 'departments.id', '=', 'employees.department_id')
            ->join('faculties', 'faculties.id', '=', 'employees.faculty_id')
            ->join('categories as status', 'status.id', '=', 'employees.employee_status_id')
            ->join('designations','designations.id','=','employees.designation_id')
            ->join('categories as staff_grade', 'staff_grade.id', '=', 'designations.staff_grade')
            ->join('categories as ugc_mis', 'ugc_mis.id', '=', 'designations.ugc_mis')
            ->select(
                'employees.employee_no',
                'employees.email',
                'employees.mobile_no',
                'employees.initials',
                'employees.last_name',
                'categories.category_name as title',
                'status.category_name as status',
                'departments.department_name',
                'faculties.faculty_name',
                'employees.employee_status_id',
                'designations.designation_name',
                'designations.salary_code',
                'staff_grade.category_name as staff_grade'
            )->whereNoTIN('ugc_mis',[135])->get();

            $response = Http::withHeaders([
                'X-API-KEY' => 'e28c73d6-8c4a-4f15-a43a-97187259412c'
            ])->get('https://usjnetsso.sjp.ac.lk/sso/api/ldap/nonacademic/list');

            $ldapEmployeeData = json_decode($response->body(), true);

        //dd($employeeData);

        // Initialize lists
        $employeesWithLdapEmployeeData = [];
        $employeesWithoutLdapEmployeeData = [];
        $ldapEmployeeDataWithoutEmployees = [];

        // Loop through employee data and check for corresponding data in LDAP
        foreach ($employeeData as $employee) {
            $found = false;
            foreach ($ldapEmployeeData as $ldapEmployee) {
                if (isset($ldapEmployee['employeenumber'][0]) && $employee->employee_no == $ldapEmployee['employeenumber'][0]) {
                    // Employee with corresponding data in LDAP
                    $employeesWithLdapEmployeeData[] = [
                        'employee' => $employee,
                        'cn' => trim($ldapEmployee->cn[0] ?? ''),
                        'sn' => trim($ldapEmployee->sn[0] ?? ''),
                        'mail' => $ldapEmployee->mail[0] ?? '',
                        'mobile' => $ldapEmployee->mobile[0] ?? '',
                        //'employeeNumber' => $ldapEmployee->employeeNumber[0] ?? '',
                    ];
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Employee without corresponding data in LDAP
                $employeesWithoutLdapEmployeeData[] = $employee;
            }
        }

        // Loop through LDAP data and check for corresponding employees
        foreach ($ldapEmployeeData as $ldapEmployee) {
            $found = false;
            foreach ($employeeData as $employee) {
                if (isset($ldapEmployee['employeenumber'][0]) && $employee->employee_no == $ldapEmployee['employeenumber'][0]) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Data in LDAP without corresponding employee
                $ldapEmployeeDataWithoutEmployees[] = $ldapEmployee;
            }
        }

        return view('admin.summary.ldap.non_academic_index', compact('employeesWithLdapEmployeeData','employeesWithoutLdapEmployeeData','ldapEmployeeDataWithoutEmployees'));
    }

    public function ldap1NetEmployee()
    {
        $response = Http::withHeaders([
            'X-API-KEY' => 'e28c73d6-8c4a-4f15-a43a-97187259412c'
        ])->get('https://usjnetsso.sjp.ac.lk/sso/api/ldap/employee/all');

        $ldapEmployeeData = json_decode($response->body(), true);

        // Retrieve USJNet data
        $empDatas = Http::withHeaders([
            'Api-Key' => 'b138892c-64cb-4a92-a4d5-a2d40dc03f8b',
        ])->get('https://usjnetsso.sjp.ac.lk/api/usjnet/active_member_list.php');
        $usjnetData = json_decode($empDatas->body(), true);


        // Remove leading zeros from reg_no in usjnetData
        foreach ($usjnetData as &$usjnetMember) {
            $usjnetMember['reg_no'] = ltrim($usjnetMember['reg_no'], '0');
        }

        // Initialize lists
        $employeesWithLdapEmployeeData = [];
        $employeesWithoutLdapEmployeeData = [];
        $ldapEmployeeDataWithoutEmployees = [];

        // Loop through employee data and check for corresponding data in LDAP
        foreach ($usjnetData as $employee) {
            $found = false;
            foreach ($ldapEmployeeData as $ldapEmployee) {
                if (isset($ldapEmployee['employeenumber'][0]) && $employee['reg_no'] == $ldapEmployee['employeenumber'][0]) {
                    // Employee with corresponding data in LDAP
                    $employeesWithLdapEmployeeData[] = array_merge($employee, [
                        'cn' => trim($ldapEmployee['cn'][0] ?? ''),
                        'sn' => trim($ldapEmployee['sn'][0] ?? ''),
                        'mail' => $ldapEmployee['mail'][0] ?? '',
                        'mobile' => $ldapEmployee['mobile'][0] ?? '',
                        'uid' => $ldapEmployee['uid'][0] ?? '',
                        'eduPersonAffiliation' => $ldapEmployee['edupersonaffiliation'][0] ?? '',
                        'userPassword' =>  $ldapEmployee['userpassword'][0] ?? '',
                    ]);
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Employee without corresponding data in LDAP
                $employeesWithoutLdapEmployeeData[] = $employee;
            }
        }

        // Loop through LDAP data and check for corresponding employees
        foreach ($ldapEmployeeData as $ldapEmployee) {
            $found = false;
            foreach ($usjnetData as $employee) {
                if ($employee['reg_no'] == $ldapEmployee['employeenumber'][0]) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Data in LDAP without corresponding employee
                $ldapEmployeeDataWithoutEmployees[] = $ldapEmployee;
            }
        }

        //dd($employeesWithLdapEmployeeData);

        return view('admin.summary.usjnet.ldap_index', compact('employeesWithLdapEmployeeData','employeesWithoutLdapEmployeeData','ldapEmployeeDataWithoutEmployees'));
    }

    public function ldap1NetEmployeeEmail()
    {
        $response = Http::withHeaders([
            'X-API-KEY' => 'e28c73d6-8c4a-4f15-a43a-97187259412c'
        ])->get('https://usjnetsso.sjp.ac.lk/sso/api/ldap/employee/all');

        $ldapEmployeeData = json_decode($response->body(), true);
        // Retrieve USJNet data
        $empDatas = Http::withHeaders([
            'Api-Key' => 'b138892c-64cb-4a92-a4d5-a2d40dc03f8b',
        ])->get('https://usjnetsso.sjp.ac.lk/api/usjnet/active_member_list.php');
        $usjnetData = json_decode($empDatas->body(), true);

        // Initialize lists
        $employeesWithLdapEmployeeData = [];
        $employeesWithoutLdapEmployeeData = [];
        $ldapEmployeeDataWithoutEmployees = [];

        // Loop through employee data and check for corresponding data in LDAP
        foreach ($usjnetData as $employee) {
            $found = false;
            foreach ($ldapEmployeeData as $ldapEmployee) {
                if ($employee['sjp_mail'] == $ldapEmployee['mail'][0]) {
                    // Employee with corresponding data in LDAP
                    $employeesWithLdapEmployeeData[] = array_merge($employee, [
                        'cn' => trim($ldapEmployee['cn'][0] ?? ''),
                        'sn' => trim($ldapEmployee['sn'][0] ?? ''),
                        'employeenumber' => $ldapEmployee['employeenumber'][0] ?? '',
                        'mobile' => $ldapEmployee['mobile'][0] ?? '',
                        'uid' => $ldapEmployee['uid'][0] ?? '',
                        'eduPersonAffiliation' => $ldapEmployee['edupersonaffiliation'][0] ?? '',
                        'userPassword' =>  $ldapEmployee['userpassword'][0] ?? '',
                    ]);
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Employee without corresponding data in LDAP
                $employeesWithoutLdapEmployeeData[] = $employee;
            }
        }

        // Loop through LDAP data and check for corresponding employees
        foreach ($ldapEmployeeData as $ldapEmployee) {
            $found = false;
            foreach ($usjnetData as $employee) {
                if ($employee['sjp_mail'] == $ldapEmployee['mail'][0]) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Data in LDAP without corresponding employee
                $ldapEmployeeDataWithoutEmployees[] = $ldapEmployee;
            }
        }

        return view('admin.summary.usjnet.ldap_index_email', compact('employeesWithLdapEmployeeData','employeesWithoutLdapEmployeeData','ldapEmployeeDataWithoutEmployees'));
    }
}
