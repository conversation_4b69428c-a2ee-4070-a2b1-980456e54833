<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RemoveCommas extends Command
{

    protected $signature = 'data:remove-commas';


    protected $description = 'Remove commas from current_basic_salary';


    public function handle()
    {
        DB::table('employees')->update([
            'current_basic_salary' => DB::raw('REPLACE(current_basic_salary, ",", "")')
        ]);
        $this->info('Commas removed from your_column.');
        return Command::SUCCESS;
    }
}
