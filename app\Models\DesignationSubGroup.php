<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class DesignationSubGroup extends Model
{
    use HasFactory,SoftDeletes,LogsActivity;

    protected $table = 'designation_sub_groups';

    protected $dates = ['deleted_at'];

    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('hrms_designation_sub_groups')
        ->logOnlyDirty()
        ->dontLogIfAttributesChangedOnly(['updated_at'])
        ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    public function designations()
    {
    	return $this->hasMany(Designation::class);
    }

    public function mainDesignationGroup()
    {
        return $this->belongsTo(DesignationMainGroup::class,'designation_main_group_id');
    }
}
