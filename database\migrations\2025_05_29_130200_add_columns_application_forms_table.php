<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('application_forms', function (Blueprint $table) {
            //
            $table->integer('short_list_status')->default(0)->after('protect_application');
            $table->text('short_list_remark')->nullable()->after('short_list_status');
            $table->integer('short_list_user')->default(0)->after('short_list_remark');
            $table->integer('short_list_user_position')->default(0)->after('short_list_user');
            $table->date('short_list_date')->nullable()->after('short_list_user_position');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('application_forms', function (Blueprint $table) {
            //
            $table->dropColumn('short_list_status');
            $table->dropColumn('short_list_remark');
            $table->dropColumn('short_list_user');
            $table->dropColumn('short_list_user_position');
            $table->dropColumn('short_list_date');
        });
    }
};
