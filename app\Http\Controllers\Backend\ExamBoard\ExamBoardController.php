<?php

namespace App\Http\Controllers\Backend\ExamBoard;

use App\Http\Controllers\Controller;
use App\Models\Designation;
use App\Models\Employee;
use App\Models\ExamBoard;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class ExamBoardController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function addExamBoardOpen()
    {
        $mainBranch = Auth()->user()->main_branch_id;

        $sixMonthsAgo = Carbon::now()->subMonths(6);

        $categories = $this->getCategories([46]);
        $interType = $categories->where('category_type_id', '46');

        $desig = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('designations.id', 'designations.designation_name', 'categories.category_name', 'designations.salary_code')
            ->where('designations.deleted_at', '=', NULL)
            ->get();

        $empText = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
            ->select('employees.employee_no', 'categories.category_name', 'employees.initials', 'employees.last_name')
            ->where('employees.employee_status_id', '=', 110)
            ->where('employees.main_branch_id', '=', 52)
            ->get();


        if ($mainBranch == 51) {

            $panel = ExamBoard::join('categories as type', 'type.id', '=', 'exam_boards.type_id')
                ->join('designations', 'designations.id', '=', 'exam_boards.desig_id')
                ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                ->join('employees', 'employees.employee_no', '=', 'exam_boards.examiner_id')
                ->join('categories as emTitle', 'emTitle.id', '=', 'employees.title_id')
                ->select(
                    'exam_boards.*',
                    'type.category_name as interType',
                    'designations.designation_name',
                    'categories.category_name',
                    'emTitle.category_name as title',
                    'employees.initials',
                    'employees.last_name'
                )
                ->where('exam_boards.created_at', '>=', $sixMonthsAgo)
                ->get();
        } elseif ($mainBranch == 52) {

            if (Auth()->user()->hasRole(['est-head'])) {

                $panel = ExamBoard::join('categories as type', 'type.id', '=', 'exam_boards.type_id')
                    ->join('designations', 'designations.id', '=', 'exam_boards.desig_id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('employees', 'employees.employee_no', '=', 'exam_boards.examiner_id')
                    ->join('categories as emTitle', 'emTitle.id', '=', 'employees.title_id')
                    ->select(
                        'exam_boards.*',
                        'type.category_name as interType',
                        'designations.designation_name',
                        'categories.category_name',
                        'emTitle.category_name as title',
                        'employees.initials',
                        'employees.last_name'
                    )
                    ->where('exam_boards.created_at', '>=', $sixMonthsAgo)
                    ->where('exam_boards.division_id', '=', 52)
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $panel = ExamBoard::join('categories as type', 'type.id', '=', 'exam_boards.type_id')
                    ->join('designations', 'designations.id', '=', 'exam_boards.desig_id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('employees', 'employees.employee_no', '=', 'exam_boards.examiner_id')
                    ->join('categories as emTitle', 'emTitle.id', '=', 'employees.title_id')
                    ->select(
                        'exam_boards.*',
                        'type.category_name as interType',
                        'designations.designation_name',
                        'categories.category_name',
                        'emTitle.category_name as title',
                        'employees.initials',
                        'employees.last_name'
                    )
                    ->where('exam_boards.created_at', '>=', $sixMonthsAgo)
                    ->where('exam_boards.division_id', '=', 52)
                    ->where('exam_boards.enter_user', '=', auth()->user()->employee_no)
                    ->get();
            }
        } elseif ($mainBranch == 53) {
            # code...
            if (Auth()->user()->hasRole(['est-head'])) {

                $panel = ExamBoard::join('categories as type', 'type.id', '=', 'exam_boards.type_id')
                    ->join('designations', 'designations.id', '=', 'exam_boards.desig_id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('employees', 'employees.employee_no', '=', 'exam_boards.examiner_id')
                    ->join('categories as emTitle', 'emTitle.id', '=', 'employees.title_id')
                    ->select(
                        'exam_boards.*',
                        'type.category_name as interType',
                        'designations.designation_name',
                        'categories.category_name',
                        'emTitle.category_name as title',
                        'employees.initials',
                        'employees.last_name'
                    )
                    ->where('exam_boards.created_at', '>=', $sixMonthsAgo)
                    ->where('exam_boards.division_id', '=', 53)
                    ->get();
            } elseif (Auth()->user()->hasRole(['cc', 'sc'])) {

                $panel = ExamBoard::join('categories as type', 'type.id', '=', 'exam_boards.type_id')
                    ->join('designations', 'designations.id', '=', 'exam_boards.desig_id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('employees', 'employees.employee_no', '=', 'exam_boards.examiner_id')
                    ->join('categories as emTitle', 'emTitle.id', '=', 'employees.title_id')
                    ->select(
                        'exam_boards.*',
                        'type.category_name as interType',
                        'designations.designation_name',
                        'categories.category_name',
                        'emTitle.category_name as title',
                        'employees.initials',
                        'employees.last_name'
                    )
                    ->where('exam_boards.created_at', '>=', $sixMonthsAgo)
                    ->where('exam_boards.division_id', '=', 53)
                    ->where('exam_boards.enter_user', '=', auth()->user()->employee_no)
                    ->get();
            }
        } else {

            $panel = array();
        }


        return view('admin.exam_board.add_exam', compact('interType', 'desig', 'panel', 'empText'));
    }

    public function examBoardStore(Request $request)
    {
        $request->validate(
            [
                'type' => 'required|integer|min:1',
                'emp' => 'required|integer|min:1',
                'desig' => 'required|integer|min:1',

            ],
            [
                'type.required' => 'Please Select exam Type',
                'type.integer' => 'Please Select exam Type',
                'type.min' => 'Please Select exam Type',
                'emp.required' => 'Please Select the examiner',
                'emp.integer' => 'Please Select the examiner',
                'emp.min' => 'Please Select the examiner',
                'desig.required' => 'Please Select one',
                'desig.integer' => 'Please Select one',
                'desig.min' => 'Please Select one',
            ]
        );

        $year = date("Y");
        $divis = "OTH";

        if (auth()->user()->main_branch_id == 53) {
            $divis = "NAC";
        } else if (auth()->user()->main_branch_id == 52) {
            $divis = "AC";
        }

        $ref = $year . "/" . $divis . "/EXA";

        $refCount = ExamBoard::select('exam_boards.*')
            ->where('year', '=', $year)
            ->where('division_id', '=', auth()->user()->main_branch_id)
            ->count();
        //dd($refText);

        $refValue = $refCount + 1;

        //dd($refValue);

        $refID = $ref . "/" . str_pad($refValue, 4, 0, STR_PAD_LEFT);

        $saveText = new ExamBoard();
        $saveText->type_id = $request->type;
        $saveText->year = $year;
        $saveText->division_id = auth()->user()->main_branch_id;
        $saveText->board_ref_no = $refID;
        $saveText->desig_id = $request->desig;
        $saveText->examiner_id = $request->emp;
        $saveText->enter_user = auth()->user()->employee_no;
        $saveText->status = 1;
        $saveText->save();

        $confText = ExamBoard::join('categories as type', 'type.id', '=', 'exam_boards.type_id')
            ->join('designations', 'designations.id', '=', 'exam_boards.desig_id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->join('employees', 'employees.employee_no', '=', 'exam_boards.examiner_id')
            ->join('categories as emTitle', 'emTitle.id', '=', 'employees.title_id')
            ->select(
                'exam_boards.*',
                'type.category_name as interType',
                'designations.designation_name',
                'categories.category_name',
                'emTitle.category_name as title',
                'employees.initials',
                'employees.last_name'
            )
            ->where('exam_boards.board_ref_no', '=', $refID)
            ->get();

        if ($confText->count() > 0) {

            foreach ($confText as $confTexts) {
                $refNo = $confTexts->board_ref_no;
                $examType = $confTexts->interType;
                $desig = $confTexts->designation_name . " " . $confTexts->category_name;
                $examiner = $confTexts->title . " " . $confTexts->initials . " " . $confTexts->last_name;
            }

            return view('admin.exam_board.add_exam_submit', compact('refNo', 'examType', 'desig', 'examiner'));
        } else {
            $notification = array(
                'message' => 'Unsuccessfully submitted',
                'alert-type' => 'error'
            );

            return redirect()->route('examBoard.add.open')->with($notification);
        }
    }

    public function examDetailsOpen(Request $request)
    {

        $sixMonthsAgo = Carbon::now()->subMonths(6);

        if (Auth()->user()->hasRole(['cc', 'sc'])) {
            $examBoard = ExamBoard::where('division_id', '=', auth()->user()->main_branch_id)
                ->where('enter_user', '=', auth()->user()->employee_no)
                ->where('created_at', '>=', $sixMonthsAgo)
                ->get();
        } else {
            $examBoard = ExamBoard::where('division_id', '=', auth()->user()->main_branch_id)
                ->where('created_at', '>=', $sixMonthsAgo)
                ->get();
        }

        if (isset($request->eBoard)) {
            $currentPanel = $request->eBoard;
            if ($currentPanel != 0) {

                $confText = ExamBoard::join('categories as type', 'type.id', '=', 'exam_boards.type_id')
                    ->join('designations', 'designations.id', '=', 'exam_boards.desig_id')
                    ->join('categories', 'designations.staff_grade', '=', 'categories.id')
                    ->join('employees', 'employees.employee_no', '=', 'exam_boards.examiner_id')
                    ->join('categories as emTitle', 'emTitle.id', '=', 'employees.title_id')
                    ->select(
                        'exam_boards.*',
                        'type.category_name as interType',
                        'designations.designation_name',
                        'categories.category_name',
                        'emTitle.category_name as title',
                        'employees.initials',
                        'employees.last_name'
                    )
                    ->where('exam_boards.id', '=', $currentPanel)
                    ->get();

                if ($confText->count() > 0) {
                    foreach ($confText as $confTexts) {
                        $refNo = $confTexts->board_ref_no;
                        $desig = $confTexts->designation_name . " " . $confTexts->category_name;
                        $examiner = $confTexts->title . " " . $confTexts->initials . " " . $confTexts->last_name;
                        $ExDate = $confTexts->exam_date;
                        $exTime = $confTexts->exam_time;
                        $exVenue = $confTexts->exam_venue;
                        $canAdd = 1;
                        $exID = $confTexts->id;
                    }
                } else {
                    $refNo = "";
                    $desig = "";
                    $examiner = "";
                    $ExDate = "";
                    $exTime = "";
                    $exVenue = "";
                    $canAdd = 0;
                    $exID = "";
                }
            } else {
                $refNo = "";
                $desig = "";
                $examiner = "";
                $ExDate = "";
                $exTime = "";
                $exVenue = "";
                $canAdd = 0;
                $exID = "";
            }
        } else {
            $currentPanel = 0;
            $refNo = "";
            $desig = "";
            $examiner = "";
            $ExDate = "";
            $exTime = "";
            $exVenue = "";
            $canAdd = 0;
            $exID = "";
        }



        return view('admin.exam_board.add_exam_details', compact(
            'examBoard',
            'currentPanel',
            'refNo',
            'desig',
            'examiner',
            'ExDate',
            'exTime',
            'exVenue',
            'canAdd',
            'exID'
        ));
    }

    public function examDetailsSave(Request $request)
    {
        $request->validate(
            [
                'examDate' => 'required|date',
                'examTime' => 'required',
                'venue' => 'required'
            ],
            [
                'examDate.required' => 'Please select the exam date',
                'examTime.required' => 'Please select the exam time',
                'venue.required' => 'Please enter the exam venue'
            ]
        );

        if ($request->examDate != "") {
            if ($request->examTime != "") {
                if ($request->venue != "") {

                    $examBoard = ExamBoard::find($request->examID);

                    if ($examBoard) {

                        $examBoard->exam_date = Carbon::parse($request->examDate)->toDateString(); // Formats to 'Y-m-d'
                        $examBoard->exam_time = $request->examTime;
                        $examBoard->exam_venue = $request->venue;
                        $examBoard->details_enter_user = auth()->user()->employee_no;
                        $examBoard->details_enter_date = Carbon::today();
                        $examBoard->save();
                    }
                    $notification = array(
                        'message' => 'Successfully save',
                        'alert-type' => 'success'
                    );
                    return redirect()->route('examBoard.details.add.Open')->with($notification);
                } else {
                    $notification = array(
                        'message' => 'Please enter the exam time before save',
                        'alert-type' => 'error'
                    );

                    return redirect()->route('examBoard.details.add.Open')->with($notification);
                }
            } else {
                $notification = array(
                    'message' => 'Please select the exam time before save',
                    'alert-type' => 'error'
                );

                return redirect()->route('examBoard.details.add.Open')->with($notification);
            }
        } else {
            $notification = array(
                'message' => 'Please select the exam date before save',
                'alert-type' => 'error'
            );

            return redirect()->route('examBoard.details.add.Open')->with($notification);
        }
    }
}
