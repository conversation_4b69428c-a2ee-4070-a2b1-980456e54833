<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('employee_change_histories', function (Blueprint $table) {
            $table->text('pervious_record')->change();
            $table->text('new_record')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('employee_change_histories', function (Blueprint $table) {
            $table->string('pervious_record', 255)->change();
            $table->string('new_record', 255)->change();
        });
    }
};
