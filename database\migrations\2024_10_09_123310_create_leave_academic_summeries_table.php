<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('leave_academic_summeries', function (Blueprint $table) {
            $table->id();
            $table->integer('emp_no');
            $table->integer('leave_type');
            $table->integer('ac_year_id')->default(0);
            $table->date('starting_date');
            $table->date('end_date');
            $table->integer('duration_year')->default(0);
            $table->integer('duration_month')->default(0);
            $table->integer('duration_day')->default(0);
            $table->text('purpose')->nullable();
            $table->integer('add_user');
            $table->date('add_date');
            $table->integer('status')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('leave_academic_summeries');
    }
};
