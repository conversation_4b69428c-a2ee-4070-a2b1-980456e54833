<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('new_employees', function (Blueprint $table) {
            $table->id();
            $table->integer('employee_no')->default(0);
            $table->string('file_reference_number')->nullable();
            $table->integer('main_branch_id')->index();
            $table->bigInteger('designation_id')->index();
            $table->bigInteger('faculty_id')->index();
            $table->bigInteger('department_id')->index();
            $table->string('sub_department_id',15)->nullable();
            $table->string('nic');
            $table->date('date_of_birth');
            $table->string('nic_old');
            $table->string('nic_new');
            $table->integer('active_nic');
            $table->date('dob_gen');
            $table->bigInteger('title_id')->index();
            $table->string('initials',100);
            $table->string('name_denoted_by_initials',255);
            $table->string('last_name',100);
            $table->bigInteger('civil_status_id')->index();
            $table->bigInteger('gender_id')->index();
            $table->bigInteger('race_id')->nullable();
            $table->bigInteger('religion_id')->nullable();
            $table->string('permanent_add1',100);
            $table->string('permanent_add2',100)->nullable();
            $table->string('permanent_add3',100)->nullable();
            $table->bigInteger('permanent_city_id')->index();
            $table->string('postal_add1',100);
            $table->string('postal_add2',100)->nullable();
            $table->string('postal_add3',100)->nullable();
            $table->bigInteger('postal_city_id')->index();
            $table->string('email',100)->nullable();
            $table->string('personal_email',100)->nullable();
            $table->string('mobile_no',15);
            $table->string('telephone_no',15)->nullable();
            $table->bigInteger('state_of_citizenship_id')->index();
            $table->string('citizen_registration_no',100)->nullable();
            $table->date('initial_appointment_date')->nullable();
            $table->date('gratuity_cal_date')->nullable();
            $table->date('current_appointment_date')->nullable();
            $table->date('salary_termination_date_1')->nullable();
            $table->date('salary_termination_date_2')->nullable();
            $table->date('retirement_date')->nullable();
            $table->string('increment_date')->nullable();
            $table->decimal('current_basic_salary', 10, 2);
            $table->date('sabbatical_start')->nullable();
            $table->date('sabbatical_end')->nullable();
            $table->bigInteger('emp_highest_edu_level')->index();
            $table->bigInteger('employee_work_type')->index();
            $table->bigInteger('salary_payment_type')->index();
            $table->bigInteger('added_ma_id')->index();
            $table->date('added_ma_date');
            $table->bigInteger('approved_ar_id')->nullable();
            $table->date('approved_ar_date')->nullable();
            $table->integer('completion_status')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('new_employees');
    }
};
