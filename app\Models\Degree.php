<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Degree extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function degreeTypeName()
    {
        return $this->belongsTo(Category::class,'degree_type');

    }

    public function degreeClassName()
    {
        return $this->belongsTo(Category::class,'degree_class');

    }

}
