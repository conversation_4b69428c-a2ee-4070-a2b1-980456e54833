<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Designation;
use App\Models\SalaryScale;
use App\Models\SalaryScaleVersion;
use App\Models\scaleVersionDesignation;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SalaryScaleController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function SalaryScsaleIndex()
    {
        $mainBranch = Auth()->user()->main_branch_id;

        // Get active version
        $activeVersion = SalaryScaleVersion::where('status', 1)->first();

        $query = SalaryScale::with(['version' => function ($q) {
            $q->select('id', 'effective_date', 'termination_date', 'status');
        }])
            ->where('status', 1)
            ->where('salary_scale_version_id', $activeVersion->id)
            ->orderBy('salary_code');

        if ($mainBranch == 51) {
            $salary_scales = $query->get();
        } elseif ($mainBranch == 52) {
            $salary_scales = $query->where('main_branch', 52)->get();
        } elseif ($mainBranch == 53) {
            $salary_scales = $query->where('main_branch', 53)->get();
        }

        //dd($salary_scales);

        return view('admin.setups.salary_scale.index', compact('salary_scales'));
    }

    public function SalaryScsaleAdd()
    {
        $mainBranch = Auth()->user()->main_branch_id;
        $roleID = Auth()->user()->role_id;
        $userID = Auth()->user()->id;
        $empNo = Auth()->user()->employee_no;
        $currentDate = date('Y-m-d');

        // Get active version
        $activeVersion = SalaryScaleVersion::where('status', 1)->first();

        $query = Designation::select('salary_code');

        // Base conditions
        $query->whereNotIn('salary_code', ['ON CONTRACT', 'Allowance', 'ON ASSIGNMENT BASIS', 'ON TEMPORARY'])
            ->where('active_status', 1);

        // Add branch-specific conditions
        if ($mainBranch == 52 || $mainBranch == 53) {
            $query->where('designation_division', $mainBranch);
        }

        // Filter out salary codes that already have scales in the active version
        if ($activeVersion) {
            $query->whereNotIn('salary_code', function ($subquery) use ($activeVersion) {
                $subquery->select('salary_code')
                    ->from('salary_scales')
                    ->where('salary_scale_version_id', $activeVersion->id);
            });
        } else {
            $query->whereNotIn('salary_code', function ($subquery) {
                $subquery->select('salary_code')->from('salary_scales');
            });
        }

        $salary_codes = $query->groupBy('salary_code')
            ->orderBy('salary_code')
            ->get();

        return view('admin.setups.salary_scale.add', compact('salary_codes', 'activeVersion'));
    }

    public function SalaryScsaleStore(Request $request)
    {

        $validatedData = $request->validate([
            'salary_code' => 'required',
            'initial_basic_salary' => 'required',
            'increment_value1' => 'required|numeric',
            'step_number1' => 'required|numeric',
            'increment_value2' => 'nullable|numeric',
            'step_number2' => 'nullable|numeric',
            'increment_value3' => 'nullable|numeric',
            'step_number3' => 'nullable|numeric',
            'salary_scale_start_step' => 'required|numeric',
        ], [
            'salary_code.required' => 'select salary code',
            'initial_basic_salary.required' => 'initial basic salary required',
            'increment_value1.required' => 'increment value 1 required',
            'step_number1' => 'increment step count required',
            'salary_scale_start_step' => 'salary scale start step required',
        ]);

        $activeVersion = SalaryScaleVersion::where('status', 1)->first();
        if (!$activeVersion) {
            $notification = array(
                'message' => 'No active salary scale version found. Please create a version first.',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
        }

        $this->updateEmployeeClassification($request->salary_code);

        // Deactivate previous salary scales for this salary code
        SalaryScale::where('salary_code', $request->salary_code)->where('status', 1)->update(['status' => 0]);

        $data = new SalaryScale();
        $data->main_branch = Auth()->user()->main_branch_id;
        $data->salary_code = $request->salary_code;
        $data->initial_basic_salary = $request->initial_basic_salary;
        $data->increment_value1 = $request->increment_value1;
        $data->step_number1 = $request->step_number1;
        $data->increment_value2 = $request->increment_value2;
        $data->step_number2 = $request->step_number2;
        $data->increment_value3 = $request->increment_value3;
        $data->step_number3 = $request->step_number3;
        $data->eb_exam = $request->eb_exam;
        $data->salary_scale_start_step = $request->salary_scale_start_step;
        $data->salary_scale_version_id = $activeVersion->id;


        if ($data->eb_exam == 0) {

            if ($data->increment_value1 == 0 && $data->step_number1 == 0) {

                $data->salary_scale_txt = 'Rs. ' . intval($data->initial_basic_salary) . ' (fixed) p.m.';
            } elseif ($data->increment_value2 != null && $data->step_number2 != null) {

                $data->salary_scale_txt = 'Rs. ' . intval($data->initial_basic_salary) . '-' . $data->step_number1 . 'x' . $data->increment_value1 . '; ' . $data->step_number2 . 'x' . $data->increment_value2 . '-' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) + ($data->increment_value2 * $data->step_number2) . ' p.m.';
            } elseif ($data->increment_value2 == null && $data->step_number2 == null) {

                $data->salary_scale_txt = 'Rs. ' . intval($data->initial_basic_salary)  . '-' . $data->step_number1 . 'x' . $data->increment_value1 . '-' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) . ' p.m.';
            } else {

                $data->salary_scale_txt = '';
            }
        } else {

            if ($data->increment_value3 != null && $data->step_number3 != null) {

                $data->salary_scale_txt = 'Rs. ' . intval($data->initial_basic_salary) . '-' . $data->step_number1 . 'x' .  $data->increment_value1 . '-' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) . '(EB)' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) + $data->increment_value2 . '-' . $data->step_number2 . '-' .  $data->increment_value2 . '; ' . $data->step_number3 . 'x' . $data->increment_value3 . 'x' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) + ($data->increment_value2 * $data->step_number2) + ($data->increment_value3 * $data->step_number3) + $data->increment_value2 . ' p.m.';
            } elseif ($data->increment_value3 == null && $data->step_number3 == null) {

                $data->salary_scale_txt = 'Rs. ' . intval($data->initial_basic_salary) . '-' . $data->step_number1 . 'x' .  $data->increment_value1 . '-' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) . '(EB)' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) + $data->increment_value2 . '-' . $data->step_number2 . 'x' .  $data->increment_value2 . '-' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) + ($data->increment_value2 * $data->step_number2) + $data->increment_value2 . ' p.m.';
            } else {

                $data->salary_scale_txt = '';
            }
        }


        $data->status = 1;
        $data->added_user_id = Auth()->user()->employee_no;
        $data->added_date = Carbon::today();
        $data->save();

        $designations = Designation::where('salary_code', $request->salary_code)->where('active_status',1)->get();

        foreach ($designations as $designation) {

            $designation->salary_scale = $data->id;
            $designation->save();


            scaleVersionDesignation::create([
                'desig_id'    => $designation->id,
                'scale_id'    => $data->id,
                'version_id'  => $activeVersion->id,
            ]);
        }


        $notification = array(
            'message' => 'New  Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('salary.scale.index')->with($notification);
    }

    public function SalaryScsaleEdit($id)
    {
        $editData = SalaryScale::find($id);
        return view('admin.setups.salary_scale.edit', compact('editData'));
    }

    public function SalaryScsaleUpdate(Request $request, $id)
    {
        $validatedData = $request->validate([
            'salary_code' => 'required',
            'initial_basic_salary' => 'required',
            'increment_value1' => 'required|numeric',
            'step_number1' => 'required|numeric',
            'increment_value2' => 'nullable|numeric',
            'step_number2' => 'nullable|numeric',
            'increment_value3' => 'nullable|numeric',
            'step_number3' => 'nullable|numeric',
            'salary_scale_start_step' => 'required|numeric',
        ], [
            'salary_code.required' => 'select salary code',
            'initial_basic_salary.required' => 'initial basic salary required',
            'increment_value1.required' => 'increment value 1 required',
            'step_number1' => 'increment step count required',
            'salary_scale_start_step' => 'salary scale start step required',
        ]);


        $data = SalaryScale::find($id);
        $data->main_branch = Auth()->user()->main_branch_id;
        $data->salary_code = $request->salary_code;
        $data->initial_basic_salary = $request->initial_basic_salary;
        $data->increment_value1 = $request->increment_value1;
        $data->step_number1 = $request->step_number1;
        $data->increment_value2 = $request->increment_value2;
        $data->step_number2 = $request->step_number2;
        $data->increment_value3 = $request->increment_value3;
        $data->step_number3 = $request->step_number3;
        $data->salary_scale_start_step = $request->salary_scale_start_step;
        $data->eb_exam = $request->eb_exam;


        if ($data->eb_exam == 0) {

            if ($data->increment_value2 != null && $data->step_number2 != null) {

                $data->salary_scale_txt = 'Rs. ' . intval($data->initial_basic_salary) . '-' . $data->step_number1 . 'x' . $data->increment_value1 . '; ' . $data->step_number2 . 'x' . $data->increment_value2 . '-' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) + ($data->increment_value2 * $data->step_number2) . ' p.m.';
            } elseif ($data->increment_value2 == null && $data->step_number2 == null) {

                $data->salary_scale_txt = 'Rs. ' . intval($data->initial_basic_salary)  . '-' . $data->step_number1 . 'x' . $data->increment_value1 . '-' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) . ' p.m.';
            } else {

                $data->salary_scale_txt = '';
            }
        } else {

            if ($data->increment_value3 != null && $data->step_number3 != null) {

                $data->salary_scale_txt = 'Rs. ' . intval($data->initial_basic_salary) . '-' . $data->step_number1 . 'x' .  $data->increment_value1 . '-' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) . '(EB)' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) + $data->increment_value2 . '-' . $data->step_number2 . '-' .  $data->increment_value2 . '; ' . $data->step_number3 . 'x' . $data->increment_value3 . 'x' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) + ($data->increment_value2 * $data->step_number2) + ($data->increment_value3 * $data->step_number3) + $data->increment_value2 . ' p.m.';
            } elseif ($data->increment_value3 == null && $data->step_number3 == null) {

                $data->salary_scale_txt = 'Rs. ' . intval($data->initial_basic_salary) . '-' . $data->step_number1 . 'x' .  $data->increment_value1 . '-' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) . '(EB)' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) + $data->increment_value2 . '-' . $data->step_number2 . 'x' .  $data->increment_value2 . '-' . intval($data->initial_basic_salary) + ($data->increment_value1 * $data->step_number1) + ($data->increment_value2 * $data->step_number2) + $data->increment_value2 . ' p.m.';
            } else {

                $data->salary_scale_txt = '';
            }
        }


        //$data->start_date = date("Y-m-d", strtotime($request->start_date));
        $data->status = 1;
        $data->edit_user_id = Auth()->user()->employee_no;
        $data->edited_date = Carbon::today();
        $data->save();

        $notification = array(
            'message' => 'salary Scale Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('salary.scale.index')->with($notification);
    }

    public function SalaryScsaleShow($id)
    {
        $data = SalaryScale::find($id);

        $scaleHistory = SalaryScale::with(['version' => function ($q) {
            $q->select('id', 'effective_date', 'termination_date', 'status');
        }])
            ->where('salary_code', $data->salary_code)
            ->orderBy('salary_scale_version_id', 'desc')
            ->get();

        //dd($scaleHistory);

        return view('admin.setups.salary_scale.show', compact('scaleHistory'));
    }

    public function SalaryScsaleDelete($id)
    {

        $data = SalaryScale::find($id);
        $data->delete();

        $designationtableupdate = Designation::where('salary_scale', '=', $id)
            ->update(['salary_scale' => 0, 'service_category_id' => 0]);

        $notification = array(
            'message' => 'salary Scale Deleted Successfully',
            'alert-type' => 'danger'
        );

        return redirect()->route('salary.scale.index')->with($notification);
    }

    private function updateEmployeeClassification($salaryCode)
    {
        $result = strtok($salaryCode, '(');

        $data = Category::where('category_code', '=', $result)->first();
        if (!$data) {
            Log::warning("No category found for salary code: {$salaryCode}");
            return;
        }

        Designation::where('salary_code', '=', $salaryCode)->update(['service_category_id' => $data->id]);
    }
}
