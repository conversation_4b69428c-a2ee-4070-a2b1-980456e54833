<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('nav_higher_eduacation_qulifications', function (Blueprint $table) {
            $table->id();
            $table->integer('result_slot_id')->unsigned();
            $table->string('Institution')->nullable();
            $table->string('Name_of_course')->nullable();
            $table->integer('type')->nullable();
            $table->string('Final_result')->nullable();
            $table->string('start_year')->nullable();
            $table->string('end_year')->nullable();
            $table->date('effective_date')->nullable();
            $table->string('duration')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('nav_higher_eduacation_qulifications');
    }
};
