<?php

namespace App\Http\Controllers\Backend\Leave;

use App\Http\Controllers\Controller;
use App\Models\AnnualLeave;
use App\Models\AnnualLeaveSummary;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Faculty;
use App\Models\Leave;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AnnualLeaveController extends Controller
{
    // Store individual leave
    public function storeLeave(Request $request){
        $year = Carbon::parse($request->fromDate)->format('Y');
        $month = Carbon::parse($request->fromDate)->format('m');
        $dept_code = $request->dept_code;
        $faculty_code = $request->faculty_code;
        $leave_type = $request->leaveType;

        if ($leave_type == 4){
            $leaveCount = 0.5;
        } else {
            $leaveCount = 1;
        }

        AnnualLeave::create([
            'empNo' => $request->empNo,
            'designation_id' =>$request->designation_id,
            'from_date' => $request->fromDate,
            'to_date' => $request->fromDate,
            'leave_count' => $leaveCount,
            'leave_type' => $request->leaveType,
            'ma_remark' => $request->ma_remark,
            'year' => $year,
            'month' => $month,
            'month_code' => $year.$month,
            'dept_code' => $dept_code,
            'faculty_code' => $faculty_code,
            'submitted_status' => 1,
        ]);
        return back();
    }

    private function getDepartmentData(){
        
    }

    public function AnnualLeaveMA(){
        $deptData = Employee::join('departments', 'employees.department_id', '=', 'departments.id')->where('employees.employee_no',auth()->user()->employee_no)->select('departments.id', 'departments.faculty_code')->first();
        // dd($deptData->id);

        // Monthly Summery
        $leaveSummary = Employee::select(
            'employees.initials',
            'employees.last_name',
            'annual_leaves.empNo',
            'employees.designation_id',
            DB::raw('SUM(CASE WHEN leave_type IN (1, 4) THEN leave_count ELSE 0 END) as casual_leave'),
            DB::raw('SUM(CASE WHEN leave_type = 2 THEN leave_count ELSE 0 END) as medical_leave'),
            DB::raw('SUM(CASE WHEN leave_type = 3 THEN leave_count ELSE 0 END) as short_leave'),
            // DB::raw('SUM(leave_count) as total_leave')
            DB::raw('SUM(CASE WHEN leave_type IN (1, 2, 4) THEN leave_count ELSE 0 END) as total_leave') // Exclude short leave (leave_type = 3)
        )
        ->where('annual_leaves.ma_forward_status', 0)
        ->join('annual_leaves', 'employees.employee_no', '=', 'annual_leaves.empNo') // Assuming `users` table has employee details
        ->groupBy('employees.initials', 'employees.last_name', 'annual_leaves.empNo', 'employees.designation_id')
        ->get();
    

        $employee = Employee::where('employee_status_id', 110)->where('department_id', $deptData->id)->get();

        // $leaveHistory = Employee::where('employee_status_id', 110)->where('department_id', $deptData->id)->get();
        $leaveHistory = Employee::select(
            'employees.employee_no',
            'employees.initials',
            'employees.name_denoted_by_initials',
            'employees.last_name',
            'employees.designation_id',
            'leaves.year',
            'leaves.casual',
            'leaves.sick',
            'leaves.medical',
            'leaves.nopay',
            'leaves.study'
        )
        ->leftJoin('leaves', function($join) {
            $join->on('employees.employee_no', '=', 'leaves.emp_no')
                 ->where('leaves.year', date('Y'));  // Filter leave data for the year
        })
        ->where('employees.department_id', $deptData->id) //5102 for CITS
        ->where('employees.employee_status_id', 110)
        ->get();
        // dd($leaveHistory);
        
        // MA data
        $login_MA_data = Employee::where('employee_no',auth()->user()->employee_no)->first();
        // dd($login_MA_data);

        $thisMonthCode = AnnualLeave::where('ma_forward_status', 0)
        ->where('dept_code', $deptData->id)
        ->max('month_code');

        // Finding the month
        $thisMonth = AnnualLeave::where('ma_forward_status', 0)->where('dept_code', $deptData->id)
        ->orderByDesc('month_code')
        ->select('month')
        ->first();
        // dd($thisMonthCode);
 
        // Check if the HOD has forwarded leave data
        $statusCount = AnnualLeaveSummary::where('dept_code', $deptData->id)->where('process_stage', 1)->count();

        $data = [
            "employee" => $employee,
            "leaveSummary" => $leaveSummary,
            "leaveHistory" => $leaveHistory,
            "login_MA_data" => $login_MA_data,
            "thisMonthCode" => $thisMonthCode,
            "thisMonth" => $thisMonth,
            "statusCount" => $statusCount
        ];

        return view('admin.leave.AnnualLeave.leave_index', $data);
    }


    public function AddLeaveData($id)
    {
        $empNo = $id;
        
        $leaveDetails = AnnualLeave::select('id','from_date', 'to_date', 'leave_count', 'leave_type', 'ma_remark')->where('empNo', $empNo)->where('ma_forward_status', 0)->get();
        $empData = Employee::where('employee_no',$empNo)->first();

        // Calculate Short leave count
        $short_leave_count = AnnualLeave::where('leave_type',3)
        ->where('empNo', $empNo)
        ->where('ma_forward_status', 0)->count();
        // dd($empData->department_id);

        // Finding the last forwarded month
        $lastMonth = AnnualLeave::whereIn('ma_forward_status', [1,2])->where('dept_code', $empData->department_id)
        ->orderByDesc('month_code')
        ->select('month')
        ->first();

        $data = [
            "leaveDetails" => $leaveDetails,
            "empData" => $empData,
            "short_leave_count" => $short_leave_count,
            "lastMonth" => $lastMonth
        ];

        return view('admin.leave.AnnualLeave.add_leave', $data);
    }

    // Delete record
    public function deleteRecord($id)
    {
        $recID = $id;

        // Find the record
        $leaveRecord = AnnualLeave::find($recID);

        // Check if record exists
        if ($leaveRecord) {
            // Delete the record
            $leaveRecord->delete();
        }
        return redirect()->back()->with('success', 'Record deleted successfully.');
    }

    // ********************** Annual Leave Summaries **********************************
    // Store leave Summary
    public function storeLeaveSummary(Request $request){
        // $year = Carbon::parse($request->fromDate)->format('Y');
        // $month = Carbon::parse($request->fromDate)->format('m');
        $dept_code = $request->dept_code;
        $faculty_code = $request->faculty_code;
        $processMonth = $request->processMonth;
        $monthCode = $request->monthCode;

        $isCreated =  AnnualLeaveSummary::create([
            'dept_code' => $dept_code,
            'faculty_code' => $faculty_code,
            'month_code' => $monthCode,
            'process_stage' => 1
        ]);

        // dd($isCreated);
        // Update in individual leave
        if($isCreated){
            AnnualLeave::where('month_code', $monthCode)->where('dept_code', $dept_code)
            ->where('month', $processMonth)
            ->update([
                'ma_forward_status' => 1,
                'summary_table_id' => $isCreated->id
            ]);
        }


        return back();
    }

    // HOD index
    public function HODViewEmployees()
    {
        // login HOD Dept data
        $deptData = Employee::join('departments', 'employees.department_id', '=', 'departments.id')
        ->where('employees.employee_no',auth()->user()->employee_no)
        ->select('departments.id', 'departments.faculty_code', 'employees.employee_no')->first();

        // forward details
        $forwardData = AnnualLeaveSummary::join('annual_leaves', 'annual_leave_summaries.id', '=', 'annual_leaves.summary_table_id')
        ->select('annual_leave_summaries.id as tableID')
        ->where('annual_leave_summaries.dept_code', $deptData->id)
        ->where('annual_leave_summaries.process_stage', 1)
        ->first();
        // dd($forwardData);

        // Login HOD| Staff data
        $leaveData = Employee::select(
            'employees.initials',
            'employees.employee_no',
            'employees.last_name',
            'employees.designation_id',
            DB::raw('COALESCE(SUM(CASE WHEN leave_type IN (1, 4) THEN leave_count ELSE 0 END), 0) as casual_leave'),
            DB::raw('COALESCE(SUM(CASE WHEN leave_type = 2 THEN leave_count ELSE 0 END), 0) as medical_leave'),
            DB::raw('COALESCE(SUM(CASE WHEN leave_type = 3 THEN leave_count ELSE 0 END), 0) as short_leave'),
            // DB::raw('COALESCE(SUM(leave_count), 0) as total_leave')
            DB::raw('SUM(CASE WHEN leave_type IN (1, 2, 4) THEN leave_count ELSE 0 END) as total_leave') // Exclude short leave (leave_type = 3)
        )
        ->leftJoin('annual_leaves', 'employees.employee_no', '=', 'annual_leaves.empNo')
        ->leftJoin('annual_leave_summaries', 'annual_leaves.summary_table_id', '=', 'annual_leave_summaries.id')
        ->where('employees.department_id', $deptData->id) // Filter by department
        ->where('employees.employee_status_id', 110)
        ->where(function ($query) {
            $query->where('annual_leaves.ma_forward_status', 1)
                  ->orWhereNull('annual_leaves.ma_forward_status'); // Include employees without leave data
        })
        ->where(function ($query) {
            $query->where('annual_leave_summaries.process_stage', 1)
                  ->orWhereNull('annual_leave_summaries.process_stage'); // Include employees without summary data
        })
        ->groupBy('employees.initials', 'employees.last_name', 'employees.employee_no', 'employees.designation_id')
        ->get();
    // dd($leaveData);

        $data = [
            "deptData" => $deptData,
            "leaveData" => $leaveData,
            "forwardData" => $forwardData
            
        ];
        return view('admin.leave.AnnualLeave.HOD_index', $data);
        
    }

    // HOD view leaves
    public function HODViewLeaves($id)
    {
        $empNo = $id;

        $leaveData = AnnualLeave::join('annual_leave_summaries', 'annual_leaves.summary_table_id', '=', 'annual_leave_summaries.id')
        ->where('annual_leave_summaries.process_stage', 1)
        ->where('annual_leaves.empNo', $empNo)
        ->select('annual_leaves.from_date', 'annual_leaves.to_date', 'annual_leaves.leave_count','annual_leaves.leave_type', 'annual_leaves.ma_remark')
        ->get();
        // dd($leaveData);

        $userData = Employee::where('employee_no', $empNo)->first();

        $data =[
            "leaveData"=> $leaveData,
            "userData"=> $userData
        ];
        return view('admin.leave.AnnualLeave.HOD_view', $data);
    }

    // HOD forward
    public function HODForward(Request $request)
    {
        $date = date('Y-m-d'); 
        AnnualLeaveSummary::where('id', $request->processTableID)->update([
            'process_stage' => 2,
            'head_empNo' => $request->login_HOD_ID,
            'head_status' => 1,
            'head_remark' => $request->hodRemark,
            'head_forward_date' => $date 
        ]);
        // dd($request->processTableID);
        return back();
    }

    // HOD | Reversal to MA
    public function HODRevers($id){
        $process_TableID = $id;
        $date = date('Y-m-d'); 

        $process_details = AnnualLeaveSummary::where('id', $process_TableID)->first();
        // dd($process_details->dept_code);

        $process_TableUpdate = AnnualLeaveSummary::where('id', $process_TableID)->where('dept_code', $process_details->dept_code)
        ->update([
            'process_stage' => 0,
            'head_empNo' => auth()->user()->employee_no,
            'head_status' => 2,
            'head_reversal_date' => $date
        ]);

        if($process_TableUpdate){
            AnnualLeave::where('summary_table_id', $process_TableID)->where('dept_code', $process_details->dept_code)
            ->update([
                'ma_forward_status' => 0
            ]);
        }
        return back();
    }

    // Executive Officer index | Department list
    public function executiveDept()
    {
        // login Executive Officer Dept data
        $deptData = Employee::join('departments', 'employees.department_id', '=', 'departments.id')
        ->where('employees.employee_no',auth()->user()->employee_no)
        ->select('departments.id', 'departments.faculty_code', 'employees.employee_no')->first();

        $max_month_code = AnnualLeaveSummary::max('month_code');
        $previous_month_code = $max_month_code - 1;
        // dd($previous_month_code);

        // department details
        $deptDetails = Department::leftjoin('annual_leave_summaries', 'departments.id', '=', 'annual_leave_summaries.dept_code')
        ->where('departments.faculty_code', $deptData->faculty_code)
        ->where(function ($query) use ($max_month_code) {
            $query->where('annual_leave_summaries.month_code', $max_month_code) // Filter by max or previous month
                ->orWhereNull('annual_leave_summaries.month_code'); // Include departments without leave data
        })
        ->select('departments.department_name', 
            'departments.id',
            'annual_leave_summaries.process_stage', 
            'annual_leave_summaries.executive_status', 
            'annual_leave_summaries.id as processID',
            'annual_leave_summaries.head_status'
        )->get();

        // Get saved month code
        $available_months = AnnualLeaveSummary::select('month_code')->distinct()->get();

        // dd($available_months);
        $data =[
            "deptDetails"=> $deptDetails,
            "available_months"=> $available_months
        ];

        return view('admin.leave.AnnualLeave.executive_Officer_Dept_index', $data);
    }

    // Executive Officer Officer index | Month Filter
    public function monthFilter(Request $request)
    {
        $month_code = $request->input('submittedMonths'); // Get selected month

        // Get available months
        $available_months = AnnualLeaveSummary::select('month_code')->distinct()->get();

        // login Executive Officer Dept data
        $deptData = Employee::join('departments', 'employees.department_id', '=', 'departments.id')
        ->where('employees.employee_no',auth()->user()->employee_no)
        ->select('departments.id', 'departments.faculty_code', 'employees.employee_no')->first();

        $deptDetails = Department::leftJoin('annual_leave_summaries', 'departments.id', '=', 'annual_leave_summaries.dept_code')
        ->where('departments.faculty_code', $deptData->faculty_code)
        ->where(function ($query) use ($month_code) {
            $query->where('annual_leave_summaries.month_code', $month_code) // Filter by selected month
                ->orWhereNull('annual_leave_summaries.month_code'); // Include departments without leave data
        })
        ->select(
            'departments.department_name',
            'departments.id',
            'annual_leave_summaries.process_stage',
            'annual_leave_summaries.executive_status',
            'annual_leave_summaries.id as processID',
            'annual_leave_summaries.head_status'
        )
        ->get();

        // if ($deptDetails->isEmpty()) {
        //     dd("No data found for month_code: " . $month_code);
        // }

        return view('admin.leave.AnnualLeave.executive_Officer_Dept_index', compact('deptDetails', 'available_months', 'month_code'));
    }

    // Executive Officer | Employee list
    public function executiveEmp($id, $pid)
    {
        $dept_id = $id;
        $process_TableID = $pid;
        // dd($process_TableID);

        $dept_Data = Department::where('id', $dept_id)->first();

        // login executive officer data
        $emp_Data = Employee::where('employee_no',auth()->user()->employee_no)->first();

        // forward details
        $forwardData = AnnualLeaveSummary::join('annual_leaves', 'annual_leave_summaries.id', '=', 'annual_leaves.summary_table_id')
        ->select('annual_leave_summaries.id as tableID')
        ->where('annual_leave_summaries.dept_code', $dept_id)
        ->where('annual_leave_summaries.process_stage', 2)
        ->first();

        // Selected Staff data view
        $leaveData = Employee::select(
            'employees.initials',
            'employees.employee_no',
            'employees.last_name',
            'employees.designation_id',
            DB::raw('COALESCE(SUM(CASE WHEN leave_type IN (1, 4) THEN leave_count ELSE 0 END), 0) as casual_leave'),
            DB::raw('COALESCE(SUM(CASE WHEN leave_type = 2 THEN leave_count ELSE 0 END), 0) as medical_leave'),
            DB::raw('COALESCE(SUM(CASE WHEN leave_type = 3 THEN leave_count ELSE 0 END), 0) as short_leave'),
            // DB::raw('COALESCE(SUM(leave_count), 0) as total_leave')
            DB::raw('SUM(CASE WHEN leave_type IN (1, 2, 4) THEN leave_count ELSE 0 END) as total_leave') // Exclude short leave (leave_type = 3)
        )
        ->leftJoin('annual_leaves', 'employees.employee_no', '=', 'annual_leaves.empNo')
        ->leftJoin('annual_leave_summaries', 'annual_leaves.summary_table_id', '=', 'annual_leave_summaries.id')
        ->where('employees.department_id', $dept_id) // Filter by department
        ->where('employees.employee_status_id', 110)
        ->where(function ($query) use ($process_TableID) {
            $query->where('annual_leave_summaries.id', $process_TableID)
                  ->orWhereNull('annual_leave_summaries.id'); // Include employees without summary
        })
        ->where(function ($query) {
            $query->where('annual_leaves.ma_forward_status', 1)
                  ->orWhereNull('annual_leaves.ma_forward_status'); // Include employees without leave data
        })
        ->where(function ($query) {
            $query->where('annual_leave_summaries.process_stage', 2)
                  ->orWhereNull('annual_leave_summaries.process_stage'); // Include employees without summary data
        })
        ->groupBy('employees.initials', 'employees.last_name', 'employees.employee_no', 'employees.designation_id')
        ->get();

        $data =[
            "leaveData"=> $leaveData,
            "dept_Data" => $dept_Data,
            "emp_Data" => $emp_Data,
            "forwardData" => $forwardData
        ];

        return view('admin.leave.AnnualLeave.executive_Officer_Emp_View', $data);
    }

    // Executive Officer | Leave details
    public function executiveLeave($id)
    {
        $empNo = $id;
        $leaveData = AnnualLeave::join('annual_leave_summaries', 'annual_leaves.summary_table_id', '=', 'annual_leave_summaries.id')
        ->where('annual_leave_summaries.process_stage', 2)
        ->where('annual_leaves.empNo', $empNo)
        ->select('annual_leaves.from_date', 'annual_leaves.to_date', 'annual_leaves.leave_count','annual_leaves.leave_type', 'annual_leaves.ma_remark', 'annual_leave_summaries.id as processID')
        ->get();
        // dd($leaveData->pluck('processID'));

        $userData = Employee::where('employee_no', $empNo)->first();

        $data =[
            "leaveData"=> $leaveData,
            "userData"=> $userData
        ];

        return view('admin.leave.AnnualLeave.executive_Officer_Leave_View', $data);
    }

    // Executive Forward
    public function executiveForward(Request $request)
    {
        $date = date('Y-m-d'); 
        AnnualLeaveSummary::where('id', $request->processTableID)->update([
            'process_stage' => 3,
            'executive_empNo' => $request->login_executive_ID,
            'executive_status' => 1,
            'executive_remark' => $request->executiveRemark,
            'executive_forward_date' => $date 
        ]);
        return back();
    }

    // Duty Clerk index | Faculty list
    public function clerkFaculty()
    {
        // All Faculty data
        // $facultyList = Faculty::all();

        $facultyList = Employee::join('faculties', 'employees.faculty_id', '=', 'faculties.id')
        ->select('faculties.id', 'faculties.faculty_name')
        ->distinct()
        ->get();


        $data = [
            "facultyList" => $facultyList
        ];

        return view('admin.leave.AnnualLeave.clerk_Faculty_index', $data);
    }

    // Duty Clerk | Department list
    public function clerkDepartment($id)
    {
        $faculty_id = $id;

        // Faculty data
        $faculty_data = Faculty::where('id', $faculty_id)->first();

        $max_month_code = AnnualLeaveSummary::max('month_code');
        $previous_month_code = $max_month_code - 1;
        // dd($previous_month_code);

        $deptDetails = Department::leftjoin('annual_leave_summaries', 'departments.id', '=', 'annual_leave_summaries.dept_code')
        ->where('departments.faculty_code', $faculty_id)
        ->where(function ($query) use ($max_month_code) {
            $query->where('annual_leave_summaries.month_code', $max_month_code) // Filter by max or previous month
                ->orWhereNull('annual_leave_summaries.month_code'); // Include departments without leave data
        })
        ->select('departments.department_name', 
            'departments.id', 
            'annual_leave_summaries.process_stage', 
            'annual_leave_summaries.id as processID',
            'annual_leave_summaries.leave_clerk_status',
            'annual_leave_summaries.dr_status',
            'annual_leave_summaries.head_status'
        )->get();

        // Get available months
        $available_months = AnnualLeaveSummary::select('month_code')->distinct()->get();

        $data = [
            "deptDetails" => $deptDetails,
            "faculty_data" => $faculty_data,
            "available_months" => $available_months
        ];

        return view('admin.leave.AnnualLeave.clerk_dept_view', $data);
    }

    // Duty Clerk | Month Filter
    public function monthFilter_clerk(Request $request, $id)
    {
        $month_code = $request->input('submittedMonths'); // Get selected month

        $faculty_id = $id;

        // Faculty data
        $faculty_data = Faculty::where('id', $faculty_id)->first();

        // Get available months
        $available_months = AnnualLeaveSummary::select('month_code')->distinct()->get();

        $deptDetails = Department::leftJoin('annual_leave_summaries', 'departments.id', '=', 'annual_leave_summaries.dept_code')
        ->where('departments.faculty_code', $faculty_id)
        ->where(function ($query) use ($month_code) {
            $query->where('annual_leave_summaries.month_code', $month_code) // Filter by selected month
                ->orWhereNull('annual_leave_summaries.month_code'); // Include departments without leave data
        })
        ->select(
            'departments.department_name',
            'departments.id',
            'annual_leave_summaries.process_stage',
            'annual_leave_summaries.executive_status',
            'annual_leave_summaries.id as processID',
            'annual_leave_summaries.leave_clerk_status',
            'annual_leave_summaries.dr_status',
            'annual_leave_summaries.head_status'
        )
        ->get();

        $data =[
            "deptDetails" => $deptDetails,
            "available_months" => $available_months,
            "faculty_data" => $faculty_data
        ];

        return view('admin.leave.AnnualLeave.clerk_dept_view', $data);
    }

    // Duty Clerk | Employees list
    public function clerkEmployee($id, $pid)
    {
        $dept_id = $id;
        $process_TableID = $pid;

        $dept_Data = Department::where('id', $dept_id)->first();

        // login duty clerk data
        $emp_Data = Employee::where('employee_no',auth()->user()->employee_no)->first();

        // forward details
        $forwardData = AnnualLeaveSummary::join('annual_leaves', 'annual_leave_summaries.id', '=', 'annual_leaves.summary_table_id')
        ->select('annual_leave_summaries.id as tableID', 'annual_leave_summaries.dr_remark', 'annual_leave_summaries.dr_status')
        ->where('annual_leave_summaries.dept_code', $dept_id)
        ->where('annual_leave_summaries.process_stage', 3)
        ->where('annual_leave_summaries.id', $process_TableID)
        ->first();

        // Selected Staff data view
        $leaveData = Employee::select(
            'employees.initials',
            'employees.employee_no',
            'employees.last_name',
            'employees.designation_id',
            DB::raw('COALESCE(SUM(CASE WHEN leave_type IN (1, 4) THEN leave_count ELSE 0 END), 0) as casual_leave'),
            DB::raw('COALESCE(SUM(CASE WHEN leave_type = 2 THEN leave_count ELSE 0 END), 0) as medical_leave'),
            DB::raw('COALESCE(SUM(CASE WHEN leave_type = 3 THEN leave_count ELSE 0 END), 0) as short_leave'),
            // DB::raw('COALESCE(SUM(leave_count), 0) as total_leave')
            DB::raw('SUM(CASE WHEN leave_type IN (1, 2, 4) THEN leave_count ELSE 0 END) as total_leave') // Exclude short leave (leave_type = 3)
        )
        ->leftJoin('annual_leaves', 'employees.employee_no', '=', 'annual_leaves.empNo')
        ->leftJoin('annual_leave_summaries', 'annual_leaves.summary_table_id', '=', 'annual_leave_summaries.id')
        ->where('employees.department_id', $dept_id) // Filter by department
        ->where('employees.employee_status_id', 110)
        ->where(function ($query) use ($process_TableID) {
            $query->where('annual_leave_summaries.id', $process_TableID)
                  ->orWhereNull('annual_leave_summaries.id'); // Include employees without summary
        })
        ->where(function ($query) {
            $query->whereIn('annual_leaves.ma_forward_status', [1,2])
                  ->orWhereNull('annual_leaves.ma_forward_status'); // Include employees without leave data
        })
        ->where(function ($query) {
            $query->where('annual_leave_summaries.process_stage', 3)
                  ->orWhereNull('annual_leave_summaries.process_stage'); // Include employees without summary data
        })
        ->groupBy('employees.initials', 'employees.last_name', 'employees.employee_no', 'employees.designation_id')
        ->get();

        $data = [
            "leaveData" => $leaveData,
            "dept_Data" => $dept_Data,
            "emp_Data" => $emp_Data,
            "forwardData" => $forwardData
        ];

        return view('admin.leave.AnnualLeave.clerk_Emp_view', $data);
    }

    // Duty Clerk | Leave Details
    public function clerkLeave($id)
    {
        $empNo = $id;

        $userData = Employee::where('employee_no', $empNo)->first();

        $leaveData = AnnualLeave::join('annual_leave_summaries', 'annual_leaves.summary_table_id', '=', 'annual_leave_summaries.id')
        ->where('annual_leave_summaries.process_stage', 3)
        ->where('annual_leaves.empNo', $empNo)
        ->select('annual_leaves.from_date', 
            'annual_leaves.to_date', 
            'annual_leaves.leave_count',
            'annual_leaves.leave_type', 
            'annual_leaves.ma_remark', 
            'annual_leaves.id', 
            'annual_leaves.dc_remark', 
            'annual_leaves.ma_forward_status',
            'annual_leave_summaries.id as processID'
        )->get();
        // dd($leaveData);

        $leaveSummary =  AnnualLeave::join('annual_leave_summaries', 'annual_leaves.summary_table_id', '=', 'annual_leave_summaries.id')
        ->where('annual_leave_summaries.process_stage', 3)
        ->where('annual_leave_summaries.dept_code', $userData->department_id)
        // ->where('annual_leaves.empNo', $empNo)
        ->select('annual_leaves.summary_table_id', 'annual_leave_summaries.month_code')
        ->first();


        $data = [
            "leaveData" => $leaveData,
            "userData" => $userData,
            "leaveSummary" => $leaveSummary
        ];
        return view('admin.leave.AnnualLeave.clerk_Leave_view', $data);
    }

    // Duty Clerk | Leave Data Update
    public function clerkLeaveUpdate(Request $request, $id)
    {
        $leave = AnnualLeave::find($id);

        if (!$leave) {
            return response()->json(['error' => 'Leave not found'], 404);
        }

        // Update fields
        $leave->from_date = $request->from_date;
        $leave->leave_type = $request->leave_type;
        if($request->leave_type == 4){
            $leave->leave_count = 0.5;
        }elseif($request->leave_type == 5){
            $leave->leave_count = 0;
        }else{
            $leave->leave_count = 1;
        }
        $leave->dc_remark = $request->dc_remark;
        $leave->dc_empNo = auth()->user()->employee_no;
        $leave->save();

        return response()->json(['success' => 'Leave details updated successfully']);
    }

    // Duty Clerk | Add new leave record
    public function storeLeave_dutyClerk(Request $request){
        $year = Carbon::parse($request->fromDate)->format('Y');
        $month = Carbon::parse($request->fromDate)->format('m');
        $dept_code = $request->dept_code;
        $faculty_code = $request->faculty_code;
        $leave_type = $request->leaveType;

        if ($leave_type == 4){
            $leaveCount = 0.5;
        } else {
            $leaveCount = 1;
        }

        AnnualLeave::create([
            'empNo' => $request->empNo,
            'designation_id' =>$request->designation_id,
            'from_date' => $request->fromDate,
            'to_date' => $request->fromDate,
            'leave_count' => $leaveCount,
            'leave_type' => $request->leaveType,
            'year' => $year,
            'month' => $month,
            'month_code' => $year.$month,
            'dept_code' => $dept_code,
            'faculty_code' => $faculty_code,
            'ma_forward_status' => 2,
            'submitted_status' => 1,
            'summary_table_id' => $request->summary_table_id,
            'dc_remark' => $request->dutyClerk_remark,
            'dc_empNo' => auth()->user()->employee_no
        ]);
        return back();
    }

    // Duty Clerk | Forward
    public function dutyClerkForward(Request $request)
    {
        $date = date('Y-m-d'); 
        AnnualLeaveSummary::where('id', $request->processTableID)->update([
            'process_stage' => 4,
            'leave_clerk_empNo' => $request->login_clerk_ID,
            'leave_clerk_status' => 1,
            'leave_clerk_remark' => $request->dutyClerkRemark,
            'leave_clerk_forward_date' => $date,
            'dr_status' => 0
        ]);
        return back();
    }


    // DR index | Faculty List
    public function DRFaculty()
    {
        
        $facultyList = Employee::join('faculties', 'employees.faculty_id', '=', 'faculties.id')
        ->select('faculties.id', 'faculties.faculty_name')
        ->distinct()
        ->get();


        $data = [
            "facultyList" => $facultyList
        ];

        return view('admin.leave.AnnualLeave.DR_Faculty_index', $data);
    }

    // DR | Department list
    public function DRDepartment($id)
    {
        $faculty_id = $id;

        // Faculty data
        $faculty_data = Faculty::where('id', $faculty_id)->first();

        $max_month_code = AnnualLeaveSummary::max('month_code');
        $previous_month_code = $max_month_code - 1;

        $deptDetails = Department::leftjoin('annual_leave_summaries', 'departments.id', '=', 'annual_leave_summaries.dept_code')
        ->where('departments.faculty_code', $faculty_id)
        ->where(function ($query) use ($max_month_code) {
            $query->where('annual_leave_summaries.month_code', $max_month_code) // Filter by max or previous month
                ->orWhereNull('annual_leave_summaries.month_code'); // Include departments without leave data
        })
        ->select('departments.department_name', 
            'departments.id', 
            'annual_leave_summaries.process_stage', 
            'annual_leave_summaries.id as processID',
            'annual_leave_summaries.dr_status',
            'annual_leave_summaries.head_status'
        )->get();

        // Get available months
        $available_months = AnnualLeaveSummary::select('month_code')->distinct()->get();

        $data = [
            "deptDetails" => $deptDetails,
            "faculty_data" => $faculty_data,
            "available_months" => $available_months
        ];

        return view('admin.leave.AnnualLeave.DR_dept_view', $data);
    }

    // DR | Month Filter
    public function monthFilter_DR(Request $request, $id)
    {
        $month_code = $request->input('submittedMonths'); // Get selected month

        $faculty_id = $id;

        // Faculty data
        $faculty_data = Faculty::where('id', $faculty_id)->first();

        // Get available months
        $available_months = AnnualLeaveSummary::select('month_code')->distinct()->get();

        $deptDetails = Department::leftJoin('annual_leave_summaries', 'departments.id', '=', 'annual_leave_summaries.dept_code')
        ->where('departments.faculty_code', $faculty_id)
        ->where(function ($query) use ($month_code) {
            $query->where('annual_leave_summaries.month_code', $month_code) // Filter by selected month
                ->orWhereNull('annual_leave_summaries.month_code'); // Include departments without leave data
        })
        ->select(
            'departments.department_name',
            'departments.id',
            'annual_leave_summaries.process_stage',
            'annual_leave_summaries.executive_status',
            'annual_leave_summaries.id as processID',
            'annual_leave_summaries.dr_status',
            'annual_leave_summaries.head_status'
        )->get();

        $data =[
            "deptDetails" => $deptDetails,
            "available_months" => $available_months,
            "faculty_data" => $faculty_data
        ];

        return view('admin.leave.AnnualLeave.DR_dept_view', $data);
    }

    // DR | Employees list
    public function DREmployee($id, $pid)
    {
        $dept_id = $id;
        $process_TableID = $pid;

        $dept_Data = Department::where('id', $dept_id)->first();

        // login Deputy Registrar data
        $emp_Data = Employee::where('employee_no',auth()->user()->employee_no)->first();

        // forward details
        $forwardData = AnnualLeaveSummary::join('annual_leaves', 'annual_leave_summaries.id', '=', 'annual_leaves.summary_table_id')
        ->select('annual_leave_summaries.id as tableID', 'annual_leaves.year')
        ->where('annual_leave_summaries.dept_code', $dept_id)
        ->where('annual_leave_summaries.process_stage', 4)
        ->first();

        // Selected Staff data view
        $leaveData = Employee::select(
            'employees.initials',
            'employees.employee_no',
            'employees.last_name',
            'employees.designation_id',
            'annual_leaves.year',
            'annual_leave_summaries.id as processID',
            DB::raw('COALESCE(SUM(CASE WHEN leave_type IN (1, 4) THEN leave_count ELSE 0 END), 0) as casual_leave'),
            DB::raw('COALESCE(SUM(CASE WHEN leave_type = 2 THEN leave_count ELSE 0 END), 0) as medical_leave'),
            DB::raw('COALESCE(SUM(CASE WHEN leave_type = 3 THEN leave_count ELSE 0 END), 0) as short_leave'),
            // DB::raw('COALESCE(SUM(leave_count), 0) as total_leave')
            DB::raw('SUM(CASE WHEN leave_type IN (1, 2, 4) THEN leave_count ELSE 0 END) as total_leave') // Exclude short leave (leave_type = 3)
        )
        ->leftJoin('annual_leaves', 'employees.employee_no', '=', 'annual_leaves.empNo')
        ->leftJoin('annual_leave_summaries', 'annual_leaves.summary_table_id', '=', 'annual_leave_summaries.id')
        ->where('employees.department_id', $dept_id) // Filter by department
        ->where('employees.employee_status_id', 110)
        ->where(function ($query) use ($process_TableID) {
            $query->where('annual_leave_summaries.id', $process_TableID)
                  ->orWhereNull('annual_leave_summaries.id'); // Include employees without summary
        })
        ->where(function ($query) {
            $query->whereIn('annual_leaves.ma_forward_status', [1,2])
                  ->orWhereNull('annual_leaves.ma_forward_status'); // Include employees without leave data
        })
        ->where(function ($query) {
            $query->where('annual_leave_summaries.process_stage', 4)
                  ->orWhereNull('annual_leave_summaries.process_stage'); // Include employees without summary data
        })
        ->groupBy('employees.initials', 'employees.last_name', 'employees.employee_no', 'employees.designation_id', 'annual_leaves.year', 'annual_leave_summaries.id')
        ->get();

        $data = [
            "leaveData" => $leaveData,
            "dept_Data" => $dept_Data,
            "emp_Data" => $emp_Data,
            "forwardData" => $forwardData

        ];

        return view('admin.leave.AnnualLeave.DR_Emp_view', $data);
    }

    // DR | Leave Details
    public function DRLeave($id)
    {
        $empNo = $id;

        $userData = Employee::where('employee_no', $empNo)->first();

        $leaveData = AnnualLeave::join('annual_leave_summaries', 'annual_leaves.summary_table_id', '=', 'annual_leave_summaries.id')
        ->where('annual_leave_summaries.process_stage', 4)
        ->where('annual_leaves.empNo', $empNo)
        ->select('annual_leaves.from_date', 
            'annual_leaves.to_date', 
            'annual_leaves.leave_count',
            'annual_leaves.leave_type', 
            'annual_leaves.ma_remark', 
            'annual_leaves.dc_remark', 
            'annual_leaves.dc_empNo', 
            'annual_leaves.ma_forward_status',
            'annual_leave_summaries.id as processID'
        )->get();

        $data = [
            "leaveData" => $leaveData,
            "userData" => $userData
        ];
        return view('admin.leave.AnnualLeave.DR_Leave_view', $data);
    }

    // Deputy Registrar | Approval
    public function DRApproval(Request $request)
    {
        $date = date('Y-m-d'); 

        // Update the process stage for each process table ID
        AnnualLeaveSummary::where('id', $request->processTableID)->update([
            'process_stage' => 5,
            'dr_empNo' => $request->login_DR_ID,
            'dr_status' => 1,
            'dr_remark' => $request->DRRemark,
            'dr_finalize_date' => $date 
        ]);
        
        // Insert or update multiple records for Leave
        foreach ($request->empNo as $index => $empNo) {
        // Check if a record already exists
        $existingLeave = Leave::where('emp_no', $empNo)
            ->where('year', $request->effectiveYear)
            ->first();

        if ($existingLeave) {
            // Add new values to existing ones
            $existingLeave->casual += $request->casual_leave[$index];
            $existingLeave->medical += $request->medical_leave[$index];
            $existingLeave->save(); // Update record
        } else {
            // Create a new record if it doesn't exist
            Leave::create([
                'emp_no' => $empNo,
                'year' => $request->effectiveYear,
                'casual' => $request->casual_leave[$index],
                'medical' => $request->medical_leave[$index]
            ]);
        }
    }

        return back();
    }

    // Deputy Registrar | Reversal to Duty Clerk
    public function DRReversal(Request $request)
    {
        $date = date('Y-m-d'); 
        
        AnnualLeaveSummary::where('id', $request->processTableID)->update([
            'process_stage' => 3,
            'dr_empNo' => $request->login_DR_ID,
            'dr_status' => 2,
            'dr_remark' => $request->reversal_remark,
            'dr_finalize_date' => $date
        ]);
        return back();
    }
}

