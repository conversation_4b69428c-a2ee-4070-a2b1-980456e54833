<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\City;
use App\Models\District;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class CityController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator');
    }

    public function cityIndex()
    {
        Log::info('CityController -> city index started');
        $cities = DB::table('cities')
            ->join('districts', 'districts.id', '=', 'cities.district_id')
            ->join('provinces', 'provinces.id', '=', 'districts.province_id')
            ->select('cities.id', 'cities.name_en as city_name', 'districts.name_en as district_name', 'provinces.name_en as province_name')
            ->whereNull('cities.deleted_at')
            ->orderBy('city_name')
            ->get();

        $trashCities = DB::table('cities')
            ->join('districts', 'districts.id', '=', 'cities.district_id')
            ->join('provinces', 'provinces.id', '=', 'districts.province_id')
            ->select('cities.id', 'cities.name_en as city_name', 'districts.name_en as district_name', 'provinces.name_en as province_name')
            ->where('cities.deleted_at', '!=', NULL)
            ->orderBy('city_name')
            ->get();

        return view('admin.setups.city.index', compact('cities', 'trashCities'));

        Log::notice('CityController -> city Count - ' . $cities->count());
        Log::info('CityController -> city index ended');
    }

    public function cityAdd()
    {
        Log::info('CityController -> city add started');

        $discricts = District::all();

        return view('admin.setups.city.add', compact('discricts'));

        Log::info('CityController -> city add ended');
    }

    public function cityStore(Request $request)
    {
        Log::info('CityController -> department store started');

        $validatedData = $request->validate([
            'name_en' => 'required|unique:cities,name_en',
            'district_id' => 'required',
        ], [
            'district_id.required' => 'select the relavent district',
            'name_en.required' => 'you must enter the city name',
            'name_en.unique' => 'city name already in the system'
        ]);

        $data = new City();
        $data->name_en = ucfirst($request->name_en);
        $data->district_id = $request->district_id;
        $data->created_at = Carbon::now();
        $data->save();

        Log::notice('CityController -> Created city id - ' . $data->id . ' created by ' . auth()->user()->id);
        Log::info('CityController -> city create ended');

        $notification = array(
            'message' => 'New City Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('city.index')->with($notification);
    }

    public function cityEdit($id)
    {
        Log::info('CityController -> city edit started');
        $editData = City::find($id);
        $discricts = District::all();
        return view('admin.setups.city.edit', compact('editData', 'discricts'));
        Log::notice('CityController -> edit city id - ' . $editData->id . ' edited by ' . auth()->user()->id);
        Log::info('CityController -> city edit ended');
    }

    public function cityUpdate(Request $request, $id)
    {

        Log::info('CityController -> city update started');
        $validatedData = $request->validate([
            'name_en' => ['required', Rule::unique('cities')->ignore($id)],
            'district_id' => 'required',
        ]);


        $data = City::find($id);
        $data->name_en = ucfirst($request->name_en);
        $data->district_id = $request->district_id;
        $data->updated_at = Carbon::now();
        $data->save();

        Log::notice('CityController -> update city id - ' . $data->id . ' updated by ' . auth()->user()->id);
        Log::info('CityController -> city update ended');

        $notification = array(
            'message' => 'City data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('city.index')->with($notification);
    }

    public function citySoftdelete($id)
    {

        Log::info('CityController -> city soft delete started');
        $city = City::find($id);
        $city->delete();

        $notification = array(
            'message' => 'City Deleted Successfully',
            'alert-type' => 'warning'
        );

        return redirect()->route('city.index')->with($notification);

        Log::notice('CityController -> soft delete city id - ' . $city->id . ' deleted by ' . auth()->user()->id);
        Log::info('CityController -> City soft delete ended');
    }

    public function cityRestore($id)
    {

        Log::info('CityController -> city restore started');

        $city = City::withTrashed()->find($id);
        $city->restore();

        $notification = array(
            'message' => 'City Restore Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('city.index')->with($notification);

        Log::notice('CityController -> restore city id - ' . $city->id . ' deleted by ' . auth()->user()->id);
        Log::info('CityController -> City restore ended');
    }

    public function cityDelete($id)
    {

        Log::info('CityController -> city delete started');

        $city = City::onlyTrashed()->find($id);
        $city->forceDelete();

        $notification = array(
            'message' => 'City Permenetly Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('city.index')->with($notification);

        Log::emergency('CityController -> delete city id - ' . $city->id . ' deleted by ' . auth()->user()->id);
        Log::info('CityController -> City delete ended');
    }
}
