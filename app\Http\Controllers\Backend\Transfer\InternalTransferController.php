<?php

namespace App\Http\Controllers\Backend\Transfer;

use App\Http\Controllers\Controller;
use App\Models\Department;
use App\Models\Employee;
use App\Models\InternalTransfer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class InternalTransferController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function indexInternal(Request $request)
    {
        if (isset($request->employee_no)) {

            Log::info('InternalTransferController -> internal transfer data getAll started');

            $mainBranch = Auth()->user()->main_branch_id;

            $search_emp_no = $request->employee_no;


            //admin user data collection
            if ($mainBranch == 51) {

                $emp_inter_trans = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                    ->select('internal_transfers.*', 'departments.department_name')
                    ->where('internal_transfers.emp_no', $search_emp_no)
                    ->where('internal_transfers.emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('internal_transfers.transfer_date')
                    ->get();

                $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                    ->where('employees.employee_no', $search_emp_no)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->get();

                $row_count = $empfetchDatas->count();
            }
            //academic devision data collection
            elseif ($mainBranch == 52) {

                $emp_inter_trans = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                    ->select('internal_transfers.*', 'departments.department_name')
                    ->where('internal_transfers.emp_no', $search_emp_no)
                    ->where('internal_transfers.emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('internal_transfers.transfer_date')
                    ->get();

                if (Auth()->user()->hasRole(['cc','est-head'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                } elseif (Auth()->user()->hasRole(['sc'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();
            }
            //non academic division data collection
            elseif ($mainBranch == 53) {

                $emp_inter_trans = InternalTransfer::join('departments', 'departments.id', '=', 'internal_transfers.dep_id')
                    ->select('internal_transfers.*', 'departments.department_name')
                    ->where('internal_transfers.emp_no', $search_emp_no)
                    ->where('internal_transfers.emp_no', '!=', auth()->user()->employee_no)
                    ->orderBy('internal_transfers.transfer_date')
                    ->get();


                if (Auth()->user()->hasRole(['cc','est-head'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                } elseif (Auth()->user()->hasRole(['sc'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();
            }

            /*********************************************************** */
            if ($row_count > 0) {
                foreach ($empfetchDatas as $empData) {
                    $emp_no = $empData->employee_no;
                    $emp_name = $empData->titleName . " " . $empData->initials . " " . $empData->last_name;
                    $designation_name = $empData->designation_name . " " . $empData->gradeName;
                    $department_name = $empData->department_name;
                }
            } else {
                $emp_no = '';
                $emp_name = '';
                $designation_name = '';
                $department_name = '';
                $emp_inter_trans = array();

                $notification = array(
                    'message' => 'employee number not found or employee profile lock',
                    'alert-type' => 'error'
                );

                return redirect()->back()->with($notification);
            }



            $pass_employee_no = $request->employee_no;
        } else {
            $pass_employee_no = '';
            $emp_no = '';
            $emp_name = '';
            $designation_name = '';
            $department_name = '';
            $emp_inter_trans = array();
        }

        $dep = DB::table('departments')->where('deleted_at', '=', NULL)->orderBy('department_name')->get();
        Log::info('InternalTransferController -> Internal trasfer data getAll ended');

        return view("admin.transfer.internal_transfer_history", compact('pass_employee_no', 'emp_no', 'emp_name', 'designation_name', 'department_name', 'emp_inter_trans', 'dep'));
    }


    public function storeInternal(Request $request)
    {
        Log::info('InternalTransferController -> old internal transfer data sotre started');

        if (!isset($request->emp_no)) {
            $notification = array(
                'message' => 'Please Search Employee Before Submit',
                'alert-type' => 'error'
            );

            return redirect()->route('transfer.internal.history.index')->with($notification);
        }

        // $request->validate(
        //     ['empNo' => 'required'],
        //     ['empNo.required' => 'Please search employee before the enter old internal transfer data']
        // );


        if ($request->effDate != null) {

            for ($i = 0; $i < count($request->effDate); $i++) {

                $interTransferHistory = new InternalTransfer();
                $interTransferHistory->emp_no = $request->emp_no;
                $interTransferHistory->transfer_date = date("Y-m-d", strtotime($request->effDate[$i]));
                $interTransferHistory->dep_id = $request->depId[$i];
                $interTransferHistory->descriptions = $request->descrip[$i];
                $interTransferHistory->user_id = auth()->user()->employee_no;
                $interTransferHistory->save();
            }

            Log::notice('InternalTransferController -> Created employee old internal transfer data employee number - ' . $request->emp_no . ' created by ' . auth()->user()->employee_no);
            Log::info('InternalTransferController -> old internal transfer data store ended');

            $notification = array(
                'message' => 'Old Internal Transfer data Inserted Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('transfer.internal.history.index', ['employee_no' => $request->emp_no])->with($notification);
        } else {

            $notification = array(
                'message' => 'Old Internal Transfer data Inserted Unsuccessfully',
                'alert-type' => 'error'
            );

            return redirect()->route('transfer.internal.history.index', ['employee_no' => $request->emp_no])->with($notification);
        }
    }

    public function editInternal($id)
    {

        Log::info('InternalTransferController -> old internal transfer data edit started');

        $departments = Department::orderBy('department_name')->get();
        $editData = InternalTransfer::find($id);

        Log::notice('InternalTransferController -> edit old internal transfer data id - ' . $editData->id . ' edited by ' . auth()->user()->employee_no);
        Log::info('InternalTransferController -> old promotion edit ended');

        return view('admin.transfer.internal_transfer_edit', compact('editData', 'departments'));
    }

    public function updateInternal(Request $request, $id)
    {

        Log::info('InternalTransferController -> old internal transfer data update started');

        $validatedData = $request->validate([
            'transfer_date' => 'required|date',
            'dep_id' => 'required',

        ], [
            'transfer_date.required' => 'internal transfer effective date required',
            'dep_id.required' => 'select the relevent department'
        ]);


        $internalTransfer = InternalTransfer::find($id);
        $internalTransfer->emp_no = $request->emp_no;
        $internalTransfer->dep_id = $request->dep_id;
        $internalTransfer->transfer_date = date("Y-m-d", strtotime($request->transfer_date));
        $internalTransfer->descriptions = $request->descriptions;
        $internalTransfer->updated_user_id = auth()->user()->employee_no;
        $internalTransfer->save();

        Log::warning('InternalTransferController -> update old internal transfer data employee number - ' . $internalTransfer->emp_no . ' updated by ' . auth()->user()->employee_no);
        Log::info('InternalTransferController -> old internal transfer data update ended');

        $notification = array(
            'message' => 'Internal Transfer data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('transfer.internal.history.index', ['employee_no' => $internalTransfer->emp_no])->with($notification);
    }

    public function deleteInternal($id)
    {

        Log::info('InternalTransferController -> internal transfer data delete started');

        $internalTransfer = InternalTransfer::find($id);
        $internalTransfer->delete();

        Log::emergency('InternalTransferController -> delete internal transfer data employee number - ' . $internalTransfer->emp_no . ' deleted by ' . auth()->user()->employee_no);
        Log::info('InternalTransferController -> internal transfer data delete ended');


        $notification = array(
            'message' => 'Internal Transfer data Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('transfer.internal.history.index', ['employee_no' => $internalTransfer->emp_no])->with($notification);
    }
}
