<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('employee_change_histories', function (Blueprint $table) {
            $table->integer('approvability')->after('updated_user_id')->default(0);
            $table->integer('approved_status')->after('approvability')->default(0);
            $table->integer('approved_user_id')->after('approved_status')->nullable();
            $table->date('approved_date')->after('approved_user_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('employee_change_histories', function (Blueprint $table) {
            $table->dropColumn('approvability');
            $table->dropColumn('approved_status');
            $table->dropColumn('approved_user_id');
            $table->dropColumn('approved_date');
        });
    }
};
