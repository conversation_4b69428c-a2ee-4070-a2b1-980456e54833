<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator');
    }

    public function roleIndex()
    {

        $roles =  Role::whereNotIn('name', ['super-admin'])->get();
        return view('admin.role.index', compact('roles'));

    }

    public function roleAdd()
    {

        return view('admin.role.add');

    }

    public function roleStore(Request $request)
    {

        $validatedData = $request->validate([
            'name' => 'required|unique:roles,name|min:3',
        ], [
            'name.required' => 'Enter Valid Role Name',
            'name.min' => 'Name Should be minimum 3 character'
        ]);

        $data = new Role();
        $data->name = $request->name;
        $data->save();

        $notification = array(
            'message' => 'New Role Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('role.index')->with($notification);
    }

    public function roleEdit($id)
    {

        $role = Role::find($id);
        $premissions = Permission::all();
        return view('admin.role.edit', compact('role','premissions'));

    }

    public function roleUpdate(Request $request, $id)
    {

        $validatedData = $request->validate([
            'name' => 'required|min:3',
        ], [
            'name.required' => 'Enter Valid Role Name',
            'name.min' => 'Name Should be minimum 3 character'
        ]);


        $data = Role::find($id);
        $data->name = $request->name;
        $data->save();

        $notification = array(
            'message' => 'Role data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('role.index')->with($notification);
    }

    public function roleDelete($id)
    {

        $role = Role::find($id);
        $role->delete();

        $notification = array(
            'message' => 'Role Permenetly Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('role.index')->with($notification);

    }


    public function permissionRoleUpdate(Request $request,Role $role)
    {
        $validatedData = $request->validate([
            'permission' => 'required',
        ], [
            'permission.required' => 'select valid permission',
        ]);

        if($role->hasPermissionTo($request->permission)){

            $notification = array(
                'message' => 'New Role Permission already Exist',
                'alert-type' => 'info'
            );
            return redirect()->route('role.edit', ['id' => $role])->with($notification);
        }

        $role->givePermissionTo($request->permission);


        $notification = array(
            'message' => 'New Role Permission Added Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('role.edit', ['id' => $role])->with($notification);
    }

    public function revokePermission(Role $role, Permission $permission)
    {
        if($role->hasPermissionTo($permission)){
            $role->revokePermissionTo($permission);

            $notification = array(
                'message' => 'Role Permission revoked',
                'alert-type' => 'error'
            );

            return redirect()->route('role.edit', ['id' => $role])->with($notification);

        }
        $notification = array(
            'message' => 'Role Permission not exists',
            'alert-type' => 'info'
        );

        return redirect()->route('role.edit', ['id' => $role])->with($notification);
    }
}
