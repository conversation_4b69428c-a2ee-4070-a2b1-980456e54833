<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\CarderDepartmentDesignation;
use App\Models\CarderDesignation;
use App\Models\Department;
use Illuminate\Http\Request;

class CarderDepartmentController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|stat');
    }

    public function CarderDepartmentAssignIndex(){

        $data = Department::all();
        return view('admin.setups.carder_department.index', compact('data'));

    }

    public function CarderDepartmentAssignAdd($id){

        $designations = CarderDesignation::where('active_status',1)->get();
        $department = Department::where('id',$id)->first();
        return view('admin.setups.carder_department.add', compact('designations','department'));
    }

    public function CarderDepartmentAssignStore(Request $request){

        $request->validate([
            'designation_id' => 'required',
            'approved_carder_count' => 'required',
        ]);

        $data = new CarderDepartmentDesignation();
        $data->department_id = $request->department_id;
        $data->designation_id = $request->designation_id;
        $data->approved_carder_count = $request->approved_carder_count;
        $data->save();

        $notification = array(
            'message' => 'New Carder Designation Assign Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('carder.department.assign.index')->with($notification);

    }

    public function CarderDepartmentAssignShow($id){

        $designations = CarderDesignation::where('active_status',1)->get();
        $department = Department::where('id',$id)->first();
        $carder_designation = CarderDepartmentDesignation::join('carder_designations','carder_department_designations.designation_id','=','carder_designations.id')->where('department_id',$id)->select('carder_department_designations.id','carder_department_designations.approved_carder_count','carder_designations.name')->get();

        return view('admin.setups.carder_department.show', compact('designations','department','carder_designation'));
    }

    public function CarderDepartmentAssignEdit($id){

        $data = CarderDepartmentDesignation::find($id);
        $designations = CarderDesignation::where('active_status',1)->get();
        return view('admin.setups.carder_department.edit', compact('data','designations'));
    }

    public function CarderDepartmentAssignUpdate(Request $request,$id){

        $request->validate([
            'designation_id' => 'required',
            'approved_carder_count' => 'required',
        ]);

        $data = CarderDepartmentDesignation::find($id);
        $data->designation_id = $request->designation_id;
        $data->approved_carder_count = $request->approved_carder_count;
        $data->save();

        $notification = array(
            'message' => 'Carder Designation Assign Updated Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('carder.department.assign.show', ['id' => $data->department_id])->with($notification);

    }

    public function CarderDepartmentAssignDelete($id)
    {
        $data = CarderDepartmentDesignation::find($id);

        $deptId = $data->department_id;

        if (!$data) {
            return redirect()->back()->with([
                'message' => 'Designation not found',
                'alert-type' => 'error'
            ]);
        }

       $data->delete();


        $notification = [
            'message' => 'Carder Designation Assign Deleted Successfully',
            'alert-type' => 'success'
        ];

        return redirect()->route('carder.department.assign.show', ['id' => $deptId])->with($notification);
    }
}
