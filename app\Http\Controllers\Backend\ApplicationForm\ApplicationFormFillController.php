<?php

namespace App\Http\Controllers\Backend\ApplicationForm;

use App\Http\Controllers\Controller;
use App\Models\AdvanceLevelResultSummary;
use App\Models\AdvanceLevelResult;
use App\Models\City;
use Illuminate\Http\Request;
use App\Models\ApplicationForm;
use App\Models\Category;
use App\Models\nav_experience;
use App\Models\nav_higher_eduacation_qulification;
use App\Models\nav_pdf_upload_path;
use App\Models\nav_professional_qulifications;
use App\Models\OrdinaryLevelResultSummary;
use App\Models\OrdinaryLevelResult;
use App\Models\VancancyNonAcademic;
use Barryvdh\DomPDF\Facade\Pdf as FacadePdf;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Carbon as SupportCarbon;

class ApplicationFormFillController extends Controller
{
    public function FormIndex(Request $request)
    {

        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'categories.display_name')
            ->find(session()->get('vacancy_id'));

        $nic = session()->get('nic');
        $mobile = session()->get('mobile_no');
        $dob = session()->get('dob');
        $age = session()->get('age');

        if (!$vacancy) {

            $notification = array(
                'message' => 'Your application session has been expired',
                'alert-type' => 'error'
            );

            return redirect()->route('nonacademic.vancancy.application.home')->with($notification);
        }

        $titles = DB::table('categories')->where('category_type_id', '=', 5)->get();

        $alStreams = DB::table('categories')->where('category_type_id', '15')->get();

        $degreeTypes = DB::table('categories')->where('category_type_id', '16')->whereIn('id', array(73, 74, 75, 76, 77, 78, 79, 80, 81, 82))->get();

        $academicTypes = DB::table('categories')->where('category_type_id', '16')->whereIn('id', array(70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82))->get();

        $civilStatuses = DB::table('categories')->where('category_type_id', '4')->get();

        $cities = City::all();

        // Check if we're loading a draft application
        $draftApplication = null;
        $higherEducationView = null;
        $proQualificationView = null;
        $experiencesView = null;

        if ($request->has('draft_id')) {
            $draftApplication = ApplicationForm::where('id', $request->draft_id)
                ->where('status', 0) // Only load draft applications
                ->first();

            if (!$draftApplication) {
                return redirect()->route('application.form.view')
                    ->with('error', 'Draft application not found or already submitted.');
            }

            // Load related data for this draft
            $higherEducationView = DB::table('nav_higher_eduacation_qulifications')
                ->where('result_slot_id', $draftApplication->id)
                ->get();

            $proQualificationView = DB::table('nav_professional_qulifications')
                ->where('user_id', $draftApplication->id)
                ->get();

            $experiencesView = DB::table('nav_experiences')
                ->where('user_id', $draftApplication->id)
                ->get();

            // Load extra curricular data
            $extraCurricular = DB::table('nav_extra_curricular')
                ->where('user_id', $draftApplication->id)
                ->first();

            // Load extra curricular PDF files
            $extraCurricularPdfs = DB::table('nav_pdf_upload_paths')
                ->where('user_id', $draftApplication->id)
                ->where('nav_cat_type', 4) // 4 for Extra Curricular
                ->get();

            // Load public sector letter PDF files
            $publicSectorLetterPdfs = DB::table('nav_pdf_upload_paths')
                ->where('user_id', $draftApplication->id)
                ->where('nav_cat_type', 5) // 5 for Public Sector Letter
                ->get();
        } else {
            // Default empty queries for new applications
            $higherEducationView = DB::table('nav_higher_eduacation_qulifications')->where('result_slot_id')->get();
            $proQualificationView = DB::table('nav_professional_qulifications')->where('user_id')->get();
            $experiencesView = DB::table('nav_experiences')->where('user_id')->get();
            $extraCurricular = null;
            $extraCurricularPdfs = collect([]);
            $publicSectorLetterPdfs = collect([]);
        }

        return view('frontend.nonacademic.application.application_form', compact(
            'titles',
            'alStreams',
            'degreeTypes',
            'cities',
            'civilStatuses',
            'academicTypes',
            'higherEducationView',
            'proQualificationView',
            'experiencesView',
            'draftApplication',
            'extraCurricular',
            'extraCurricularPdfs',
            'publicSectorLetterPdfs',
            'vacancy',
            'nic',
            'mobile',
            'dob',
            'age'
        ));
    }


    public function FormSave(Request $request)
    {

        $check_text = ApplicationForm::where('vacancy_id', $request->vacancy_id)
            ->where('nic', $request->nic)
            ->first();

        if ($check_text) {
            $application_id = $check_text->id;
            return redirect()->route('nac.application.submit.open', encrypt($application_id));
        } else {
            //    dd($check_text);
        }

        // Check if this is a draft or final submission
        $isDraft = $request->has('save_draft');

        // Only validate required fields for final submission
        if (!$isDraft) {
            // Basic validation rules
            $validationRules = [
                'title_id' => 'required',
                'name_with_initials' => 'required',
                'name_denoted_by_initials' => 'required',
                'mobile_no' => 'required',
                'email' => 'required|email',
                'nic' => 'required',
                'permanent_address_line1' => 'required',
                'permanent_address_city' => 'required',
                'postal_address_line1' => 'required',
                'postal_address_city' => 'required',
                'date_of_birth' => 'required',
                'age' => 'required',
                'civil_status' => 'required',
                'state_of_citizenship_id' => 'required',
                'terms' => 'required',
            ];

            // Add conditional validation for public sector letter
            if ($request->has('publicSector') && $request->publicSector == 1) {
                // Check if there are already uploaded files for this user (for existing drafts)
                $existingFiles = 0;
                if ($request->has('draft_id')) {
                    $existingFiles = nav_pdf_upload_path::where('user_id', $request->draft_id)
                        ->where('nav_cat_type', 5) // 5 for Public Sector Letter
                        ->count();
                }

                // Only require the file upload if there are no existing files
                if ($existingFiles == 0) {
                    $validationRules['public_sector_letter'] = 'required|file|mimes:pdf|max:10240'; // 10MB max
                }
            }

            $request->validate($validationRules);
        }

        // Check if we're updating an existing draft
        if ($request->has('draft_id')) {
            $applicationForm = ApplicationForm::where('id', $request->draft_id)
                ->where('status', 0) // Only update draft applications
                ->first();

            if (!$applicationForm) {
                return redirect()->route('application.form.view')
                    ->with('error', 'Draft application not found or already submitted.');
            }
        } else {
            $applicationForm = new ApplicationForm();
        }
        //$applicationForm->reference_number = session()->get('reference_no');
        $applicationForm->vacancy_id = $request->vacancy_id;
        $applicationForm->title = $request->title_id;
        $applicationForm->name_with_initials = $request->name_with_initials;
        $applicationForm->name_denoted_by_initials = $request->name_denoted_by_initials;
        $applicationForm->last_name = $request->last_name;
        $applicationForm->telephone_mobile = $request->mobile_no;
        $applicationForm->telephone_residence = $request->phone_no;
        $applicationForm->email_address = $request->email;
        $applicationForm->nic = $request->nic;
        //$applicationForm->nic_copy = $request->nic_copy;
        if ($request->hasFile('nic_copy')) {
            $file = $request->file('nic_copy');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = 'uploads/nic_copy/';
            $file->move(public_path($path), $filename);
            $applicationForm->nic_copy = $path . $filename;
        }
        $applicationForm->permanent_address_line1 = $request->permanent_address_line1;
        $applicationForm->permanent_address_line2 = $request->permanent_address_line2;
        $applicationForm->permanent_address_line3 = $request->permanent_address_line3;
        $applicationForm->permanent_address_city = $request->permanent_address_city;
        $applicationForm->postal_address_line1 = $request->postal_address_line1;
        $applicationForm->postal_address_line2 = $request->postal_address_line2;
        $applicationForm->postal_address_line3 = $request->postal_address_line3;
        $applicationForm->postal_address_city = $request->postal_address_city;
        $applicationForm->date_of_birth = date("Y-m-d", strtotime($request->date_of_birth));
        $applicationForm->age_application_date = $request->age;
        $applicationForm->civil_status = $request->civil_status;
        $applicationForm->citizen_sri_lanka_obtained = $request->state_of_citizenship_id;
        $applicationForm->citizen_registration_no = $request->citizen_registration_no;
        $applicationForm->academic_qualification = $request->pqualification_type_1;
        $applicationForm->academic_qualification = $this->academicQualificationName($request->pqualification_type_1);
        $applicationForm->sinhala_exam = $request->sinhala_exam;
        $applicationForm->tamil_exam = $request->tamil_exam;
        $applicationForm->english_exam = $request->english_exam;
        $applicationForm->protect_application = 1;

        // Set status based on whether this is a draft or final submission
        $applicationForm->status = $isDraft ? 0 : 1; // 0 = draft, 1 = submitted

        $applicationForm->save();
        //dd($applicationForm);

        //ol summary


        //dd($request->attempt1);
        $ol1ResultSummary = new OrdinaryLevelResultSummary();
        $ol1ResultSummary->reference_no = $applicationForm->id;
        $ol1ResultSummary->index_no = $request->ol_result1_index_no;
        $ol1ResultSummary->year = $request->ol_result1_year;
        $ol1ResultSummary->attempt = $request->attempt1;
        // if ($request->hasFile('certificate1')) {
        //     $olResultSummary->file_path = $save_url;
        // }
        if ($request->hasFile('ol_result1_sheet')) {
            $file = $request->file('ol_result1_sheet');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = 'uploads/ol_certificates/';
            $file->move(public_path($path), $filename);
            $ol1ResultSummary->file_path = $path . $filename;
        }
        $ol1ResultSummary->save();

        $olResult = new OrdinaryLevelResult();

        $result = OrdinaryLevelResultSummary::where('reference_no')->where('attempt', '=', 1)->get('id');
        $id = json_decode($result, true);
        $id = !empty($id) ? $id[0]["id"] : null;

        if ($request->subject_name != null) {
            // Remove any existing results for this slot
            if (count($request->grade) > 0) {
                OrdinaryLevelResult::where('result_slot_id', $id)->delete();
            }

            // Loop through subjects and save only if subject name exists
            for ($x = 0; $x < count($request->subject_name); $x++) {
                if ($request->subject_name[$x] != null && $request->subject_name[$x] != '') {
                    $olResult = new OrdinaryLevelResult();
                    $olResult->result_slot_id = $applicationForm->id;
                    $olResult->subject_name = $request->subject_name[$x];
                    $olResult->grade = strtoupper($request->grade[$x]);
                    $olResult->attempt = $request->attempt1; // Save the attempt value
                    $olResult->save();
                }
            }
        }

        if ($request->has('additionalResults')) {
            $olResultSummary2 = new OrdinaryLevelResultSummary();
            $olResultSummary2->reference_no = $applicationForm->id;
            $olResultSummary2->index_no = $request->ol_result2_index_no;
            $olResultSummary2->year = $request->ol_result2_year;
            $olResultSummary2->attempt = $request->attempt2;
            if ($request->hasFile('ol_result2_sheet')) {
                $file = $request->file('ol_result2_sheet');
                $filename = time() . '_' . $file->getClientOriginalName();
                $path = 'uploads/ol_certificates/';
                $file->move(public_path($path), $filename);
                $olResultSummary2->file_path = $path . $filename;
            }
            $olResultSummary2->save();

            $olResult2 = new OrdinaryLevelResult();

            $result2 = OrdinaryLevelResultSummary::where('reference_no')->where('attempt', '=', 2)->get('id');
            $id = json_decode($result2, true);
            $id = !empty($id) ? $id[0]["id"] : null;

            if ($request->subject_name2 != null) {
                // Remove any existing results for this slot
                if (count($request->grade) > 0) {
                    OrdinaryLevelResult::where('result_slot_id', $id)->delete();
                }

                // Loop through subjects and save only if subject name exists
                for ($x = 0; $x < count($request->subject_name2); $x++) {
                    if ($request->subject_name2[$x] != null && $request->subject_name2[$x] != '') {
                        $olResult2 = new OrdinaryLevelResult();
                        $olResult2->result_slot_id = $applicationForm->id;
                        $olResult2->subject_name = $request->subject_name2[$x];
                        $olResult2->grade = strtoupper($request->grade2[$x]);
                        $olResult2->attempt = $request->attempt2; // Save the attempt value
                        $olResult2->save();
                    }
                }
            }
        }

        //al summary
        $alResultSummary = new AdvanceLevelResultSummary();
        $alResultSummary->reference_no = $applicationForm->id;
        $alResultSummary->index_no = $request->al1_index_no;
        $alResultSummary->year = $request->al1_year;
        $alResultSummary->stream = $request->al1_stream_id;
        $alResultSummary->attempt = $request->al_attempt;
        $alResultSummary->level = $request->al_level;
        if ($request->hasFile('al_certificate1')) {
            $file = $request->file('al_certificate1');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = 'uploads/al_certificates/';
            $file->move(public_path($path), $filename);
            $alResultSummary->file_path = $path . $filename;
        }
        $alResultSummary->save();

        //al results
        $alResult = new AdvanceLevelResult();

        $result = AdvanceLevelResultSummary::where('reference_no')->where('attempt', '=', 1)->get('id');
        $id = json_decode($result, true);
        $id = !empty($id) ? $id[0]["id"] : null;

        if ($request->al_subject_name != null) {
            // Remove any existing results for this slot
            if (count($request->al_grade) > 0) {
                AdvanceLevelResult::where('result_slot_id', $id)->delete();
            }

            // Loop through subjects and save only if subject name exists
            for ($x = 0; $x < count($request->al_subject_name); $x++) {
                if ($request->al_subject_name[$x] != null && $request->al_subject_name[$x] != '') {
                    $alResult = new AdvanceLevelResult();
                    $alResult->result_slot_id = $applicationForm->id;
                    $alResult->subject_name = $request->al_subject_name[$x];
                    $alResult->grade = strtoupper($request->al_grade[$x]);
                    $alResult->attempt = $request->al_attempt; // Save the attempt value
                    $alResult->save();
                }
            }
        }

        if ($request->has('additionalResults2')) {
            $alResult2Summary = new AdvanceLevelResultSummary();
            $alResult2Summary->reference_no = $applicationForm->id;
            $alResult2Summary->index_no = $request->al_index_no2;
            $alResult2Summary->year = $request->al_year2;
            $alResult2Summary->stream = $request->al_stream_id2;
            $alResult2Summary->attempt = $request->al_attempt2;
            $alResult2Summary->level = $request->al_level2;
            if ($request->hasFile('al_certificate2')) {
                $file = $request->file('al_certificate2');
                $filename = time() . '_' . $file->getClientOriginalName();
                $path = 'uploads/al_certificates/';
                $file->move(public_path($path), $filename);
                $alResult2Summary->file_path = $path . $filename;
            }
            $alResult2Summary->save();

            //al results
            $alResult = new AdvanceLevelResult();

            $result = AdvanceLevelResultSummary::where('reference_no')->where('attempt', '=', 2)->get('id');
            $id = json_decode($result, true);
            $id = !empty($id) ? $id[0]["id"] : null;

            if ($request->al_subject_name2 != null) {
                // Remove any existing results for this slot
                if (count($request->al_grade2) > 0) {
                    AdvanceLevelResult::where('result_slot_id', $id)->delete();
                }

                // Loop through subjects and save only if subject name exists
                for ($x = 0; $x < count($request->al_subject_name2); $x++) {
                    if ($request->al_subject_name2[$x] != null && $request->al_subject_name2[$x] != '') {
                        $alResult = new AdvanceLevelResult();
                        $alResult->result_slot_id = $applicationForm->id;
                        $alResult->subject_name = $request->al_subject_name2[$x];
                        $alResult->grade = strtoupper($request->al_grade2[$x]);
                        $alResult->attempt = $request->al_attempt2; // Save the attempt value
                        $alResult->save();
                    }
                }
            }
        }


        // ******************************
        // Save the Higher Education Details
        // ******************************

        if (isset($request->hqualification_data)) {
            try {
                $qualifications = json_decode($request->hqualification_data, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('JSON decode error: ' . json_last_error_msg());
                    throw new \Exception('Invalid JSON data provided');
                }

                if (!empty($qualifications) && is_array($qualifications)) {
                    foreach ($qualifications as $qualification) {
                        $required_fields = ['courseName', 'university', 'type', 'grade', 'effectiveDate', 'startYear', 'endYear'];
                        foreach ($required_fields as $field) {
                            if (!isset($qualification[$field])) {
                                throw new \Exception("Missing required field: {$field}");
                            }
                        }

                        $higherEducation = new nav_higher_eduacation_qulification();
                        $higherEducation->result_slot_id = $applicationForm->id;
                        $higherEducation->Name_of_course = $qualification['courseName'];
                        $higherEducation->Institution = $qualification['university'];
                        $higherEducation->type = $qualification['type'];
                        $higherEducation->Final_result = $qualification['grade'];
                        $higherEducation->effective_date = date("Y-m-d", strtotime($qualification['effectiveDate']));
                        $higherEducation->start_year = $qualification['startYear'];
                        $higherEducation->end_year = $qualification['endYear'];
                        $higherEducation->save();

                        // We'll handle file uploads after all qualifications are saved
                        // to ensure we have the correct type for each file

                    }
                }

                // Now handle file uploads after all qualifications are saved
                if ($request->hasFile('degree_certificate')) {
                    $files = $request->file('degree_certificate');

                    // Get all the qualification types we just saved
                    $qualificationTypes = [];
                    foreach ($qualifications as $qualification) {
                        if (isset($qualification['type'])) {
                            $qualificationTypes[] = $qualification['type'];
                        }
                    }

                    // If we have no qualification types, use the first one as default (if available)
                    $defaultType = !empty($qualificationTypes) ? $qualificationTypes[0] : null;

                    foreach ($files as $file) {
                        if ($file->isValid() && $file->getClientOriginalExtension() === 'pdf') {
                            try {
                                $filename = uniqid() . '_' . time() . '_' . $file->getClientOriginalName();
                                $path = 'uploads/higher_education_certificates/';

                                // Ensure directory exists
                                if (!File::isDirectory(public_path($path))) {
                                    File::makeDirectory(public_path($path), 0777, true);
                                }

                                $file->move(public_path($path), $filename);

                                // Create new record for each file
                                $higherEducationPath = new nav_pdf_upload_path();
                                $higherEducationPath->user_id = $applicationForm->id;
                                $higherEducationPath->type = $defaultType; // Use the default type for all files
                                $higherEducationPath->nav_cat_type = 1; // 1 for Higher Education
                                $higherEducationPath->file_path = $path . $filename;
                                $higherEducationPath->save();

                                Log::info('File uploaded successfully: ' . $filename);
                            } catch (\Exception $e) {
                                Log::error('File upload failed: ' . $e->getMessage());
                                continue;
                            }
                        } else {
                            Log::error('Invalid file or not a PDF: ' . $file->getClientOriginalName());
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::error('Error saving higher education data: ' . $e->getMessage());
                throw $e;
            }
        }

        // ******************************
        // Save the Professional Qualifications
        // ******************************
        if (isset($request->pqualification_data)) {
            try {
                $pqualifications = json_decode($request->pqualification_data, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('JSON decode error: ' . json_last_error_msg());
                    throw new \Exception('Invalid JSON data provided');
                }

                if (!empty($pqualifications) && is_array($pqualifications)) {
                    foreach ($pqualifications as $pqualification) {
                        $required_fields = ['qualificationName', 'university', 'nvqLevel', 'effectiveDate', 'startYear', 'endYear'];
                        foreach ($required_fields as $field) {
                            if (!isset($pqualification[$field])) {
                                throw new \Exception("Missing required field: {$field}");
                            }
                        }

                        $pQualificatio = new nav_professional_qulifications();
                        $pQualificatio->user_id = $applicationForm->id;
                        $pQualificatio->qualification = $pqualification['qualificationName'];
                        $pQualificatio->institution = $pqualification['university'];
                        $pQualificatio->NVQ = $pqualification['nvqLevel'];

                        $pQualificatio->effective_date = date("Y-m-d", strtotime($pqualification['effectiveDate']));
                        $pQualificatio->start_year = $pqualification['startYear'];
                        $pQualificatio->end_year = $pqualification['endYear'];
                        $pQualificatio->save();
                    }
                }

                // Handle professional qualification file uploads
                if ($request->hasFile('pqualification_certificate')) {
                    $files = $request->file('pqualification_certificate');

                    foreach ($files as $file) {
                        if ($file->isValid() && $file->getClientOriginalExtension() === 'pdf') {
                            try {
                                $filename = uniqid() . '_' . time() . '_' . $file->getClientOriginalName();
                                $path = 'uploads/professional_certificates/';

                                // Ensure directory exists
                                if (!File::isDirectory(public_path($path))) {
                                    File::makeDirectory(public_path($path), 0777, true);
                                }

                                $file->move(public_path($path), $filename);

                                // Create new record for each file
                                $profQualificationPath = new nav_pdf_upload_path();
                                $profQualificationPath->user_id = $applicationForm->id;
                                $profQualificationPath->nav_cat_type = 2; // 2 for Professional Qualification
                                $profQualificationPath->file_path = $path . $filename;
                                $profQualificationPath->save();

                                Log::info('Professional qualification file uploaded successfully: ' . $filename);
                            } catch (\Exception $e) {
                                Log::error('Professional qualification file upload failed: ' . $e->getMessage());
                                continue;
                            }
                        } else {
                            Log::error('Invalid professional qualification file or not a PDF: ' . $file->getClientOriginalName());
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::error('Error saving professional qualification data: ' . $e->getMessage());
                throw $e;
            }
        }

        // ******************************
        // Save Extra Curricular Activities
        // ******************************

        // Save the extra curricular description
        if ($request->has('extra_curricular_description')) {
            // Check if there's an existing record
            $extraCurricular = DB::table('nav_extra_curricular')
                ->where('user_id', $applicationForm->id)
                ->first();

            if ($extraCurricular) {
                // Update existing record
                DB::table('nav_extra_curricular')
                    ->where('user_id', $applicationForm->id)
                    ->update([
                        'description' => $request->extra_curricular_description,
                        'updated_at' => now()
                    ]);
            } else {
                // Create new record
                DB::table('nav_extra_curricular')->insert([
                    'user_id' => $applicationForm->id,
                    'description' => $request->extra_curricular_description,
                    'status' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        }

        // Handle extra curricular PDF uploads
        if ($request->hasFile('extra_curricular_files')) {
            $files = $request->file('extra_curricular_files');

            foreach ($files as $file) {
                if ($file->isValid() && $file->getClientOriginalExtension() === 'pdf') {
                    try {
                        $filename = uniqid() . '_' . time() . '_' . $file->getClientOriginalName();
                        $path = 'uploads/extra_curricular_certificates/';

                        // Ensure directory exists
                        if (!File::isDirectory(public_path($path))) {
                            File::makeDirectory(public_path($path), 0777, true);
                        }

                        $file->move(public_path($path), $filename);

                        // Create new record for each file
                        $extraCurricularPath = new nav_pdf_upload_path();
                        $extraCurricularPath->user_id = $applicationForm->id;
                        $extraCurricularPath->nav_cat_type = 4; // 4 for Extra Curricular
                        $extraCurricularPath->file_path = $path . $filename;
                        $extraCurricularPath->original_filename = $file->getClientOriginalName();
                        $extraCurricularPath->save();

                        Log::info('Extra curricular file uploaded successfully: ' . $filename);
                    } catch (\Exception $e) {
                        Log::error('Extra curricular file upload failed: ' . $e->getMessage());
                        continue;
                    }
                } else {
                    Log::error('Invalid extra curricular file or not a PDF: ' . $file->getClientOriginalName());
                }
            }
        }

        // Handle removed extra curricular files
        if ($request->has('removed_extra_curricular_files') && !empty($request->removed_extra_curricular_files)) {
            try {
                $removedFileIds = json_decode($request->removed_extra_curricular_files, true);

                if (is_array($removedFileIds) && count($removedFileIds) > 0) {
                    foreach ($removedFileIds as $fileId) {
                        // Get the file record
                        $fileRecord = nav_pdf_upload_path::find($fileId);

                        if ($fileRecord && $fileRecord->nav_cat_type == 4) { // Make sure it's an extra curricular file
                            // Delete the physical file if it exists
                            if (File::exists(public_path($fileRecord->file_path))) {
                                File::delete(public_path($fileRecord->file_path));
                            }

                            // Delete the database record
                            $fileRecord->delete();

                            Log::info('Extra curricular file deleted: ' . $fileId);
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::error('Error removing extra curricular files: ' . $e->getMessage());
            }
        }

        // ******************************
        // Handle Public Sector Letter Upload
        // ******************************

        // Save public sector status
        if ($request->has('publicSector')) {
            $applicationForm->public_sector = $request->publicSector;
            $applicationForm->save();

            // Handle public sector letter upload if user is a public sector employee
            if ($request->publicSector == 1) {
                // Check if there are already uploaded files for this user
                $existingFiles = nav_pdf_upload_path::where('user_id', $applicationForm->id)
                    ->where('nav_cat_type', 5) // 5 for Public Sector Letter
                    ->count();

                // We've already validated the file upload in the main validation section
                // No need to validate again here

                // Process the file if it's uploaded
                if ($request->hasFile('public_sector_letter')) {
                    $file = $request->file('public_sector_letter');

                    if ($file->isValid() && $file->getClientOriginalExtension() === 'pdf') {
                        try {
                            $filename = uniqid() . '_' . time() . '_' . $file->getClientOriginalName();
                            $path = 'uploads/public_sector_letters/';

                            // Ensure directory exists
                            if (!File::isDirectory(public_path($path))) {
                                File::makeDirectory(public_path($path), 0777, true);
                            }

                            $file->move(public_path($path), $filename);

                            // Create new record for the file
                            $publicSectorLetterPath = new nav_pdf_upload_path();
                            $publicSectorLetterPath->user_id = $applicationForm->id;
                            $publicSectorLetterPath->nav_cat_type = 5; // 5 for Public Sector Letter
                            $publicSectorLetterPath->file_path = $path . $filename;
                            $publicSectorLetterPath->original_filename = $file->getClientOriginalName();
                            $publicSectorLetterPath->save();

                            Log::info('Public sector letter file uploaded successfully: ' . $filename);
                        } catch (\Exception $e) {
                            Log::error('Public sector letter file upload failed: ' . $e->getMessage());
                            if (!$isDraft) {
                                throw $e; // Re-throw the exception for final submissions
                            }
                        }
                    } else {
                        Log::error('Invalid public sector letter file or not a PDF: ' . $file->getClientOriginalName());
                        // if (!$isDraft) {
                        //     return redirect()->back()->with('error', 'Invalid public sector letter file. Please upload a PDF file.');
                        // }
                    }
                }
            }
        }

        // ******************************
        // Save working status


        if ($request->has('is_working') && $request->is_working === 'yes') {
            //$applicationForm-> = new nav_experience();
            $currentWorkingStatus = new nav_experience();
            $currentWorkingStatus->user_id = $applicationForm->id;
            $currentWorkingStatus->is_current = true;
            $currentWorkingStatus->company_name = $request->current_workplace;
            $currentWorkingStatus->position = $request->current_job;
            $currentWorkingStatus->duration = $request->notice_period;
            $currentWorkingStatus->start_date = date('Y-m-d', strtotime($request->start_date));
            $currentWorkingStatus->basic_salary = $request->basic_salary;
            $currentWorkingStatus->allowance = $request->allowance;
            $currentWorkingStatus->total_salary = $request->basic_salary + $request->allowance;
            $currentWorkingStatus->save();
        }





        if (!empty($request->emp_data)) {
            try {
                $experiences = json_decode($request->emp_data, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('JSON decode error: ' . json_last_error_msg());
                    throw new \Exception('Invalid JSON data provided');
                }

                if (!empty($experiences) && is_array($experiences)) {
                    foreach ($experiences as $experience) {
                        $required_fields = ['empPlace', 'designation', 'startYear', 'endYear'];
                        foreach ($required_fields as $field) {
                            if (!isset($experience[$field])) {
                                throw new \Exception("Missing required field: {$field}");
                            }
                        }

                        $employmentRecord = new nav_experience();
                        $employmentRecord->user_id = $applicationForm->id;
                        $employmentRecord->company_name = $experience['empPlace'];
                        $employmentRecord->position = $experience['designation'];
                        $employmentRecord->start_date = date('Y-m-d', strtotime($experience['startYear']));
                        $employmentRecord->end_date = date('Y-m-d', strtotime($experience['endYear']));

                        if (!$employmentRecord->save()) {
                            Log::error('Failed to save employment record');
                            throw new \Exception('Failed to save employment record');
                        }
                    }
                }

                // Handle experience file uploads
                if ($request->hasFile('employee_certificate')) {
                    $files = $request->file('employee_certificate');

                    foreach ($files as $file) {
                        if ($file->isValid() && $file->getClientOriginalExtension() === 'pdf') {
                            try {
                                $filename = uniqid() . '_' . time() . '_' . $file->getClientOriginalName();
                                $path = 'uploads/experience_certificates/';

                                // Ensure directory exists
                                if (!File::isDirectory(public_path($path))) {
                                    File::makeDirectory(public_path($path), 0777, true);
                                }

                                $file->move(public_path($path), $filename);

                                // Create new record for each file
                                $experiencePath = new nav_pdf_upload_path();
                                $experiencePath->user_id = $applicationForm->id;
                                $experiencePath->nav_cat_type = 3; // 3 for Experience
                                $experiencePath->file_path = $path . $filename;
                                $experiencePath->save();

                                Log::info('Experience file uploaded successfully: ' . $filename);
                            } catch (\Exception $e) {
                                Log::error('Experience file upload failed: ' . $e->getMessage());
                                continue;
                            }
                        } else {
                            Log::error('Invalid experience file or not a PDF: ' . $file->getClientOriginalName());
                        }
                    }
                }

                // Handle driving license file upload if applying for driving job
                if ($request->has('isDriver') && $request->isDriver && $request->hasFile('driving_license')) {
                    $file = $request->file('driving_license');

                    if ($file->isValid() && $file->getClientOriginalExtension() === 'pdf') {
                        try {
                            $filename = uniqid() . '_' . time() . '_' . $file->getClientOriginalName();
                            $path = 'uploads/driving_licenses/';

                            // Ensure directory exists
                            if (!File::isDirectory(public_path($path))) {
                                File::makeDirectory(public_path($path), 0777, true);
                            }

                            $file->move(public_path($path), $filename);

                            // Create new record for driving license
                            $licensePath = new nav_pdf_upload_path();
                            $licensePath->user_id = $applicationForm->id;
                            $licensePath->nav_cat_type = 3; // 3 for Experience (driving is a type of experience)
                            $licensePath->file_path = $path . $filename;
                            $licensePath->save();

                            Log::info('Driving license file uploaded successfully: ' . $filename);
                        } catch (\Exception $e) {
                            Log::error('Driving license file upload failed: ' . $e->getMessage());
                        }
                    } else {
                        Log::error('Invalid driving license file or not a PDF: ' . $file->getClientOriginalName());
                    }
                }
            } catch (\Exception $e) {
                Log::error('Error saving employment data: ' . $e->getMessage());
                throw $e;
            }
        }

        return redirect()->route('nac.application.submit.open', encrypt($applicationForm->id));
        // // Set appropriate notification based on draft or final submission
        // if ($isDraft) {
        //     $notification = array(
        //         'message' => 'Your application has been saved as a draft. You can continue editing it later.',
        //         'alert-type' => 'info'
        //     );

        //     return redirect()->route('nonacademic.vancancy.application.home')->with($notification);
        // } else {
        //     $notification = array(
        //         'message' => 'Your application has been successfully submitted.',
        //         'alert-type' => 'success'
        //     );

        //     // Check if this is an AJAX request (from the PDF download modal)
        //     if (request()->ajax()) {
        //         return response()->json([
        //             'success' => true,
        //             'message' => 'Your application has been successfully submitted.',
        //             'application_id' => $applicationForm->id
        //         ]);
        //     }

        //     // Regular form submission (non-AJAX)
        //     return redirect()->route('nonacademic.vancancy.application.home')->with($notification);
        // }
    }

    // ******************************
    // Save the Higher Education Details
    // ******************************

    public function print_navpdf_comment($id)
    {
        // Use the $id parameter to find the application form
        $applicationForm = ApplicationForm::find($id);

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'categories.display_name')
            ->find($applicationForm->vacancy_id);

        // Get related data
        // For O/L results, try different approaches to find the data
        $olSummaries = OrdinaryLevelResultSummary::where('reference_no', $applicationForm->id)
            ->orderBy('attempt', 'asc')
            ->get();

        // If no results found, try with reference_no as string
        if ($olSummaries->count() == 0) {
            $olSummaries = OrdinaryLevelResultSummary::where('reference_no', (string)$applicationForm->id)
                ->orderBy('attempt', 'asc')
                ->get();
        }

        $olSummary = null;
        $olSummary2 = null;
        $olResults = [];
        $olResults2 = [];

        // Process O/L summaries
        if ($olSummaries->count() > 0) {
            // First attempt
            $olSummary = $olSummaries->first();
            if ($olSummary) {
                $olResults = OrdinaryLevelResult::where('result_slot_id', $olSummary->id)
                    ->get();

                // If no results found, try direct query to find all O/L results
                if ($olResults->count() == 0) {
                    $olResults = DB::table('ordinary_level_results')
                        ->join('ordinary_level_result_summaries', 'ordinary_level_results.result_slot_id', '=', 'ordinary_level_result_summaries.reference_no')
                        ->where('ordinary_level_result_summaries.reference_no', $applicationForm->id)
                        ->select('ordinary_level_results.*')
                        ->get();
                }
            }

            // Second attempt (if exists)
            if ($olSummaries->count() > 1) {
                $olSummary2 = $olSummaries->skip(1)->first();
                if ($olSummary2) {
                    $olResults2 = OrdinaryLevelResult::where('result_slot_id', $olSummary2->id)
                        ->get();
                }
            }
        }

        // For A/L results, try different approaches to find the data
        $alSummaries = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)
            ->orderBy('attempt', 'asc')
            ->get();

        // If no results found, try with reference_no as string
        if ($alSummaries->count() == 0) {
            $alSummaries = AdvanceLevelResultSummary::where('reference_no', (string)$applicationForm->id)
                ->orderBy('attempt', 'asc')
                ->get();
        }

        $alSummary = null;
        $alSummary2 = null;
        $alResults = [];
        $alResults2 = [];

        // Process A/L summaries
        if ($alSummaries->count() > 0) {
            // First attempt
            $alSummary = $alSummaries->first();
            if ($alSummary) {
                $alResults = AdvanceLevelResult::where('result_slot_id', $alSummary->reference_no)
                    ->get();

                // If no results found, try direct query to find all A/L results
                if ($alResults->count() == 0) {
                    $alResults = DB::table('advance_level_results')
                        ->join('advance_level_result_summaries', 'advance_level_results.result_slot_id', '=', 'advance_level_result_summaries.reference_no')
                        ->where('advance_level_result_summaries.reference_no', $applicationForm->id)
                        ->select('advance_level_results.*')
                        ->get();
                }
            }

            // Second attempt (if exists)
            if ($alSummaries->count() > 1) {
                $alSummary2 = $alSummaries->skip(1)->first();
                if ($alSummary2) {
                    $alResults2 = AdvanceLevelResult::where('result_slot_id', $alSummary2->reference_no)
                        ->get();
                }
            }
        }

        // If still no results found, try a direct query to the results tables
        if (empty($olResults) || count($olResults) == 0) {
            // First try to find results with attempt = 1
            $olResults = OrdinaryLevelResult::where('result_slot_id', $applicationForm->id)
                ->where(function ($query) {
                    $query->where('attempt', 1)
                        ->orWhereNull('attempt'); // Include results without attempt value for backward compatibility
                })
                ->get();

            // If still no results, get all results for this application
            if ($olResults->count() == 0) {
                $olResults = OrdinaryLevelResult::where('result_slot_id', $applicationForm->id)->get();
            }
        }

        if (empty($alResults) || count($alResults) == 0) {
            // First try to find results with attempt = 1
            $alResults = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)
                ->where(function ($query) {
                    $query->where('attempt', 1)
                        ->orWhereNull('attempt'); // Include results without attempt value for backward compatibility
                })
                ->get();

            // If still no results, get all results for this application
            if ($alResults->count() == 0) {
                $alResults = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)->get();
            }
        }

        // SIMPLIFIED APPROACH: Get all O/L summaries for this application
        $allOlSummaries = OrdinaryLevelResultSummary::where('reference_no', $applicationForm->id)->get();

        // Initialize variables
        $olSummary2 = null;
        $olResults2 = collect([]);

        // If we have more than one summary, the second one is likely the second attempt
        if ($allOlSummaries->count() > 1) {
            // Sort by attempt number if available, otherwise by ID
            $sortedSummaries = $allOlSummaries->sortBy(function ($summary) {
                return $summary->attempt ?? $summary->id;
            });

            // Skip the first one (which should be attempt 1) and take the next one
            $olSummary2 = $sortedSummaries->slice(1, 1)->first();

            if ($olSummary2) {
                $olResults2 = OrdinaryLevelResult::where('result_slot_id', $olSummary2->reference_no)->get();
            }
        }

        // If we still don't have results, look for any results with attempt = 2
        if ($olResults2->count() == 0) {
            $olResults2 = OrdinaryLevelResult::where('attempt', 2)
                ->where(function ($query) use ($applicationForm, $allOlSummaries) {
                    $query->where('result_slot_id', $applicationForm->id);

                    // Also include any result_slot_ids from the summaries
                    if ($allOlSummaries->count() > 0) {
                        $query->orWhereIn('result_slot_id', $allOlSummaries->pluck('reference_no'));
                    }
                })
                ->get();

            // If we found results but no summary, create a dummy summary
            if ($olResults2->count() > 0 && !$olSummary2) {
                $olSummary2 = new OrdinaryLevelResultSummary();
                $olSummary2->attempt = 2;
                $olSummary2->reference_no = $applicationForm->id;
            }
        }

        // SIMPLIFIED APPROACH: Get all A/L summaries for this application
        $allAlSummaries = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)->get();

        // Initialize variables
        $alSummary2 = null;
        $alResults2 = collect([]);

        // First, try to find a summary with level = 2 (this is the most reliable indicator)
        $level2Summary = $allAlSummaries->where('level', 2)->first();
        if ($level2Summary) {
            $alSummary2 = $level2Summary;
            $alResults2 = AdvanceLevelResult::where('result_slot_id', $level2Summary->reference_no)->get();
        }
        // If not found, try to find a summary with attempt = 2
        else {
            $attempt2Summary = $allAlSummaries->where('attempt', 2)->first();
            if ($attempt2Summary) {
                $alSummary2 = $attempt2Summary;
                $alResults2 = AdvanceLevelResult::where('result_slot_id', $attempt2Summary->reference_no)->get();
            }
            // If still not found and we have more than one summary, use the second one
            elseif ($allAlSummaries->count() > 1) {
                // Sort by level, then attempt, then ID
                $sortedSummaries = $allAlSummaries->sortBy(function ($summary) {
                    return ($summary->level ?? 0) * 100 + ($summary->attempt ?? 0) * 10 + $summary->id;
                });

                // Skip the first one and take the next one
                $alSummary2 = $sortedSummaries->slice(1, 1)->first();

                if ($alSummary2) {
                    $alResults2 = AdvanceLevelResult::where('result_slot_id', $alSummary2->reference_no)->get();
                }
            }
        }

        // If we still don't have results, look for any results with attempt = 2
        if ($alResults2->count() == 0) {
            $alResults2 = AdvanceLevelResult::where('attempt', 2)
                ->where(function ($query) use ($applicationForm, $allAlSummaries) {
                    $query->where('result_slot_id', $applicationForm->id);

                    // Also include any result_slot_ids from the summaries
                    if ($allAlSummaries->count() > 0) {
                        $query->orWhereIn('result_slot_id', $allAlSummaries->pluck('reference_no'));
                    }
                })
                ->get();

            // If we found results but no summary, create a dummy summary
            if ($alResults2->count() > 0 && !$alSummary2) {
                $alSummary2 = new AdvanceLevelResultSummary();
                $alSummary2->attempt = 2;
                $alSummary2->level = 2;
                $alSummary2->reference_no = $applicationForm->id;
            }
        }

        $higherEducation = nav_higher_eduacation_qulification::where('result_slot_id', $applicationForm->id)->get();

        $proQualifications = nav_professional_qulifications::where('user_id', $applicationForm->id)->get();
        $experiences = nav_experience::where('user_id', $applicationForm->id)->get();

        // Get extra curricular data
        $extraCurricular = DB::table('nav_extra_curricular')
            ->where('user_id', $applicationForm->id)
            ->first();

        // Get extra curricular PDF files
        $extraCurricularPdfs = DB::table('nav_pdf_upload_paths')
            ->where('user_id', $applicationForm->id)
            ->where('nav_cat_type', 4) // 4 for Extra Curricular
            ->get();

        // Get public sector letter PDF files
        $publicSectorLetterPdfs = DB::table('nav_pdf_upload_paths')
            ->where('user_id', $applicationForm->id)
            ->where('nav_cat_type', 5) // 5 for Public Sector Letter
            ->get();

        // Load categories for reference
        $titles = DB::table('categories')->where('category_type_id', '=', 5)->get();
        $civilStatuses = DB::table('categories')->where('category_type_id', '4')->get();
        $cities = DB::table('cities')->get();
        $degreeTypes = DB::table('categories')->where('category_type_id', '16')->get();
        $alStreams = DB::table('categories')->where('category_type_id', '15')->get();

        // Add debug information to the log
        Log::info('PDF Generation Data', [
            'Application ID' => $applicationForm->id,
            'vacancy' => $vacancy,
            'olResults Count' => $olResults ? count($olResults) : 0,
            'olSummary' => $olSummary ? 'Present' : 'Not Present',
            'olSummary ID' => $olSummary ? $olSummary->id : 'N/A',
            'olSummary Attempt' => $olSummary && isset($olSummary->attempt) ? $olSummary->attempt : 'N/A',
            'olResults2 Count' => $olResults2 ? count($olResults2) : 0,
            'olSummary2' => $olSummary2 ? 'Present' : 'Not Present',
            'olSummary2 ID' => $olSummary2 ? $olSummary2->id : 'N/A',
            'olSummary2 Attempt' => $olSummary2 && isset($olSummary2->attempt) ? $olSummary2->attempt : 'N/A',
            'alResults Count' => $alResults ? count($alResults) : 0,
            'alSummary' => $alSummary ? 'Present' : 'Not Present',
            'alSummary ID' => $alSummary ? $alSummary->id : 'N/A',
            'alSummary Attempt' => $alSummary && isset($alSummary->attempt) ? $alSummary->attempt : 'N/A',
            'alResults2 Count' => $alResults2 ? count($alResults2) : 0,
            'alSummary2' => $alSummary2 ? 'Present' : 'Not Present',
            'alSummary2 ID' => $alSummary2 ? $alSummary2->id : 'N/A',
            'alSummary2 Attempt' => $alSummary2 && isset($alSummary2->attempt) ? $alSummary2->attempt : 'N/A',
            'alSummary2 Level' => $alSummary2 && isset($alSummary2->level) ? $alSummary2->level : 'N/A',
            'Extra Curricular' => $extraCurricular ? 'Present' : 'Not Present',
            'Extra Curricular PDFs Count' => $extraCurricularPdfs ? count($extraCurricularPdfs) : 0,
        ]);

        // Ensure collections are properly initialized
        if (!$olResults2) $olResults2 = collect([]);
        if (!$alResults2) $alResults2 = collect([]);

        $AL_Text1 = AdvanceLevelResultSummary::join('categories', 'categories.id', '=', 'advance_level_result_summaries.stream')
            ->select(
                'advance_level_result_summaries.*',
                'categories.category_name'
            )
            ->where('advance_level_result_summaries.reference_no', $applicationForm->id)
            ->where('advance_level_result_summaries.attempt', 1)
            ->get();

        if (count($AL_Text1) > 0) {
            foreach ($AL_Text1 as $AL_Text1s) {
                $AL1_index = $AL_Text1s->index_no;
                $AL1_year = $AL_Text1s->year;
                $AL1_stream = $AL_Text1s->category_name;
            }
        } else {
            $AL1_index = '';
            $AL1_year = '';
            $AL1_stream = '';
        }

        $AL1_result = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)
            ->where('attempt', 1)
            ->get();

        $AL_Text2 = AdvanceLevelResultSummary::join('categories', 'categories.id', '=', 'advance_level_result_summaries.stream')
            ->select(
                'advance_level_result_summaries.*',
                'categories.category_name'
            )
            ->where('advance_level_result_summaries.reference_no', $applicationForm->id)
            ->where('advance_level_result_summaries.attempt', 2)
            ->get();

        if (count($AL_Text2) > 0) {
            foreach ($AL_Text2 as $AL_Text2s) {
                $AL2_index = $AL_Text2s->index_no;
                $AL2_year = $AL_Text2s->year;
                $AL2_stream = $AL_Text2s->category_name;
            }
        } else {
            $AL2_index = '';
            $AL2_year = '';
            $AL2_stream = '';
        }

        $AL2_result = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)
            ->where('attempt', 2)
            ->get();

        $pdf = Pdf::loadView('frontend.nonacademic.application.form_components.applicant_pdf', [
            'applicationForm' => $applicationForm,
            'vacancy' => $vacancy,
            'id' => $id,
            'olResults' => $olResults,
            'olSummary' => $olSummary,
            'olResults2' => $olResults2,
            'olSummary2' => $olSummary2,
            'alResults' => $alResults,
            'alSummary' => $alSummary,
            'alResults2' => $alResults2,
            'alSummary2' => $alSummary2,
            'higherEducation' => $higherEducation,
            'proQualifications' => $proQualifications,
            'experiences' => $experiences,
            'extraCurricular' => $extraCurricular,
            'extraCurricularPdfs' => $extraCurricularPdfs,
            'publicSectorLetterPdfs' => $publicSectorLetterPdfs,
            'titles' => $titles,
            'civilStatuses' => $civilStatuses,
            'cities' => $cities,
            'degreeTypes' => $degreeTypes,
            'alStreams' => $alStreams,
            'AL1_index' => $AL1_index,
            'AL1_year' => $AL1_year,
            'AL1_stream' => $AL1_stream,
            'AL1_result' => $AL1_result,
            'AL2_index' => $AL2_index,
            'AL2_year' => $AL2_year,
            'AL2_stream' => $AL2_stream,
            'AL2_result' => $AL2_result
        ]);

        return $pdf->download('application_form_' . $id . '.pdf');
    }

    private function academicQualificationName($id)
    {
        $acaName = Category::where('id', '=', $id)->first();

        return $acaName ? $acaName->category_name : NULL;
    }

    public function draftApplications()
    {
        $draftApplications = ApplicationForm::where('status', 0)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('frontend.nonacademic.application.draft_applications', compact('draftApplications'));
    }


    public function deleteDraftApplication($id)
    {
        $draft = ApplicationForm::where('id', $id)
            ->where('status', 0) // Only delete draft applications
            ->first();

        if (!$draft) {
            return redirect()->route('application.drafts')
                ->with('error', 'Draft application not found or already submitted.');
        }

        // Delete the draft
        $draft->delete();

        return redirect()->route('application.drafts')
            ->with('success', 'Draft application deleted successfully.');
    }


    public function applicationsView()
    {

        $data_text = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->select(
                't.category_name as title',
                'application_forms.name_with_initials',
                'application_forms.last_name',
                'application_forms.nic',
                'application_forms.email_address',
                'application_forms.telephone_mobile',
                'application_forms.created_at',
                'application_forms.id',
            )
            ->orderBy('application_forms.created_at')
            ->get();

        // return view('admin.applications.applications_view', compact('applications', 'titles', 'civilStatuses', 'cities'));
        return view('admin.applications.applications_view', compact('data_text'));
    }

    public function downPageOpen()
    {
        return view('frontend.nonacademic.application.down_page');
    }

    public function print_navpdf($id)
    {

        $app_id = $id;

        $data_text = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->join('vancancy_non_academics', 'vancancy_non_academics.id', '=', 'application_forms.vacancy_id')
            ->join('designations', 'designations.id', '=', 'vancancy_non_academics.designation_id',)
            ->join('categories as g', 'g.id', '=', 'designations.staff_grade',)
            ->join('categories as dc', 'dc.id', '=', 'vancancy_non_academics.designation_category')
            ->join('cities as perCity', 'perCity.id', '=', 'application_forms.permanent_address_city')
            ->join('cities as postCity', 'postCity.id', '=', 'application_forms.postal_address_city')
            ->select(
                't.category_name as title_name',
                'application_forms.*',
                'cs.category_name as civilStatus',
                'designations.designation_name',
                'g.category_name as grade',
                'dc.category_name as desig_cat',
                'perCity.name_en as permanent_city',
                'postCity.name_en as postal_city'
            )
            ->where('application_forms.id', $app_id)
            ->get();

        if (count($data_text) > 0) {
            foreach ($data_text as $data_texts) {
                $desig_name = $data_texts->designation_name . ' (' . $data_texts->grade . ') ';
                $title = $data_texts->title_name;
                $nic = $data_texts->nic;
                $initial = $data_texts->name_with_initials;
                $bDate = $data_texts->date_of_birth;
                $fName = $data_texts->name_denoted_by_initials;
                $LName = $data_texts->last_name;
                $civil_status = $data_texts->civilStatus;
                $citizen = $data_texts->citizen_sri_lanka_obtained;
                $citizen_regNo = $data_texts->citizen_registration_no;
                $mobile = $data_texts->telephone_mobile;
                $homeTP = $data_texts->telephone_residence;
                $email = $data_texts->email_address;

                $permanent_address_line1 = $data_texts->permanent_address_line1;
                $permanent_address_line2 = $data_texts->permanent_address_line2;
                $permanent_address_line3 = $data_texts->permanent_address_line3;
                $permanent_city = $data_texts->permanent_city;

                $postal_address_line1 = $data_texts->postal_address_line1;
                $postal_address_line2 = $data_texts->postal_address_line2;
                $postal_address_line3 = $data_texts->postal_address_line3;
                $postal_city = $data_texts->postal_city;

                $highest_edu = $data_texts->academic_qualification;

                $sinhala_exam = $data_texts->sinhala_exam;
                $tamil_exam = $data_texts->tamil_exam;
                $english_exam = $data_texts->english_exam;
            }

            $OL_summary_test1 = OrdinaryLevelResultSummary::where('reference_no', $app_id)
                ->where('attempt', 1)
                ->get();
            if (count($OL_summary_test1) > 0) {
                foreach ($OL_summary_test1 as $OL_summary_test1s) {
                    $OL_index1 = $OL_summary_test1s->index_no;
                    $OL_year1 = $OL_summary_test1s->year;
                }
                $OL_result1 = OrdinaryLevelResult::where('result_slot_id', $app_id)
                    ->where('attempt', 1)
                    ->get();
            } else {
                $OL_index1 = 0;
                $OL_year1 = '';
                $OL_result1 = array();
            }

            $OL_summary_test2 = OrdinaryLevelResultSummary::where('reference_no', $app_id)
                ->where('attempt', 2)
                ->get();
            if (count($OL_summary_test2) > 0) {
                foreach ($OL_summary_test2 as $OL_summary_test2s) {
                    $OL_index2 = $OL_summary_test2s->index_no;
                    $OL_year2 = $OL_summary_test2s->year;
                }
                $OL_result2 = OrdinaryLevelResult::where('result_slot_id', $app_id)
                    ->where('attempt', 2)
                    ->get();
            } else {
                $OL_index2 = 0;
                $OL_year2 = '';
                $OL_result2 = array();
            }

            $AL_Text1 = AdvanceLevelResultSummary::join('categories', 'categories.id', '=', 'advance_level_result_summaries.stream')
                ->select(
                    'advance_level_result_summaries.*',
                    'categories.category_name'
                )
                ->where('advance_level_result_summaries.reference_no', $app_id)
                ->where('advance_level_result_summaries.attempt', 1)
                ->get();

            if (count($AL_Text1) > 0) {
                foreach ($AL_Text1 as $AL_Text1s) {
                    $AL1_index = $AL_Text1s->index_no;
                    $AL1_year = $AL_Text1s->year;
                    $AL1_stream = $AL_Text1s->category_name;
                }

                $AL1_result = AdvanceLevelResult::where('result_slot_id', $app_id)
                    ->where('attempt', 1)
                    ->get();
            } else {
                $AL1_index = 0;
                $AL1_year = '';
                $AL1_stream = '';
                $AL1_result = array();
            }



            $AL_Text2 = AdvanceLevelResultSummary::join('categories', 'categories.id', '=', 'advance_level_result_summaries.stream')
                ->select(
                    'advance_level_result_summaries.*',
                    'categories.category_name'
                )
                ->where('advance_level_result_summaries.reference_no', $app_id)
                ->where('advance_level_result_summaries.attempt', 2)
                ->get();

            if (count($AL_Text2) > 0) {
                foreach ($AL_Text2 as $AL_Text2s) {
                    $AL2_index = $AL_Text2s->index_no;
                    $AL2_year = $AL_Text2s->year;
                    $AL2_stream = $AL_Text2s->category_name;
                }

                $AL2_result = AdvanceLevelResult::where('result_slot_id', $app_id)
                    ->where('attempt', 2)
                    ->get();
            } else {
                $AL2_index = 0;
                $AL2_year = '';
                $AL2_stream = '';
                $AL2_result = array();
            }

            $AL_Text3 = AdvanceLevelResultSummary::join('categories', 'categories.id', '=', 'advance_level_result_summaries.stream')
                ->select(
                    'advance_level_result_summaries.*',
                    'categories.category_name'
                )
                ->where('advance_level_result_summaries.reference_no', $app_id)
                ->where('advance_level_result_summaries.attempt', 3)
                ->get();

            if (count($AL_Text3) > 0) {
                foreach ($AL_Text3 as $AL_Text3s) {
                    $AL3_index = $AL_Text3s->index_no;
                    $AL3_year = $AL_Text3s->year;
                    $AL3_stream = $AL_Text3s->category_name;
                }

                $AL3_result = AdvanceLevelResult::where('result_slot_id', $app_id)
                    ->where('attempt', 3)
                    ->get();
            } else {
                $AL3_index = 0;
                $AL3_year = '';
                $AL3_stream = '';
                $AL3_result = array();
            }

            $heq_text = nav_higher_eduacation_qulification::join('categories', 'categories.id', '=', 'nav_higher_eduacation_qulifications.type')
                ->select(
                    'nav_higher_eduacation_qulifications.*',
                    'categories.category_name as type__name'
                )
                ->where('nav_higher_eduacation_qulifications.result_slot_id', $app_id)
                ->get();

            $pq_text = nav_professional_qulifications::where('user_id', $app_id)->get();

            $work_text = nav_experience::where('user_id', $app_id)->get();

            $extra_text = DB::table('nav_extra_curricular')
                ->where('user_id', $app_id)
                ->get();
        } else {
            $desig_name = '';
            $title = '';
            $nic = '';
            $initial = '';
            $bDate = '';
            $fName = '';
            $LName = '';
            $civil_status = '';
            $citizen = '';
            $citizen_regNo = '';
            $mobile = '';
            $homeTP = '';
            $email = '';

            $permanent_address_line1 = '';
            $permanent_address_line2 = '';
            $permanent_address_line3 = '';
            $permanent_city = '';

            $postal_address_line1 = '';
            $postal_address_line2 = '';
            $postal_address_line3 = '';
            $postal_city = '';

            $highest_edu = '';

            $OL_index1 = 0;
            $OL_year1 = '';
            $OL_result1 = array();

            $OL_index2 = 0;
            $OL_year2 = '';
            $OL_result2 = array();

            $AL1_index = 0;
            $AL1_year = '';
            $AL1_stream = '';
            $AL1_result = array();

            $AL2_index = 0;
            $AL2_year = '';
            $AL2_stream = '';
            $AL2_result = array();

            $AL3_index = 0;
            $AL3_year = '';
            $AL3_stream = '';
            $AL3_result = array();

            $heq_text = array();
            $pq_text = array();

            $sinhala_exam = '';
            $tamil_exam = '';
            $english_exam = '';

            $work_text = array();
            $extra_text = array();
        }


        $data = [
            'desig_name' => $desig_name,
            'app_id' => $app_id,
            'title' => $title,
            'nic' => $nic,
            'initial' => $initial,
            'bDate' => $bDate,
            'fName' => $fName,
            'LName' => $LName,
            'civil_status' => $civil_status,
            'citizen' => $citizen,
            'citizen_regNo' => $citizen_regNo,
            'mobile' => $mobile,
            'homeTP' => $homeTP,
            'email' => $email,
            'permanent_address_line1' => $permanent_address_line1,
            'permanent_address_line2' => $permanent_address_line2,
            'permanent_address_line3' => $permanent_address_line3,
            'permanent_city' => $permanent_city,
            'postal_address_line1' => $postal_address_line1,
            'postal_address_line2' => $postal_address_line2,
            'postal_address_line3' => $postal_address_line3,
            'postal_city' => $postal_city,
            'highest_edu' => $highest_edu,
            'OL_index1' => $OL_index1,
            'OL_year1' => $OL_year1,
            'OL_result1' => $OL_result1,
            'OL_index2' => $OL_index2,
            'OL_year2' => $OL_year2,
            'OL_result2' => $OL_result2,
            'AL1_index' => $AL1_index,
            'AL1_year' => $AL1_year,
            'AL1_stream' => $AL1_stream,
            'AL1_result' => $AL1_result,
            'AL2_index' => $AL2_index,
            'AL2_year' => $AL2_year,
            'AL2_stream' => $AL2_stream,
            'AL2_result' => $AL2_result,
            'AL3_index' => $AL3_index,
            'AL3_year' => $AL3_year,
            'AL3_stream' => $AL3_stream,
            'AL3_result' => $AL3_result,
            'heq_text' => $heq_text,
            'pq_text' => $pq_text,
            'sinhala_exam' => $sinhala_exam,
            'tamil_exam' => $tamil_exam,
            'english_exam' => $english_exam,
            'work_text' => $work_text,
            'extra_text' => $extra_text
        ];

        $pdf = FacadePdf::loadView('frontend.nonacademic.application.form_components.application_form', $data)->setPaper('a4');

        return $pdf->stream('appicationForm.pdf');
    }

    public function submitPageOpen($id)
    {
        $app_id = decrypt($id);

        return view('frontend.nonacademic.application.form_components.submit_page', compact('app_id'));
    }

    public function removeMulti_page1_open()
    {
        $currentDate = date('Y-m-d');

        $vacancies = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'categories.display_name')
            //  ->where('date_closed', '>=', $currentDate)
            //  ->where('date_opened', '<=', $currentDate)
            ->where('vacancy_visibility_status', '=', 200)
            ->where('vacancy_status_id', '=', 1)
            ->whereIn('vacancy_status_type_id', array(27, 259))
            ->orderBy('vancancy_non_academics.id')
            ->get();
        return view('admin.nonacademic_vacancy.removeMultiple.page1', compact('vacancies'));
    }

    public function removeMulti_page2_open($id)
    {

        $vacancyid = decrypt($id);

        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->select('designations.designation_name')
            ->where('vancancy_non_academics.id', $vacancyid)->first();
        if ($vacancy) {
            $vacancy_name = $vacancy->designation_name;
        } else {
            $vacancy_name = '';
        }

        $pro_text = ApplicationForm::where('vacancy_id', $vacancyid)->where('protect_application', 1)->get();

        $pro_count = count($pro_text);

        $data_text = ApplicationForm::select(
            'id',
            'name_with_initials',
            'last_name',
            'nic',
            'created_at',
            'protect_application'
        )
            ->where('vacancy_id', $vacancyid)
            ->where('protect_application', 0)
            ->orderBy('nic', 'asc')
            ->orderBy('id', 'asc')
            ->get();

        return view('admin.nonacademic_vacancy.removeMultiple.page2', compact('data_text', 'vacancy_name', 'pro_count'));
    }

    public function removeMulti_page3_open($id)
    {

        $appID = decrypt($id);
        // Find the application
        $applicationForm = ApplicationForm::join('categories as t', 't.id', '=', 'application_forms.title')
            ->join('categories as cs', 'cs.id', '=', 'application_forms.civil_status')
            ->select(
                'application_forms.*',
                't.category_name as title_name',
                'cs.category_name as civil_status_name'
            )
            ->where('application_forms.id', $appID)
            ->first();

        if (!$applicationForm) {
            return redirect()->back()->with('error', 'Application form not found.');
        }

        // Get vacancy information with designation name
        $vacancy = VancancyNonAcademic::join('designations', 'vancancy_non_academics.designation_id', '=', 'designations.id')
            ->join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('vancancy_non_academics.*', 'designations.designation_name', 'categories.display_name')
            ->find($applicationForm->vacancy_id);

        // Get O/L results
        $olResults = OrdinaryLevelResult::where('result_slot_id', $applicationForm->id)
            ->where('attempt', 1)
            ->get();

        $olResults2 = OrdinaryLevelResult::where('result_slot_id', $applicationForm->id)
            ->where('attempt', 2)
            ->get();

        // Get O/L summaries
        $olSummary = OrdinaryLevelResultSummary::where('reference_no', $applicationForm->id)
            ->where('attempt', 1)
            ->first();

        $olSummary2 = OrdinaryLevelResultSummary::where('reference_no', $applicationForm->id)
            ->where('attempt', 2)
            ->first();

        // Get A/L results
        $alResults = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)
            ->where('attempt', 1)
            ->get();

        $alResults2 = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)
            ->where('attempt', 2)
            ->get();

        $alResults3 = AdvanceLevelResult::where('result_slot_id', $applicationForm->id)
            ->where('attempt', 3)
            ->get();

        // Get A/L summaries
        $alSummary = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)
            ->where('attempt', 1)
            ->first();

        $alSummary2 = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)
            ->where('attempt', 2)
            ->first();

        $alSummary3 = AdvanceLevelResultSummary::where('reference_no', $applicationForm->id)
            ->where('attempt', 3)
            ->first();

        // Get higher education qualifications
        $higherEducation = nav_higher_eduacation_qulification::where('result_slot_id', $applicationForm->id)->get();

        // Get professional qualifications
        $proQualifications = nav_professional_qulifications::where('user_id', $applicationForm->id)->get();

        // Get experience information
        $experiences = nav_experience::where('user_id', $applicationForm->id)->get();

        // Get extra curricular data
        $extraCurricular = DB::table('nav_extra_curricular')
            ->where('user_id', $applicationForm->id)
            ->first();

        // Get all uploaded documents grouped by category
        $documents = nav_pdf_upload_path::where('user_id', $applicationForm->id)->get();

        $documentsByCategory = [
            'higher_education' => $documents->where('nav_cat_type', 1),
            'professional_qualifications' => $documents->where('nav_cat_type', 2),
            'experience' => $documents->where('nav_cat_type', 3),
            'extra_curricular' => $documents->where('nav_cat_type', 4),
            'public_sector' => $documents->where('nav_cat_type', 5),
        ];

        // Load reference data
        $titles = DB::table('categories')->where('category_type_id', '=', 5)->get();
        $civilStatuses = DB::table('categories')->where('category_type_id', '4')->get();
        $cities = City::all();
        $degreeTypes = DB::table('categories')->where('category_type_id', '16')->get();
        $alStreams = DB::table('categories')->where('category_type_id', '15')->get();

        return view('admin.nonacademic_vacancy.removeMultiple.page3', compact(
            'applicationForm',
            'vacancy',
            'olResults',
            'olResults2',
            'olSummary',
            'olSummary2',
            'alResults',
            'alResults2',
            'alResults3',
            'alSummary',
            'alSummary2',
            'alSummary3',
            'higherEducation',
            'proQualifications',
            'experiences',
            'extraCurricular',
            'documents',
            'documentsByCategory',
            'titles',
            'civilStatuses',
            'cities',
            'degreeTypes',
            'alStreams'
        ));
    }

    /**
     * View a document
     */
    public function viewDocument($id)
    {
        $document = nav_pdf_upload_path::find($id);

        if (!$document) {
            return response()->json(['error' => 'Document not found'], 404);
        }

        $path = public_path($document->file_path);

        if (!file_exists($path)) {
            return response()->json(['error' => 'File not found'], 404);
        }

        return response()->file($path, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="document.pdf"',
            'X-Frame-Options' => 'SAMEORIGIN',
            'X-Content-Type-Options' => 'nosniff',
            'Access-Control-Allow-Origin' => '*'
        ]);
    }

    /**
     * View NIC document
     */

    public function viewNicDocument($id)
    {
        $application = ApplicationForm::find($id);

        if (!$application || !$application->nic_copy) {
            return response()->json(['error' => 'NIC document not found'], 404);
        }

        $path = public_path($application->nic_copy);

        if (!file_exists($path)) {
            return response()->json(['error' => 'NIC file not found'], 404);
        }

        return response()->file($path, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="nic_document.pdf"',
            'X-Frame-Options' => 'SAMEORIGIN',
            'X-Content-Type-Options' => 'nosniff',
            'Access-Control-Allow-Origin' => '*'
        ]);
    }

    public function protect_application($id)
    {
        $appID = decrypt($id);

        $update_text = ApplicationForm::where('id', $appID)->first();

        if ($update_text) {
            $update_text->protect_application = 1;
            $update_text->save();

            $notification = array(
                'message' => 'Update successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('nac.vacancy.remove.multiple.page2.open', encrypt($update_text->vacancy_id))->with($notification);
        } else {
            $notification = array(
                'message' => 'Update unsuccessfully',
                'alert-type' => 'error'
            );

            return redirect()->route('nac.vacancy.remove.multiple.page1.open')->with($notification);
        }
    }

    public function remove_application($id)
    {
        $appID = decrypt($id);

        $update_text = ApplicationForm::where('id', $appID)->first();

        if ($update_text) {
            $update_text->protect_application = 2;
            $update_text->save();

            $notification = array(
                'message' => 'Update successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('nac.vacancy.remove.multiple.page2.open', encrypt($update_text->vacancy_id))->with($notification);
        } else {
            $notification = array(
                'message' => 'Update unsuccessfully',
                'alert-type' => 'error'
            );

            return redirect()->route('nac.vacancy.remove.multiple.page1.open')->with($notification);
        }
    }

    public function ageCalculationModification()
    {
        $data = ApplicationForm::where('protect_application', 1)->get();
        $closingDate = SupportCarbon::parse('2025-05-28');

        foreach ($data as $value) {
            // Clone the date_of_birth to avoid mutating original
            $dob = SupportCarbon::parse($value->date_of_birth);

            // Calculate age components
            $ageYears = $closingDate->diffInYears($dob);
            $dob->addYears($ageYears);

            $ageMonths = $closingDate->diffInMonths($dob);
            $dob->addMonths($ageMonths);

            $ageDays = $closingDate->diffInDays($dob);

            // Format as string, e.g., "25 Years, 3 Months, 12 Days"
            $ageString = "{$ageYears} Years {$ageMonths} Months {$ageDays} Days";

            // Update and save the record
            $value->age_application_date_new = $ageString;
            $value->save();
        }
    }
}



//test
