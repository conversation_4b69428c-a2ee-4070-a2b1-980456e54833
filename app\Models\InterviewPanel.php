<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class InterviewPanel extends Model
{
    use HasFactory,LogsActivity;
    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('hrms_interview_panels')
        ->logOnlyDirty()
        ->dontLogIfAttributesChangedOnly(['updated_at'])
        ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    public function designationName()
    {
        return $this->belongsTo(Designation::class,'designation_id');
    }

    public function InterviewTypeName()
    {
        return $this->belongsTo(Category::class,'interview_type');
    }

    public function MainBranchName()
    {
        return $this->belongsTo(Category::class,'division_id');
    }

    public function addUserName()
    {
        return $this->belongsTo(Employee::class,'user_id');
    }

    public function designations()
    {
    return $this->belongsTo(Designation::class, 'designation_id');
    }

    public function faculties()
    {
    return $this->belongsTo(Faculty::class, 'faculty_id');
    }

    public function departments()
    {
    return $this->belongsTo(Department::class, 'department_id');
    }


}
