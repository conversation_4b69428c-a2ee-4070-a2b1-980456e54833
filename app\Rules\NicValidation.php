<?php

namespace App\Rules;

use App\Models\NewEmployee;
use Illuminate\Contracts\Validation\Rule;

class NicValidation implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $exists = NewEmployee::where('nic', $value)
        ->where('completion_status', '=', 1)
        ->exists();

        return !$exists;
    }

    public function message()
    {
        return 'The NIC has already been taken for an incomplete record.';
    }
}
