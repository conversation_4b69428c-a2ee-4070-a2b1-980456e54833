<?php

namespace App\Http\Controllers\Backend\Promotion;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Designation;
use App\Models\Employee;
use App\Models\Promotion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PromotionController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function index()
    {
        Log::info('PromotionController -> promotion data getAll started');

        return view('admin.promotion.index');

        Log::info('PromotionController -> romotion data getAll ended');
    }

    //Old promotion add page disply
    public function oldPromo(Request $request)
    {

        Log::info('PromotionController -> old promotion data list started');

        if (isset($request->employee_no)) {

            $mainBranch = Auth()->user()->main_branch_id;

            $search_emp_no = $request->employee_no;

            //admin user data collection
            if ($mainBranch == 51) {

                $empfetchDatas = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                    ->select('categories.category_name', 'employees.employee_no', 'employees.initials', 'employees.last_name')
                    ->where('employees.employee_no', $search_emp_no)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->get();

                $row_count = $empfetchDatas->count();

                $emp_promo = Promotion::join('designations', 'promotions.designation_id', '=', 'designations.id')
                    ->join('categories', 'categories.id', '=', 'promotions.type_id')
                    ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                    ->select('promotions.duty_assumed_date', 'designations.designation_name', 'designations.salary_code', 'categories.category_name', 'promotions.basic_salary', 'promotions.descriptions', 'promotions.last_working_date', 'promotions.id', 'gread.category_name as gName')
                    ->where('promotions.employee_no', $search_emp_no)
                    ->orderBy('promotions.duty_assumed_date')
                    ->get();
            } //academic devision data collection
            elseif ($mainBranch == 52) {

                if (Auth()->user()->hasRole(['cc','est-head'])) {

                    $empfetchDatas = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                        ->select('categories.category_name', 'employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();

                } elseif (Auth()->user()->hasRole(['sc'])) {


                    $empfetchDatas = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                        ->select('categories.category_name', 'employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 52)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();

                $emp_promo = Promotion::join('designations', 'promotions.designation_id', '=', 'designations.id')
                    ->join('categories', 'categories.id', '=', 'promotions.type_id')
                    ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                    ->select('promotions.duty_assumed_date', 'designations.designation_name', 'designations.salary_code', 'categories.category_name', 'promotions.basic_salary', 'promotions.descriptions', 'promotions.last_working_date', 'promotions.id', 'gread.category_name as gName')
                    ->where('promotions.employee_no', $search_emp_no)
                    ->orderBy('promotions.duty_assumed_date')
                    ->get();

            } //non academic division data collection
            elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['cc','est-head'])) {

                    $empfetchDatas = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                        ->select('categories.category_name', 'employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                } elseif (Auth()->user()->hasRole(['sc'])) {


                    $empfetchDatas = Employee::join('categories', 'categories.id', '=', 'employees.title_id')
                        ->select('categories.category_name', 'employees.employee_no', 'employees.initials', 'employees.last_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        ->where('employees.main_branch_id', 53)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        ->whereIn('employees.lock', array(0, 2))
                        ->get();
                }

                $row_count = $empfetchDatas->count();

                $emp_promo = Promotion::join('designations', 'promotions.designation_id', '=', 'designations.id')
                    ->join('categories', 'categories.id', '=', 'promotions.type_id')
                    ->join('categories as gread', 'designations.staff_grade', '=', 'gread.id')
                    ->select('promotions.duty_assumed_date', 'designations.designation_name', 'designations.salary_code', 'categories.category_name', 'promotions.basic_salary', 'promotions.descriptions', 'promotions.last_working_date', 'promotions.id', 'gread.category_name as gName')
                    ->where('promotions.employee_no', $search_emp_no)
                    ->orderBy('promotions.duty_assumed_date')
                    ->get();
            }

            /*********************************************************** */

            if ($row_count > 0) {
                foreach ($empfetchDatas as $empData) {
                    $emp_no = $empData->employee_no;
                    $emp_name = $empData->category_name . " " . $empData->initials . " " . $empData->last_name;
                }
            } else {
                $emp_no = '';
                $emp_name = '';

                $notification = array(
                    'message' => 'employee number not found or employee profile lock',
                    'alert-type' => 'error'
                );

                return redirect()->back()->with($notification);
            }

            $pass_employee_no = $request->employee_no;
        } else {
            $pass_employee_no = '';
            $emp_no = '';
            $emp_name = '';
            $emp_promo = array();
        }

        $desig = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('designations.id', 'designations.designation_name', 'categories.category_name', 'designations.salary_code')
            ->where('designations.deleted_at', '=', NULL)
            ->get();

        $promoType = Category::where('category_type_id', '=', '29')->orderBy('sorting_order')->get();

        Log::info('PromotionController -> old promotion data list ended');

        return view('admin.promotion.old_promotions', compact('pass_employee_no', 'emp_no', 'emp_name', 'desig', 'promoType', 'emp_promo'));
    }


    //Old Promotions Add Submit
    public function oldPromoSubmit(Request $requ)
    {

        Log::info('PromotionController -> old promotion submit started');

        if (!isset($requ->emp_no)) {
            $notification = array(
                'message' => 'Please Search Employee Before Submit',
                'alert-type' => 'error'
            );

            return redirect()->route('promotion.old.add')->with($notification);
        }


        $requ->validate([
            'emp_no' => 'required'
        ], [
            'emp_no.required' => 'Please search employee before the enter old promotion data'
        ]);

        if ($requ->promoType != null) {
            for ($i = 0; $i < count($requ->promoType); $i++) {

                $promotion = new Promotion();
                $promotion->employee_no = $requ->emp_no;
                $promotion->type_id = $requ->promoType[$i];
                $promotion->designation_id = $requ->promoDesig[$i];
                $promotion->duty_assumed_date = date("Y-m-d", strtotime($requ->assumedDate[$i]));
                $promotion->last_working_date = date("Y-m-d", strtotime($requ->lastDate[$i]));
                $promotion->basic_salary = $requ->promoBSal[$i];
                $promotion->descriptions = $requ->promoDescrip[$i];
                $promotion->added_user_id = auth()->user()->employee_no;
                $promotion->save();
            }

            Log::notice('PromotionController -> Created employee old promotions data employee number - ' . $requ->emp_no . ' created by ' . auth()->user()->employee_no);
            Log::info('PromotionController -> old employee promotions store ended');

            $notification = array(
                'message' => 'Old Promotion data Inserted Successfully',
                'alert-type' => 'success'
            );

            return redirect()->route('promotion.old.add', ['employee_no' => $requ->emp_no])->with($notification);
        } else {

            $notification = array(
                'message' => 'Old Promotion data Inserted Unsuccessfully',
                'alert-type' => 'error'
            );
            Log::info('PromotionController -> old employee promotions store ended');
            return redirect()->route('promotion.old.add', ['employee_no' => $requ->emp_no])->with($notification);
        }
    }

    public function oldPromotionEdit($id)
    {

        Log::info('PromotionController -> old promotion data edit started');

        $categories = $this->getCategories([29]);
        $serviceTypes = $categories->where('category_type_id', '29');
        $designations = Designation::join('categories', 'designations.staff_grade', '=', 'categories.id')
            ->select('designations.id', 'designations.designation_name', 'categories.category_name', 'designations.salary_code')
            ->where('designations.deleted_at', '=', NULL)
            ->get();
        $editData = Promotion::find($id);

        Log::notice('PromotionController -> edit old promotion data id - ' . $editData->id . ' edited by ' . auth()->user()->employee_no);
        Log::info('PromotionController -> old promotion edit ended');

        return view('admin.promotion.edit', compact('editData', 'serviceTypes', 'designations'));
    }

    public function oldPromotionUpdate(Request $request, $id)
    {

        Log::info('PromotionController -> old promotion data update started');

        $validatedData = $request->validate([
            'type_id' => 'required',
            'designation_id' => 'required',
            'basic_salary' => 'required|numeric',
            'duty_assumed_date' => 'required|date',
            'last_working_date' => 'nullable|date',
        ]);


        $promotion = Promotion::find($id);
        $promotion->employee_no = $request->employee_no;
        $promotion->type_id = $request->type_id;
        $promotion->designation_id = $request->designation_id;
        $promotion->duty_assumed_date = date("Y-m-d", strtotime($request->duty_assumed_date));
        $promotion->last_working_date = date("Y-m-d", strtotime($request->last_working_date));
        $promotion->basic_salary = $request->basic_salary;
        $promotion->descriptions = $request->descriptions;
        $promotion->updated_user_id = auth()->user()->employee_no;
        $promotion->save();

        Log::warning('PromotionController -> update old promotion data employee number - ' . $promotion->employee_no . ' updated by ' . auth()->user()->employee_no);
        Log::info('PromotionController -> old promotion data update ended');

        $notification = array(
            'message' => 'Promotion data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('promotion.old.add', ['employee_no' => $promotion->employee_no])->with($notification);
    }

    public function oldPromotionDelete($id)
    {

        Log::info('PromotionController -> promotion data delete started');

        $promotion = Promotion::find($id);
        $promotion->delete();

        Log::emergency('PromotionController -> delete promotion data employee number - ' . $promotion->employee_no . ' deleted by ' . auth()->user()->employee_no);
        Log::info('PromotionController -> promotion data delete ended');


        $notification = array(
            'message' => 'Promotion data Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('promotion.old.add', ['employee_no' => $promotion->employee_no])->with($notification);
    }
}
