<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Application extends Model
{
    use HasFactory,SoftDeletes,LogsActivity;

    protected $guarded = [];

    public $primaryKey = 'reference_no';

    public $incrementing = false;

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('hrms_applications')
        ->logOnlyDirty()
        ->dontLogIfAttributesChangedOnly(['updated_at'])
        ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    public function designations()
    {
        return $this->belongsTo(Designation::class,'designation_id');
    }

    public function faculties()
    {
        return $this->belongsTo(Faculty::class,'faculty_id');
    }

    public function departments()
    {
        return $this->belongsTo(Department::class,'department_id');
    }

    public function ApplicationDecisionStatus()
    {
        return $this->belongsTo(Category::class,'application_decision_id');
    }

    public function gender()
    {
        return $this->belongsTo(Category::class,'gender_id');
    }

    public function civilStatus()
    {
        return $this->belongsTo(Category::class,'civil_status_id');
    }

    public function race()
    {
        return $this->belongsTo(Category::class,'race_id')->withDefault();
    }

    public function religion()
    {
        return $this->belongsTo(Category::class,'religion_id')->withDefault();
    }

    public function pCity()
    {
        return $this->belongsTo(City::class,'permanent_city_id');
    }

    public function PoCity()
    {
        return $this->belongsTo(City::class,'postal_city_id');
    }

    public function highestEducationalQualification()
    {
        return $this->belongsTo(Category::class,'emp_highest_edu_level');
    }

    public function sorcategoryname()
    {
        return $this->belongsTo(Category::class,'sor_category');
    }

    public function applicationStatusName()
    {
        return $this->belongsTo(Category::class,'application_decision_id');
    }

    public function applicantTitle()
    {
        return $this->belongsTo(Category::class,'titel_id');
    }

    public function citizenships()
    {
        return $this->belongsTo(Category::class,'state_of_citizenship_id');
    }
}
