<?php

namespace App\Http\Controllers\Backend\Leave;

use App\Http\Controllers\Controller;
use App\Imports\ImportLeave;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf as FacadePdf;
use App\Models\Leave;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;

class LeaveController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|sc|lc');
    }
    public function index(Request $request)
    {

        Log::info('LeaveController -> employee leaves data getAll started');

        if (isset($request->employee_no)) {

            $mainBranch = Auth()->user()->main_branch_id;

            $search_emp_no = $request->employee_no;

            if ($mainBranch == 51) {

                $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                    ->where('employees.employee_no', $search_emp_no)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    //->where('employees.promotion_active_status',0)
                    //->where('employees.increment_process_active',0)
                    ->get();

                $row_count = $empfetchDatas->count();

                $emp_leaves = Leave::where('emp_no', $search_emp_no)->orderBy('year', 'DESC')->get();
            } elseif ($mainBranch == 53) {

                if (Auth()->user()->hasRole(['lc','est-head'])) {

                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        //->whereIn('employees.lock', array(0, 2))
                        //->where('employees.promotion_active_status',0)
                        //-->where('employees.increment_process_active',0)
                        ->get();

                } elseif (Auth()->user()->hasRole(['sc'])) {


                    $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                        ->join('designations', 'designations.id', '=', 'employees.designation_id')
                        ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                        ->join('departments', 'departments.id', '=', 'employees.department_id')
                        ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                        ->where('employees.employee_no', $search_emp_no)
                        ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                        //->where('employees.main_branch_id',53)
                        ->where('employees.assign_ma_user_id', auth()->user()->employee_no)
                        //->whereIn('employees.lock', array(0, 2))
                        //->where('employees.promotion_active_status',0)
                        //->where('employees.increment_process_active',0)
                        ->get();
                }

                $row_count = $empfetchDatas->count();

                $emp_leaves = Leave::where('emp_no', $search_emp_no)->orderBy('year', 'DESC')->get();
            }

            /************************************************************************************** */


            //dd( $emp_leaves);

            if ($row_count > 0) {
                foreach ($empfetchDatas as $empData) {
                    $emp_no = $empData->employee_no;
                    $emp_name = $empData->titleName . " " . $empData->initials . " " . $empData->last_name;
                    $designation_name = $empData->designation_name . " " . $empData->gradeName;
                    $department_name = $empData->department_name;
                }
            } else {
                $emp_no = '';
                $emp_name = '';
                $designation_name = '';
                $department_name = '';

                $notification = array(
                    'message' => 'employee number not found or employee profile lock',
                    'alert-type' => 'error'
                );

                return redirect()->back()->with($notification);
            }

            $pass_employee_no = $request->employee_no;
        } else {
            $pass_employee_no = '';
            $emp_no = '';
            $emp_name = '';
            $designation_name = '';
            $department_name = '';
            $emp_leaves = array();
        }

        Log::info('LeaveController -> Employee Leaves data getAll ended');
        return view('admin.leave.old_leave', compact('pass_employee_no', 'emp_no', 'emp_name', 'emp_leaves', 'designation_name', 'department_name'));
    }

    public function bulkDataUploadView()
    {

        Log::info('LeaveController -> employee leaves data bulk upload started');

        return view('admin.leave.bulk_leave');

        Log::info('LeaveController -> employee leaves data bulk upload ended');
    }

    public function edit($id)
    {

        Log::info('LeaveController -> old leave edit started');
        $leaveId = decrypt($id);
        $editData = Leave::find($leaveId);

        Log::notice('LeaveController -> edit old leave data id - ' . $editData->id . ' edited by ' . auth()->user()->employee_no);
        Log::info('LeaveController -> old leave edit ended');

        return view('admin.leave.edit', compact('editData'));
    }

    public function update(Request $request, $id)
    {

        Log::info('LeaveController -> old leave data update started');

        $validatedData = $request->validate([
            'casual' => 'nullable|regex:/^\d+(\.\d{1,1})?$/',
            'sick' => 'nullable|regex:/^\d+(\.\d{1,1})?$/',
            'medical' => 'nullable|regex:/^\d+(\.\d{1,1})?$/',
            'nopay' => 'nullable|regex:/^\d+(\.\d{1,1})?$/',
            'study' => 'nullable|regex:/^\d+(\.\d{1,1})?$/',
        ], [
            'casual.regex' => 'casual leave entered value invalid',
            'sick.regex' => 'sick leave entered value invalid',
            'medical.regex' => 'medical leave entered value invalid',
            'nopay.regex' => 'nopay leave entered value invalid',
            'study.regex' => 'study leave entered value invalid',
        ]);


        $data = Leave::find($id);
        $data->emp_no = $request->emp_no;
        $data->casual = $request->casual;
        $data->sick = $request->sick;
        $data->medical = $request->medical;
        $data->nopay = $request->nopay;
        $data->study = $request->study;
        $data->updated_user_id = Auth()->user()->employee_no;
        $data->updated_at = Carbon::now();
        $data->save();

        Log::warning('LeaveController -> update old leave data employee number - ' . $request->emp_no . ' updated by ' . auth()->user()->employee_no);
        Log::info('LeaveController -> old leave data update ended');

        $notification = array(
            'message' => 'Leave data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('leave.old.index', ['employee_no' => $request->emp_no])->with($notification);
    }

    public function import(Request $request)
    {

        if ($request->hasFile('file')) {

            $request->validate([
                'file' => 'required|mimes:xlsx,xlx,csv',
            ]);

            try {
                Excel::import(new ImportLeave, $request->file('file'));

                $notification = array(
                    'message' => 'Leave data Uploaded Successfully',
                    'alert-type' => 'success'
                );

                return redirect()->route('leave.bulk.index')->with($notification);
            } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
                $failures = $e->failures();
                return view('admin.leave.bulk_leave', compact('failures'));
            }
        }


        $notification = array(
            'message' => 'please select excel file',
            'alert-type' => 'error'
        );

        return redirect()->route('leave.bulk.index')->with($notification);
    }

    public function print(Request $request)
    {

        Log::info('LeaveController -> employee leaves data print started');

        if (isset($request->employee_no)) {

            $search_emp_no = $request->employee_no;

            $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                    ->where('employees.employee_no', $search_emp_no)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->get();

                $row_count = $empfetchDatas->count();

                $emp_leaves = Leave::where('emp_no', $search_emp_no)->orderBy('year', 'DESC')->get();

            /************************************************************************************** */

            if ($row_count > 0) {
                foreach ($empfetchDatas as $empData) {
                    $emp_no = $empData->employee_no;
                    $emp_name = $empData->titleName . " " . $empData->initials . " " . $empData->last_name;
                    $designation_name = $empData->designation_name . " " . $empData->gradeName;
                    $department_name = $empData->department_name;
                }
            } else {
                $emp_no = '';
                $emp_name = '';
                $designation_name = '';
                $department_name = '';

                $notification = array(
                    'message' => 'employee number not found',
                    'alert-type' => 'error'
                );

                return redirect()->back()->with($notification);
            }

            $pass_employee_no = $request->employee_no;

        } else {
            $pass_employee_no = '';
            $emp_no = '';
            $emp_name = '';
            $designation_name = '';
            $department_name = '';
            $emp_leaves = array();
        }

        Log::info('LeaveController -> Employee Leaves data print ended');
        return view('admin.leave.leave_print', compact('pass_employee_no', 'emp_no', 'emp_name', 'emp_leaves', 'designation_name', 'department_name'));
    }

    public function dataPrint($id){

        $empNo = decrypt($id);

        $search_emp_no = $empNo;

            $empfetchDatas = Employee::join('categories as title', 'title.id', '=', 'employees.title_id')
                    ->join('designations', 'designations.id', '=', 'employees.designation_id')
                    ->join('categories as grade', 'designations.grade_id', '=', 'grade.id')
                    ->join('departments', 'departments.id', '=', 'employees.department_id')
                    ->select('title.category_name as titleName', 'employees.employee_no', 'employees.initials', 'employees.last_name', 'designations.designation_name', 'grade.category_name as gradeName', 'departments.department_name')
                    ->where('employees.employee_no', $search_emp_no)
                    ->where('employees.employee_no', '!=', auth()->user()->employee_no)
                    ->get();

        $row_count = $empfetchDatas->count();

        $emp_leaves = Leave::where('emp_no', $search_emp_no)->orderBy('year', 'DESC')->take(12)->get();

        if ($row_count > 0) {
            foreach ($empfetchDatas as $empData) {
                $emp_no = $empData->employee_no;
                $emp_name = $empData->titleName . " " . $empData->initials . " " . $empData->last_name;
                $designation_name = $empData->designation_name . " " . $empData->gradeName;
                $department_name = $empData->department_name;
            }
        }

        $data = [
            'emp_no' => $emp_no,
            'emp_name' => $emp_name,
            'designation_name' => $designation_name,
            'department_name' => $department_name,
            'emp_leaves' => $emp_leaves,

        ];

        $pdf = FacadePdf::loadView('admin.leave.leave_summary', $data)->setPaper('a4');

        return $pdf->stream($emp_no.' - leave summary report.pdf');

    }
}
