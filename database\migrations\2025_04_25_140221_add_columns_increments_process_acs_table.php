<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('increments_process_acs', function (Blueprint $table) {
            $table->integer('salary_scale_version_id')->nullable()->after('new_sal_step');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('increments_process_acs', function (Blueprint $table) {
            $table->dropColumn('salary_scale_version_id');
        });
    }
};
