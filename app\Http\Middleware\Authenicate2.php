<?php

namespace App\Http\Middleware;

use App\Models\LoginHistory;
use Closure;
use Exception;
use Illuminate\Console\View\Components\Alert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class Authenicate2
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        //return $next($request);
        $access_token = $request->session()->get('access_token');
        $response = Http::withHeaders([
            "Accept" => "application/json",
            "Authorization" => "Bearer " . $access_token
        ])->get("https://usjnetsso.sjp.ac.lk/sso" .  "/api/availability");

        try {
            response($response["message"]);
            try {
                LoginHistory::where('user_id', '=', auth()->user()->id)->orderBy('created_at', 'desc')->limit(1)->update([
                    'logout_time' => now(),
                    'updated_at' => now(),
                ]);
            } catch (Exception $ex) {
            }
            //dd("auth");
            return redirect('/sso/login');
        } catch (Exception $ex) {
            return $next($request);
        }
    }
}
