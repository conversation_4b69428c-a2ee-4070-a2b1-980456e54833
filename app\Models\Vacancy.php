<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Vacancy extends Model
{
    use HasFactory,SoftDeletes,LogsActivity;

    protected $dates = ['deleted_at'];
    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('hrms_vacancies')
        ->logOnlyDirty()
        ->dontLogIfAttributesChangedOnly(['updated_at'])
        ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    public function designations()
    {
        return $this->belongsTo(Designation::class,'designation_id');
    }

    public function faculties()
    {
        return $this->belongsTo(Faculty::class,'faculty_id');
    }

    public function departments()
    {
        return $this->belongsTo(Department::class,'department_id');
    }

    public function visibility()
    {
        return $this->belongsTo(Category::class,'vacancy_visibility_status');
    }

    public function vacancyCategory()
    {
        return $this->belongsTo(Category::class,'main_category_id');
    }

    public function vacancyStatusType()
    {
        return $this->belongsTo(Category::class,'vacancy_status_type_id');
    }

    public function faculty()
    {
        return $this->belongsTo(Faculty::class, 'faculty_id', 'id');
    }

    public function department()
    {
        return $this->belongsTo(Faculty::class, 'department_id', 'id');
    }

    public function operatorName()
    {
        return $this->belongsTo(Employee::class,'published_user_id')->with('category');
    }

    public function officerName()
    {
        return $this->belongsTo(Employee::class,'updated_user_id')->with('category');
    }

    public function minQulificationName()
    {
        return $this->belongsTo(Category::class,'min_qualification');
    }

    public function vacancyExtenderName()
    {
        return $this->belongsTo(Employee::class,'deadline_extented_emp')->with('category');
    }

    public function AdminOfficerName()
    {
        return $this->belongsTo(Employee::class,'admin_officer_check_emp')->with('category');
    }

    public function DepartmentHeadName()
    {
        return $this->belongsTo(Employee::class, 'dept_head_check_emp')->with('category');
    }


}
