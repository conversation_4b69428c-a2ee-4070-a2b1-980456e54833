<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Http\Request;

class CommonAPIController extends Controller
{
    public function nicDataGet(Request $request)
    {

        $verifiedUsernic = $request->input('nic');

        if (mb_strlen($verifiedUsernic) == 10) {

            $part01 = substr($verifiedUsernic, 0, 2); //Birth by year

            $part02 = substr($verifiedUsernic, 2, 3); //Birth day of the year

            $part03 = substr($verifiedUsernic, 5, 3); //Serial number

            $part04 = substr($verifiedUsernic, 8, 1); //Check digit

            $part05 = substr($verifiedUsernic, 9, 1); //Special letter

            $currentYear = Carbon::now()->year % 100;

            if ($part01 < $currentYear) {

                $fullYear = '20' . $part01;
            } else {

                $fullYear = '19' . $part01;
            }

            if ((($fullYear % 4 == 0) && ($fullYear % 100 != 0)) || ($fullYear % 400 == 0)) {

                //$isLeapYear = "Leap Year";
                if ($part02 >= 1 && $part02 <= 366) {

                    $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 1);
                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    $dateOfBirth = Carbon::createFromFormat('Y-m-d', $dob);
                    $currentDate = Carbon::now();

                    // Calculate the difference in years, months, and days
                    $ageYears = $currentDate->diffInYears($dateOfBirth);
                    $ageMonths = $currentDate->diffInMonths($dateOfBirth->addYears($ageYears));
                    $ageDays = $currentDate->diffInDays($dateOfBirth->addMonths($ageMonths));

                    $gender = 1;
                    $oldnic = $verifiedUsernic;
                    $newnic = $fullYear . $part02 . str_pad($part03, 4, '0', STR_PAD_LEFT) . $part04;
                    $activenic = 1;

                    return json_encode([
                        'dob' => $dob,
                        'age' => [
                            'years' => $ageYears,
                            'months' => $ageMonths,
                            'days' => $ageDays
                        ],
                        'gender' => $gender,
                        'oldnic' => $oldnic,
                        'newnic' => $newnic,
                        'activenic' => $activenic
                    ]);

                } else if ($part02 >= 501 && $part02 <= 866) {

                    $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 501);
                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    $dateOfBirth = Carbon::createFromFormat('Y-m-d', $dob);
                    $currentDate = Carbon::now();

                    // Calculate the difference in years, months, and days
                    $ageYears = $currentDate->diffInYears($dateOfBirth);
                    $ageMonths = $currentDate->diffInMonths($dateOfBirth->addYears($ageYears));
                    $ageDays = $currentDate->diffInDays($dateOfBirth->addMonths($ageMonths));

                    $gender = 2;
                    $oldnic = $verifiedUsernic;
                    $newnic = $fullYear . $part02 . str_pad($part03, 4, '0', STR_PAD_LEFT) . $part04;
                    $activenic = 1;

                    return json_encode([
                        'dob' => $dob,
                        'age' => [
                            'years' => $ageYears,
                            'months' => $ageMonths,
                            'days' => $ageDays
                        ],
                        'gender' => $gender,
                        'oldnic' => $oldnic,
                        'newnic' => $newnic,
                        'activenic' => $activenic
                    ]);

                } else {

                    return json_encode([
                        'message' => 'Invalid NIC Number'
                    ]);

                }
            } else {

                //$isLeapYear = "Not a Leap Year";
                if ($part02 >= 1 && $part02 <= 366) {

                    if ($part02 >= 1 && $part02 <= 59) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 1);
                    } else if ($part02 == 60) {

                        return json_encode([
                            'message' => 'Invalid NIC Number'
                        ]);

                    } else if ($part02 >= 61 && $part02 <= 366) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 2);
                    }


                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    $dateOfBirth = Carbon::createFromFormat('Y-m-d', $dob);
                    $currentDate = Carbon::now();

                    // Calculate the difference in years, months, and days
                    $ageYears = $currentDate->diffInYears($dateOfBirth);
                    $ageMonths = $currentDate->diffInMonths($dateOfBirth->addYears($ageYears));
                    $ageDays = $currentDate->diffInDays($dateOfBirth->addMonths($ageMonths));

                    $gender = 1;
                    $oldnic = $verifiedUsernic;
                    $newnic = $fullYear . $part02 . str_pad($part03, 4, '0', STR_PAD_LEFT) . $part04;
                    $activenic = 1;

                    return json_encode([
                        'dob' => $dob,
                        'age' => [
                            'years' => $ageYears,
                            'months' => $ageMonths,
                            'days' => $ageDays
                        ],
                        'gender' => $gender,
                        'oldnic' => $oldnic,
                        'newnic' => $newnic,
                        'activenic' => $activenic
                    ]);

                } else if ($part02 >= 501 && $part02 <= 866) {

                    if ($part02 >= 501 && $part02 <= 559) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 501);
                    } else if ($part02 == 560) {

                        return json_encode([
                            'message' => 'Invalid NIC Number'
                        ]);

                    } else if ($part02 >= 561 && $part02 <= 866) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 502);
                    }


                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    $dateOfBirth = Carbon::createFromFormat('Y-m-d', $dob);
                    $currentDate = Carbon::now();

                    // Calculate the difference in years, months, and days
                    $ageYears = $currentDate->diffInYears($dateOfBirth);
                    $ageMonths = $currentDate->diffInMonths($dateOfBirth->addYears($ageYears));
                    $ageDays = $currentDate->diffInDays($dateOfBirth->addMonths($ageMonths));

                    $gender = 2;
                    $oldnic = $verifiedUsernic;
                    $newnic = $fullYear . $part02 . str_pad($part03, 4, '0', STR_PAD_LEFT) . $part04;
                    $activenic = 1;

                    return json_encode([
                        'dob' => $dob,
                        'age' => [
                            'years' => $ageYears,
                            'months' => $ageMonths,
                            'days' => $ageDays
                        ],
                        'gender' => $gender,
                        'oldnic' => $oldnic,
                        'newnic' => $newnic,
                        'activenic' => $activenic
                    ]);

                } else {

                    return json_encode([
                        'message' => 'Invalid NIC Number'
                    ]);
                }
            }


            /******************************************************************************************** */
        } elseif (mb_strlen($verifiedUsernic) == 12) {

            $fullYear = substr($verifiedUsernic, 0, 4); //Birth by year

            $part02 = substr($verifiedUsernic, 4, 3); //Birth day of the year

            $part03 = substr($verifiedUsernic, 7, 4); //Serial number

            $part04 = substr($verifiedUsernic, 11, 1); //Check digit


            if ((($fullYear % 4 == 0) && ($fullYear % 100 != 0)) || ($fullYear % 400 == 0)) {

                //$isLeapYear = "Leap Year";
                if ($part02 >= 1 && $part02 <= 366) {

                    $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 1);
                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    $dateOfBirth = Carbon::createFromFormat('Y-m-d', $dob);
                    $currentDate = Carbon::now();

                    // Calculate the difference in years, months, and days
                    $ageYears = $currentDate->diffInYears($dateOfBirth);
                    $ageMonths = $currentDate->diffInMonths($dateOfBirth->addYears($ageYears));
                    $ageDays = $currentDate->diffInDays($dateOfBirth->addMonths($ageMonths));

                    $gender = 1;
                    $oldnic = substr($fullYear, 2, 2) . $part02 . substr($part03, 1, 3) . $part04 . 'V';
                    $newnic = $verifiedUsernic;
                    $activenic = 2;

                    return json_encode([
                        'dob' => $dob,
                        'age' => [
                            'years' => $ageYears,
                            'months' => $ageMonths,
                            'days' => $ageDays
                        ],
                        'gender' => $gender,
                        'oldnic' => $oldnic,
                        'newnic' => $newnic,
                        'activenic' => $activenic
                    ]);

                } else if ($part02 >= 501 && $part02 <= 866) {

                    $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 501);
                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    $dateOfBirth = Carbon::createFromFormat('Y-m-d', $dob);
                    $currentDate = Carbon::now();

                    // Calculate the difference in years, months, and days
                    $ageYears = $currentDate->diffInYears($dateOfBirth);
                    $ageMonths = $currentDate->diffInMonths($dateOfBirth->addYears($ageYears));
                    $ageDays = $currentDate->diffInDays($dateOfBirth->addMonths($ageMonths));

                    $gender = 2;
                    $oldnic = substr($fullYear, 2, 2) . $part02 . substr($part03, 1, 3) . $part04 . 'V';
                    $newnic = $verifiedUsernic;
                    $activenic = 2;

                    return json_encode([
                        'dob' => $dob,
                        'age' => [
                            'years' => $ageYears,
                            'months' => $ageMonths,
                            'days' => $ageDays
                        ],
                        'gender' => $gender,
                        'oldnic' => $oldnic,
                        'newnic' => $newnic,
                        'activenic' => $activenic
                    ]);

                } else {

                    return json_encode([
                        'message' => 'Invalid NIC Number'
                    ]);
                }
            } else {

                //$isLeapYear = "Not a Leap Year";
                if ($part02 >= 1 && $part02 <= 366) {

                    if ($part02 >= 1 && $part02 <= 59) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 1);
                    } else if ($part02 == 60) {

                        return json_encode([
                            'message' => 'Invalid NIC Number'
                        ]);

                    } else if ($part02 >= 61 && $part02 <= 366) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 2);
                    }


                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    $dateOfBirth = Carbon::createFromFormat('Y-m-d', $dob);
                    $currentDate = Carbon::now();

                    // Calculate the difference in years, months, and days
                    $ageYears = $currentDate->diffInYears($dateOfBirth);
                    $ageMonths = $currentDate->diffInMonths($dateOfBirth->addYears($ageYears));
                    $ageDays = $currentDate->diffInDays($dateOfBirth->addMonths($ageMonths));

                    $gender = 1;
                    $oldnic = substr($fullYear, 2, 2) . $part02 . substr($part03, 1, 3) . $part04 . 'V';
                    $newnic = $verifiedUsernic;
                    $activenic = 2;

                    return json_encode([
                        'dob' => $dob,
                        'age' => [
                            'years' => $ageYears,
                            'months' => $ageMonths,
                            'days' => $ageDays
                        ],
                        'gender' => $gender,
                        'oldnic' => $oldnic,
                        'newnic' => $newnic,
                        'activenic' => $activenic
                    ]);

                } else if ($part02 >= 501 && $part02 <= 866) {

                    if ($part02 >= 501 && $part02 <= 559) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 501);
                    } else if ($part02 == 560) {

                        return json_encode([
                            'message' => 'Invalid NIC Number'
                        ]);

                    } else if ($part02 >= 561 && $part02 <= 866) {

                        $date = Carbon::createFromDate($fullYear, 1, 1)->addDays($part02 - 502);
                    }


                    $month = $date->month;
                    $day = $date->day;

                    $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);  // Format month as two digits
                    $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);      // Format day as two digits

                    $formattedDate = "$fullYear-$month-$day";
                    $dob = Carbon::createFromFormat('Y-m-d', $formattedDate)->format('Y-m-d');

                    $dateOfBirth = Carbon::createFromFormat('Y-m-d', $dob);
                    $currentDate = Carbon::now();

                    // Calculate the difference in years, months, and days
                    $ageYears = $currentDate->diffInYears($dateOfBirth);
                    $ageMonths = $currentDate->diffInMonths($dateOfBirth->addYears($ageYears));
                    $ageDays = $currentDate->diffInDays($dateOfBirth->addMonths($ageMonths));

                    $gender = 2;
                    $oldnic = substr($fullYear, 2, 2) . $part02 . substr($part03, 1, 3) . $part04 . 'V';
                    $newnic = $verifiedUsernic;
                    $activenic = 2;

                    return json_encode([
                        'dob' => $dob,
                        'age' => [
                            'years' => $ageYears,
                            'months' => $ageMonths,
                            'days' => $ageDays
                        ],
                        'gender' => $gender,
                        'oldnic' => $oldnic,
                        'newnic' => $newnic,
                        'activenic' => $activenic
                    ]);

                } else {

                    return json_encode([
                        'message' => 'Invalid NIC Number'
                    ]);
                }
            }
        } else {

            return json_encode([
                'message' => 'Invalid NIC Number'
            ]);
        }
    }
}
