<?php

namespace App\Console\Commands;

use App\Mail\TemporyEmployeeExpirationMail;
use App\Models\Department;
use App\Models\DepartmentHead;
use App\Models\Employee;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class TemporyEmployeeExpirationCron extends Command
{

    protected $signature = 'temporyexp:cron';

    protected $description = 'Tempory employee expiration notification send head';

    public function handle()
    {
        $dataAfterMonth = date('Y-m-d', strtotime('+1 month'));

        $employees = Employee::whereDate('salary_termination_date_1', '=', $dataAfterMonth)
                     ->whereIn('employee_work_type',[140, 141, 142])
                     ->where('employee_status_id', 110)
                     ->get();

        foreach ($employees as $employee) {

            $EmpNo = $employee->employee_no;
            $departmentId = $employee->department_id;

            if (App::environment('production')) {

                $departmentHeadEmail = $this->getDepartmentHeadEmail($departmentId);

            } else {

                $departmentHeadEmail = '<EMAIL>';
            }

            $emailData = [
                'employeeName' => $this->getEmployeeName($EmpNo),
                'AppointmentType' => $this->getAppointmentType($EmpNo),
                'DepartmentName' => $this->getDepartmentName($departmentId),
                'MainBranch' => $this->getMainBranch($EmpNo),
                'TerminationDate' => $employee->salary_termination_date_1,
                'DepartmentHeadName' => $this->getDepartmentHeadName($departmentId),
            ];

            $mail = new TemporyEmployeeExpirationMail($emailData);

            if (!empty($departmentHeadEmail)) {
                Mail::to($departmentHeadEmail)->send($mail);
            } else {
                Log::error("Department head email is missing for Department ID: " . $departmentId);
            }
        }

        return Command::SUCCESS;
    }

    private function getDepartmentHeadEmail($deptId){

        $departmentHead = Department::where('id', $deptId)->select('head_email')->first();

        return $departmentHead ? $departmentHead->head_email : null;

    }

    private function getEmployeeName($empNo){

        $employeeName = Employee::join('categories', 'employees.title_id', '=', 'categories.id')->where('employee_no', $empNo)->select('initials','last_name','category_name')->first();

        return $employeeName ? $employeeName->category_name . ' ' . $employeeName->initials . ' ' . $employeeName->last_name : null;

    }

    private function getAppointmentType($empNo){

        $appointmentType = Employee::join('categories', 'employees.employee_work_type', '=', 'categories.id')->where('employee_no', $empNo)->select('category_name')->first();

        return $appointmentType ? $appointmentType->category_name : null;
    }

    private function getDepartmentName($deptId){

        $departmentName = Department::where('id', $deptId)->select('department_name')->first();

        return $departmentName ? $departmentName->department_name : null;
    }


    private function getMainBranch($empNo){

        $mainBranch = Employee::where('employee_no', $empNo)->select('main_branch_id')->first();

        return $mainBranch ? $mainBranch->main_branch_id : null;
    }

    private function getDepartmentHeadName($deptId){

        $departmentHead = DepartmentHead::where('department_id', $deptId)->select('emp_no')->first();
        $departmentHeadName = Employee::join('categories', 'employees.title_id', '=', 'categories.id')->where('employee_no', $departmentHead->emp_no)->select('initials','last_name','category_name')->first();

        return $departmentHeadName ? $departmentHeadName->category_name . ' ' . $departmentHeadName->initials . ' ' . $departmentHeadName->last_name : null;

    }

}
