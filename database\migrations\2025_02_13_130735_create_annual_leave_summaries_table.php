<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('annual_leave_summaries', function (Blueprint $table) {
            $table->id();
            $table->integer('dept_code')->default(0);
            $table->integer('faculty_code')->default(0);
            $table->integer('month_code')->default(0);
            $table->integer('process_stage')->default(0);
            
            $table->integer('head_empNo')->default(0);
            $table->integer('head_status')->default(0);
            $table->string('head_remark')->nullable();
            $table->date('head_forward_date')->nullable();
            $table->date('head_reversal_date')->nullable();

            $table->integer('executive_empNo')->default(0);
            $table->integer('executive_status')->default(0);
            $table->string('executive_remark')->nullable();
            $table->date('executive_forward_date')->nullable();

            $table->integer('leave_clerk_empNo')->default(0);
            $table->integer('leave_clerk_status')->default(0);
            $table->string('leave_clerk_remark')->nullable();
            $table->date('leave_clerk_forward_date')->nullable();

            $table->integer('dr_empNo')->default(0);
            $table->integer('dr_status')->default(0);
            $table->string('dr_remark')->nullable();
            $table->date('dr_finalize_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('annual_leave_summaries');
    }
};
