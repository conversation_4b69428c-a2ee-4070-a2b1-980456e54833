<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Employee extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $dates = ['deleted_at'];

    protected $guarded = [];

    public $primaryKey = 'employee_no';

    public $incrementing = false;

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*'])
            ->useLogName('hrms_employees')
            ->logOnlyDirty()
            ->dontLogIfAttributesChangedOnly(['updated_at'])
            ->dontSubmitEmptyLogs();
        // Chain fluent methods for configuration options
    }

    public function mainBranch()
    {
        return $this->belongsTo(Category::class, 'main_branch_id');
    }

    public function designationName()
    {
        return $this->belongsTo(Designation::class, 'designation_id');
    }

    public function genderName()
    {
        return $this->belongsTo(Category::class, 'gender_id');
    }

    public function civilStatusName()
    {
        return $this->belongsTo(Category::class, 'civil_status_id');
    }

    public function raceName()
    {
        return $this->belongsTo(Category::class, 'race_id')->withDefault();
    }

    public function religionName()
    {
        return $this->belongsTo(Category::class, 'religion_id')->withDefault();
    }

    public function citizenshipeStateName()
    {
        return $this->belongsTo(Category::class, 'state_of_citizenship_id');
    }

    public function higheduName()
    {
        return $this->belongsTo(Category::class, 'emp_highest_edu_level');
    }

    public function empStatusName()
    {
        return $this->belongsTo(Category::class, 'employee_status_id');
    }

    public function empStatusTypeName()
    {
        return $this->belongsTo(Category::class, 'employee_status_type_id');
    }

    public function workTypeName()
    {
        return $this->belongsTo(Category::class, 'employee_work_type');
    }

    public function empDecisionName()
    {
        return $this->belongsTo(Category::class, 'emp_decision_id');
    }

    public function permanentCityName()
    {

        return $this->belongsTo(City::class, 'permanent_city_id');
    }

    public function postalCityName()
    {

        return $this->belongsTo(City::class, 'postal_city_id');
    }

    public function getNameTitle()
    {
        return $this->belongsTo(Category::class, 'title_id');
    }

    public function getDepartmentName()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

    public function getAddedOperator()
    {
        return $this->belongsTo(Employee::class, 'added_ma_user_id');
    }

    public function getUpdatedOperator()
    {
        return $this->belongsTo(Employee::class, 'updated_ma_user_id');
    }

    public function getAssignOperator()
    {
        return $this->belongsTo(Employee::class, 'assign_ma_user_id');
    }

    public function getFacultyName()
    {
        return $this->belongsTo(Faculty::class, 'faculty_id');
    }

    public function designation()
    {
        return $this->belongsTo(Designation::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'title_id'); // Assuming 'category_id' is the foreign key in the employees table that links to the categories table
    }

    public function getPaymentMethod()
    {
        return $this->belongsTo(Category::class, 'salary_payment_type');
    }

    public function desigantions()
    {
        return $this->hasOne(Designation::class, 'id', 'designation_id');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($employee) {
            if ($employee->employee_status_id == 110) {
                $exists = self::where('nic', $employee->nic)
                    ->where('employee_status_id', 110)
                    ->exists();

                if ($exists) {
                    throw new \Exception('Duplicate NIC with employee_status_id = 110');
                }
            }
        });

        static::updating(function ($model) {

            if ($user = Auth()->user()) {
                $original = $model->getOriginal();
                $newModel = new EmployeeHistory();
                $newModel->fill($original);
                $newModel->updated_ma_user_id = Auth()->user()->employee_no;
                $newModel->updated_ma_date =  date('Y-m-d');
                $newModel->updated_at = now();
                $newModel->save();
            }
        });

        static::updated(function ($employee) {
            // Check if any relevant fields were updated
            if ($employee->isDirty(['file_reference_number', 'main_branch_id', 'designation_id', 'faculty_id', 'department_id', 'sub_department_id', 'initials', 'name_denoted_by_initials', 'last_name', 'civil_status_id', 'gender_id', 'race_id', 'religion_id', 'permanent_add1', 'permanent_add2', 'permanent_add3', 'permanent_city_id', 'postal_add1', 'postal_add2', 'postal_add3', 'postal_city_id', 'email', 'personal_email', 'state_of_citizenship_id', 'citizen_registration_no', 'initial_appointment_date', 'gratuity_cal_date', 'current_appointment_date', 'salary_termination_date_1', 'salary_termination_date_2', 'retirement_date', 'current_basic_salary', 'increment_date', 'confirmation_date', 'sabbatical_start', 'sabbatical_end', 'etf_no', 'upf_no', 'pension_reference_no', 'emp_highest_edu_level', 'assign_ma_user_id', 'assign_ma_date', 'status_id', 'employee_status_id', 'employee_status_type_id', 'employee_work_type', 'emp_decision_id', 'mobile_no', 'telephone_no', 'nic', 'date_of_birth', 'title_id', 'promo_eligibility', 'lock', 'promotion_active_status', 'user_account_status', 'salary_payment_type', 'pension_status', 'salary_status'])) {
                // Update the "employee_salary_statuses" table
                $employee->updateSalaryStatus();
            }
        });
    }

    public function updateSalaryStatus()
    {
        // Get the related "employee_salary_status" record
        $statusRecord = EmployeeSalaryStatus::where('employee_no', $this->employee_no)->first();

        if ($statusRecord) {
            if ($statusRecord->status == 0) {
                // Update the status to 2 and "last_updated_date" to today's date
                $statusRecord->status = 2;
                $statusRecord->last_updated_date = now();
            } elseif ($statusRecord->status == 1) {
                // Update the status to 3 and "last_updated_date" to today's date
                $statusRecord->status = 3;
                $statusRecord->last_updated_date = now();
            } elseif ($statusRecord->status == 2) {
                // Update the status to 3 and "last_updated_date" to today's date
                //$statusRecord->status = 3;
                $statusRecord->last_updated_date = now();
            } elseif ($statusRecord->status == 3) {
                // Update the status to 3 and "last_updated_date" to today's date
                //$statusRecord->status = 3;
                $statusRecord->last_updated_date = now();
            }



            $statusRecord->save();
        }
    }
}
