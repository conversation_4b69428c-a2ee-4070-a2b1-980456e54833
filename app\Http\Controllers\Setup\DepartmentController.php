<?php

namespace App\Http\Controllers\Setup;

use App\Http\Controllers\Controller;
use App\Models\Department;
use App\Models\Faculty;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class DepartmentController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head|cc|sc');
    }

    public function departmentIndex()
    {
        Log::info('DepartmentController -> department index started');
        $departments = Department::all();
        $trashDepartments = Department::onlyTrashed()->get();
        return view('admin.setups.department.index', compact('departments', 'trashDepartments'));

        Log::notice('DepartmentController -> department Count - ' . $departments->count());
        Log::info('DepartmentController -> department index ended');
    }

    public function departmentAdd()
    {
        Log::info('DepartmentController -> department add started');

        $faculties = Faculty::all();
        return view('admin.setups.department.add', compact('faculties'));
        Log::info('DepartmentController -> department add ended');
    }

    public function departmentStore(Request $request)
    {
        Log::info('DepartmentController -> department store started');

        $validatedData = $request->validate([
            'department_name' => 'required|unique:departments,department_name',
            'faculty_code' => 'required',
            'name_status' => 'required',
            'is_faculty_office' => 'required'
        ]);

        $count =  DB::table('departments')
                 ->where('faculty_code',$request->faculty_code)
                 ->count();
        //dd($count);

        if($count > 0){

            //get next table id
        $maxnumber = DB::table('departments')
                     ->where('faculty_code',$request->faculty_code)
                     ->select(DB::raw('MAX(id) as value'))
                     ->get();

        $maxValue = json_decode($maxnumber, true);

        $nextId = $maxValue[0]["value"] + 1;

        }else{

            $nextId = $request->faculty_code.'01';

        }

         //dd($nextId);
        $data = new Department();
        $data->id = $nextId;
        $data->department_name = $request->department_name;
        $data->faculty_code = $request->faculty_code;
        $data->name_status = $request->name_status;
        $data->is_faculty_office = $request->is_faculty_office;
        $data->created_at = Carbon::now();
        $data->save();

        Log::notice('DepartmentController -> Created department id - ' . $data->id . ' created by ' . auth()->user()->id);
        Log::info('DepartmentController -> department create ended');

        $notification = array(
            'message' => 'New Department Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('department.index')->with($notification);
    }

    public function departmentEdit($id)
    {
        Log::info('DepartmentController -> department edit started');
        $editData = Department::find($id);
        $faculties = Faculty::all();
        return view('admin.setups.department.edit', compact('editData', 'faculties'));
        Log::notice('DepartmentController -> edit department id - ' . $editData->id . ' edited by ' . auth()->user()->id);
        Log::info('DepartmentController -> department edit ended');
    }

    public function departmentUpdate(Request $request, $id)
    {

        Log::info('DepartmentController -> department update started');
        $validatedData = $request->validate([
            //'department_code' => ['required', Rule::unique('departments')->ignore($id)],
            'department_name' => ['required', Rule::unique('departments')->ignore($id)],
            'name_status' => 'required',
            'is_faculty_office' => 'required'
        ]);


        $data = Department::find($id);
        $data->department_name = $request->department_name;
        $data->name_status = $request->name_status;
        $data->is_faculty_office = $request->is_faculty_office;
        $data->updated_at = Carbon::now();
        $data->save();

        Log::notice('DepartmentController -> update department id - ' . $data->id . ' updated by ' . auth()->user()->id);
        Log::info('DepartmentController -> department update ended');

        $notification = array(
            'message' => 'department data Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('department.index')->with($notification);
    }

    public function departmentSoftdelete($id)
    {

        Log::info('DepartmentController -> department soft delete started');
        $department = Department::find($id);
        $department->delete();

        $notification = array(
            'message' => 'Department Deleted Successfully',
            'alert-type' => 'warning'
        );

        return redirect()->route('department.index')->with($notification);

        Log::notice('DepartmentController -> soft delete department id - ' . $department->id . ' deleted by ' . auth()->user()->id);
        Log::info('DepartmentController -> Department soft delete ended');
    }

    public function departmentRestore($id)
    {

        Log::info('DepartmentController -> department restore started');

        $department = Department::withTrashed()->find($id);
        $department->restore();

        $notification = array(
            'message' => 'Department Restore Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('department.index')->with($notification);

        Log::notice('DepartmentController -> restore Department id - ' . $department->id . ' deleted by ' . auth()->user()->id);
        Log::info('DepartmentController -> Department restore ended');
    }

    public function departmentDelete($id)
    {

        Log::info('DepartmentController -> department delete started');

        $department = Department::onlyTrashed()->find($id);
        $department->forceDelete();

        $notification = array(
            'message' => 'Department Permenetly Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('department.index')->with($notification);

        Log::emergency('DepartmentController -> delete Department id - ' . $department->id . ' deleted by ' . auth()->user()->id);
        Log::info('DepartmentController -> Department delete ended');
    }
}
