<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\DB;

class UserRegistrationExistance implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }


    public function passes($attribute, $value)
    {
        return DB::table('applicant_verifications')
               ->where('vacancy_id',session()->get('vacancy_id'))
               ->where('nic',$value)
               //->where('email',$value)
               ->where('status_id', 1)
               ->count() != 1;
        
    }

    public function message()
    {
        return 'You are already register for this position you can modify your application using edit and submit option in home page';
    }
}
