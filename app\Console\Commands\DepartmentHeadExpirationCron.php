<?php

namespace App\Console\Commands;

use App\Mail\HeadExpirationMail;
use App\Models\Department;
use App\Models\DepartmentHead;
use App\Models\Employee;
use App\Models\Faculty;
use App\Models\FacultyDean;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class DepartmentHeadExpirationCron extends Command
{

    protected $signature = 'headexp:cron';


    protected $description = 'Head Expiration Notification Send Dean Cron';

    public function handle()
    {
        $dataAfterMonth = date('Y-m-d', strtotime('+1 month'));

        $data = DepartmentHead::whereDate('end_date', '=', $dataAfterMonth)
            ->whereDate('end_date', '!=', '1970-01-01')
            ->where('active_status', 1)
            ->get();

        foreach ($data as $head) {

            $deptId = $head->department_id;
            $headEmp = $head->emp_no;

            if (App::environment('production')) {

                $facultyDeanEmail = $this->getFacultyDeanEmail($deptId);

            } else {

                $facultyDeanEmail = '<EMAIL>';
            }

            $emailData = [
                'departmentName' => $this->getDepartmentName($deptId),
                'headName' => $this->getDepartmentHeadName($headEmp),
                'headPosition' => $this->getHeadPositionName($deptId),
                'headTerminationDate' => $head->end_date,
                'deanName' => $this->getDeanName($deptId),
                'facultyName' => $this->getFacultyName($deptId),
            ];

            $mail = new HeadExpirationMail($emailData);

            if (!empty($facultyDeanEmail)) {
                Mail::to($facultyDeanEmail)->send($mail);
            } else {
                Log::error("Faculty Dean email is missing for Department ID: " . $deptId);
            }

        }

        return Command::SUCCESS;
    }

    private function getFacultyDeanEmail($deptId){

        $faculty =  Department::where('id', $deptId)->select('faculty_code')->first();
        $facultyDean = Faculty::where('id', $faculty->faculty_code)->select('dean_email')->first();

        return $facultyDean ? $facultyDean->dean_email : null;

    }

    private function getDepartmentName($deptId){

        $department = Department::where('id', $deptId)->select('department_name')->first();

        return $department ? $department->department_name : null;
    }

    private function getDepartmentHeadName($empNo){

        $departmentHeadName = Employee::join('categories', 'employees.title_id', '=', 'categories.id')->where('employee_no', $empNo)->select('initials','last_name','category_name')->first();

        return $departmentHeadName ? $departmentHeadName->category_name . ' ' . $departmentHeadName->initials . ' ' . $departmentHeadName->last_name : null;
    }

    private function getHeadPositionName($deptId){

        $departmentHead = DepartmentHead::where('department_id', $deptId)->first();

        return $departmentHead ? $departmentHead->HeadPositionName->category_name : null;
    }

    private function getDeanName($deptId){

        $faculty =  Department::where('id', $deptId)->select('faculty_code')->first();
        $facultyDean = FacultyDean::where('faculty_id', $faculty->faculty_code)->select('emp_no')->first();
        $deanName = Employee::join('categories', 'employees.title_id', '=', 'categories.id')->where('employee_no', $facultyDean->emp_no)->select('initials','last_name','category_name')->first();

        return $deanName ? $deanName->category_name . ' ' . $deanName->initials . ' ' . $deanName->last_name : null;
    }

    private function getFacultyName($deptId){

        $faculty =  Department::where('id', $deptId)->select('faculty_code')->first();
        $facultyName = Faculty::where('id', $faculty->faculty_code)->select('faculty_name')->first();

        return $facultyName ? $facultyName->faculty_name : null;
    }
}
