<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreVancancyRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {

        if($this->has('submit')){

        $rules['designation_id'] = 'required';
        $rules['main_category_id'] = 'required';

        if($this->get('main_category_id') == 44 || $this->get('main_category_id') == 204){

            $rules['faculty_id'] = 'required';
            $rules['department_id'] = 'required';

        }
        $rules['min_qualification'] = 'required';
        //$rules['min_age'] = 'required';
        //$rules['max_age'] = 'required';
        $rules['vacancy_visibility_status'] = 'required';
        $rules['date_opened'] = 'required|date|after:yesterday';
        $rules['date_closed'] = 'required|date|after:date_opened';
        $rules['operator'] = 'required';
        //$rules['vacancy_notice'] = 'required';

        }

        if($this->has('update')){

            $rules['designation_id'] = 'required';
            $rules['main_category_id'] = 'required';

            if($this->get('main_category_id') == 44 || $this->get('main_category_id') == 204){

                $rules['faculty_id'] = 'required';
                $rules['department_id'] = 'required';

            }
            $rules['min_qualification'] = 'required';
            //$rules['min_age'] = 'required';
            //$rules['max_age'] = 'required';
            $rules['vacancy_visibility_status'] = 'required';
            //$rules['date_opened'] = 'required|date';
            //$rules['date_closed'] = 'required|date|after:date_opened';
            //$rules['operator'] = 'required';

            }

        return $rules;

    }

    public function messages()
    {
        return [
            'designation_id.required' => 'select the relavent designation',
            'main_category_id.required' => 'select the relavent employeement category',
            'faculty_id.required' => 'select the relavent faculty',
            'department_id.required' => 'select the relavent department',
            'min_qualification.required' => 'select vacancy minimum requirement',
            'min_age.required' => 'enter minimum age limit in vacancy',
            'max_age.required' => 'enter maximum age limit in vacancy',
            'vacancy_visibility_status.required' => 'select the vacancy traget group',
            'date_opened.required' => 'vacancy publish date required',
            'date_closed.required' => 'vacancy close date required',
            'operator.required' => 'select at least one operator',
            'vacancy_notice.required' => 'vancancy notice should be upload',
        ];
    }
}
