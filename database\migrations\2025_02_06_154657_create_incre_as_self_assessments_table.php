<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('incre_as_self_assessments', function (Blueprint $table) {
            $table->id();
            $table->integer('incre_id');
            $table->string('incre_ref_no');
            $table->text('task')->nullable();
            $table->integer('assessment1')->default(0);
            $table->integer('assessment2')->default(0);
            $table->integer('assessment3')->default(0);
            $table->integer('assessment4')->default(0);
            $table->integer('assessment5')->default(0);
            $table->integer('user_id')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('incre_as_self_assessments');
    }
};
