<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('application_forms', function (Blueprint $table) {
            $table->integer('written_exam_status')->default(0)->after('short_list_date');
            $table->decimal('written_marks', 10, 2)->nullable()->after('written_exam_status');
            $table->date('written_exam_date')->nullable()->after('written_marks');
            $table->integer('practical_exam_status')->default(0)->after('written_exam_date');
            $table->decimal('practical_marks', 10, 2)->nullable()->after('practical_exam_status');
            $table->date('practical_exam_date')->nullable()->after('practical_marks');
            $table->decimal('interview_marks', 10, 2)->nullable()->after('interview_date');
            $table->date('interview_marks_date')->nullable()->after('interview_marks');
            $table->decimal('total_marks', 10, 2)->nullable()->after('interview_marks_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('application_forms', function (Blueprint $table) {
            $table->dropColumn('written_exam_status');
            $table->dropColumn('written_marks');
            $table->dropColumn('written_exam_date');
            $table->dropColumn('practical_exam_status');
            $table->dropColumn('practical_marks');
            $table->dropColumn('practical_exam_date');
            $table->dropColumn('interview_marks');
            $table->dropColumn('interview_marks_date');
            $table->dropColumn('total_marks');
        });
    }
};
