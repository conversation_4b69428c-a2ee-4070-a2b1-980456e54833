<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('vancancy_non_academics', function (Blueprint $table) {
            $table->id();
            $table->integer('designation_id')->default(0)->index();
            $table->string('display_name')->nullable();
            $table->date('date_opened');
            $table->date('date_closed');
            $table->integer('vacancy_visibility_status')->index();
            $table->integer('published_user_id');
            $table->date('published_date');
            $table->integer('updated_user_id')->nullable();
            $table->date('updated_date')->nullable();
            $table->integer('deadline_extented_status')->default(0);
            $table->integer('deadline_extented_emp')->nullable();
            $table->date('deadline_extented_date')->nullable();
            $table->integer('vacancy_status_id')->default(0);
            $table->integer('vacancy_status_type_id')->default(27);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vancancy_non_academics');
    }
};
