<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('salary_revision2025s', function (Blueprint $table) {
            $table->id();
            $table->integer('emp_no');
            $table->text('designation');
            $table->string('grade');
            $table->string('sal_code');
            $table->string('service_category');
            $table->integer('sal_step_2024');
            $table->double('bSal_2024');
            $table->double('sal_2024');
            $table->integer('sal_step_2025');
            $table->double('bSal_2025');
            $table->double('sal_2025');
            $table->double('unpaid_2025');
            $table->double('paid_2025');
            $table->integer('sal_step_withIncre_2025');
            $table->double('bSal_withIncre_2025');
            $table->double('sal_withIncre_2025');
            $table->double('unpaid_withIncre_2025');
            $table->double('paid_withIncre_2025');
            $table->integer('sal_step_2026');
            $table->double('unpaid_2026');
            $table->double('paid_2026');
            $table->integer('sal_step_withIncre_2026');
            $table->double('bSal_withIncre_2026');
            $table->double('sal_withIncre_2026');
            $table->double('unpaid_withIncre_2026');
            $table->double('paid_withIncre_2026');
            $table->double('sal_2027');
            $table->integer('status')->default(0);
            $table->integer('check_user')->default(0);
            $table->date('check_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('salary_revision2025s');
    }
};
