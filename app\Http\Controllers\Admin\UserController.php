<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Mail\UserActivateMail;
use App\Mail\UserDeactivateMail;
use App\Mail\UserRegistrationMail;
use App\Models\Employee;
use App\Models\LoginHistory;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Spatie\Activitylog\Models\Activity;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    public function __construct()
    {
        session()->put('special_callback_url', "");
        $this->middleware('auth');
        $this->middleware('role:super-admin|administrator|est-head');
    }

    public function UserView()
    {

        Log::info('UserController -> user view started');

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {


             $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('users.id','users.status_id', 'employees.initials', 'employees.last_name','employees.employee_no')
                ->groupBy('users.id','users.status_id', 'employees.initials', 'employees.last_name','employees.employee_no')
                ->selectRaw('GROUP_CONCAT(roles.name) as roles')
                ->where('users.id','!=',1)
                ->orderby('users.id')
                ->get();

        } elseif ($mainBranch == 52) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('users.id','users.status_id', 'employees.initials', 'employees.last_name','employees.employee_no')
                ->groupBy('users.id','users.status_id', 'employees.initials', 'employees.last_name','employees.employee_no')
                ->selectRaw('GROUP_CONCAT(roles.name) as roles')
                ->whereIn('roles.id', [5, 6, 7])
                ->where('users.main_branch_id', 52)
                ->distinct()
                ->orderby('users.id')
                ->get();

        } elseif ($mainBranch == 53) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('users.id','users.status_id', 'employees.initials', 'employees.last_name','employees.employee_no')
                ->groupBy('users.id','users.status_id', 'employees.initials', 'employees.last_name','employees.employee_no')
                ->selectRaw('GROUP_CONCAT(roles.name) as roles')
                ->whereIn('roles.id', [5, 6, 7])
                ->where('users.main_branch_id', 53)
                ->distinct()
                ->orderby('users.id')
                ->get();
        }

        //$ip = $request->ip(); //Dynamic IP address
        //$ip = trim(shell_exec("dig +short myip.opendns.com @resolver1.opendns.com"));
        //$ip = '**************'; /* Static IP address */
        //$currentUserInfo = Location::get($ip);
        //dd($currentUserInfo);
        return view('admin.user.index', compact('users'));

        Log::notice('UserController -> Users Count - ' . $users->count());
        Log::info('UserController -> user index ended');
    }

    public function UserAdd()
    {
        Log::info('UserController -> new user add started');

        $categories = $this->getCategories([13]);

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            $Employees = Employee::where('user_account_status', 0)
                ->where('employee_no', '!=', Auth()->user()->employee_no)
                ->where('employee_status_id',110)
                ->where('email','!=',NULL)
                ->get();
            $roles = Role::whereIn('id', array(2,3,4,5,6,7,14,15))->get();
            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [51, 52, 53]);

        } elseif ($mainBranch == 52) {

            $Employees = Employee::where('user_account_status', 0)
                ->where('employee_no', '!=', Auth()->user()->employee_no)
                ->where('employee_status_id',110)
                ->where('main_branch_id', 53)
                ->where('email','!=',NULL)
                ->get();
            $roles = Role::whereIn('id', array(5, 6, 7))->get();
            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);

        } elseif ($mainBranch == 53) {

            $Employees = Employee::where('user_account_status', 0)
                ->where('employee_no', '!=', Auth()->user()->employee_no)
                ->where('employee_status_id',110)
                ->where('main_branch_id', 53)
                ->where('email','!=',NULL)
                ->get();
            $roles = Role::whereIn('id', array(5, 6, 7))->get();
            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }
        return view('admin.user.add', compact('roles', 'Employees', 'mainBranches'));

        Log::info('UserController -> new user add ended');
    }

    public function UserStore(Request $request)
    {
        Log::info('UserController -> new user store started');

        $validatedData = $request->validate([
            'employee_no' => 'required',
            'role' => 'required',
            'main_branch_id' => 'required',
        ], [
            'employee_no.required' => 'select relavent employee to make user account',
            'role.required' => 'select relavent user role for user account',
            'main_branch_id.required' => 'select relavent user working branch to make user account',
        ]);

        //get employee name
        $last_name = Employee::where('employee_no', '=', $request->employee_no)
            ->select('last_name')
            ->get();

        $last_name = json_decode($last_name, true);
        $last_name = $last_name[0]["last_name"];

        //get employee email
        $email = Employee::where('employee_no', '=', $request->employee_no)
            ->select('email')
            ->get();

        $email = json_decode($email, true);
        $email = $email[0]["email"];

        $data = new User();
        $code = rand(1000, 9999);
        $data->employee_no =  $request->employee_no;
        $data->main_branch_id = $request->main_branch_id;
        $data->name = $last_name;
        $data->email = $email;
        $data->password = bcrypt($code);
        $data->status_id = 1;
        $data->my_number = $request->my_number;
        $data->created_user_id = Auth()->user()->employee_no;
        $data->save();

        $data->assignRole('user',$request->role);

        Log::notice('UserController -> Created user id - ' . $data->id . ' created by ' . auth()->user()->employee_no);
        Log::alert('UserController -> User login otp code resend - ' . $code . ' send to the ' . $request->employee_no . ' and ' . $email . ' with Name ' . $last_name);

        $emp = Employee::find($request->employee_no);
        $emp->user_account_status = 1;
        $emp->save();

        $data1 = [
            'name' => $last_name,
            'email' => $email,
            'password' => $code
        ];

        $mail = new UserRegistrationMail($data1);

        Mail::to($email)->send($mail);


        Log::info('UserController -> user store ended');

        $notification = array(
            'message' => 'User Inserted Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('user.view')->with($notification);
    }

    public function UserEdit($id)
    {
        Log::info('UserController -> user edit started');
        $userId = decrypt($id);
        $editData = User::find($userId);
        $categories = $this->getCategories([13]);

        $loginTempEmployees = Employee::where('user_account_status', 0)->where('email','!=',NULL)->get();

        $mainBranch = Auth()->user()->main_branch_id;

        if ($mainBranch == 51) {

            // $roles = Role::whereIn('id', array(2,3,4,5,6,7))->get();
            $mainBranches = $categories->where('category_type_id', '13')->whereIn('id', [51, 52, 53]);
        } elseif ($mainBranch == 52) {

            // $roles = Role::whereIn('id', array(5, 6, 7))->get();
            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 52);
        } elseif ($mainBranch == 53) {

            // $roles = Role::whereIn('id', array(5, 6, 7))->get();
            $mainBranches = $categories->where('category_type_id', '13')->where('id', '=', 53);
        }
        Log::notice('UserController -> edit user id - ' . $userId . ' edited by ' . auth()->user()->employee_no);
        Log::info('UserController -> user edit ended');
        return view('admin.user.edit', compact('editData', 'loginTempEmployees', 'mainBranches'));
    }

    public function UserUpdate(Request $request, $id)
    {

        Log::info('UserController -> role update started');
        $validatedData = $request->validate([
            'main_branch_id' => 'required',
        ], [
            'main_branch_id.required' => 'select relavent user working branch to make user account',
        ]);

        $data = User::find($id);
        $data->main_branch_id = $request->main_branch_id;
        $data->my_number = $request->my_number;
        $data->updated_user_id = Auth()->user()->employee_no;
        $data->save();

        Log::notice('UserController -> update user id - ' . $data->id . ' updated by ' . auth()->user()->employee_no);
        Log::info('UserController -> user update ended');

        $notification = array(
            'message' => 'User Updated Successfully',
            'alert-type' => 'info'
        );

        return redirect()->route('user.view')->with($notification);
    }

    public function UserDelete($id)
    {

        Log::info('UserController -> user delete started');
        $userId = decrypt($id);
        $user = User::find($userId);
        $user->delete();

        $emp = Employee::find($user->employee_no);
        $emp->user_account_status = 0;
        $emp->save();

        Log::notice('UserController -> delete User id - ' . $user->id . ' deleted by ' . auth()->user()->employee_no);
        Log::info('UserController -> user delete ended');


        $notification = array(
            'message' => 'User Deleted Successfully',
            'alert-type' => 'error'
        );

        return redirect()->route('user.view')->with($notification);
    }

    public function UserInactive($id)
    {

        Log::info('UserController -> user inactive started');
        $userId = decrypt($id);
        $user = User::find($userId);
        $user->status_id = 0;
        $user->updated_user_id = Auth()->user()->employee_no;
        $user->save();

        //$role_name = Role::find($user->role_id);

        $emailData = [
            'id' => $user->id,
            'name' =>  $user->name,
            'email' => $user->email,
            //'role' => $role_name->name
        ];

        //$mail = new UserDeactivateMail($emailData);
        //sending email
        //Mail::to($user->email)->send($mail);

        Log::notice('UserController -> inactive user id - ' . $user->id . ' inactivate by ' . auth()->user()->employee_no);
        Log::info('UserController -> user inactive ended');

        $notification = array(
            'message' => 'User Inactive successfully',
            'alert-type' => 'success'
        );

        return redirect()->back()->with($notification);
    } //end method


    public function UserActive($id)
    {

        Log::info('UserController -> user active started');
        $userId = decrypt($id);
        $user = User::find($userId);
        $user->status_id = 1;
        $user->updated_user_id = Auth()->user()->employee_no;
        $user->save();

        //$role_name = Role::find($user->role_id);

        $emailData = [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            //'role' => $role_name->name
        ];

        $mail = new UserActivateMail($emailData);
        //  //sending email
        //Mail::to($user->email)->send($mail);

        Log::notice('UserController -> active user id - ' . $user->id . ' inactivate by ' . auth()->user()->employee_no);
        Log::info('UserController -> user active ended');

        $notification = array(
            'message' => 'User Active successfully',
            'alert-type' => 'success'
        );

        return redirect()->back()->with($notification);
    } //end method

    public function UserDetail($id)
    {
        $categories = $this->getCategories([13]);
        //$userId = decrypt($id);
        $user = User::find($id);
        $employee = Employee::find($user->employee_no);
        $roles = Role::where('id','!=',1)->get();
        $permissions = Permission::all();
        $login_histories = LoginHistory::where('user_id', $id)->orderBy('created_at', 'DESC')->get();
        $main_branches = $categories->where('category_type_id', '13');
        return view('admin.user.detail', compact('user','employee', 'roles','permissions','main_branches', 'login_histories'));
    }

    public function CurrentUserView()
    {

        Log::info('UserController -> current user view started');

        $sessions = DB::table('sessions')
            ->join('users', 'users.id', '=', 'sessions.user_id')
            //->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
            //->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
            ->select('users.employee_no', 'sessions.user_id', 'sessions.id', 'sessions.ip_address', 'sessions.last_activity','employees.initials','employees.last_name','employees.email','users.main_branch_id')
            ->where('sessions.user_id', '!=', '')
            ->where('sessions.user_id', '!=', 1)
            ->orderBy('sessions.last_activity', 'DESC')
            //->distinct()
            ->get();

        return view('admin.user.current', compact('sessions'));

        Log::info('UserController -> current user view ended');
    }

    public function SessionRemove($id)
    {

        Log::info('UserController -> current user logout started');

        $session = DB::table('sessions')
            ->where('id', $id)
            ->delete();

        Log::notice('UserController -> session delete session id - ' . $id . ' deleted by ' . auth()->user()->employee_no);
        Log::info('UserController -> current user logout ended');

        $notification = array(
            'message' => 'User has beed logout Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('current.user.view')->with($notification);
    }

    public function loginHistoryView()
    {
        $employees = array();
        $start = "";
        $end = "";

        return view('admin.user.login_summary', compact('employees', 'start', 'end'));
    }

    public function loginHistorySearch(Request $request)
    {

        if ($request->start != NULL && $request->end != NULL) {


            $employees = DB::table('login_histories')
                         ->join('users', 'users.id', '=', 'login_histories.user_id')
                         //->join('roles', 'roles.id', '=', 'users.role_id')
                         ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                         ->select('employees.employee_no', 'employees.email', 'login_histories.ip', 'login_histories.login_time','login_histories.logout_time','employees.initials','employees.last_name','employees.email','users.main_branch_id')
                         //->whereBetween('login_histories.login_time',[date("Y-m-d", strtotime($request->start)).'%', date("Y-m-d", strtotime($request->end)).'%'])
                         ->where('login_histories.login_time', '>=', date("Y-m-d", strtotime($request->start)).'%')
                         ->where('login_histories.login_time', '<',date("Y-m-d", strtotime("+1 day", strtotime($request->end))).'%')
                         ->orderBy('login_histories.login_time', 'DESC')
                         ->get();
            $start = $request->start;
            $end = $request->end;

        return view('admin.user.login_summary', compact('employees', 'start', 'end'));



        } elseif ($request->start != NULL && $request->end == NULL) {

            $employees = DB::table('login_histories')
                         ->join('users', 'users.id', '=', 'login_histories.user_id')
                         //->join('roles', 'roles.id', '=', 'users.role_id')
                         ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                         ->select('employees.employee_no', 'employees.email', 'login_histories.ip', 'login_histories.login_time','login_histories.logout_time','employees.initials','employees.last_name','employees.email','users.main_branch_id')
                         ->where('login_histories.login_time', 'LIKE', '%'.date("Y-m-d", strtotime($request->start)).'%')
                         ->orderBy('login_histories.login_time', 'DESC')
                         ->get();

            $start = $request->start;
            $end = "";
            return view('admin.user.login_summary', compact('employees', 'start', 'end'));


        }  else {

            $employees = array();
            $start = "";
            $end = "";

        return view('admin.user.login_summary', compact('employees', 'start', 'end'));
        }
    }

    public function UserActiveLogView($id)
    {
         $userId = decrypt($id);
         $user_activities = Activity::where('causer_id',$userId)->latest()->get();

         return view('admin.user.active_log', compact('user_activities'));
    }

    public function ActiveLogHistoryView(Request $request){

        if ($request->user_id != NULL && $request->log_date != NULL && $request->event != NULL && $request->subject_type != NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                     ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                     ->get();

            $activity_events = Activity::distinct()->pluck('event');

            $activity_subject_types = Activity::distinct()->pluck('subject_type');

            $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                              ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                              //->join('roles', 'roles.id', '=', 'users.role_id')
                              ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                              ->where('causer_id',$request->user_id)
                              ->where('event',$request->event)
                              ->where('subject_type',$request->subject_type)
                              ->whereDate('activity_log.created_at', $request->log_date)
                              ->latest()
                              ->get();

            $user_id = $request->user_id;
            $log_date = $request->log_date;
            $event = $request->event;
            $subject = $request->subject_type;

            return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


        } elseif ($request->user_id != NULL && $request->log_date != NULL && $request->event != NULL && $request->subject_type == NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                     ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                     ->get();

            $activity_events = Activity::distinct()->pluck('event');

            $activity_subject_types = Activity::distinct()->pluck('subject_type');

            $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                              ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                              //->join('roles', 'roles.id', '=', 'users.role_id')
                              ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                              ->where('causer_id',$request->user_id)
                              ->where('event',$request->event)
                              //->where('subject_type',$request->subject_type)
                              ->whereDate('activity_log.created_at', $request->log_date)
                              ->latest()
                              ->get();

            $user_id = $request->user_id;
            $log_date = $request->log_date;
            $event = $request->event;
            $subject = "";

            return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


        } elseif ($request->user_id != NULL && $request->log_date != NULL && $request->event == NULL && $request->subject_type == NULL) {

          $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                     ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                     ->get();

            $activity_events = Activity::distinct()->pluck('event');

            $activity_subject_types = Activity::distinct()->pluck('subject_type');

            $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                              ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                              //->join('roles', 'roles.id', '=', 'users.role_id')
                              ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                              ->where('causer_id',$request->user_id)
                              //->where('event',$request->event)
                              //->where('subject_type',$request->subject_type)
                              ->whereDate('activity_log.created_at', $request->log_date)
                              ->latest()
                              ->get();

            $user_id = $request->user_id;
            $log_date = $request->log_date;
            $event = "";
            $subject = "";

            return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


        }elseif ($request->user_id != NULL && $request->log_date == NULL && $request->event == NULL && $request->subject_type == NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                       ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                       ->get();

              $activity_events = Activity::distinct()->pluck('event');

              $activity_subject_types = Activity::distinct()->pluck('subject_type');

              $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                ->where('causer_id',$request->user_id)
                                //->where('event',$request->event)
                                //->where('subject_type',$request->subject_type)
                                //->whereDate('activity_log.created_at', $request->log_date)
                                ->latest()
                                ->get();

              $user_id = $request->user_id;
              $log_date = "";
              $event = "";
              $subject = "";

              return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


          }elseif ($request->user_id == NULL && $request->log_date != NULL && $request->event != NULL && $request->subject_type != NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                       ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                       ->get();

              $activity_events = Activity::distinct()->pluck('event');

              $activity_subject_types = Activity::distinct()->pluck('subject_type');

              $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                //->where('causer_id',$request->user_id)
                                ->where('event',$request->event)
                                ->where('subject_type',$request->subject_type)
                                ->whereDate('activity_log.created_at', $request->log_date)
                                ->latest()
                                ->get();

            $user_id = "";
            $log_date = $request->log_date;
            $event = $request->event;
            $subject = $request->subject_type;

              return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


          }elseif ($request->user_id == NULL && $request->log_date == NULL && $request->event != NULL && $request->subject_type != NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                       ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                       ->get();

              $activity_events = Activity::distinct()->pluck('event');

              $activity_subject_types = Activity::distinct()->pluck('subject_type');

              $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                //->where('causer_id',$request->user_id)
                                ->where('event',$request->event)
                                ->where('subject_type',$request->subject_type)
                                //->whereDate('activity_log.created_at', $request->log_date)
                                ->latest()
                                ->get();

            $user_id = "";
            $log_date = "";
            $event = $request->event;
            $subject = $request->subject_type;

              return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


          }elseif ($request->user_id == NULL && $request->log_date == NULL && $request->event == NULL && $request->subject_type != NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                       ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                       ->get();

              $activity_events = Activity::distinct()->pluck('event');

              $activity_subject_types = Activity::distinct()->pluck('subject_type');

              $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                //->where('causer_id',$request->user_id)
                                //->where('event',$request->event)
                                ->where('subject_type',$request->subject_type)
                                //->whereDate('activity_log.created_at', $request->log_date)
                                ->latest()
                                ->get();

            $user_id = "";
            $log_date = "";
            $event = "";
            $subject = $request->subject_type;

              return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


          }elseif ($request->user_id != NULL && $request->log_date == NULL && $request->event != NULL && $request->subject_type != NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                       ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                       ->get();

              $activity_events = Activity::distinct()->pluck('event');

              $activity_subject_types = Activity::distinct()->pluck('subject_type');

              $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                ->where('causer_id',$request->user_id)
                                ->where('event',$request->event)
                                ->where('subject_type',$request->subject_type)
                                //->whereDate('activity_log.created_at', $request->log_date)
                                ->latest()
                                ->get();

            $user_id = $request->user_id;
            $log_date = "";
            $event = $request->event;
            $subject = $request->subject_type;

              return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


          }elseif ($request->user_id != NULL && $request->log_date == NULL && $request->event == NULL && $request->subject_type != NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                       ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                       ->get();

              $activity_events = Activity::distinct()->pluck('event');

              $activity_subject_types = Activity::distinct()->pluck('subject_type');

              $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                ->where('causer_id',$request->user_id)
                                //->where('event',$request->event)
                                ->where('subject_type',$request->subject_type)
                                //->whereDate('activity_log.created_at', $request->log_date)
                                ->latest()
                                ->get();

            $user_id = $request->user_id;
            $log_date = "";
            $event = "";
            $subject = $request->subject_type;

              return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


          }elseif ($request->user_id != NULL && $request->log_date == NULL && $request->event == NULL && $request->subject_type != NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                       ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                       ->get();

              $activity_events = Activity::distinct()->pluck('event');

              $activity_subject_types = Activity::distinct()->pluck('subject_type');

              $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                ->where('causer_id',$request->user_id)
                                //->where('event',$request->event)
                                //->where('subject_type',$request->subject_type)
                                ->whereDate('activity_log.created_at', $request->log_date)
                                ->latest()
                                ->get();

            $user_id = $request->user_id;
            $log_date =  $request->log_date;
            $event = "";
            $subject = "";

              return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


          }elseif ($request->user_id == NULL && $request->log_date != NULL && $request->event != NULL && $request->subject_type == NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                       ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                       ->get();

              $activity_events = Activity::distinct()->pluck('event');

              $activity_subject_types = Activity::distinct()->pluck('subject_type');

              $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                //->where('causer_id',$request->user_id)
                                ->where('event',$request->event)
                                //->where('subject_type',$request->subject_type)
                                ->whereDate('activity_log.created_at', $request->log_date)
                                ->latest()
                                ->get();

            $user_id = "";
            $log_date =  $request->log_date;
            $event = $request->event;
            $subject = "";

              return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


          }elseif ($request->user_id == NULL && $request->log_date != NULL && $request->event == NULL && $request->subject_type != NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                       ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                       ->get();

              $activity_events = Activity::distinct()->pluck('event');

              $activity_subject_types = Activity::distinct()->pluck('subject_type');

              $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                //->where('causer_id',$request->user_id)
                                //->where('event',$request->event)
                                ->where('subject_type',$request->subject_type)
                                ->whereDate('activity_log.created_at', $request->log_date)
                                ->latest()
                                ->get();

            $user_id = "";
            $log_date =  $request->log_date;
            $event = "";
            $subject = $request->subject_type;

              return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


          }elseif ($request->user_id == NULL && $request->log_date != NULL && $request->event == NULL && $request->subject_type != NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                       ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                       ->get();

              $activity_events = Activity::distinct()->pluck('event');

              $activity_subject_types = Activity::distinct()->pluck('subject_type');

              $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                //->where('causer_id',$request->user_id)
                                //->where('event',$request->event)
                                ->where('subject_type',$request->subject_type)
                                ->whereDate('activity_log.created_at', $request->log_date)
                                ->latest()
                                ->get();

            $user_id = "";
            $log_date =  $request->log_date;
            $event = "";
            $subject = $request->subject_type;

              return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


          }elseif ($request->user_id == NULL && $request->log_date != NULL && $request->event == NULL && $request->subject_type == NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                       ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                       ->get();

              $activity_events = Activity::distinct()->pluck('event');

              $activity_subject_types = Activity::distinct()->pluck('subject_type');

              $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                //->where('causer_id',$request->user_id)
                                //->where('event',$request->event)
                                //->where('subject_type',$request->subject_type)
                                ->whereDate('activity_log.created_at', $request->log_date)
                                ->latest()
                                ->get();

            $user_id = "";
            $log_date =  $request->log_date;
            $event = "";
            $subject = "";

              return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


          }elseif ($request->user_id == NULL && $request->log_date == NULL && $request->event != NULL && $request->subject_type == NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                       ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                       ->get();

              $activity_events = Activity::distinct()->pluck('event');

              $activity_subject_types = Activity::distinct()->pluck('subject_type');

              $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                //->where('causer_id',$request->user_id)
                                ->where('event',$request->event)
                                //->where('subject_type',$request->subject_type)
                                //->whereDate('activity_log.created_at', $request->log_date)
                                ->latest()
                                ->get();

            $user_id = "";
            $log_date =  "";
            $event = $request->event;
            $subject = "";

              return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


          }elseif ($request->user_id == NULL && $request->log_date == NULL && $request->event != NULL && $request->subject_type == NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                       ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                       ->get();

              $activity_events = Activity::distinct()->pluck('event');

              $activity_subject_types = Activity::distinct()->pluck('subject_type');

              $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                ->where('causer_id',$request->user_id)
                                //->where('event',$request->event)
                                //->where('subject_type',$request->subject_type)
                                //->whereDate('activity_log.created_at', $request->log_date)
                                ->latest()
                                ->get();

            $user_id = $request->user_id;
            $log_date =  "";
            $event = "";
            $subject = "";

              return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


          }elseif ($request->user_id != NULL && $request->log_date == NULL && $request->event != NULL && $request->subject_type == NULL) {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                       ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                       ->get();

              $activity_events = Activity::distinct()->pluck('event');

              $activity_subject_types = Activity::distinct()->pluck('subject_type');

              $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                ->where('causer_id',$request->user_id)
                                ->where('event',$request->event)
                                //->where('subject_type',$request->subject_type)
                                //->whereDate('activity_log.created_at', $request->log_date)
                                ->latest()
                                ->get();

            $user_id = $request->user_id;
            $log_date = "" ;
            $event = $request->event;
            $subject = "";

            return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));


          }else {

            $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                     ->select('users.id','employees.initials','employees.employee_no','employees.last_name')
                     ->get();

            $activity_events = Activity::distinct()->pluck('event');

            $activity_subject_types = Activity::distinct()->pluck('subject_type');


            $user_activities = Activity::join('users', 'users.id', '=', 'activity_log.causer_id')
                                ->join('employees', 'employees.employee_no', '=', 'users.employee_no')
                                //->join('roles', 'roles.id', '=', 'users.role_id')
                                ->select('activity_log.*','employees.employee_no','employees.initials','employees.last_name')
                                //->where('causer_id',$request->user_id)
                                //->where('event',$request->event)
                                //->where('subject_type',$request->subject_type)
                                ->whereDate('activity_log.created_at', date("Y-m-d"))
                                ->latest()
                                ->get();
            $user_id = "";
            $log_date = "";
            $event = "";
            $subject = "";

            return view('admin.user.log_summary', compact('users', 'user_activities', 'user_id','log_date','activity_events','activity_subject_types','event','subject'));
        }


    }

    public function UserShow($id)
    {
        $user = User::find($id);
        $roles = Role::all();
        $permissions = Permission::all();

        return view('admin.user.show', compact('user', 'roles', 'permissions'));
    }

    public function assignRole(Request $request, User $user)
    {
        $validatedData = $request->validate([
            'role' => 'required',
        ], [
            'role.required' => 'select valid role',
        ]);

        if ($user->hasRole($request->role)) {

            $notification = array(
                'message' => 'Role already add to the user',
                'alert-type' => 'info'
            );
            return redirect()->route('user.detail', ['id' => $user])->with($notification);
        }

        $user->assignRole($request->role);

        $notification = array(
            'message' => 'Role added to the user',
            'alert-type' => 'success'
        );
        return redirect()->route('user.detail', ['id' => $user])->with($notification);
    }

    public function removeRole(User $user, Role $role)
    {


        if ($user->hasRole($role)) {
            $user->removeRole($role);
            $notification = array(
                'message' => 'Role removed from the user',
                'alert-type' => 'error'
            );
            return redirect()->route('user.detail', ['id' => $user])->with($notification);
        }

        $notification = array(
            'message' => 'Role not exsits',
            'alert-type' => 'error'
        );
        return redirect()->route('user.detail', ['id' => $user])->with($notification);
    }

    public function givePermission(Request $request, User $user)
    {
        $validatedData = $request->validate([
            'permission' => 'required',
        ], [
            'permission.required' => 'select valid permission',
        ]);

        if ($user->hasPermissionTo($request->permission)) {

            $notification = array(
                'message' => 'Permission already add to the user',
                'alert-type' => 'info'
            );
            return redirect()->route('user.detail', ['id' => $user])->with($notification);
        }
        $user->givePermissionTo($request->permission);

        $notification = array(
            'message' => 'Permission added to the user',
            'alert-type' => 'success'
        );
        return redirect()->route('user.detail', ['id' => $user])->with($notification);
    }

    public function revokePermission(User $user, Permission $permission)
    {
        if ($user->hasPermissionTo($permission)) {
            $user->revokePermissionTo($permission);
            $notification = array(
                'message' => 'Permission revoked to the user',
                'alert-type' => 'warning'
            );
            return redirect()->route('user.detail', ['id' => $user])->with($notification);
        }

        $notification = array(
            'message' => 'Permission dose not exsits',
            'alert-type' => 'info'
        );
        return redirect()->route('user.detail', ['id' => $user])->with($notification);
    }

    public function UserRoleList(){

        $users = User::join('employees', 'employees.employee_no', '=', 'users.employee_no')
                ->join('model_has_roles', 'model_has_roles.model_id', '=', 'users.id')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->select('users.id','users.status_id', 'employees.initials', 'employees.last_name','employees.employee_no','users.main_branch_id','employees.employee_status_id','users.last_login_at')
                ->groupBy('users.id','users.status_id', 'employees.initials', 'employees.last_name','employees.employee_no','users.main_branch_id','employees.employee_status_id','users.last_login_at')
                ->selectRaw('GROUP_CONCAT(roles.name) as roles')
                ->orderby('users.id')
                ->get();


        return view('admin.user.user_role', compact('users'));
    }
}
