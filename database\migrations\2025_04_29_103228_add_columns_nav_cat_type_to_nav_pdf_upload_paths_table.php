<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('nav_pdf_upload_paths', function (Blueprint $table) {
            //
            $table->integer('nav_cat_type')->default(0)->after('user_id');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('nav_pdf_upload_paths', function (Blueprint $table) {
            $table->dropColumn('nav_cat_type');
            //
        });
    }
};
