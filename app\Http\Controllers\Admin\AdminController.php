<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Models\User;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth as FacadesAuth;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('PageControl:0');
    }

    public function ProfileView()
    {

        Log::info('AdminController -> admin profile started');

        $categories = $this->getCategories([13]);
        $id = FacadesAuth::user()->id;
        $employee_no = FacadesAuth::user()->employee_no;
        $user = User::find($id);
        $employee = Employee::find($employee_no);
        $roles = Role::all();
        $main_branches = $categories->where('category_type_id', '13');
        return view('admin.profile', compact('user', 'roles', 'main_branches','employee'));

        Log::info('AdminController -> admin profile ended');
    }

    public function PasswordView()
    {

        $id = Auth()->user()->id;

        $user = User::find($id);

        return view('admin.change_password', compact('user'));
    }

    public function PasswordUpdate(Request $request)
    {
        $validatedData = $request->validate([
            'oldpassword' => 'required',
            'password' => 'required|confirmed',
            'password_confirmation' => 'required'
        ], [
            'oldpassword.required' => 'The old password is required.',
            'password.required' => 'The new password is required.',
            'password.confirmed' => 'The new password and confirmation password do not match.',
            'password_confirmation.required' => 'The confirmation password is required.'
        ]);



        $hashedPassword = Auth()->user()->password;

        if (Hash::check($request->oldpassword, $hashedPassword)) {
            $user = User::find(Auth()->user()->id);
            $user->password = Hash::make($request->password);
            $user->save();

            Log::alert('AdminController -> ' . Auth()->user()->employee_no . 'password Changed');
            Auth::logout();
            return redirect()->route('login');
        } else {

            $notification = array(
                'message' => 'User faild update password.Try Again',
                'alert-type' => 'error'
            );
            return redirect()->route('password.view')->with($notification);
        }
    } // End Metod
}
