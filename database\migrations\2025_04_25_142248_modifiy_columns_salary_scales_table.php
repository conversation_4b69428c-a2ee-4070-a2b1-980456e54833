<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('salary_scales', function (Blueprint $table) {
            $table->dropColumn('start_date');
            $table->dropColumn('end_date');
            $table->integer('salary_scale_version_id')->after('id')->default(1);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('salary_scales', function (Blueprint $table) {
            $table->date('start_date')->after('salary_scale_txt')->nullable();
            $table->integer('end_date')->after('start_date')->nullable();
            $table->dropColumn('salary_scale_version_id');

        });
    }
};
